import { inAppDefaults } from 'apollo/cache';
import { NetworkState } from 'app/shared/hooks/network/useNetworkInfo';
import { useEffect } from 'react';

export const useListenToNetworkConnectionChange = () => {
  const handleConnectionChange = () => {
    const condition = navigator.onLine ? 'online' : 'offline';
    if (condition === 'online') {
      const webPing = setTimeout(() => {
        // TODO make f1etch to Clinify's API
        fetch('https://api.staging.myclinify.com/health')
          .then(async (res) => {
            const settings = inAppDefaults();
            if (!res.ok)
              return inAppDefaults({ ...settings, isConnected: NetworkState.DISCONNECTED });
            inAppDefaults({
              ...settings,
              isConnected:
                settings?.isConnected === NetworkState.DISCONNECTED
                  ? NetworkState?.RESTORED
                  : settings?.isConnected,
            });
            return clearInterval(webPing);
          })
          .catch(() => {
            const settings = inAppDefaults();
            inAppDefaults({ ...settings, isConnected: NetworkState.DISCONNECTED });
          });
      }, 2000);
    } else {
      const settings = inAppDefaults();
      return inAppDefaults({ ...settings, isConnected: NetworkState.DISCONNECTED });
    }
  };

  useEffect(() => {
    window.addEventListener('online', handleConnectionChange);
    window.addEventListener('offline', handleConnectionChange);

    return () => {
      window.removeEventListener('online', handleConnectionChange);
      window.removeEventListener('offline', handleConnectionChange);
    };
  }, []);
};
