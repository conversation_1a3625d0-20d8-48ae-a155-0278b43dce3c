import '@testing-library/jest-dom';
import 'fake-indexeddb/auto';

const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 39,
  key: jest.fn(),
};

global.localStorage = localStorageMock;

global.addToast = jest.fn();

jest.mock('app/shared/components/inputs/withDynamicRequiredFields', () => (Component) => Component);
jest.mock('app/shared/components/speech-to-text/RenderSpeechToText', () => (Component) =>
  Component,
);
jest.mock('app/shared/hoc/withLiveTranscription', () => (Component) => Component);
jest.mock('@excalidraw/excalidraw', () => ({}));
jest.mock('@akumzy/material-ui-pickers/adapter/date-fns', () => ({}));

jest.setTimeout(15000);

Object.defineProperty(window, 'sessionStorage', {
  value: {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
    length: 39,
    key: jest.fn(),
  },
  writable: true,
});

export {};
