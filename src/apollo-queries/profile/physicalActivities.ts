import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

const PHYSICAL_ACTIVITY_FIELD = gql`
  fragment PhysicalActivity on PhysicalActivityModel {
    id
    type
    name
    additionalNote
    profile {
      id
      clinifyId
    }
    ${AUDIT_FIELDS}
  }
`;

export const GET_PHYSICAL_ACTIVITIES = gql`
  query GetPhysicalActivities($filterInput: ProfileInfosFilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      physicalActivities(filterInput: $filterInput) {
        totalCount
        list {
          ...PhysicalActivity
        }
      }
    }
  }
  ${PHYSICAL_ACTIVITY_FIELD}
`;

export const ADD_PHYSICAL_ACTIVITY = gql`
  mutation AddPhysicalActivity($input: PhysicalActivityInput!, $id: String) {
    addPhysicalActivityInfo(input: $input, id: $id) {
      ...PhysicalActivity
    }
  }
  ${PHYSICAL_ACTIVITY_FIELD}
`;

export const UPDATE_PHYSICAL_ACTIVITY = gql`
  mutation UpdatePhysicalActivity($input: PhysicalActivityInput!, $id: String!) {
    updatePhysicalActivityInfo(input: $input, id: $id) {
      ...PhysicalActivity
    }
  }
  ${PHYSICAL_ACTIVITY_FIELD}
`;

export const DELETE_PHYSICAL_ACTIVITY = gql`
  mutation DeletePhysicalActivity($id: String!, $clinifyId: String!) {
    deletePhysicalActivityInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const PHYSICAL_ACTIVITY_ADDED_SUBS = gql`
  subscription PhysicalActivityAddedSubs($profileId: String!) {
    PhysicalActivityAdded(profileId: $profileId) {
      ...PhysicalActivity
    }
  }
  ${PHYSICAL_ACTIVITY_FIELD}
`;

export const PHYSICAL_ACTIVITY_UPDATED_SUBS = gql`
  subscription PhysicalActivityUpdatedSubs($profileId: String!) {
    PhysicalActivityUpdated(profileId: $profileId) {
      ...PhysicalActivity
    }
  }
  ${PHYSICAL_ACTIVITY_FIELD}
`;

export const DELETE_PHYSICAL_ACTIVITY_SUBS = gql`
  subscription DeletePhysicalActivitySubs($profileId: String!) {
    PhysicalActivityRemoved(profileId: $profileId) {
      id
    }
  }
`;
