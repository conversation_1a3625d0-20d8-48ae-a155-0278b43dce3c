import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

const DEVELOPMENTAL_HISTORY = gql`
  fragment DevelopmentalHistory on DevelopmentalHistoryModel {
    id
   groupName
   milestones {
    id 
    milestone
    ageRange
    whenMet
    ${AUDIT_FIELDS}
   }
  ${AUDIT_FIELDS}
  }
`;

export const GET_DEVELOPMENTAL_HISTORIES = gql`
  query getDevelopmentalHistories($filterInput: ProfileInfosFilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      developmentalHistories(filterInput: $filterInput) {
        totalCount
        list {
          ...DevelopmentalHistory
        }
      }
    }
  }
  ${DEVELOPMENTAL_HISTORY}
`;

export const ADD_DEVELOPMENTAL_HISTORY = gql`
  mutation AddDevelopmentalHistory($input: DevelopmentalHistoryInput!) {
    addDevelopmentalHistory(input: $input) {
      ...DevelopmentalHistory
    }
  }
  ${DEVELOPMENTAL_HISTORY}
`;

export const UPDATE_DEVELOPMENTAL_HISTORY = gql`
  mutation UpdateDevelopmentalHistory($input: DevelopmentalHistoryInput!) {
    updateDevelopmentalHistory(input: $input) {
      ...DevelopmentalHistory
    }
  }
  ${DEVELOPMENTAL_HISTORY}
`;

export const DELETE_DEVELOPMENTAL_HISTORY = gql`
  mutation DeleteDevelopmentalHistory($milestoneId: String!, $clinifyId: String!) {
    deleteDevelopmentalHistory(milestoneId: $milestoneId, clinifyId: $clinifyId) {
      ...DevelopmentalHistory
    }
  }
  ${DEVELOPMENTAL_HISTORY}
`;
