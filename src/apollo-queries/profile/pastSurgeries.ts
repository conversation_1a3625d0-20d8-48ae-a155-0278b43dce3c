import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

const PAST_SURGERY_FIELDS = gql`
  fragment PastSurgery on PastSurgeryModel {
    id
    type
    operationDate
    additionalNote
    profile {
      id
      clinifyId
    }
    ${AUDIT_FIELDS}
  }
`;

export const GET_PAST_SURGERY = gql`
  query GetPastSurgeries($filterInput: ProfileInfosFilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      pastSurgeries(filterInput: $filterInput) {
        totalCount
        list {
          ...PastSurgery
        }
      }
    }
  }
  ${PAST_SURGERY_FIELDS}
`;

export const ADD_PAST_SURGERY = gql`
  mutation AddPastSurgery($input: PastSurgeryInput!, $id: String) {
    addPastSurgeryInfo(input: $input, id: $id) {
      ...PastSurgery
    }
  }
  ${PAST_SURGERY_FIELDS}
`;

export const UPDATE_PAST_SURGERY = gql`
  mutation UpdatePastSurgery($input: PastSurgeryInput!, $id: String!) {
    updatePastSurgeryInfo(input: $input, id: $id) {
      ...PastSurgery
    }
  }
  ${PAST_SURGERY_FIELDS}
`;

export const DELETE_PAST_SURGERY = gql`
  mutation DeletePastSurgery($id: String!, $clinifyId: String!) {
    deletePastSurgeryInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const PAST_SURGERY_ADDED_SUBS = gql`
  subscription PastSurgeryAddedSubs($profileId: String!) {
    PastSurgeryAdded(profileId: $profileId) {
      ...PastSurgery
    }
  }
  ${PAST_SURGERY_FIELDS}
`;

export const PAST_SURGERY_UPDATED_SUBS = gql`
  subscription PastSurgeryUpdatedSubs($profileId: String!) {
    PastSurgeryUpdated(profileId: $profileId) {
      ...PastSurgery
    }
  }
  ${PAST_SURGERY_FIELDS}
`;

export const DELETE_PAST_SURGERY_SUBS = gql`
  subscription DeletePastSurgerySubs($profileId: String!) {
    PastSurgeryRemoved(profileId: $profileId) {
      id
    }
  }
`;
