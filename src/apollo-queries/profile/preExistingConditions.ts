import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

const PRE_EXISTING_CONDITION_FIELD = gql`
  fragment PreExistingCondition on PreExistingConditionModel {
    id
    ageOfOnset
    diagnosedDate
    duration
    additionalNote
    conditionICD10
    conditionICD11
    conditionSNOMED
    profile {
      id
      clinifyId
    }
    ${AUDIT_FIELDS}
  }
`;

export const GET_PRE_EXISTING_CONDITION = gql`
  query GetPreExistingConditions($filterInput: ProfileInfosFilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      preExistingConditions(filterInput: $filterInput) {
        totalCount
        list {
          ...PreExistingCondition
        }
      }
    }
  }
  ${PRE_EXISTING_CONDITION_FIELD}
`;

export const ADD_PRE_EXISTING_CONDITION = gql`
  mutation AddPreExistingCondition($input: PreExistingConditionInput!, $id: String) {
    addPreExistingConditionInfo(input: $input, id: $id) {
      ...PreExistingCondition
    }
  }
  ${PRE_EXISTING_CONDITION_FIELD}
`;

export const UPDATE_PRE_EXISTING_CONDITION = gql`
  mutation UpdatePreExistingCondition($input: PreExistingConditionInput!, $id: String!) {
    updatePreExistingConditionInfo(input: $input, id: $id) {
      ...PreExistingCondition
    }
  }
  ${PRE_EXISTING_CONDITION_FIELD}
`;

export const DELETE_PRE_EXISTING_CONDITION = gql`
  mutation DeletePreExistingCondition($id: String!, $clinifyId: String!) {
    deletePreExistingConditionInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const PRE_EXISTING_CONDITION_ADDED_SUBS = gql`
  subscription PreExistingConditionAddedSubs($profileId: String!) {
    PreExistingConditionAdded(profileId: $profileId) {
      ...PreExistingCondition
    }
  }
  ${PRE_EXISTING_CONDITION_FIELD}
`;

export const PRE_EXISTING_CONDITION_UPDATED_SUBS = gql`
  subscription PreExistingConditionUpdatedSubs($profileId: String!) {
    PreExistingConditionUpdated(profileId: $profileId) {
      ...PreExistingCondition
    }
  }
  ${PRE_EXISTING_CONDITION_FIELD}
`;

export const DELETE_PRE_EXISTING_CONDITION_SUBS = gql`
  subscription DeletePreExistingConditionSubs($profileId: String!) {
    PreExistingConditionRemoved(profileId: $profileId) {
      id
    }
  }
`;
