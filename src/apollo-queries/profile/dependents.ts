import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

const DEPENDENT_FIELDS = gql`
  fragment Dependent on DependentModel {
    id
    title
    firstName
    lastName
    middleName
    gender
    dateOfBirth
    bloodGroup
    relationship
    emailAddress
    phoneNumber {
      value
      countryCode
      countryName
    }
    hmoProfile {
      id
      memberPlan
      memberNumber
      primaryProviderId
      primaryProviderName
      primaryProviderAddress
      provider {
        id
        name
      }
    }
    profile {
      id
      clinifyId
    }
    displayPictureUrl
    ${AUDIT_FIELDS}
  }
`;

export const GET_DEPENDENTS = gql`
  query GetDependents($filterInput: ProfileInfosFilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      dependents(filterInput: $filterInput) {
        totalCount
        list {
          ...Dependent
        }
      }
    }
  }
  ${DEPENDENT_FIELDS}
`;

export const ADD_DEPENDENTS = gql`
  mutation AddDependents($input: DependentInput!, $id: String) {
    addDependentInfo(input: $input, id: $id) {
      ...Dependent
    }
  }
  ${DEPENDENT_FIELDS}
`;

export const UPDATE_DEPENDENTS = gql`
  mutation UpdateDependents($input: DependentInput!, $id: String!) {
    updateDependentInfo(input: $input, id: $id) {
      ...Dependent
    }
  }
  ${DEPENDENT_FIELDS}
`;

export const DELETE_DEPENDENT = gql`
  mutation DeleteDependent($id: String!, $clinifyId: String!) {
    deleteDependentInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const DEPENDENT_ADDED_SUBS = gql`
  subscription DependentAddedSubs($profileId: String!) {
    DependentAdded(profileId: $profileId) {
      ...Dependent
    }
  }
  ${DEPENDENT_FIELDS}
`;

export const DEPENDENT_UPDATED_SUBS = gql`
  subscription DependentUpdatedSubs($profileId: String!) {
    DependentUpdated(profileId: $profileId) {
      ...Dependent
    }
  }
  ${DEPENDENT_FIELDS}
`;

export const DELETE_DEPENDENT_SUBS = gql`
  subscription DeleteDependentSubs($profileId: String!) {
    DependentRemoved(profileId: $profileId) {
      id
    }
  }
`;
