import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

const ONCOLOGY_REGISTER_FIELDS = gql`
  fragment OncologyRegister on OncologyRegisterModel {
    id
    creatorName
    lastModifierName
    treatmentChart {
      noOfCycles
      periods {
        day
        cycles {
          cycleNo
          value
        }
      }
    }
    therapyChart {
      noOfCycles
      periods {
        day
        cycles {
          cycleNo
          value
        }
      }
    }
    createdDate
    updatedDate
  }
`;

const ONCOLOGY_HISTORY = gql`
  fragment OncologyHistory on OncologyHistoryModel {
    id
    initialDiagnosisICD10
    initialDiagnosisICD11
    initialDiagnosisSNOMED
    finalDiagnosisICD10
    finalDiagnosisICD11
    finalDiagnosisSNOMED
    diagnosisDateTime
    diagnosedBy
    additionalNote
    stageDiagnosisICD10
    stageDiagnosisICD11
    stageDiagnosisSNOMED
    stageDiagnosisDateTime
    classification
    stage
    stageDate
    histopathologicType
    stageTiming
    primaryTumor
    residualTumor
    tumorDetails {
      size
      laterality
    }
    lymphovascularInvasion
    regionalLymphNodes
    numberOfNodes
    distantMetastasis
    grade
    stageStatus
    cancerType
    progression
    relapse
    remission
    stageTreatmentType
    stageAdditionalNote
    treatmentType
    treatmentSite
    intentOfTreatment
    lineOfTreatment
    concurrentTreatment
    treatmentPlanProvider
    treatmentDepartment
    treatmentStatus
    treatmentPriority
    treatmentInterval
    treatmentStartDate
    treatmentEndDate
    treatmentCycleDays
    treatmentCycleNumber
    treatmentPatientType
    treatmentAdverseReaction
    treatmentSpecificReaction
    treatmentOutcome
    treatmentResponse
    treatmentFollowupDate
    treatmentAdditionalNote
    therapyType
    therapySite
    intentOfTherapy
    lineOfTherapy
    concurrentTherapy
    therapyPlanProvider
    therapyDepartment
    therapyStatus
    therapyPriority
    therapyInterval
    therapyStartDate
    therapyEndDate
    therapyCycleDays
    therapyCycleNumber
    therapyPatientType
    therapyAdverseReaction
    therapySpecificReaction
    therapyOutcome
    therapyResponse
    therapyFollowupDate
    therapyAdditionalNote
    oncologyRegister {
      ...OncologyRegister
    }
    profile {
      id
      clinifyId
    }
    ${AUDIT_FIELDS}
  }
  ${ONCOLOGY_REGISTER_FIELDS}
`;

export const GET_ONCOLOGY_HISTORIES = gql`
  query getOncologyHistories($filterInput: ProfileInfosFilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      oncologyHistories(filterInput: $filterInput) {
        totalCount
        list {
          ...OncologyHistory
        }
      }
    }
  }
  ${ONCOLOGY_HISTORY}
`;

export const ADD_ONCOLOGY_HISTORY = gql`
  mutation AddOncologyHistoryInfo($input: OncologyHistoryInput!, $id: String) {
    addOncologyHistoryInfo(input: $input, id: $id) {
      ...OncologyHistory
    }
  }
  ${ONCOLOGY_HISTORY}
`;

export const UPDATE_ONCOLOGY_HISTORY = gql`
  mutation UpdateOncologyHistoryInfo($input: OncologyHistoryInput!, $id: String!) {
    updateOncologyHistoryInfo(input: $input, id: $id) {
      ...OncologyHistory
    }
  }
  ${ONCOLOGY_HISTORY}
`;

export const DELETE_ONCOLOGY_HISTORY = gql`
  mutation DeleteOncologyHistoryInfo($id: String!, $clinifyId: String!) {
    deleteOncologyHistoryInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const UPDATE_ONCOLOGY_CHART = gql`
  mutation UpdateOncologyRegister(
    $clinifyId: String!
    $input: OncologyRegisterChart!
    $chartType: String!
    $id: String!
  ) {
    updateOncologyRegister(clinifyId: $clinifyId, input: $input, chartType: $chartType, id: $id) {
      ...OncologyRegister
    }
  }
  ${ONCOLOGY_REGISTER_FIELDS}
`;

export const ONCOLOGY_HISTORY_ADDED_SUBS = gql`
  subscription OncologyHistoryAddedSubs($profileId: String!) {
    OncologyAdded(profileId: $profileId) {
      ...OncologyHistory
    }
  }
  ${ONCOLOGY_HISTORY}
`;

export const ONCOLOGY_HISTORY_UPDATED_SUBS = gql`
  subscription OncologyHistoryUpdatedSubs($profileId: String!) {
    OncologyUpdated(profileId: $profileId) {
      ...OncologyHistory
    }
  }
  ${ONCOLOGY_HISTORY}
`;

export const DELETE_ONCOLOGY_HISTORY_SUBS = gql`
  subscription DeleteOncologyHistorySubs($profileId: String!) {
    OncologyRemoved(profileId: $profileId) {
      id
    }
  }
`;

export const ONCOLOGY_CHART_UPDATED_SUBS = gql`
  subscription OncologyChartUpdatedSubs($profileId: String!) {
    OncologyChartUpdated(profileId: $profileId) {
      ...OncologyRegister
    }
  }
  ${ONCOLOGY_REGISTER_FIELDS}
`;
