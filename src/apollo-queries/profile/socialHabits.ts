import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

const SOCIAL_HABIT = gql`
  fragment Habit on HabitModel {
    id
    socialHabit
    level
    duration
    typeSpecified
    cigrattesPerDay
    unitPerWeek
    additionalNote
    profile {
      id
      clinifyId
    }
    ${AUDIT_FIELDS}
  }
`;

export const GET_SOCIAL_HABIT = gql`
  query GetSocialhabits($filterInput: ProfileInfosFilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      habits(filterInput: $filterInput) {
        totalCount
        list {
          ...Habit
        }
      }
    }
  }
  ${SOCIAL_HABIT}
`;

export const ADD_SOCIAL_HABIT = gql`
  mutation AddSocialHabit($input: HabitInput!, $id: String) {
    addHabitInfo(input: $input, id: $id) {
      ...Habit
    }
  }
  ${SOCIAL_HABIT}
`;

export const UPDATE_SOCIAL_HABIT = gql`
  mutation UpdateSocialHabit($input: HabitInput!, $id: String!) {
    updateHabitInfo(input: $input, id: $id) {
      ...Habit
    }
  }
  ${SOCIAL_HABIT}
`;

export const DELETE_SOCIAL_HABIT = gql`
  mutation DeleteSocialHabit($id: String!, $clinifyId: String!) {
    deleteHabitInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const SOCIAL_HABIT_ADDED_SUBS = gql`
  subscription HabitAddedSubs($profileId: String!) {
    HabitAdded(profileId: $profileId) {
      ...Habit
    }
  }
  ${SOCIAL_HABIT}
`;

export const SOCIAL_HABIT_UPDATED_SUBS = gql`
  subscription HabitUpdatedSubs($profileId: String!) {
    HabitUpdated(profileId: $profileId) {
      ...Habit
    }
  }
  ${SOCIAL_HABIT}
`;

export const DELETE_SOCIAL_HABIT_SUBS = gql`
  subscription DeleteHabitSubs($profileId: String!) {
    HabitRemoved(profileId: $profileId) {
      id
    }
  }
`;
