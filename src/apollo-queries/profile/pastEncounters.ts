import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

const PAST_ENCOUNTER_FIELDS = gql`
  fragment PastEncounter on PastEncounterModel {
    id
    clinicName
    clinicAddress
    details {
      id
      diagnosisDate
      duration
      diagnosedBy
      specialty
      symptoms
      diagnosisICD10
      diagnosisICD11
      diagnosisSNOMED
    }
    additionalNote
    profile {
      id
      clinifyId
    }
    ${AUDIT_FIELDS}
  }
`;

export const GET_ENCOUNTERS = gql`
  query FetchEncounters($filterInput: ProfileInfosFilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      pastEncounters(filterInput: $filterInput) {
        totalCount
        list {
          ...PastEncounter
        }
      }
    }
  }
  ${PAST_ENCOUNTER_FIELDS}
`;

export const ADD_ENCOUNTER = gql`
  mutation AddEncounter($input: PastEncountersInput!, $id: String) {
    addPastEncounterInfo(input: $input, id: $id) {
      ...PastEncounter
    }
  }
  ${PAST_ENCOUNTER_FIELDS}
`;

export const UPDATE_ENCOUNTER = gql`
  mutation UpdateEncounter($input: PastEncountersInput!, $id: String!) {
    updatePastEncounterInfo(input: $input, id: $id) {
      ...PastEncounter
    }
  }
  ${PAST_ENCOUNTER_FIELDS}
`;

export const DELETE_ENCOUNTER = gql`
  mutation DeleteEncounter($id: String!, $clinifyId: String!) {
    deletePastEncounterInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const PAST_ENCOUNTER_ADDED_SUBS = gql`
  subscription PastEncounterAddedSubs($profileId: String!) {
    PastEncounterAdded(profileId: $profileId) {
      ...PastEncounter
    }
  }
  ${PAST_ENCOUNTER_FIELDS}
`;

export const PAST_ENCOUNTER_UPDATED_SUBS = gql`
  subscription PastEncounterUpdatedSubs($profileId: String!) {
    PastEncounterUpdated(profileId: $profileId) {
      ...PastEncounter
    }
  }
  ${PAST_ENCOUNTER_FIELDS}
`;

export const DELETE_PAST_ENCOUNTER_SUBS = gql`
  subscription DeletePastEncounterSubs($profileId: String!) {
    PastEncounterRemoved(profileId: $profileId) {
      id
    }
  }
`;
