import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

const OBSTETRIC_HISTORY = gql`
  fragment ObstetricHistory on ObstetricHistoryModel {
    id
    childrenCount
    lastBirth
    placeOfBirth
    modeOfBirth
    typeOfClient
    decisionSeekingCare
    modeOfTransportation
    parity
    partographUsed
    osytocinReceiveed
    misoprostolReceived
    marternalComplication
    receivedMgso4WithEclampsia
    counselledOnBreastFeeding
    counselledOnFamilyPlanning
    admitted
    discharged
    referredOut
    postAbortionCare
    motherStatus
    mdaConducted
    deliveryDateTime
    gender
    abortion
    preTerm
    notBreathingOrCrying
    babyResuscitated
    liveBirthWeight
    stillBirth
    deadLessThan7days
    liveHivBirth
    deliveredBy
    timeCordClamped
    chxGelApplied
    babyPutToBreast
    temperatureAt1hr
    temperatureAt1hrUnit
    additionalNote
    profile {
      id
      clinifyId
    }
    motherCauseOfDeath
    admittedForComplicationsOfUnsafeAbortion
    admittedOnKMC
    dischargedAfterKMC
    babyCauseOfDeath
    babyStatus
    ${AUDIT_FIELDS}
  }
`;

export const GET_OBSTETRICS = gql`
  query getObstetricHistories($filterInput: ProfileInfosFilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      obstetricHistories(filterInput: $filterInput) {
        totalCount
        list {
          ...ObstetricHistory
        }
      }
    }
  }
  ${OBSTETRIC_HISTORY}
`;

export const ADD_OBSTETRIC_HISTORY = gql`
  mutation AddObstetricHistory($input: ObstetricHistoryInput!, $id: String) {
    addObstetricInfo(input: $input, id: $id) {
      ...ObstetricHistory
    }
  }
  ${OBSTETRIC_HISTORY}
`;

export const UPDATE_OBSTETRIC_HISTORY = gql`
  mutation UpdateObstetrics($input: ObstetricHistoryInput!, $id: String!) {
    updateObstetricHistoryInfo(input: $input, id: $id) {
      ...ObstetricHistory
    }
  }
  ${OBSTETRIC_HISTORY}
`;

export const DELETE_OBSTETRIC_HISTORY = gql`
  mutation DeleteObstetricHistory($id: String!, $clinifyId: String!) {
    deleteObstetricInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const OBSTETRIC_HISTORY_ADDED_SUBS = gql`
  subscription ObstetricHistoryAddedSubs($profileId: String!) {
    ObstetricAdded(profileId: $profileId) {
      ...ObstetricHistory
    }
  }
  ${OBSTETRIC_HISTORY}
`;

export const OBSTETRIC_HISTORY_UPDATED_SUBS = gql`
  subscription ObstetricHistoryUpdatedSubs($profileId: String!) {
    ObstetricUpdated(profileId: $profileId) {
      ...ObstetricHistory
    }
  }
  ${OBSTETRIC_HISTORY}
`;

export const DELETE_OBSTETRIC_HISTORY_SUBS = gql`
  subscription DeleteObstetricHistorySubs($profileId: String!) {
    ObstetricRemoved(profileId: $profileId) {
      id
    }
  }
`;
