import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

const DISABILITY_FIELDS = gql`
  fragment Disability on DisabilityModel {
    id
    disability
    type
    additionalNote
    profile {
      id
      clinifyId
    }
    ${AUDIT_FIELDS}
  }
`;

export const GET_DISABILITY = gql`
  query GetDisabilities($filterInput: ProfileInfosFilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      disabilities(filterInput: $filterInput) {
        totalCount
        list {
          ...Disability
        }
      }
    }
  }
  ${DISABILITY_FIELDS}
`;

export const ADD_DISABILITY = gql`
  mutation AddDisabilty($input: DisabilityInput!, $id: String) {
    addDisabilityInfo(input: $input, id: $id) {
      ...Disability
    }
  }
  ${DISABILITY_FIELDS}
`;

export const UPDATE_DISABILITY = gql`
  mutation UpdateDisability($input: DisabilityInput!, $id: String!) {
    updateDisabilityInfo(input: $input, id: $id) {
      ...Disability
    }
  }
  ${DISABILITY_FIELDS}
`;

export const DELETE_DISABILITY = gql`
  mutation DeleteDisability($id: String!, $clinifyId: String!) {
    deleteDisabilityInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const DISABILITY_ADDED_SUBS = gql`
  subscription DisabilityAddedSubs($profileId: String!) {
    DisabilityAdded(profileId: $profileId) {
      ...Disability
    }
  }
  ${DISABILITY_FIELDS}
`;

export const DISABILITY_UPDATED_SUBS = gql`
  subscription DisabilityUpdatedSubs($profileId: String!) {
    DisabilityUpdated(profileId: $profileId) {
      ...Disability
    }
  }
  ${DISABILITY_FIELDS}
`;

export const DELETE_DISABILITY_SUBS = gql`
  subscription DeleteDisabilitySubs($profileId: String!) {
    DisabilityRemoved(profileId: $profileId) {
      id
    }
  }
`;
