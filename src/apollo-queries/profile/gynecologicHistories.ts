import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

export const GYNECOLOGIC_HISTORY = gql`
  fragment GynecologicHistory on GynecologicHistoryModel {
    id
    firstMenstrualAge
    menstrualCycleLength
    menstrualFlowDuration
    lastMenstrualPeriod
    menstrualFlow
    contraceptiveUse
    contraceptiveType
    specifyContraceptiveType
    newAcceptor
    revisit
    removeContraceptive
    quantityGiven
    miscarriageOrAbortion
    miscarriageOrAbortionCount
    pregnancyCount
    currentlyPregnant
    babyDelivered
    menstrualStatus
    breastFeeding
    sourceOfReferral
    counselledOnFp
    conunselledOnPpfp
    firstTimeFpUser
    emergencyContraception
    familyPlanningClientType
    referredOut
    followupVisit
    additionalNote
    profile {
      id
      clinifyId
    }
    ${AUDIT_FIELDS}
  }
`;

export const GET_GYNECOLOGIC_HISTORIES = gql`
  query GetGynecologicHistories($filterInput: ProfileInfosFilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      gynecologicHistories(filterInput: $filterInput) {
        totalCount
        list {
          ...GynecologicHistory
        }
      }
    }
  }
  ${GYNECOLOGIC_HISTORY}
`;

export const ADD_GYNECOLOGIC_HISTORY = gql`
  mutation AddGynecologicHistory($input: GynecologicHistoryInput!, $id: String) {
    addGynecologicHistoryInfo(input: $input, id: $id) {
      ...GynecologicHistory
    }
  }
  ${GYNECOLOGIC_HISTORY}
`;

export const UPDATE_GYNECOLOGIC_HISTORY = gql`
  mutation UpdateGynecologicHistory($input: GynecologicHistoryInput!, $id: String!) {
    updateGynecologicHistoryInfo(input: $input, id: $id) {
      ...GynecologicHistory
    }
  }
  ${GYNECOLOGIC_HISTORY}
`;

export const DELETE_GYNECOLOGIC_HISTORY = gql`
  mutation DeleteGynecologicHistory($id: String!, $clinifyId: String!) {
    deleteGynecologicInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const GYNECOLOGIC_HISTORY_ADDED_SUBS = gql`
  subscription GynecologicHistoryAddedSubs($profileId: String!) {
    GynecologicHistoryAdded(profileId: $profileId) {
      ...GynecologicHistory
    }
  }
  ${GYNECOLOGIC_HISTORY}
`;

export const GYNECOLOGIC_HISTORY_UPDATED_SUBS = gql`
  subscription GynecologicHistoryUpdatedSubs($profileId: String!) {
    GynecologicHistoryUpdated(profileId: $profileId) {
      ...GynecologicHistory
    }
  }
  ${GYNECOLOGIC_HISTORY}
`;

export const DELETE_GYNECOLOGIC_HISTORY_SUBS = gql`
  subscription DeleteGynecologicHistorySubs($profileId: String!) {
    GynecologicHistoryRemoved(profileId: $profileId) {
      id
    }
  }
`;
