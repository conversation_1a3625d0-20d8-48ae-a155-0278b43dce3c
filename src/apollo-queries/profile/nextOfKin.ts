import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

const NEXT_OF_KIN_FIELDS = gql`
  fragment NextOfKin on NextOfKinModel {
    id
    firstName
    lastName
    gender
    title
    middleName
    bloodGroup
    genoType
    phoneNumber {
      countryName
      countryCode
      value
    }
    phoneNumberAlt {
      countryName
      countryCode
      value
    }
    email
    emailAlt
    relationship
    occupation
    address
    profile {
      id
      clinifyId
    }
    ${AUDIT_FIELDS}
  }
`;

export const GET_NEXT_OF_KIN = gql`
  query GetNextOfKins($filterInput: ProfileInfosFilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      nextOfKins(filterInput: $filterInput) {
        totalCount
        list {
          ...NextOfKin
        }
      }
    }
  }
  ${NEXT_OF_KIN_FIELDS}
`;

export const ADD_NEXT_OF_KIN = gql`
  mutation AddNextOfKin($input: NextOfKinInput!, $id: String) {
    addNextOfKinInfo(input: $input, id: $id) {
      ...NextOfKin
    }
  }
  ${NEXT_OF_KIN_FIELDS}
`;

export const UPDATE_NEXT_OF_KIN = gql`
  mutation UpdateNextOfKin($input: NextOfKinInput!, $id: String!) {
    updateNextOfKinInfo(input: $input, id: $id) {
      ...NextOfKin
    }
  }
  ${NEXT_OF_KIN_FIELDS}
`;

export const DELETE_NEXT_OF_KIN = gql`
  mutation DeleteNextOfKin($id: String!, $clinifyId: String!) {
    deleteNextOfKinInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const NEXT_OF_KIN_ADDED_SUBS = gql`
  subscription NextOfKinAddedSubs($profileId: String!) {
    NextOfKinAdded(profileId: $profileId) {
      ...NextOfKin
    }
  }
  ${NEXT_OF_KIN_FIELDS}
`;

export const NEXT_OF_KIN_UPDATED_SUBS = gql`
  subscription NextOfKinUpdatedSubs($profileId: String!) {
    NextOfKinUpdated(profileId: $profileId) {
      ...NextOfKin
    }
  }
  ${NEXT_OF_KIN_FIELDS}
`;

export const DELETE_NEXT_OF_KIN_SUBS = gql`
  subscription DeleteNextOfKinSubs($profileId: String!) {
    NextOfKinRemoved(profileId: $profileId) {
      id
    }
  }
`;
