import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

const FAMILY_HISTORY = gql`
  fragment FamilyHistory on FamilyHistoryModel {
    id
    title
    firstName
    lastName
    middleName
    lastName
    dateOfBirth
    bloodGroup
    relationship
    conditions {
      diagnosisICD10
      diagnosisICD11
      diagnosisSNOMED
      ageOfOnset
    }
    gender
    additionalNote
    profile {
      id
      clinifyId
    }
    ${AUDIT_FIELDS}
  }
`;

export const GET_FAMILY_HISTORIES = gql`
  query GetFamilyHistories($filterInput: ProfileInfosFilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      familyHistories(filterInput: $filterInput) {
        totalCount
        list {
          ...FamilyHistory
        }
      }
    }
  }
  ${FAMILY_HISTORY}
`;

export const ADD_FAMILY_HISTORY = gql`
  mutation AddFamilyHistory($input: FamilyHistoryInput!, $id: String) {
    addFamilyHistoryInfo(input: $input, id: $id) {
      ...FamilyHistory
    }
  }
  ${FAMILY_HISTORY}
`;

export const UPDATE_FAMILY_HISTORY = gql`
  mutation UpdateFamilyHistory($input: FamilyHistoryInput!, $id: String!) {
    updateFamilyHistoryInfo(input: $input, dependentId: $id) {
      ...FamilyHistory
    }
  }
  ${FAMILY_HISTORY}
`;

export const DELETE_FAMILY_HISTORY = gql`
  mutation DeleteFamilyHistory($id: String!, $clinifyId: String!) {
    deleteFamilyHistoryInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const FAMILY_HISTORY_ADDED_SUBS = gql`
  subscription FamilyHistoryAddedSubs($profileId: String!) {
    FamilyHistoryAdded(profileId: $profileId) {
      ...FamilyHistory
    }
  }
  ${FAMILY_HISTORY}
`;

export const FAMILY_HISTORY_UPDATED_SUBS = gql`
  subscription FamilyHistoryUpdatedSubs($profileId: String!) {
    FamilyHistoryUpdated(profileId: $profileId) {
      ...FamilyHistory
    }
  }
  ${FAMILY_HISTORY}
`;

export const DELETE_FAMILY_HISTORY_SUBS = gql`
  subscription DeleteFamilyHistorySubs($profileId: String!) {
    FamilyHistoryRemoved(profileId: $profileId) {
      id
    }
  }
`;
