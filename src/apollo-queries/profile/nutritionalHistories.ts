import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

const NUTRITIONAL_HISTORY = gql`
  fragment NutritionalHistory on NutritionalHistoryModel {
    id
    feedingMethod
    feedingDifficulties
    counseledOnNutrition
    receivedNutritionServices
    childIsGrowingWell
    receivedMicroNutrientPowder
    admittedToTreatSevereMalnutrition
    outcomeTreatingSevereMalnutrition
    dietaryRecallNote
    feedingSinceBirthNote
    additionalNote
     ${AUDIT_FIELDS}
  }
`;

const NUTRITIONAL_HISTORY_GROWTH = gql`
  fragment NutritionalHistoryGrowth on NutritionalHistoryGrowthModel {
    id
    months
    years
    weight
    height
    babyLength
    headCircumference
    measurementType
     ${AUDIT_FIELDS}
  }
`;

export const GET_NUTRITIONAL_HISTORIES = gql`
  query getNutritionalHistories($filterInput: ProfileInfosFilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      nutritionalHistories(filterInput: $filterInput) {
        totalCount
        list {
          ...NutritionalHistory
        }
      }
    }
  }
  ${NUTRITIONAL_HISTORY}
`;

export const ADD_NUTRITIONAL_HISTORY = gql`
  mutation AddNutritionalHistory($input: NutritionalHistoryInput!) {
    addNutritionalHistory(input: $input) {
      ...NutritionalHistory
    }
  }
  ${NUTRITIONAL_HISTORY}
`;

export const UPDATE_NUTRITIONAL_HISTORY = gql`
  mutation UpdateNutritionalHistory($input: NutritionalHistoryInput!, $id: String!) {
    updateNutritionalHistory(input: $input, id: $id) {
      ...NutritionalHistory
    }
  }
  ${NUTRITIONAL_HISTORY}
`;

export const DELETE_NUTRITIONAL_HISTORY = gql`
  mutation DeleteNutritionalHistory($id: String!, $clinifyId: String!) {
    deleteNutritionalHistory(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const GET_NUTRITIONAL_HISTORY_GROWTH = gql`
  query getNutritionalHistoryGrowth($filterInput: ProfileInfosFilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      nutritionalHistoryGrowths(filterInput: $filterInput) {
        totalCount
        list {
          ...NutritionalHistoryGrowth
        }
      }
    }
  }
  ${NUTRITIONAL_HISTORY_GROWTH}
`;

export const ADD_NUTRITIONAL_HISTORY_GROWTH = gql`
  mutation AddNutritionalHistoryGrowth($input: NutritionalHistoryGrowthInput!) {
    addNutritionalHistoryGrowth(input: $input) {
      ...NutritionalHistoryGrowth
    }
  }
  ${NUTRITIONAL_HISTORY_GROWTH}
`;

export const UPDATE_NUTRITIONAL_HISTORY_GROWTH = gql`
  mutation UpdateNutritionalHistoryGrowth($input: NutritionalHistoryGrowthInput!, $id: String!) {
    updateNutritionalHistoryGrowth(input: $input, id: $id) {
      ...NutritionalHistoryGrowth
    }
  }
  ${NUTRITIONAL_HISTORY_GROWTH}
`;

export const DELETE_NUTRITIONAL_HISTORY_GROWTH = gql`
  mutation DeleteNutritionalHistoryGrowth($id: String!, $clinifyId: String!) {
    deleteNutritionalHistoryGrowth(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;
