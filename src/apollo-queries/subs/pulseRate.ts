import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

export const PULSE_RATE_FIELDS = gql`
  fragment PulseRate on PulseRateModel {
    id
    readingDateTime
    reading
    checkMethod
    checkMethodSpecify
    rhythm
    additionalNote
    concealAdditionalNote
    isReadingCritical
  }
`;

export const ADD_PULSE_RATE = gql`
  mutation AddPulseRate($input: PulseRateVitalFields!, $parentRecordId: String!, $clinifyId: String!) {
    addPulseRateInfo(input: $input, vitalId: $parentRecordId, clinifyId: $clinifyId) {
      ...PulseRate
      ${AUDIT_FIELDS}
    }
  }
  ${PULSE_RATE_FIELDS}
`;

export const UPDATE_PULSE_RATE = gql`
  mutation UpdatePulseRateInfo($input: PulseRateVitalFields!, $id: String!, $clinifyId: String!) {
    updatePulseRateInfo(input: $input, id: $id, clinifyId: $clinifyId) {
      ...PulseRate
      ${AUDIT_FIELDS}
    }
  }
  ${PULSE_RATE_FIELDS}
`;

export const GET_PULSE_RATE_INFOS = gql`
  query GetPulseRateInfos($parentRecordId: String!, $clinifyId: String!) {
    getPulseRateInfos(vitalId: $parentRecordId, clinifyId: $clinifyId) {
      ...PulseRate
      ${AUDIT_FIELDS}
    }
  }
  ${PULSE_RATE_FIELDS}
`;

export const DELETE_PULSE_RATE = gql`
  mutation DeletePulseRate($id: String!, $clinifyId: String!) {
    deletePulseRateInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const PULSE_RATE_ADDED_SUBS = gql`
  subscription PulseRateAddedSubs($profileId: String!) {
    PulseRateAdded(profileId: $profileId) {
      ...PulseRate
      vitalId
      ${AUDIT_FIELDS}
    }
  }
  ${PULSE_RATE_FIELDS}
`;

export const PULSE_RATE_UPDATED_SUBS = gql`
  subscription PulseRateUpdatedSubs($profileId: String!) {
    PulseRateUpdated(profileId: $profileId) {
      ...PulseRate
      ${AUDIT_FIELDS}
    }
  }
  ${PULSE_RATE_FIELDS}
`;

export const DELETE_PULSE_RATE_SUBS = gql`
  subscription DeletePulseRateSubs($profileId: String!) {
    PulseRateRemoved(profileId: $profileId) {
      id
      vitalId
    }
  }
`;
