import { gql } from '@apollo/client';

export const GRANT_ORG_PERMISSION_SUB = gql`
  subscription organizationPermissionGrantedSubs($recipientId: String!) {
    OrganizationPermissionGranted(recipientId: $recipientId) {
      id
      rules {
        action
        subject
        conditions {
          organizationId
        }
      }
    }
  }
`;

export const REVOKE_ORG_PERMISSION_SUB = gql`
  subscription organizationPermissionRevokedSubs($recipientId: String!) {
    OrganizationPermissionRevoked(recipientId: $recipientId) {
      id
      rules {
        action
        subject
        conditions {
          organizationId
        }
      }
    }
  }
`;
