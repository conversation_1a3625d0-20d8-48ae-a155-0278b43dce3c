import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

export const ANTHROPOMETRY_FIELDS = gql`
  fragment Anthropometry on AnthropometryModel {
    id
    bmi
    bsa
    readingDateTime
    height
    heightUnit
    weight
    weightUnit
    hipCircumference
    hipCircumferenceUnit
    waistCircumference
    waistCircumferenceUnit
    skinfoldThickness
    skinfoldThicknessUnit
    leftUpperLimbCircumference
    rightUpperLimbCircumference
    upperLimbCircumferenceUnit
    leftLowerLimbCircumference
    rightLowerLimbCircumference
    lowerLimbCircumferenceUnit
    leftThighCircumference
    rightThighCircumference
    thighCircumferenceUnit
    abdominalGirth
    abdominalGirthUnit
    additionalNote
    concealAdditionalNote
    isAbdominalGirthCritical
    isHeightCritical
    isWeightCritical
    isSkinfoldThicknessCritical
    isWaistCircumferenceCritical
    isHipCircumferenceCritical
  }
`;

export const ADD_ANTHROPOMETRY = gql`
  mutation AddAnthropometry($input: AnthropometryVitalFields!, $parentRecordId: String!, $clinifyId: String!) {
    addAnthropometryInfo(input: $input, vitalId: $parentRecordId, clinifyId: $clinifyId) {
      ...Anthropometry
      ${AUDIT_FIELDS}
    }
  }
  ${ANTHROPOMETRY_FIELDS}
`;

export const UPDATE_ANTHROPOMETRY = gql`
  mutation UpdateAnthropometryInfo($input: AnthropometryVitalFields!, $id: String!, $clinifyId: String!) {
    updateAnthropometryInfo(input: $input, id: $id, clinifyId: $clinifyId) {
      ...Anthropometry
      ${AUDIT_FIELDS}
    }
  }
  ${ANTHROPOMETRY_FIELDS}
`;

export const GET_ANTHROPOMETRY_INFOS = gql`
  query GetAnthropometryInfos($parentRecordId: String!, $clinifyId: String!) {
    getAnthropometryInfos(vitalId: $parentRecordId, clinifyId: $clinifyId) {
      ...Anthropometry
      ${AUDIT_FIELDS}
    }
  }
  ${ANTHROPOMETRY_FIELDS}
`;

export const DELETE_ANTHROPOMETRY = gql`
  mutation DeleteAnthropometry($id: String!, $clinifyId: String!) {
    deleteAnthropometryInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const ANTHROPOMETRY_ADDED_SUBS = gql`
  subscription AnthropometryAddedSubs($profileId: String!) {
    AnthropometryAdded(profileId: $profileId) {
      ...Anthropometry
      vitalId
      ${AUDIT_FIELDS}
    }
  }
  ${ANTHROPOMETRY_FIELDS}
`;

export const ANTHROPOMETRY_UPDATED_SUBS = gql`
  subscription AnthropometryUpdatedSubs($profileId: String!) {
    AnthropometryUpdated(profileId: $profileId) {
      ...Anthropometry
      ${AUDIT_FIELDS}
    }
  }
  ${ANTHROPOMETRY_FIELDS}
`;

export const DELETE_ANTHROPOMETRY_SUBS = gql`
  subscription DeleteAnthropometrySubs($profileId: String!) {
    AnthropometryRemoved(profileId: $profileId) {
      id
      vitalId
    }
  }
`;

export const CONCEAL_ANTHROPOMETRY_ADDITIONAL_NOTE = gql`
  mutation ConcealAnthropometryAdditionalNote($id: String!, $concealStatus: Boolean!, $clinifyId: String!) {
    concealAnthropometryAdditionalNote(anthropometryId: $id, concealStatus: $concealStatus, clinifyId: $clinifyId) {
      id
      concealAdditionalNote
      additionalNote
        ${AUDIT_FIELDS}
    }
  }
`;

export const CONCEAL_BLOOD_GLUCOSE_ADDITIONAL_NOTE = gql`
  mutation ConcealBloodGlucoseAdditionalNote(
    $id: String!
    $concealStatus: Boolean!
    $clinifyId: String!
  ) {
    concealBloodGlucoseAdditionalNote(
      bloodGlucoseId: $id
      concealStatus: $concealStatus
      clinifyId: $clinifyId
    ) {
      id
      concealAdditionalNote
      additionalNote
    }
  }
`;

export const CONCEAL_BLOOD_PRESSURE_ADDITIONAL_NOTE = gql`
  mutation ConcelBloodPressureAdditionalNote(
    $id: String!
    $concealStatus: Boolean!
    $clinifyId: String!
  ) {
    concealBloodPressureAdditionalNote(
      bloodPressureId: $id
      concealStatus: $concealStatus
      clinifyId: $clinifyId
    ) {
      id
      additionalNote
      concealAdditionalNote
    }
  }
`;

export const CONCEAL_PULSE_RATE_ADDITIONAL_NOTE = gql`
  mutation ConcealPulseRateAdditionalNote(
    $id: String!
    $concealStatus: Boolean!
    $clinifyId: String!
  ) {
    concealPulseRateAdditionalNote(
      pulseRateId: $id
      concealStatus: $concealStatus
      clinifyId: $clinifyId
    ) {
      id
      concealAdditionalNote
      additionalNote
    }
  }
`;

export const CONCEAL_RESPIRATORY_RATE_ADDITIONAL_NOTE = gql`
  mutation ConcealRespiratoryRateAdditionalNote(
    $id: String!
    $concealStatus: Boolean!
    $clinifyId: String!
  ) {
    concealRespiratoryRateAdditionalNote(
      respiratoryRateId: $id
      concealStatus: $concealStatus
      clinifyId: $clinifyId
    ) {
      id
      concealAdditionalNote
      additionalNote
    }
  }
`;

export const CONCEAL_TEMPERATURE_ADDITIONAL_NOTE = gql`
  mutation ConcealTemperatureAdditionalNote(
    $id: String!
    $concealStatus: Boolean!
    $clinifyId: String!
  ) {
    concealTemperatureAdditionalNote(
      temperatureId: $id
      concealStatus: $concealStatus
      clinifyId: $clinifyId
    ) {
      id
      additionalNote
      concealAdditionalNote
    }
  }
`;

export const CONCEAL_URINE_DIPSTICK_ADDITIONAL_NOTE = gql`
  mutation ConcealUrineDipstickAdditionalNote(
    $id: String!
    $concealStatus: Boolean!
    $clinifyId: String!
  ) {
    concealUrineDipstickAdditionalNote(
      urineDipstickId: $id
      concealStatus: $concealStatus
      clinifyId: $clinifyId
    ) {
      id
      additionalNote
      concealAdditionalNote
    }
  }
`;
