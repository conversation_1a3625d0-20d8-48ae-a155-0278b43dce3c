import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

export const VISUAL_ACUITY_FIELDS = gql`
  fragment VisualAcuity on VisualAcuityModel {
    id
    readingDateTime
    withGlassesLeft
    withGlassesRight
    withoutGlassesLeft
    withoutGlassesRight
    additionalNote
    concealAdditionalNote
  }
`;

export const ADD_VISUAL_ACUITY = gql`
  mutation AddVisualAcuity($input: VisualAcuityVitalFields!, $parentRecordId: String!, $clinifyId: String!) {
    addVisualAcuityInfo(input: $input, vitalId: $parentRecordId, clinifyId: $clinifyId) {
      ...VisualAcuity
      ${AUDIT_FIELDS}
    }
  }
  ${VISUAL_ACUITY_FIELDS}
`;

export const UPDATE_VISUAL_ACUITY = gql`
  mutation UpdateVisualAcuityInfo($input: VisualAcuityVitalFields!, $id: String!, $clinifyId: String!) {
    updateVisualAcuityInfo(input: $input, id: $id, clinifyId: $clinifyId) {
      ...VisualAcuity
      ${AUDIT_FIELDS}
    }
  }
  ${VISUAL_ACUITY_FIELDS}
`;

export const GET_VISUAL_ACUITY_INFOS = gql`
  query GetVisualAcuityInfos($parentRecordId: String!, $clinifyId: String!) {
    getVisualAcuityInfos(vitalId: $parentRecordId, clinifyId: $clinifyId) {
      ...VisualAcuity
      ${AUDIT_FIELDS}
    }
  }
  ${VISUAL_ACUITY_FIELDS}
`;

export const DELETE_VISUAL_ACUITY = gql`
  mutation DeleteVisualAcuity($id: String!, $clinifyId: String!) {
    deleteVisualAcuityInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const VISUAL_ACUITY_ADDED_SUBS = gql`
  subscription VisualAcuityAddedSubs($profileId: String!) {
    VisualAcuityAdded(profileId: $profileId) {
      ...VisualAcuity
      vitalId
      ${AUDIT_FIELDS}
    }
  }
  ${VISUAL_ACUITY_FIELDS}
`;

export const VISUAL_ACUITY_UPDATED_SUBS = gql`
  subscription VisualAcuityUpdatedSubs($profileId: String!) {
    VisualAcuityUpdated(profileId: $profileId) {
      ...VisualAcuity
      ${AUDIT_FIELDS}
    }
  }
  ${VISUAL_ACUITY_FIELDS}
`;

export const DELETE_VISUAL_ACUITY_SUBS = gql`
  subscription DeleteVisualAcuitySubs($profileId: String!) {
    VisualAcuityRemoved(profileId: $profileId) {
      id
      vitalId
    }
  }
`;

export const CONCEAL_VISUAL_ACUITY_ADDITIONAL_NOTE = gql`
  mutation ConcealVisualAcuityAdditionalNote(
    $id: String!
    $concealStatus: Boolean!
    $clinifyId: String!
  ) {
    concealVisualAcuityAdditionalNote(
      visualAcuityId: $id
      concealStatus: $concealStatus
      clinifyId: $clinifyId
    ) {
      id
      concealAdditionalNote
      additionalNote
    }
  }
`;
