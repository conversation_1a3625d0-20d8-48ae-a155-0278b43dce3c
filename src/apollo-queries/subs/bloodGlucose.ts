import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

export const BLOOD_GLUCOSE_FIELDS = gql`
  fragment BloodGlucose on BloodGlucoseModel {
    id
    readingDateTime
    reading
    readingUnit
    mealTime
    additionalNote
    concealAdditionalNote
    isReadingCritical
  }
`;

export const ADD_BLOOD_GLUCOSE = gql`
  mutation AddBloodGlucose($input: BloodGlucoseVitalFields!, $parentRecordId: String!, $clinifyId: String!) {
    addBloodGlucoseInfo(input: $input, vitalId: $parentRecordId, clinifyId: $clinifyId) {
      ...BloodGlucose
      ${AUDIT_FIELDS}
    }
  }
  ${BLOOD_GLUCOSE_FIELDS}
`;

export const UPDATE_BLOOD_GLUCOSE = gql`
  mutation UpdateBloodGlucoseInfo($input: BloodGlucoseVitalFields!, $id: String!, $clinifyId: String!) {
    updateBloodGlucoseInfo(input: $input, id: $id, clinifyId: $clinifyId) {
      ...BloodGlucose
      ${AUDIT_FIELDS}
    }
  }
  ${BLOOD_GLUCOSE_FIELDS}
`;

export const GET_BLOOD_GLUCOSE_INFOS = gql`
  query GetBloodGlucoseInfos($parentRecordId: String!, $clinifyId: String!) {
    getBloodGlucoseInfos(vitalId: $parentRecordId, clinifyId: $clinifyId) {
      ...BloodGlucose
      ${AUDIT_FIELDS}
    }
  }
  ${BLOOD_GLUCOSE_FIELDS}
`;

export const DELETE_BLOOD_GLUCOSE = gql`
  mutation DeleteBloodGlucose($id: String!, $clinifyId: String!) {
    deleteBloodGlucoseInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const BLOOD_GLUCOSE_ADDED_SUBS = gql`
  subscription BloodGlucoseAddedSubs($profileId: String!) {
    BloodGlucoseAdded(profileId: $profileId) {
      ...BloodGlucose
      vitalId
      ${AUDIT_FIELDS}
    }
  }
  ${BLOOD_GLUCOSE_FIELDS}
`;

export const BLOOD_GLUCOSE_UPDATED_SUBS = gql`
  subscription BloodGlucoseUpdatedSubs($profileId: String!) {
    BloodGlucoseUpdated(profileId: $profileId) {
      ...BloodGlucose
      ${AUDIT_FIELDS}
    }
  }
  ${BLOOD_GLUCOSE_FIELDS}
`;

export const DELETE_BLOOD_GLUCOSE_SUBS = gql`
  subscription DeleteBloodGlucoseSubs($profileId: String!) {
    BloodGlucoseRemoved(profileId: $profileId) {
      id
      vitalId
    }
  }
`;
