import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

export const URINE_DIPSTICK_FIELDS = gql`
  fragment UrineDipstick on UrineDipstickModel {
    id
    readingDateTime
    blood
    glucose
    ketones
    ph
    protein
    nitrites
    leucocyte
    urobilinogen
    additionalNote
    concealAdditionalNote
    isPhCritical
  }
`;

export const ADD_URINE_DIPSTICK = gql`
  mutation AddUrineDipstick($input: UrineDipstickVitalFields!, $parentRecordId: String!, $clinifyId: String!) {
    addUrineDipstickInfo(input: $input, vitalId: $parentRecordId, clinifyId: $clinifyId) {
      ...UrineDipstick
      ${AUDIT_FIELDS}
    }
  }
  ${URINE_DIPSTICK_FIELDS}
`;

export const UPDATE_URINE_DIPSTICK = gql`
  mutation UpdateUrineDipstickInfo($input: UrineDipstickVitalFields!, $id: String!, $clinifyId: String!) {
    updateUrineDipstickInfo(input: $input, id: $id, clinifyId: $clinifyId) {
      ...UrineDipstick
      ${AUDIT_FIELDS}
    }
  }
  ${URINE_DIPSTICK_FIELDS}
`;

export const GET_URINE_DIPSTICK_INFOS = gql`
  query GetUrineDipstickInfos($parentRecordId: String!, $clinifyId: String!) {
    getUrineDipstickInfos(vitalId: $parentRecordId, clinifyId: $clinifyId) {
      ...UrineDipstick
      ${AUDIT_FIELDS}
    }
  }
  ${URINE_DIPSTICK_FIELDS}
`;

export const DELETE_URINE_DIPSTICK = gql`
  mutation DeleteUrineDipstick($id: String!, $clinifyId: String!) {
    deleteUrineDipstickInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const URINE_DIPSTICK_ADDED_SUBS = gql`
  subscription UrineDipstickAddedSubs($profileId: String!) {
    UrineDipstickAdded(profileId: $profileId) {
      ...UrineDipstick
      vitalId
      ${AUDIT_FIELDS}
    }
  }
  ${URINE_DIPSTICK_FIELDS}
`;

export const URINE_DIPSTICK_UPDATED_SUBS = gql`
  subscription UrineDipstickUpdatedSubs($profileId: String!) {
    UrineDipstickUpdated(profileId: $profileId) {
      ...UrineDipstick
      ${AUDIT_FIELDS}
    }
  }
  ${URINE_DIPSTICK_FIELDS}
`;

export const DELETE_URINE_DIPSTICK_SUBS = gql`
  subscription DeleteUrineDipstickSubs($profileId: String!) {
    UrineDipstickRemoved(profileId: $profileId) {
      id
      vitalId
    }
  }
`;
