import { gql } from '@apollo/client';
import { BILL_FIELDS, BILL_WITHOUT_RAISED_BY } from 'apollo-queries/mains/billing';

export const ORG_BILL_ADDED = gql`
  subscription OrgBillAdded($profileId: String!, $hospitalId: String!) {
    OrgBillAdded(profileId: $profileId, hospitalId: $hospitalId) {
      ...BillFields
    }
  }
  ${BILL_FIELDS}
`;

export const ORG_BILL_PAID = gql`
  subscription OrgBillPaid($profileId: String!, $hospitalId: String!) {
    PayOrgBill(profileId: $profileId, hospitalId: $hospitalId) {
      ...BillFields
    }
  }
  ${BILL_FIELDS}
`;

export const ORG_BILL_DETAIL_UPDATED = gql`
  subscription OrgBillDetailUpdated($profileId: String!, $hospitalId: String!) {
    OrgBillDetailUpdated(profileId: $profileId, hospitalId: $hospitalId) {
      ...BillFields
    }
  }
  ${BILL_FIELDS}
`;

export const ORG_BILL_UPDATED = gql`
  subscription OrgBillUpdated($profileId: String!, $hospitalId: String!) {
    OrgBillUpdated(profileId: $profileId, hospitalId: $hospitalId) {
      ...Bill
    }
  }
  ${BILL_WITHOUT_RAISED_BY}
`;

export const ORG_BILL_DELETED = gql`
  subscription OrgBillRemoved($profileId: String!, $hospitalId: String!) {
    OrgBillRemoved(profileId: $profileId, hospitalId: $hospitalId) {
      ...BillFields
    }
  }
  ${BILL_FIELDS}
`;

export const ORG_BILL_ITEMS_REMOVED = gql`
  subscription OrgBillItemsRemoved($profileId: String!, $hospitalId: String!) {
    OrgBillItemRemoved(profileId: $profileId, hospitalId: $hospitalId) {
      id
      reference
      serviceType
      billId
    }
  }
`;

export const ORG_BILL_ARCHIVED = gql`
  subscription OrgBillArchived($profileId: String!, $hospitalId: String!) {
    OrgBillArchived(profileId: $profileId, hospitalId: $hospitalId) {
      ...BillFields
    }
  }
  ${BILL_FIELDS}
`;

export const ORG_BILL_UNARCHIVED = gql`
  subscription OrgBillUnarchived($profileId: String!, $hospitalId: String!) {
    OrgBillUnarchived(profileId: $profileId, hospitalId: $hospitalId) {
      ...BillFields
    }
  }
  ${BILL_FIELDS}
`;

export const ORG_BILL_RECALLED = gql`
  subscription OrgBillRecalled($profileId: String!, $hospitalId: String!) {
    OrgBillRecalled(profileId: $profileId, hospitalId: $hospitalId) {
      ...BillFields
    }
  }
  ${BILL_FIELDS}
`;

export const ORG_BILL_CANCELLED = gql`
  subscription OrgBillCancelled($profileId: String!, $hospitalId: String!) {
    OrgBillCancelled(profileId: $profileId, hospitalId: $hospitalId) {
      ...BillFields
    }
  }
  ${BILL_FIELDS}
`;

export const CONSULTATION_ADDED = gql`
  subscription ConsultationAdded($profileId: String!, $hospitalId: String!) {
    ConsultationAdded(profileId: $profileId, hospitalId: $hospitalId) {
      id
      bill {
        id
        amountPaid
        totalAmount
        billStatus
        details {
          paymentType
          patientType
          unitPrice
          createdBy {
            clinifyId
            type
            hospital {
              id
            }
          }
        }
      }
    }
  }
`;

export const IMMUNIZATION_ADDED = gql`
  subscription ImmunizationAdded($profileId: String!, $hospitalId: String!) {
    ImmunizationAdded(profileId: $profileId, hospitalId: $hospitalId) {
      bill {
        ...BillFields
      }
    }
  }
  ${BILL_FIELDS}
`;

export const WALLET_TRANSACTION_EVENT = gql`
  subscription WalletTransaction($walletId: String!) {
    WalletTransactionEvent(walletId: $walletId) {
      id
      createdDate
      amount
      transactionType
      transactionStatus
      transactionDetails
      currency
      amountSent
      description
      senderWallet {
        profile {
          clinifyId
          fullName
        }
      }
      receiverWallet {
        profile {
          clinifyId
          fullName
        }
      }
      currency
    }
  }
`;

export const WALLET_BALANCE = gql`
  subscription WalletBalnce($clinifyId: String!) {
    WalletEvent(clinifyId: $clinifyId) {
      totalBalance
      availableBalance
    }
  }
`;

export const CARD_ADDED = gql`
  subscription CardAdded($profileId: String!) {
    CardAdded(profileId: $profileId) {
      id
      expiryDate
      bank
      lastFourDigits
      isDefault
      brand
    }
  }
`;

export const CARD_REMOVED = gql`
  subscription CardRemoved($profileId: String!) {
    CardRemoved(profileId: $profileId) {
      id
      expiryDate
      bank
      lastFourDigits
      isDefault
      brand
    }
  }
`;
