import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

export const RESPIRATORY_RATE_FIELDS = gql`
  fragment RespiratoryRate on RespiratoryRateModel {
    id
    readingDateTime
    reading
    oxygenSaturation
    spO2Site
    O2FlowRate
    fIO2
    O2Therapy
    etco2
    etco2Unit
    cardiacRythm
    rhythm
    additionalNote
    concealAdditionalNote
    isReadingCritical
    isO2FlowRateCritical
    isOxygenSaturationCritical
    isEtco2Critical
    isFIO2Critical
  }
`;

export const ADD_RESPIRATORY_RATE = gql`
  mutation AddRespiratoryRate($input: RespiratoryRateVitalFields!, $parentRecordId: String!, $clinifyId: String!) {
    addRespiratoryRateInfo(input: $input, vitalId: $parentRecordId, clinifyId: $clinifyId) {
      ...RespiratoryRate
      ${AUDIT_FIELDS}
    }
  }
  ${RESPIRATORY_RATE_FIELDS}
`;

export const UPDATE_RESPIRATORY_RATE = gql`
  mutation UpdateRespiratoryRateInfo($input: RespiratoryRateVitalFields!, $id: String!, $clinifyId: String!) {
    updateRespiratoryRateInfo(input: $input, id: $id, clinifyId: $clinifyId) {
      ...RespiratoryRate
      ${AUDIT_FIELDS}
    }
  }
  ${RESPIRATORY_RATE_FIELDS}
`;

export const GET_RESPIRATORY_RATE_INFOS = gql`
  query GetRespiratoryRateInfos($parentRecordId: String!, $clinifyId: String!) {
    getRespiratoryRateInfos(vitalId: $parentRecordId, clinifyId: $clinifyId) {
      ...RespiratoryRate
      ${AUDIT_FIELDS}
    }
  }
  ${RESPIRATORY_RATE_FIELDS}
`;

export const DELETE_RESPIRATORY_RATE = gql`
  mutation DeleteRespiratoryRate($id: String!, $clinifyId: String!) {
    deleteRespiratoryRateInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const RESPIRATORY_RATE_ADDED_SUBS = gql`
  subscription RespiratoryRateAddedSubs($profileId: String!) {
    RespiratoryRateAdded(profileId: $profileId) {
      ...RespiratoryRate
      vitalId
      ${AUDIT_FIELDS}
    }
  }
  ${RESPIRATORY_RATE_FIELDS}
`;

export const RESPIRATORY_RATE_UPDATED_SUBS = gql`
  subscription RespiratoryRateUpdatedSubs($profileId: String!) {
    RespiratoryRateUpdated(profileId: $profileId) {
      ...RespiratoryRate
      ${AUDIT_FIELDS}
    }
  }
  ${RESPIRATORY_RATE_FIELDS}
`;

export const DELETE_RESPIRATORY_RATE_SUBS = gql`
  subscription DeleteRespiratoryRateSubs($profileId: String!) {
    RespiratoryRateRemoved(profileId: $profileId) {
      id
      vitalId
    }
  }
`;
