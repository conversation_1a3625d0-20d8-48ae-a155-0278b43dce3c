import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

export const PAIN_FIELDS = gql`
  fragment Pain on PainModel {
    id
    score
    type
    dateTimePainStarted
    location
    specifyLocation
    orientation
    specifyOrientation
    radiatingTowards
    descriptors
    specifyDescriptors
    frequency
    specifyFrequency
    onset
    specifyOnset
    clinicalProgression
    specifyClinicalProgression
    aggravatingFactors
    specifyAggravatingFactors
    painCausedAsResultOfInjury
    workRelatedInjury
    painGoal
    interventions
    specifyInterventions
    responsetoInterventions
    additionalNote
    concealAdditionalNote
  }
`;

export const ADD_PAIN = gql`
  mutation AddPainInfo($input: PainVitalFields!, $parentRecordId: String!, $clinifyId: String!) {
    addPainInfo(input: $input, vitalId: $parentRecordId, clinifyId: $clinifyId) {
      ...Pain
      ${AUDIT_FIELDS}
    }
  }
  ${PAIN_FIELDS}
`;

export const UPDATE_PAIN = gql`
  mutation UpdatePainInfo($input: PainVitalFields!, $id: String!, $clinifyId: String!) {
    updatePainInfo(input: $input, id: $id, clinifyId: $clinifyId) {
      ...Pain
      ${AUDIT_FIELDS}
    }
  }
  ${PAIN_FIELDS}
`;

export const GET_PAIN_INFOS = gql`
  query GetPainInfos($parentRecordId: String!, $clinifyId: String!) {
    getPainInfos(vitalId: $parentRecordId, clinifyId: $clinifyId) {
      ...Pain
      ${AUDIT_FIELDS}
    }
  }
  ${PAIN_FIELDS}
`;

export const DELETE_PAIN = gql`
  mutation DeletePainInfo($id: String!, $clinifyId: String!) {
    deletePainInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const CONCEAL_PAIN_ADDITIONAL_NOTE = gql`
  mutation ConcealPainAdditionalNote($id: String!, $concealStatus: Boolean!, $clinifyId: String!) {
    concealPainAdditionalNote(
      painRecordId: $id
      concealStatus: $concealStatus
      clinifyId: $clinifyId
    ) {
      id
      concealAdditionalNote
      additionalNote
    }
  }
`;

export const PAIN_ADDED_SUBS = gql`
  subscription PainAddedSubs($profileId: String!) {
    PainAdded(profileId: $profileId) {
      ...Pain
      vitalId
      ${AUDIT_FIELDS}
    }
  }
  ${PAIN_FIELDS}
`;

export const PAIN_UPDATED_SUBS = gql`
  subscription PainUpdatedSubs($profileId: String!) {
    PainUpdated(profileId: $profileId) {
      ...Pain
      ${AUDIT_FIELDS}
    }
  }
  ${PAIN_FIELDS}
`;

export const DELETE_PAIN_SUBS = gql`
  subscription DeletePainSubs($profileId: String!) {
    PainRemoved(profileId: $profileId) {
      id
      vitalId
    }
  }
`;
