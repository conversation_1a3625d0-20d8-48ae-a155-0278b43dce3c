import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

export const TEMPERATURE_FIELDS = gql`
  fragment Temperature on TemperatureModel {
    id
    readingDateTime
    checkMethod
    reading
    readingUnit
    additionalNote
    concealAdditionalNote
    isReadingCritical
  }
`;

export const ADD_TEMPERATURE = gql`
  mutation AddTemperature($input: TemperatureVitalFields!, $parentRecordId: String!, $clinifyId: String!) {
    addTemperatureInfo(input: $input, vitalId: $parentRecordId, clinifyId: $clinifyId) {
      ...Temperature
      ${AUDIT_FIELDS}
    }
  }
  ${TEMPERATURE_FIELDS}
`;

export const UPDATE_TEMPERATURE = gql`
  mutation UpdateTemperatureInfo($input: TemperatureVitalFields!, $id: String!, $clinifyId: String!) {
    updateTemperatureInfo(input: $input, id: $id, clinifyId: $clinifyId) {
      ...Temperature
      ${AUDIT_FIELDS}
    }
  }
  ${TEMPERATURE_FIELDS}
`;

export const GET_TEMPERATURE_INFOS = gql`
  query GetTemperatureInfos($parentRecordId: String!, $clinifyId: String!) {
    getTemperatureInfos(vitalId: $parentRecordId, clinifyId: $clinifyId) {
      ...Temperature
      ${AUDIT_FIELDS}
    }
  }
  ${TEMPERATURE_FIELDS}
`;

export const DELETE_TEMPERATURE = gql`
  mutation DeleteTemperature($id: String!, $clinifyId: String!) {
    deleteTemperatureInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const TEMPERATURE_ADDED_SUBS = gql`
  subscription TemperatureAddedSubs($profileId: String!) {
    TemperatureAdded(profileId: $profileId) {
      ...Temperature
      vitalId
      ${AUDIT_FIELDS}
    }
  }
  ${TEMPERATURE_FIELDS}
`;

export const TEMPERATURE_UPDATED_SUBS = gql`
  subscription TemperatureUpdatedSubs($profileId: String!) {
    TemperatureUpdated(profileId: $profileId) {
      ...Temperature
      ${AUDIT_FIELDS}
    }
  }
  ${TEMPERATURE_FIELDS}
`;

export const DELETE_TEMPERATURE_SUBS = gql`
  subscription DeleteTemperatureSubs($profileId: String!) {
    TemperatureRemoved(profileId: $profileId) {
      id
      vitalId
    }
  }
`;
