import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

export const BLOOD_PRESSURE_FIELDS = gql`
  fragment BloodPressure on BloodPressureModel {
    id
    readingDateTime
    diastolic
    systolic
    meanArterialPressure
    heartRate
    fetalHeartRate
    location
    method
    additionalNote
    position
    rhythm
    concealAdditionalNote
    isDiastolicCritical
    isSystolicCritical
    isHeartRateCritical
    isFetalHeartRateCritical
  }
`;

export const ADD_BLOOD_PRESSURE = gql`
  mutation AddBloodPressure($input: BloodPressureVitalFields!, $parentRecordId: String!, $clinifyId: String!) {
    addBloodPressureInfo(input: $input, vitalId: $parentRecordId, clinifyId: $clinifyId) {
      ...BloodPressure
      ${AUDIT_FIELDS}
    }
  }
  ${BLOOD_PRESSURE_FIELDS}
`;

export const UPDATE_BLOOD_PRESSURE = gql`
  mutation UpdateBloodPressureInfo($input: BloodPressureVitalFields!, $id: String!, $clinifyId: String!) {
    updateBloodPressureInfo(input: $input, id: $id, clinifyId: $clinifyId) {
      ...BloodPressure
      ${AUDIT_FIELDS}
    }
  }
  ${BLOOD_PRESSURE_FIELDS}
`;

export const GET_BLOOD_PRESSURE_INFOS = gql`
  query GetBloodPressureInfos($parentRecordId: String!, $clinifyId: String!) {
    getBloodPressureInfos(vitalId: $parentRecordId, clinifyId: $clinifyId) {
      ...BloodPressure
      ${AUDIT_FIELDS}
    }
  }
  ${BLOOD_PRESSURE_FIELDS}
`;

export const DELETE_BLOOD_PRESSURE = gql`
  mutation DeleteBloodPressure($id: String!, $clinifyId: String!) {
    deleteBloodPressureInfo(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const BLOOD_PRESSURE_ADDED_SUBS = gql`
  subscription BloodPressureAddedSubs($profileId: String!) {
    BloodPressureAdded(profileId: $profileId) {
      ...BloodPressure
      vitalId
      ${AUDIT_FIELDS}
    }
  }
  ${BLOOD_PRESSURE_FIELDS}
`;

export const BLOOD_PRESSURE_UPDATED_SUBS = gql`
  subscription BloodPressureUpdatedSubs($profileId: String!) {
    BloodPressureUpdated(profileId: $profileId) {
      ...BloodPressure
      ${AUDIT_FIELDS}
    }
  }
  ${BLOOD_PRESSURE_FIELDS}
`;

export const DELETE_BLOOD_PRESSURE_SUBS = gql`
  subscription DeleteBloodPressureSubs($profileId: String!) {
    BloodPressureRemoved(profileId: $profileId) {
      id
      vitalId
    }
  }
`;
