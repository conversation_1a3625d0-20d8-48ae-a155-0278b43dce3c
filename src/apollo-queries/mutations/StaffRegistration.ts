import { gql } from '@apollo/client';
import { PERSONAL_INFORMATION_FIELDS } from 'apollo-queries/mains/user';

export const CREATE_AND_ASSIGN_TO_HOSPITAL = gql`
  mutation CreateAndAssignToHospital(
    $createAndAssignToHospitalInput: CreateAndAssignToHospitalInput!
  ) {
    createAndAssignToHospital(createAndAssignToHospitalInput: $createAndAssignToHospitalInput) {
      id
      clinifyId
      active
      fullName
      type
      personalInformation {
        ...PersonalInformationFields
      }
      user {
        id
        email
        corporatePhoneNumber
        country
      }
      permissions {
        id
        rules {
          subject
          action
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
`;

export const DELETE_HOSPITAL_STAFF = gql`
  mutation DeleteHospitalStaff($profileIds: [String!]!) {
    deleteHospitalStaff(profileIds: $profileIds) {
      id
    }
  }
`;

export const BULK_CREATE_AND_ASSIGN_TO_HOSPITAL = gql`
  mutation BulkCreateAndAssignToHospital($inputs: [CreateAndAssignToHospitalInput!]!) {
    bulkCreateAndAssignToHospital(createAndAssignToHospitalInput: $inputs) {
      id
      clinifyId
      active
      fullName
      type
      personalInformation {
        ...PersonalInformationFields
      }
      user {
        id
        email
        corporatePhoneNumber
        country
      }
      permissions {
        id
        rules {
          subject
          action
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
`;

export const ACTIVATE_HOSPITAL_STAFF = gql`
  mutation ActivateHospitalStaff($active: Boolean, $profileIds: [String!]!) {
    activateHospitalStaff(active: $active, profileIds: $profileIds) {
      id
      clinifyId
      active
      fullName
      type
      personalInformation {
        ...PersonalInformationFields
      }
      user {
        id
        email
        corporatePhoneNumber
        country
      }
      permissions {
        id
        rules {
          subject
          action
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
`;
