import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from './audit';

export const FACILITY_BILLING_INFORMATION_FRAGMENT = gql`
  fragment FacilityBillingInformation on FacilityBillingInformationModel {
    id
    accountName
    accountNumber
    bankName
    bvn
    branchName
    isPreferredPayoutAccount
    ${AUDIT_FIELDS}
  }
`;

export const SPONSOR_BILLING_INFORMATION_FRAGMENT = gql`
  fragment SponsorBillingInformation on SponsorBillingInformationModel {
    id
    accountName
    accountNumber
    bankName
    bvn
    branchName
    sponsorName
    ${AUDIT_FIELDS}
  }
`;

export const ENROLLMENT_TPA_NON_TPA_FRAGMENT = gql`
  fragment EnrollmentTpaNonTpa on EnrollmentTpaNonTpaModel {
    id
    name
    address
    isTpa
    country
    state
    profile {
      id
      fullName
    }
    localGovernmentArea
    primaryPhoneNumber {
      countryCode
      value
      countryName
    }
    primaryEmailAddress
    secondaryPhoneNumber {
      countryCode
      value
      countryName
    }
    secondaryEmailAddress

    contactPersonTitle
    contactPersonFirstName
    contactPersonMiddleName
    contactPersonLastName
    contactPersonPhoneNumber {
      countryCode
      value
      countryName
    }
    contactPersonEmailAddress
    contactPersonAltTitle
    contactPersonAltFirstName
    contactPersonAltMiddleName
    contactPersonAltLastName
    contactPersonAltPhoneNumber {
      countryCode
      value
      countryName
    }
    contactPersonAltEmailAddress
    tpaNumber
    tpaCode
    accountName
    accountNumber
    bankName
    bvn
    branchName
    status
  }
`;

export const ENROLLMENT_AGENCY_FRAGMENT = gql`
  fragment EnrollmentAgency on EnrollmentAgencyModel {
    id
    name
    agencyCode
    address
    country
    state
    agencyType
    profile {
      id
      fullName
    }

    tpaNonTpa {
      id
      name
    }
    localGovernmentArea
    primaryPhoneNumber {
      countryCode
      value
      countryName
    }
    primaryEmailAddress
    secondaryPhoneNumber {
      countryCode
      value
      countryName
    }
    secondaryEmailAddress

    contactPersonTitle
    contactPersonFirstName
    contactPersonMiddleName
    contactPersonLastName
    contactPersonPhoneNumber {
      countryCode
      value
      countryName
    }
    contactPersonEmailAddress
    contactPersonAltTitle
    contactPersonAltFirstName
    contactPersonAltMiddleName
    contactPersonAltLastName
    contactPersonAltPhoneNumber {
      countryCode
      value
      countryName
    }
    contactPersonAltEmailAddress
    accountName
    accountNumber
    bankName
    bvn
    branchName
    status
  }
`;

export const ENROLLMENT_AGENT_FRAGMENT = gql`
  fragment EnrollmentAgent on EnrollmentAgentModel {
    id
    profile {
      id
      fullName
    }
    agency {
      id
      name
      agencyType
      isTpa
    }
    tpaNonTpa {
      id
      name
      isTpa
    }
    accountNumber
    accountName
    bankName
    bvn
    branchName
    status
  }
`;

export const CUSTOM_MANDATORY_FIELDS_FRAGMENT = gql`
  fragment CustomMandatoryFields on MandatoryFields {
    admission
    allergy
    consultation
    antenatal
    immunization
    medication
    medicationDetails
    procedure
    bloodTransfusion
    dischargePatient
    transferPatient
    admissionInput
    admissionOutput
    admissionLine
    treatmentPlan
    vitals
    anthropometry
    bloodGlucose
    bloodPressure
    pain
    respiratoryRate
    temperature
    urineDipstick
    visualAcuity
    investigation
    radiologyExam
    postnatal
    labourAndDelivery
    nextOfKin
    dependents
    medicalReport
    admissionNotes
    nursingServices
    oncology
    requestProcedure
    laboratory
    radiology
    postOperationChecklist
    preChemoEducation
    cancerScreening
  }
`;

export const FACILITY_PREFERENCE_MANDATORY_FIELDS_FRAGMENT = gql`
  fragment FacilityPreferenceMandatoryFields on FacilityPreferenceModel {
    id
    mandatoryFields {
      ...CustomMandatoryFields
    }
    hospitalId
    lastModifierName
    updatedDate
  }
  ${CUSTOM_MANDATORY_FIELDS_FRAGMENT}
`;

export const FACILITY_PREFERENCE_FRAGMENT = gql`
  fragment FacilityPreference on FacilityPreferenceModel {
    id
    welcomeMailTemplate {
      body
      subject
    }
    mandatoryFields {
      ...CustomMandatoryFields
    }
    hospitalId
    lastModifierName
    updatedDate
    patientAccessType
    radiologyExamSource
    procedureTypeSource
    radiologyContrastConfirmation
    dashboardColourMode
    generateFileNumber
    hospitalShortName
    laboratoryTestSource
    retainershipSource
    rolesServiceDetailsIsHidden
    showServiceDetails
    commissionPayer
    outPatientLink
    inPatientLink
    receiptSize
    useHQFacilityInventory
    useHQFacilityTariffs
    inventoryClass
    hmoSingleVisitPACode
    enableBusinessRulePreventSubmit
    customPaFormatType
    registrationFee
    enrolleeCapitationAmount
    autoProcessClaims
    autoProcessPreauthorizations
    enrollmentAgencyPreferences {
      administrationAgency
      agencies
    }

    enrolleeSponsors
    fieldOfficers {
      profileId
      administrationAgency
      enrollmentAgency
      accountNumber
      accountName
      bankName
      bvn
      branchName
      status
    }
    enrolleeReferrals {
      name
      referrerCode
      accountNumber
      accountName
      bankName
      bvn
      branchName
      email
      status
      phoneNumber {
        countryCode
        value
        countryName
      }
    }
    enrolleeSponsorAssigments {
      ref
      sponsorName
      sponsorType
      sponsorLives
      agencyLives
      amountDue
      paymentFrequency
      nextRenewalDate
      renewalCount
      paymentStatus
      paymentDateTime
      sponsoredPremiumPerLife
      totalSponsoredPremium
      status
      percentageCovered
      sponsorEmailAddress
      sponsorPhoneNumber {
        countryCode
        value
        countryName
      }
    }
    enrolleeCapitationAmountPerPlan
    payoutCommissionPayer
    enrolleeCapitionAmountByPlanType {
      planTypeId
      amount
    }
  }
  ${CUSTOM_MANDATORY_FIELDS_FRAGMENT}
`;

export const FACILITY_PREFERENCE_TEST_REFERENCE_RANGE_FRAGMENT = gql`
  fragment TestReferenceRangeFields on TestReferenceRangeModel {
    id
    testName
    maleReferenceRange
    femaleReferenceRange
    referenceRange
    updatedDate
    lastModifierName
  }
`;

export const CHEMO_VERIFICATION_TEMPLATE_FRAGMENT = gql`
  fragment ChemoVerificationTemplate on ChemoDiagnosisTemplateModel {
    id
    combinationName
    creatorName
    createdDate
    type
    section
    cycles {
      id
      cycleNumber
      investigationDetails {
        investigationName
        investigationType
      }
      drugs {
        day
        dosage
        dosagePercentage
        infusionUsed
        route
        drugName
        drugId
        frequency
        ref
        note
        inventoryClass
      }
    }
    updatedDate
    lastModifierName
  }
`;

export const FACILITY_BRANCH_FRAGMENT = gql`
  fragment FacilityBranchFragment on HospitalModel {
    id
    name
    supportMail
    website
    address
    phoneNumber {
      value
      countryCode
      countryName
    }
    secondaryPhoneNumber {
      countryCode
      value
      countryName
    }
    orgAdmin {
      id
      title
      personalInformation {
        firstName
        middleName
        lastName
      }
    }
    preference {
      id
      useHQFacilityInventory
      useHQFacilityTariffs
    }
  }
`;
