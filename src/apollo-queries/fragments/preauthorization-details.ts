import { gql } from '@apollo/client';

export const PREAUTHORIZATION_DETAILS_FRAGMENT = gql`
  fragment PreauthorizationDetails on PreauthorizationDetailsModel {
    id
    recordType
    ref
    paCode
    paStatus
    admissionId
    antenatalDetailsId
    consultationId
    immunizationDetailId
    investigationId
    medicationDetailsId
    surgeryId
    registrationProfileId
    nursingServiceId
    postnatalId
    labourDeliveryId
    oncologyConsultationHistoryId
    hospitalId
    lastModifierId
    lastModifierName
    updatedDate
    providerId
    provider {
      id
      name
      providerCode
    }
  }
`;

export const PA_DETAILS_ON_ADMISSION_FRAGMENT = gql`
  fragment PaDetailsOnAdmission on AdmissionModel {
    preauthorizationDetails {
      id
      recordType
      ref
      paCode
      paStatus
      admissionId
      antenatalDetailsId
      consultationId
      immunizationDetailId
      investigationId
      medicationDetailsId
      surgeryId
      registrationProfileId
      nursingServiceId
      postnatalId
      labourDeliveryId
      oncologyConsultationHistoryId
      hospitalId
      lastModifierId
      lastModifierName
      updatedDate
      providerId
      provider {
        id
        name
        providerCode
      }
    }
  }
`;

export const PA_DETAILS_ON_ANTENATAL_FRAGMENT = gql`
  fragment PaDetailsAntenatalDetails on AntenatalDetailsModel {
    preauthorizationDetails {
      id
      recordType
      ref
      paCode
      paStatus
      admissionId
      antenatalDetailsId
      consultationId
      immunizationDetailId
      investigationId
      medicationDetailsId
      surgeryId
      registrationProfileId
      nursingServiceId
      postnatalId
      labourDeliveryId
      oncologyConsultationHistoryId
      hospitalId
      lastModifierId
      lastModifierName
      updatedDate
      providerId
      provider {
        id
        name
        providerCode
      }
    }
  }
`;

export const PA_DETAILS_ON_CONSULTATION_FRAGMENT = gql`
  fragment PaDetailsOnConsultation on ConsultationModel {
    preauthorizationDetails {
      id
      recordType
      ref
      paCode
      paStatus
      admissionId
      antenatalDetailsId
      consultationId
      immunizationDetailId
      investigationId
      medicationDetailsId
      surgeryId
      registrationProfileId
      nursingServiceId
      postnatalId
      labourDeliveryId
      oncologyConsultationHistoryId
      hospitalId
      lastModifierId
      lastModifierName
      updatedDate
      providerId
      provider {
        id
        name
        providerCode
      }
    }
  }
`;

export const PA_DETAILS_ON_IMMUNIZATION_FRAGMENT = gql`
  fragment PaDetailsOnImmunizationDetails on ImmunizationDetailModel {
    preauthorizationDetails {
      id
      recordType
      ref
      paCode
      paStatus
      admissionId
      antenatalDetailsId
      consultationId
      immunizationDetailId
      investigationId
      medicationDetailsId
      surgeryId
      registrationProfileId
      nursingServiceId
      postnatalId
      labourDeliveryId
      oncologyConsultationHistoryId
      hospitalId
      lastModifierId
      lastModifierName
      updatedDate
      providerId
      provider {
        id
        name
        providerCode
      }
    }
  }
`;

export const PA_DETAILS_ON_INVESTIGATION_FRAGMENT = gql`
  fragment PaDetailsOnInvestigation on InvestigationModel {
    examinationType {
      ref
      priority
      examType
      loinc
      provider
      itemId
      preauthorizationDetailsId
    }
    testInfo {
      testName
      priority
      testCategory
      specimen
      clinicalDiagnosisICD10
      clinicalDiagnosisICD11
      clinicalDiagnosisSNOMED
      loinc
      ref
      provider
      itemId
      preauthorizationDetailsId
    }
    preauthorizationDetails {
      id
      recordType
      ref
      paCode
      paStatus
      admissionId
      antenatalDetailsId
      consultationId
      immunizationDetailId
      investigationId
      medicationDetailsId
      surgeryId
      registrationProfileId
      nursingServiceId
      postnatalId
      labourDeliveryId
      oncologyConsultationHistoryId
      hospitalId
      lastModifierId
      lastModifierName
      updatedDate
      providerId
      provider {
        id
        name
        providerCode
      }
    }
  }
`;

export const PA_DETAILS_ON_MEDICATION_FRAGMENT = gql`
  fragment PaDetailsOnMedicationDetails on MedicationDetailsModel {
    preauthorizationDetails {
      id
      recordType
      ref
      paCode
      paStatus
      admissionId
      antenatalDetailsId
      consultationId
      immunizationDetailId
      investigationId
      medicationDetailsId
      surgeryId
      registrationProfileId
      nursingServiceId
      postnatalId
      labourDeliveryId
      oncologyConsultationHistoryId
      hospitalId
      lastModifierId
      lastModifierName
      updatedDate
      providerId
      provider {
        id
        name
        providerCode
      }
    }
  }
`;

export const PA_DETAILS_ON_SURGERY_FRAGMENT = gql`
  fragment PaDetailsOnSurgery on SurgeryModel {
    procedureType {
      ref
      type
      priority
      provider
      itemId
      preauthorizationDetailsId
    }
    preauthorizationDetails {
      id
      recordType
      ref
      paCode
      paStatus
      admissionId
      antenatalDetailsId
      consultationId
      immunizationDetailId
      investigationId
      medicationDetailsId
      surgeryId
      registrationProfileId
      nursingServiceId
      postnatalId
      labourDeliveryId
      oncologyConsultationHistoryId
      hospitalId
      lastModifierId
      lastModifierName
      updatedDate
      providerId
      provider {
        id
        name
        providerCode
      }
    }
  }
`;

export const PA_DETAILS_ON_REGISTRATION_FRAGMENT = gql`
  fragment PaDetailsOnProfile on ProfileModel {
    preauthorizationDetails {
      id
      recordType
      ref
      paCode
      paStatus
      admissionId
      antenatalDetailsId
      consultationId
      immunizationDetailId
      investigationId
      medicationDetailsId
      surgeryId
      registrationProfileId
      nursingServiceId
      postnatalId
      labourDeliveryId
      oncologyConsultationHistoryId
      hospitalId
      lastModifierId
      lastModifierName
      updatedDate
      providerId
      provider {
        id
        name
        providerCode
      }
    }
  }
`;

export const PA_DETAILS_ON_NURSING_SERVICE_FRAGMENT = gql`
  fragment PaDetailsOnNursingService on NursingServiceModel {
    preauthorizationDetails {
      id
      recordType
      ref
      paCode
      paStatus
      admissionId
      antenatalDetailsId
      consultationId
      immunizationDetailId
      investigationId
      medicationDetailsId
      surgeryId
      registrationProfileId
      nursingServiceId
      postnatalId
      labourDeliveryId
      oncologyConsultationHistoryId
      hospitalId
      lastModifierId
      lastModifierName
      updatedDate
      providerId
      provider {
        id
        name
        providerCode
      }
    }
  }
`;

export const PA_DETAILS_ON_POSTNATAL_FRAGMENT = gql`
  fragment PaDetailsOnPostnatal on PostnatalModel {
    preauthorizationDetails {
      id
      recordType
      ref
      paCode
      paStatus
      admissionId
      antenatalDetailsId
      consultationId
      immunizationDetailId
      investigationId
      medicationDetailsId
      surgeryId
      registrationProfileId
      nursingServiceId
      postnatalId
      labourDeliveryId
      oncologyConsultationHistoryId
      hospitalId
      lastModifierId
      lastModifierName
      updatedDate
      providerId
      provider {
        id
        name
        providerCode
      }
    }
  }
`;

export const PA_DETAILS_ON_LABOUR_AND_DELIVERY_FRAGMENT = gql`
  fragment PaDetailsOnLabourAndDelivery on LabourDeliveryModel {
    preauthorizationDetails {
      id
      recordType
      ref
      paCode
      paStatus
      admissionId
      antenatalDetailsId
      consultationId
      immunizationDetailId
      investigationId
      medicationDetailsId
      surgeryId
      registrationProfileId
      nursingServiceId
      postnatalId
      labourDeliveryId
      oncologyConsultationHistoryId
      hospitalId
      lastModifierId
      lastModifierName
      updatedDate
      providerId
      provider {
        id
        name
        providerCode
      }
    }
  }
`;

export const PA_DETAILS_ON_ONCOLOGY_CONSULTATION_HISTORY_FRAGMENT = gql`
  fragment PaDetailsOnOncologyConsultationHistory on OncologyConsultationHistoryModel {
    preauthorizationDetails {
      id
      recordType
      ref
      paCode
      paStatus
      admissionId
      antenatalDetailsId
      consultationId
      immunizationDetailId
      investigationId
      medicationDetailsId
      surgeryId
      registrationProfileId
      nursingServiceId
      postnatalId
      labourDeliveryId
      oncologyConsultationHistoryId
      hospitalId
      lastModifierId
      lastModifierName
      updatedDate
      providerId
      provider {
        id
        name
        providerCode
      }
    }
  }
`;
