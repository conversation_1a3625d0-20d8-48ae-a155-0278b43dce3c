import { gql } from '@apollo/client';

export const GET_REMINDERS = gql`
  query GetReminders($profileId: String, $filterOptions: ReminderFilterInput) {
    getReminders(profileId: $profileId, filterOptions: $filterOptions) {
      list {
        id
        title
        description
        tag
        priority
        dueDate
        createdDate
        profileId
        hospitalId
        hmoProviderId
        icon
        viewedBy
        isSeen
        metaData {
          id
          clinifyId
          profileId
          hospitalId
          providerId
          claimId
          preAuthId
          referralId
          utilisationId
          enrolleeId
          ids
        }
      }
      totalCount
      unreadCount
    }
  }
`;

export const MARK_REMINDERS_AS_SEEN = gql`
  mutation MarkRemindersAsSeen($reminderIds: [String!]!) {
    markRemindersAsSeen(reminderIds: $reminderIds)
  }
`;

export const MARK_ALL_REMINDERS_AS_SEEN = gql`
  mutation MarkAllRemindersAsSeen {
    markAllRemindersAsSeen
  }
`;

export const REMINDER_SUBSCRIPTION = gql`
  subscription ReminderSubscription {
    ReminderSubscription {
      triggeredBy
      data {
        id
        title
        description
        tag
        priority
        dueDate
        createdDate
        profileId
        hospitalId
        hmoProviderId
        icon
        viewedBy
        isSeen
        metaData {
          id
          clinifyId
          profileId
          hospitalId
          providerId
          claimId
          preAuthId
          referralId
          utilisationId
          enrolleeId
          ids
        }
      }
    }
  }
`;
