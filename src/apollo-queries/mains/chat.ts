import { gql } from '@apollo/client';

export const ONLINE_STATUS_UPDATE = gql`
  mutation OnlineStatusUpdate($input: OnlineStatusInput!) {
    onlineStatusUpdate(input: $input)
  }
`;

export const ONLINE_STATUS_SUB = gql`
  subscription OnlineStatusSub($hospitalId: String!) {
    OnlineStatusUpdated(hospitalId: $hospitalId) {
      id
      status
    }
  }
`;

export const GET_CHAT_MESSAGES = gql`
  query GetChatMessages($conversationId: String!, $filters: ChatMessageFilters) {
    chatMessages(conversationId: $conversationId, filters: $filters) {
      list {
        id
        createdDate
        senderId
        messageType
        content
        status
        conversationId
      }
      totalCount
    }
  }
`;

// chatConversations
export const GET_CHAT_CONVERSATIONS = gql`
  query GetChatConversations($filters: ConversationFilters) {
    chatConversations(filters: $filters) {
      list {
        id
        createdDate
        participant1Id
        participant2Id
        participant1HospitalId
        participant2HospitalId
        participant1HospitalName
        participant2HospitalName
        hmoProviderId
        unreadMessageCount
        lastMessage {
          id
          createdDate
          senderId
          messageType
          content
          status
          conversationId
          senderName
          facilityName
        }
        opponent {
          id
          fullName
          type
          personalInformation {
            displayPictureUrl
          }
        }
      }
      totalCount
    }
  }
`;

// sendChatMessage
export const SEND_CHAT_MESSAGE = gql`
  mutation SendChatMessage($input: SendMessageInput!) {
    sendChatMessage(input: $input) {
      id
      createdDate
      senderId
      messageType
      content
      status
      conversationId
    }
  }
`;

// markConversationAsRead
export const MARK_CONVERSATION_AS_READ = gql`
  mutation MarkConversationAsRead($conversationId: String!) {
    markConversationAsRead(conversationId: $conversationId)
  }
`;

// CHAT_CONVERSATION_ADDED subscription
export const CHAT_CONVERSATION_ADDED = gql`
  subscription ChatConversationAdded {
    ChatConversationAdded {
      id
      createdDate
      participant1Id
      participant2Id
      participant1HospitalId
      participant2HospitalId
      participant1HospitalName
      participant2HospitalName
      hmoProviderId
      unreadMessageCount
      lastMessage {
        id
        createdDate
        senderId
        messageType
        content
        status
        conversationId
        senderName
        facilityName
      }
      opponent {
        id
        fullName
        type
        personalInformation {
          displayPictureUrl
        }
      }
    }
  }
`;
// CHAT_CONVERSATION_UPDATED
export const CHAT_CONVERSATION_UPDATED = gql`
  subscription ChatConversationUpdated {
    ChatConversationUpdated {
      id
      createdDate
      participant1Id
      participant2Id
      participant1HospitalId
      participant2HospitalId
      participant1HospitalName
      participant2HospitalName
      hmoProviderId
      unreadMessageCount
      lastMessage {
        id
        createdDate
        senderId
        messageType
        content
        status
        conversationId
        senderName
        facilityName
      }
      opponent {
        id
        fullName
        type
        personalInformation {
          displayPictureUrl
        }
      }
    }
  }
`;

// CHAT_MESSAGE_ADDED
export const CHAT_MESSAGE_ADDED = gql`
  subscription ChatMessageAdded {
    ChatMessageAdded {
      id
      createdDate
      senderId
      messageType
      content
      status
      conversationId
      senderName
      facilityName
    }
  }
`;
