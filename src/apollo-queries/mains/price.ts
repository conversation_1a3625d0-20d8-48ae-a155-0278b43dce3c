import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';
import { PROVIDER_PAYLOAD, SERVICE_PAYLOAD } from './hospital';

export const PRICE_FIELDS = gql`
  fragment Price on PricesModel {
    id
    name
    code
    category
    price
    aliasCode
    serviceType
    providerCode
    aliasName
    ${AUDIT_FIELDS}
  }
`;

export const GET_ONLY_HOSPITAL_PRICE_LIST = gql`
  query GetOnlyHospitalPricesList($filterOptions: PricesFilterInput) {
    hospital {
      id
      prices(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Price
        }
      }
    }
  }
  ${PRICE_FIELDS}
`;

export const GET_HOSPITAL_PRICES_LIST = gql`
  query GetHospitalPricesAndProvidersList($filterOptions: PricesFilterInput) {
    hospital {
      id
      providers {
        ...Providers
      }
      services {
        ...Services
      }
      prices(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Price
        }
      }
    }
  }
  ${PRICE_FIELDS}
  ${PROVIDER_PAYLOAD}
  ${SERVICE_PAYLOAD}
`;

export const ADD_HOSPITAL_PRICE = gql`
  mutation AddHospitalPrice($input: NewPricesInput!) {
    addPrice(price: $input) {
      ...Price
    }
  }
  ${PRICE_FIELDS}
`;

export const UPDATE_HOSPITAL_PRICE = gql`
  mutation UpdateHospitalPrice($id: String!, $input: NewPricesInput!) {
    updatePrice(input: $input, id: $id) {
      ...Price
    }
  }
  ${PRICE_FIELDS}
`;

export const DELETE_HOSPITAL_PRICES = gql`
  mutation DeleteHospitalPrices($ids: [String!]!) {
    deletePrices(ids: $ids) {
      id
    }
  }
`;

export const GET_PRICE_PROVIDER_LIST = gql`
  query GetPriceProviderList($filterOptions: PricesFilterInput) {
    getPriceProviders(filterOptions: $filterOptions) {
      totalCount
      list {
        id
        name
        price
        serviceType
      }
    }
  }
`;

export const FIND_SERVICE_NAME = gql`
  query FindPriceServiceName($filterOptions: PricesServiceNameFilterInput) {
    findServiceName(filterOptions: $filterOptions) {
      list {
        id
        name
        price
        serviceType
      }
      totalCount
    }
  }
`;
