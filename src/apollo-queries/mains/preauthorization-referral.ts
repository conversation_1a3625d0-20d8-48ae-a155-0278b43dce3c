import { gql } from '@apollo/client';
import { CREATED_BY, UPDATED_BY } from 'apollo-queries/fragments/audit';

export const PREAUTHORIZATION_REFERRAL_UTILIZATIONS = gql`
  fragment PreauthorizationReferralUtilizations on PreAuthReferralUtilisationsModel {
    id
    category
    type
    quantity
    price
    status
    paCode
    utilizationCode
    utilizationId
    rhesusFactor
    specialty
    rejectionReason
    specifyReasonForRejection
    statusHistory {
      status
      creatorName
      createdDate
    }
  }
`;

export const PREAUTHORIZATION_REFERRALS = gql`
  fragment PreauthorizationReferrals on PreauthorisationReferralModel {
    id
    requestDateTime
    requestedBy
    referredBy
    serviceType
    serviceTypeCode
    provider {
      id
      name
    }
    serviceName
    priority
    diagnosis {
      diagnosisICD10
      diagnosisICD11
      diagnosisSNOMED
    }
    additionalNote
    documentUrl
    code
    facilityName
    facilityAddress
    rank
    department
    specialty
    utilizations {
      ...PreauthorizationReferralUtilizations
    }
    totalQuantity
    grandTotal
    profileId
    referralProviderRemark
    referredProvider {
      id
      name
    }
    hospital {
      id
      name
      address
    }
    enrolleeNumber
    isExternalPlanType
    externalPlanTypeId
  }
  ${PREAUTHORIZATION_REFERRAL_UTILIZATIONS}
`;

export const FETCH_PREAUTHORIZATION_REFERRAL = gql`
  query PreauthorizationReferral($id: String!, $clinifyId: String!) {
    preauthorizationReferral(id: $id, clinifyId: $clinifyId) {
      ...PreauthorizationReferrals
      referredProvider {
        id
        name
        plan
      }
      ${CREATED_BY}
      ${UPDATED_BY}
    }
  }
  ${PREAUTHORIZATION_REFERRALS}
`;

export const FETCH_PREAUTHORIZATION_REFERRAL_BY_CODE = gql`
  query GetPreauthorizationReferralByCode($referralCode: String!) {
    getPreauthorizationReferralByCode(referralCode: $referralCode) {
      id
      referredBy
      facilityName
      referredProviderName
      enrolleeNumber
      isExternalPlanType
      externalPlanTypeId
      provider {
        id
        name
      }
      referredBy
      requestDateTime
      serviceType
      serviceTypeCode
      serviceName
      priority
      specialty
      totalQuantity
      grandTotal
      diagnosis {
        diagnosisICD10
        diagnosisICD11
        diagnosisSNOMED
      }
      utilizations {
        ...PreauthorizationReferralUtilizations
      }
    }
  }
  ${PREAUTHORIZATION_REFERRAL_UTILIZATIONS}
`;

export const GET_USER_PREAUTHORIZATION_REFERRALS = gql`
  query UserPreauthorizationReferrals(
    $id: String!
    $filterOptions: PreauthorizationReferralFilterInput
  ) {
    profile(clinifyId: $id) {
      id
      preauthorizationReferrals(filterOptions: $filterOptions) {
        totalCount
        list {
          ...PreauthorizationReferrals
          enrolleeNumber
        }
      }
    }
  }
  ${PREAUTHORIZATION_REFERRALS}
`;

export const GET_PREAUTHORIZATION_REFERRALS_BY_HOSPITAL = gql`
  query HospitalPreauthorizationReferrals(
    $hospitalId: String
    $filterOptions: PreauthorizationReferralFilterInput
  ) {
    hospital(hospitalId: $hospitalId) {
      id
      preauthorizationReferrals(filterOptions: $filterOptions) {
        totalCount
        list {
          ...PreauthorizationReferrals
          profile {
            id
            clinifyId
            fullName
          }
        }
      }
    }
  }
  ${PREAUTHORIZATION_REFERRALS}
`;

export const ADD_PREAUTHORIZATION_REFERRAL = gql`
  mutation AddPreauthorizationReferral($input: PreauthorisationReferralInput!) {
    addPreauthorizationReferral(input: $input) {
      ...PreauthorizationReferrals
      profile {
        id
        clinifyId
        fullName
      }
    }
  }
  ${PREAUTHORIZATION_REFERRALS}
`;

export const UPDATE_PREAUTHORIZATION_REFERRAL = gql`
  mutation UpdatePreauthorizationReferral(
    $id: String!
    $input: PreauthorisationReferralUpdateInput!
  ) {
    updatePreauthorizationReferral(id: $id, input: $input) {
      ...PreauthorizationReferrals
      profile {
        id
        clinifyId
        fullName
      }
      ${CREATED_BY}
      ${UPDATED_BY}
    }
  }
  ${PREAUTHORIZATION_REFERRALS}
`;

export const ARCHIVE_PREAUTHORIZATION_REFERRALS = gql`
  mutation ArchivePreauthorizationReferrals($ids: [String!]!, $archive: Boolean!) {
    archivePreauthorizationReferrals(ids: $ids, archive: $archive) {
      ...PreauthorizationReferrals
      profile {
        id
        clinifyId
        fullName
      }
    }
  }
  ${PREAUTHORIZATION_REFERRALS}
`;

export const DELETE_PREAUTHORIZATION_REFERRALS = gql`
  mutation DeletePreauthorizationReferrals($ids: [String!]!) {
    deletePreauthorizationReferralss(ids: $ids) {
      id
    }
  }
`;

export const GET_ACTIVE_PREAUTHORIZATION_REFERRAL = gql`
  query GetActivePreauthorizationReferral($profileId: String!) {
    activePreauthorizationReferral(profileId: $profileId) {
      ...PreauthorizationReferrals
    }
  }
  ${PREAUTHORIZATION_REFERRALS}
`;

export const GET_HMO_HOSPITALS_BY_PROVIDER_ID = gql`
  query GetHmoHospitalsByProviderId($providerId: String!, $filterOptions: HmoFilterOptions) {
    getHmoHospitalByProviderId(providerId: $providerId, filterOptions: $filterOptions) {
      totalCount
      list {
        id
        name
        address
        lga
        plan
        planStatus
      }
    }
  }
`;

export const PROCESS_REFERRAL_STATUS = gql`
  mutation ProcessReferralUtilStatus(
    $referralCode: String!
    $status: String!
    $rejectionReason: [String!]!
    $specifyReasonForRejection: String!
  ) {
    updateReferralUtilStatus(
      status: $status
      referralCode: $referralCode
      rejectionReason: $rejectionReason
      specifyReasonForRejection: $specifyReasonForRejection
    ) {
      ...PreauthorizationReferralUtilizations
    }
  }
  ${PREAUTHORIZATION_REFERRAL_UTILIZATIONS}
`;

export const ADD_PREAUTHORIZATION_REFERRAL_SUBSCRIPTION = gql`
  subscription PreauthorizationReferralAdded($profileId: String!) {
    PreauthorisationReferralAdded(profileId: $profileId) {
      ...PreauthorizationReferrals
      enrolleeNumber
      profile {
        id
        clinifyId
      }
    }
  }
  ${PREAUTHORIZATION_REFERRALS}
`;

export const UPDATE_PREAUTHORIZATION_REFERRAL_UPDATED_SUBS = gql`
  subscription PreauthorisationReferralUpdatedSubs(
    $profileId: String!
    $hospitalId: String!
    $hmoProviderId: String
  ) {
    PreauthorisationReferralUpdated(
      profileId: $profileId
      hospitalId: $hospitalId
      hmoProviderId: $hmoProviderId
    ) {
      ...PreauthorizationReferrals
      profile {
        id
        clinifyId
      }
      ${CREATED_BY}
      ${UPDATED_BY}
    }
  }
  ${PREAUTHORIZATION_REFERRALS}
`;

export const UTILIZATION_REFERRAL_UPDATED_SUBS = gql`
  subscription UtilizationReferralUpdatedSubs(
    $profileId: String!
    $hospitalId: String!
    $hmoProviderId: String
  ) {
    UtilizationReferralUpdated(
      profileId: $profileId
      hospitalId: $hospitalId
      hmoProviderId: $hmoProviderId
    ) {
      ...PreauthorizationReferralUtilizations
    }
  }
  ${PREAUTHORIZATION_REFERRAL_UTILIZATIONS}
`;

export const UPDATE_REFERRAL_UTILIZATION_STATUS = gql`
  mutation UpdateReferralUtilisationStatus($input: UpdateReferralUtilisationStatusInput!) {
    updateReferralUtilisationStatus(input: $input) {
      id
      status
      rejectionReason
      specifyReasonForRejection
    }
  }
`;
