import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from 'apollo-queries/fragments/audit';

export const PACKAGE_FIELDS = gql`
  fragment Package on PackageModel {
    id
    packageDate
    name
    price
    gender
    serviceDetails {
      serviceType
      serviceName
      extraInformation
    }
    createdDate
    updatedDate
  }
`;

export const GET_ORG_USER_PACKAGES = gql`
  query GetOrgUserPackages($filterOptions: PackageFilterInput, $id: String!) {
    profile(clinifyId: $id) {
      id
      packages(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Package
        }
      }
    }
  }
  ${PACKAGE_FIELDS}
`;

export const GET_HOSPITAL_PACKAGES = gql`
  query GetHospitalPackages($filterOptions: PackageFilterInput) {
    hospital {
      id
      packages(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Package
        }
      }
    }
  }
  ${PACKAGE_FIELDS}
`;

export const GET_PACKAGE = gql`
  query GetPackage($id: String!, $clinifyId: String!) {
    package(id: $id, clinifyId: $clinifyId) {
      ...Package
      ${AUDIT_FIELDS}
    }
  }
  ${PACKAGE_FIELDS}
`;

export const ADD_PACKAGE = gql`
  mutation AddPackage($input: NewPackageInput!, $id: String) {
    addPackage(package: $input, id: $id) {
      ...Package
      ${AUDIT_FIELDS}
    }
  }
  ${PACKAGE_FIELDS}
`;

export const UPDATE_PACKAGE = gql`
  mutation UpdatePackage($input: PackageInput!, $id: String!) {
    updatePackage(package: $input, id: $id) {
      ...Package
      ${AUDIT_FIELDS}
    }
  }
  ${PACKAGE_FIELDS}
`;

export const DELETE_PACKAGES = gql`
  mutation DeletePackages($ids: [String!]!, $clinifyId: String!) {
    deletePackages(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const ARCHIVE_PACKAGES = gql`
  mutation ArchivePackages($ids: [String!]!, $archive: Boolean, $clinifyId: String!) {
    archivePackages(ids: $ids, archive: $archive, clinifyId: $clinifyId) {
      ...Package
      ${AUDIT_FIELDS}
    }
  }
  ${PACKAGE_FIELDS}
`;

export const PACKAGE_ADDED_SUBS = gql`
  subscription PackageAddedSubs($hospitalId: String!) {
    PackageAdded(hospitalId: $hospitalId) {
      ...Package
      ${AUDIT_FIELDS}
    }
  }
  ${PACKAGE_FIELDS}
`;

export const PACKAGE_UPDATED_SUBS = gql`
  subscription PackageUpdatedSubs($hospitalId: String!) {
    PackageUpdated(hospitalId: $hospitalId) {
      ...Package
      ${AUDIT_FIELDS}
    }
  }
  ${PACKAGE_FIELDS}
`;

export const DELETE_PACKAGE_SUBS = gql`
  subscription DeletePackageSubs($hospitalId: String!) {
    PackageRemoved(hospitalId: $hospitalId) {
      id
    }
  }
`;

export const ARCHIVE_PACKAGE_SUBS = gql`
  subscription ArchivePackageSubs($hospitalId: String!) {
    PackageArchived(hospitalId: $hospitalId) {
      ...Package
    }
  }
  ${PACKAGE_FIELDS}
`;

export const UNARCHIVE_PACKAGE_SUBS = gql`
  subscription UnarchivePackageSubs($hospitalId: String!) {
    PackageUnarchived(hospitalId: $hospitalId) {
      ...Package
    }
  }
  ${PACKAGE_FIELDS}
`;
