import { gql } from '@apollo/client';
import { CAPITATION_TRANSFER_FUND_FIELDS } from './hospital';

export const INVOICE_PAYMENT_FIELDS = gql`
  fragment InvoicePaymentField on InvoicePaymentModel {
    id
    paymentStatus
    payoutStatus
    amountDue
    amountPaid
    commissionFeeAmount
    commissionFeePercentage
    paymentMethod
    invoice {
      id
      invoiceReference
      issueDate
      dueDate
      recipient {
        phone
        address
        email
        name
        clinifyId
      }
    }
  }
`;

export const VIRTUAL_SERVICES_PAYMENT_FIELDS = gql`
  fragment VirtualServicesPaymentFields on VirtualServicesPaymentModel {
    id
    billId
    bill {
      id
      billingDateTime
      raisedBy
      receiverProfile {
        id
        fullName
        clinifyId
      }
    }
    amountDue
    amountPaid
    paymentStatus
    commissionPayer
    commissionFeeAmount
    payoutId
    payoutStatus
    createdDate
    updatedDate
  }
`;

const PAYOUT_FIELDS = gql`
  fragment PayoutFields on PayoutModel {
    id
    hospitalId
    transactionStartDate
    transactionEndDate
    amountPaid
    payoutDescription
    totalCommissionFeeAmount
    collectionAccount {
      accountNumber
      bankName
    }
    createdDate
    updatedDate
    currency
    payoutDateTime
    payoutReference
    payoutStatus
    performerId
    receiverInitialWalletBalance
    receiverAccount {
      bankName
      accountNumber
      accountName
    }
    retryPayoutId
    transactionFee
    withdrawalRequestId
    hospital {
      id
      name
      address
    }
    invoicePayments {
      ...InvoicePaymentField
    }
    virtualServicesPayment {
      ...VirtualServicesPaymentFields
    }
    performedBy {
      id
      fullName
    }
    additionalNote
  }
  ${INVOICE_PAYMENT_FIELDS}
  ${VIRTUAL_SERVICES_PAYMENT_FIELDS}
`;

export const FETCH_HOSPITAL_PAYOUTS = gql`
  query FetchPayouts($filterOptions: PayoutFilterInput!, $hospitalId: String) {
    hospital(hospitalId: $hospitalId) {
      id
      payouts(filterInput: $filterOptions) {
        totalCount
        list {
          ...PayoutFields
        }
      }
    }
  }
  ${PAYOUT_FIELDS}
`;

export const FETCH_PAYOUT = gql`
  query FetchPayout($id: String!) {
    payout(id: $id) {
      ...PayoutFields
    }
  }
  ${PAYOUT_FIELDS}
`;

export const FETCH_PAID_INVOICES_FOR_PAYOUT = gql`
  query FetchPaidInvoicesForPayout($hospitalId: String!, $dateRange: DateRangeInput!) {
    fetchPaidInvoicesOnPayout(hospitalId: $hospitalId, dateRange: $dateRange) {
      totalCount
      totalCommissionFeeAmount
      totalCommissionFeeAmount
      totalVirtualAccountTransferPaid
      virtualAccountPayments {
        id
        paymentStatus
        payoutStatus
        amountDue
        amountPaid
        commissionFeeAmount
        commissionFeePercentage
        paymentMethod
        invoice {
          id
          invoiceReference
          issueDate
          dueDate
          recipient {
            phone
            address
            email
            name
            clinifyId
          }
        }
      }
    }
  }
`;

export const FETCH_PAID_VIRTUAL_SERVICES_FOR_PAYOUT = gql`
  query FetchPaidVirtualServicesForPayout($hospitalId: String!, $dateRange: DateRangeInput!) {
    fetchPaidVirtualServicesForPayout(hospitalId: $hospitalId, dateRange: $dateRange) {
      totalCommissionFeeAmount
      totalVirtualServicesAmountPaid
      virtualServicesPayments {
        id
        paymentStatus
        payoutStatus
        amountDue
        amountPaid
        commissionFeeAmount
        commissionPayer
        commissionFeePercentage
        paymentMethod
        billId
        bill {
          id
          billingDateTime
          raisedBy
          receiverProfile {
            id
            fullName
            clinifyId
          }
        }
      }
    }
  }
`;

export const PAYOUT_ADDED_SUBS = gql`
  subscription PayoutAdded($hospitalId: String!) {
    PayoutAdded(hospitalId: $hospitalId) {
      ...PayoutFields
    }
  }
  ${PAYOUT_FIELDS}
`;

export const GET_PAYOUT_SUMMARY = gql`
  query GetPayoutSummary($options: PayoutFilterInput!) {
    getPayoutSummary(options: $options) {
      totalPayouts
      totalPayoutsAmount
      totalPaidPayouts
      totalPaidPayoutsAmount
      totalFailedPayouts
      totalFailedPayoutsAmount
      totalPendingPayouts
      totalPendingPayoutsAmount
      totalReversedPayouts
      totalReversedPayoutsAmount
    }
  }
`;

export const GET_REQUEST_PAYOUT_SUMMARY = gql`
  query GetRequestPayoutSummary($options: PayoutFilterInput!) {
    getRequestPayoutSummary(options: $options) {
      totalRequestedPayouts
      totalPendingRequestPayouts
      totalApprovedRequestPayouts
      totalRejectedRequestPayouts
      totalRequestedPayoutAmount
      totalPendingRequestedPayoutAmount
      totalApprovedRequestedPayoutAmount
      totalRejectedRequestedPayoutAmount
    }
  }
`;

export const PAYOUT_CAPITATION = gql`
  mutation PayoutCapitation($inputs: [CapitationPayoutInput!]!, $origin: String!) {
    payoutCapitation(inputs: $inputs, origin: $origin) {
      ...CapitationTransferFundFields
    }
  }
  ${CAPITATION_TRANSFER_FUND_FIELDS}
`;

export const CAPITATION_PAYOUT_APPROVAL = gql`
  mutation CapitationPayoutApproval(
    $hospitalIds: [String!]!
    $startDate: String!
    $endDate: String!
    $status: Boolean!
    $hmoPlanTypeId: String
  ) {
    capitationAuditApproval(
      startDate: $startDate
      endDate: $endDate
      hospitalIds: $hospitalIds
      status: $status
      hmoPlanTypeId: $hmoPlanTypeId
    ) {
      ...CapitationTransferFundFields
    }
  }
  ${CAPITATION_TRANSFER_FUND_FIELDS}
`;

export const UPDATE_CAPITATED_ENROLLEE_COUNT = gql`
  mutation UpdateCapitatedEnrolleeCount(
    $hospitalId: String!
    $startDate: String!
    $endDate: String!
    $enrolleeCount: Float!
    $hmoPlanTypeId: String
  ) {
    updateCapitationEnrolleeCount(
      endDate: $endDate
      hospitalId: $hospitalId
      startDate: $startDate
      enrolleeCount: $enrolleeCount
      hmoPlanTypeId: $hmoPlanTypeId
    ) {
      ...CapitationTransferFundFields
    }
  }
  ${CAPITATION_TRANSFER_FUND_FIELDS}
`;

export const HOSPITAL_CAPITATION_DETAILS_UPDATED_SUBS = gql`
  subscription HospitalCapitationDetailsUpdatedSubs($hmoProviderId: String!) {
    HospitalCapitationDetailsUpdated(hmoProviderId: $hmoProviderId) {
      ...CapitationTransferFundFields
    }
  }
  ${CAPITATION_TRANSFER_FUND_FIELDS}
`;

export const SEND_TRANSACTION_RECEIPT_TO_PROVIDER = gql`
  query SendTransactionReceiptToProvider(
    $input: [EmailTransactionReceiptInput!]!
    $origin: String!
  ) {
    sendTransactionReceiptToProvider(input: $input, origin: $origin) {
      sent
      email
      id
    }
  }
`;
