import { gql } from '@apollo/client';
import { COVERAGE_DETAILS_FIELDS } from 'apollo-queries/mains/converage';
import {
  FACILITY_BILLING_INFORMATION_FRAGMENT,
  SPONSOR_BILLING_INFORMATION_FRAGMENT,
} from '../fragments/hospital';
import { PREAUTHORIZATION_DETAILS_FRAGMENT } from '../fragments/preauthorization-details';

export const MERGE_PROFILE = gql`
  mutation MergeProfiles($input: MergeProfilesInput!, $clinifyId: String!) {
    mergeProfiles(input: $input, clinifyId: $clinifyId) {
      id
      targetProfile {
        clinifyId
      }
    }
  }
`;

export const UNMERGE_PROFILE = gql`
  mutation UndoProfileMerge($clinifyId: String!) {
    undoProfileMerge(clinifyId: $clinifyId) {
      id
      targetProfile {
        id
        clinifyId
      }
      sourceProfiles {
        id
        clinifyId
      }
    }
  }
`;

export const PERSONAL_INFORMATION_FIELDS = gql`
  fragment PersonalInformationFields on PersonalInformation {
    id
    rank
    title
    department
    speciality
    displayPictureUrl
    firstName
    lastName
    middleName
    dateOfBirth
    bloodGroup
    genoType
    weight
    weightUnit
    height
    heightUnit
    address
    gender
    secondaryEmail
    folioNumber
    clinicalTrials
    patientFileOrCardNo
    coverageDetails {
      ...CoverageDetailsField
    }
    secondaryPhoneNumber {
      value
      countryCode
      countryName
    }
    nin
    votersId
    passportNumber
    bvn
    speciality
    registrationNote
    lga
    ward
    buildingName
    buildingLevel
    countryOfResidence
    stateOfResidence
    patientCategory
    nationality
    state
    originLga
    city
    placeOfBirth
    userRole
  }
  ${COVERAGE_DETAILS_FIELDS}
`;

const BASE_PROFILE_FIELDS = gql`
  fragment BaseProfileFields on ProfileModel {
    id
    clinifyId
    fullName
    active
    isDefault
    createdDate
    updatedDate
    patientProfileType
    dataAccessType
    type
    typeAlias
    title
    gender
    patientStatus
    deathDateTime
    deathLocation
    causeOfDeath
    creatorName
    lastModifierName
    billStatus
    createdFromHmo
    shareData
    fileNumbers {
      coverageRef
      fileNumber
      existingFamily
    }
    coverageDetails {
      ...CoverageDetailsField
    }
    serviceDetails {
      priceId
      type
      name
      quantity
      pricePerUnit
      patientType
      paymentType
    }
  }
  ${COVERAGE_DETAILS_FIELDS}
`;

const BACKGROUND_INFORMATION_FIELDS = gql`
  fragment BackgroundInformationFields on backgroundInformation {
    id
    maritalStatus
    numberOfChildren
    education
    state
    religion
    nationality
    organDonor
    occupation
    salaryRange
    bloodDonor
    preferredLanguage
    modeOfCommunication
    tissueDonor
    boneMarrowDonor
    originLga
    ethnicity
  }
`;

export const PROFILE_FIELDS = gql`
  fragment ProfileFragment on ProfileModel {
    id
    clinifyId
    active
    isDefault
    createdDate
    updatedDate
    creatorName
    lastModifierName
    patientProfileType
    enrollmentTpaNonTpa {
      id
      name
    }
    enrollmentAgency {
      id
      name
    }
    coverageDetails {
      ...CoverageDetailsField
    }
    fileNumbers {
      coverageRef
      fileNumber
      existingFamily
    }
    active
    fullName
    type
    typeAlias
    title
    gender
    patientStatus
    deathDateTime
    deathLocation
    causeOfDeath
    dataAccessType
    serviceDetails {
      priceId
      type
      name
      quantity
      pricePerUnit
      patientType
      paymentType
    }
    personalInformation {
      ...PersonalInformationFields
    }

    backgroundInformation {
      id
      maritalStatus
      numberOfChildren
      education
      state
      religion
      nationality
      organDonor
      occupation
      salaryRange
      bloodDonor
      preferredLanguage
      modeOfCommunication
      tissueDonor
      boneMarrowDonor
      originLga
      ethnicity
    }
    billStatus
    createdFromHmo
    shareData
    hospital {
      name
      id
      address
      clinifyId
      plan
      website
      facilityLogo
      specialties
      hmoHospitals {
        hmoProviderId
        planVisibility {
          id
          name
        }
        provider {
          id
          name
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
  ${COVERAGE_DETAILS_FIELDS}
`;

export const HOSPITAL_FIELDS = gql`
  fragment Hospital on HospitalModel {
    id
    clinifyId
    name
    state
    lga
    politicalWard
    city
    ownership
    level
    address
    website
    country
    plan
    licenseNumber
    documentUrl
    billingInformation {
      ...FacilityBillingInformation
    }
    sponsorBillingInformation {
      ...SponsorBillingInformation
    }
    hmoHospitals {
      id
      hmoProviderId
      planVisibility {
        id
        name
      }
      provider {
        id
        name
        providerCode
        documentUrl
      }
    }
    phoneNumber {
      countryCode
      value
      countryName
    }
    facilityLogo
    facebook
    twitter
    instagram
    secondaryPhoneNumber {
      countryCode
      value
      countryName
    }
    hospitalSupportPhoneNumber {
      countryCode
      value
      countryName
    }
    supportMail
    preferenceId
    hqFacilityId
    hmoId
    lastPaymentDateTime
    nextPaymentDateTime
    hmo {
      id
      providerCode
    }
  }
  ${FACILITY_BILLING_INFORMATION_FRAGMENT}
  ${SPONSOR_BILLING_INFORMATION_FRAGMENT}
`;

export const TEMP_USER_FIELDS = gql`
  fragment TempUser on TempUserModel {
    id
    amountDue
    phoneNumber
    amountPaid
    paymentStatus
    hospitalId
    virtualBankAccount {
      id
      accountName
      accountNumber
      bank
    }
    hospital {
      id
      name
      address
    }
  }
`;

export const HMO_FIELDS = gql`
  fragment Hmo on HmoProfileModel {
    id
    createdDate
    updatedDate
    memberPlan
    memberPlanId
    memberNumber
    memberStatus
    employeeNumber
    capturedDate
    capturedBy
    enrolledBy
    enrollmentDateTime
    paymentDateTime
    paymentFrequency
    memberStartDate
    memberDueDate
    companyName
    memberUniqueId
    parentMemberUniqueId
    primaryProviderName
    secondaryProviderName
    tertiaryProviderName
    primaryProviderAddress
    secondaryProviderAddress
    tertiaryProviderAddress
    memberPlanGroup
    memberPlanSubGroup
    terminationDate
    premiumCollected
    premiumOutstanding
    provider {
      id
      name
      providerCode
    }
  }
`;

export const GET_PROFILE_DETAILS = gql`
  query GetUserDetails {
    profileDetails {
      ...ProfileFragment
      user {
        id
        email
        nonCorporateEmail
        phoneNumber
        country
      }
    }
  }
  ${PROFILE_FIELDS}
`;

export const GET_USER_WITH_HOSPITAL = gql`
  query GetUserWithHospital($id: String!) {
    profile(clinifyId: $id, withHospital: true) {
      ...BaseProfileFields
      user {
        id
        email
        corporatePhoneNumber
        country
        phoneNumber
        nonCorporateEmail
      }
      hospital {
        ...Hospital
      }
    }
  }
  ${BASE_PROFILE_FIELDS}
  ${HOSPITAL_FIELDS}
`;

export const GET_USER_WITH_DETAILS = gql`
  query GetUserWithDetails($id: String!) {
    profile(clinifyId: $id, withDetails: true) {
      ...BaseProfileFields
      personalInformation {
        ...PersonalInformationFields
      }
      backgroundInformation {
        ...BackgroundInformationFields
      }
      user {
        id
        email
        corporatePhoneNumber
        country
        phoneNumber
        nonCorporateEmail
      }
    }
  }
  ${BASE_PROFILE_FIELDS}
  ${PERSONAL_INFORMATION_FIELDS}
  ${BACKGROUND_INFORMATION_FIELDS}
`;

export const GET_USER_WITH_DETAILS_AND_HOSPITAL = gql`
  query GetUserWithDetailsAndHospital($id: String!) {
    profile(clinifyId: $id, withDetails: true, withHospital: true) {
      ...ProfileFragment
      user {
        id
        email
        nonCorporateEmail
        corporatePhoneNumber
        phoneNumber
        country
        hasPin
        isPinMandatory
        isMfaEnabled
        forcePasswordChange
      }
      hospital {
        ...Hospital
      }
      permissions {
        id
        rules {
          action
          subject
          conditions {
            organizationId
          }
        }
      }
    }
  }
  ${PROFILE_FIELDS}
  ${HOSPITAL_FIELDS}
`;

export const GET_ENROLLEE_ENROLLMENT_PAYMENTS = gql`
  query GetEnrolleeEnrollmentPayments($clinifyId: String!) {
    profile(clinifyId: $clinifyId) {
      id
      enrollmentPayments {
        id
        amountDue
        amountPaid
        paymentStatus
        commissionPayer
        commissionFeeAmount
        payoutId
        payoutStatus
        createdDate
        updatedDate
        registrationHmoProfileId
        virtualBankAccount {
          id
          accountName
          accountNumber
          bank
        }
      }
    }
  }
`;

export const GET_USER = gql`
  query GetUser($id: String!) {
    profile(clinifyId: $id) {
      ...BaseProfileFields
      user {
        id
        email
        nonCorporateEmail
        corporatePhoneNumber
        phoneNumber
        country
        hasPin
        isPinMandatory
        isMfaEnabled
      }
    }
  }
  ${BASE_PROFILE_FIELDS}
`;

export const GET_USER_PROFILE = gql`
  query GetUserProfile($id: String!) {
    profile(clinifyId: $id, withDetails: true, withHospital: true) {
      id
      type
      title
      fullName
      clinifyId
      coverageDetails {
        ...CoverageDetailsField
      }
      gender
      fileNumbers {
        coverageRef
        fileNumber
        existingFamily
      }
      createdDate
      updatedDate
      creatorName
      lastModifierName
      hmoId
      isPartnerProfile
      partnerId
      allergies {
        totalCount
        list {
          id
          specialty
          details {
            type
            trigger
            reactions
          }
        }
      }
      personalInformation {
        ...PersonalInformationFields
      }

      user {
        id
        email
        nonCorporateEmail
        phoneNumber
        country
        hasPin
        isPinMandatory
        forcePinChange
        hasPin
      }
      hospital {
        ...Hospital
      }
      careTeam {
        id
        team {
          specialistTitle
          specialistFullName
          specialty
          specialistRole
          specialistId
          rank
        }
      }
    }
  }
  ${HOSPITAL_FIELDS}
  ${PERSONAL_INFORMATION_FIELDS}
  ${COVERAGE_DETAILS_FIELDS}
`;

export const GET_USER_PERSONAL_INFORMATION = gql`
  query GetPersonalInformation($id: String!) {
    profile(clinifyId: $id, withDetails: true) {
      id
      gender
      patientStatus
      deathDateTime
      deathLocation
      causeOfDeath
      dataAccessType
      creatorName
      createdDate
      lastModifierName
      updatedDate
      shareData
      registeredWithId
      user {
        id
        nonCorporateEmail
        phoneNumber
        country
      }
      preauthorizationDetails {
        ...PreauthorizationDetails
      }
      personalInformation {
        ...PersonalInformationFields
      }
      nin
      votersId
      passportNumber
      bvn
      dependents(filterInput: { skip: 0 }) {
        totalCount
        list {
          id
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
  ${PREAUTHORIZATION_DETAILS_FRAGMENT}
`;

export const GET_BACKGROUND_INFO = gql`
  query GetBackgroundInfo($id: String!) {
    profile(clinifyId: $id, withDetails: true) {
      id
      creatorName
      createdDate
      lastModifierName
      updatedDate
      backgroundInformation {
        id
        maritalStatus
        numberOfChildren
        education
        state
        religion
        nationality
        organDonor
        occupation
        salaryRange
        bloodDonor
        preferredLanguage
        modeOfCommunication
        tissueDonor
        boneMarrowDonor
        originLga
        ethnicity
      }
      user {
        id
        nonCorporateEmail
        phoneNumber
        country
      }
    }
  }
`;

export const UPDATE_PROFILE_DETAILS = gql`
  mutation UpdateProfileDetails($input: ProfileDetailsInput!) {
    updateProfileDetails(input: $input) {
      ...ProfileFragment
      user {
        id
        email
        corporatePhoneNumber
        nonCorporateEmail
        country
      }
    }
  }
  ${PROFILE_FIELDS}
`;

export const UPDATE_PROFILE_DETAILS_SUB = gql`
  subscription UpdateProfileDetailsSub($profileId: String!, $hospitalId: String!) {
    ProfileDetailsUpdated(profileId: $profileId, hospitalId: $hospitalId) {
      ...ProfileFragment
    }
  }
  ${PROFILE_FIELDS}
`;

export const LOOKUP_PATIENT = gql`
  query LookupPatient($filterOptions: ProfilesFilterInputs, $requestId: String) {
    profiles(filterOptions: $filterOptions, requestId: $requestId) {
      totalCount
      list {
        id
        clinifyId
        fullName
        type
        fileNumbers {
          coverageRef
          fileNumber
          existingFamily
        }
        patientStatus
        deathDateTime
        deathLocation
        causeOfDeath
        lastCheckinDate
        coverageDetails {
          ...CoverageDetailsField
          virtualServicesPayment {
            id
            amountDue
            amountPaid
            createdDate
          }
        }
        dependents(filterInput: {}) {
          totalCount
          list {
            id
            firstName
            middleName
            lastName
          }
        }
        hmoProfiles {
          id
          checkedIn
          verificationCode
          visitationId
          memberPlan
          memberNumber
          memberStatus
          memberPlanId
          primaryProviderName
          provider {
            id
            name
            providerCode
          }
          memberStartDate
          memberDueDate
          planEligibilityText
          planEligibility
          planEnrolleeDob
          employer
          companyName
          lastCheckIn {
            checkInDate
          }
          capturedDate
          capturedBy
          enrolledBy
          enrollmentDateTime
          paymentDateTime
          planCategory
          paymentFrequency
          memberPlanGroup
          memberPlanSubGroup
          terminationDate
          premiumCollected
          premiumOutstanding
          referrer
          commissionRate
          commissionPayable
          tpaNonTpaCommissionRate
          tpaNonTpaCommissionPayable
          referrerCommissionRate
          referrerCommissionPayable
          activationDatetime
          employerCode
          totalPremiumAmountPaid
          hmoPlanType {
            id
            premiumDetails {
              frequency
              category
              amount
            }
          }
        }
        createdDate
        updatedDate
        creatorName

        gender
        bill {
          id
          createdDate
        }
        billStatus
        personalInformation {
          ...PersonalInformationFields
        }
        user {
          id
          email
          nonCorporateEmail
          phoneNumber
          country
        }
        registeredWith {
          id
          name
          address
        }
        isProfileMerged {
          isMerged
        }
        preauthorizationDetails {
          ...PreauthorizationDetails
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
  ${PREAUTHORIZATION_DETAILS_FRAGMENT}
  ${COVERAGE_DETAILS_FIELDS}
`;

export const LOOKUP_ENROLLEE_LIST = gql`
  query LookupEnrolleeList($filterOptions: ProfilesFilterInputs) {
    enrolleeList(filterOptions: $filterOptions) {
      totalCount
      list {
        id
        clinifyId
        fullName
        type
        fileNumbers {
          coverageRef
          fileNumber
          existingFamily
        }
        patientStatus
        deathDateTime
        deathLocation
        causeOfDeath
        lastCheckinDate
        coverageDetails {
          ...CoverageDetailsField
          virtualServicesPayment {
            id
            amountDue
            amountPaid
            createdDate
          }
        }
        dependents(filterInput: {}) {
          totalCount
          list {
            id
            firstName
            middleName
            lastName
          }
        }
        hmoProfiles {
          id
          checkedIn
          verificationCode
          visitationId
          memberPlan
          memberPlanId
          memberNumber
          memberStatus
          primaryProviderName
          provider {
            id
            name
            providerCode
          }
          memberStartDate
          memberDueDate
          planEligibilityText
          planEligibility
          planEnrolleeDob
          employer
          companyName
          lastCheckIn {
            checkInDate
          }
          capturedDate
          capturedBy
          enrolledBy
          enrollmentDateTime
          paymentDateTime
          planCategory
          paymentFrequency
          memberPlanGroup
          memberPlanSubGroup
          terminationDate
          premiumCollected
          premiumOutstanding
          referrer
          commissionRate
          tpaNonTpaCommissionRate
          tpaNonTpaCommissionPayable
          commissionPayable
          referrerCommissionRate
          referrerCommissionPayable
          activationDatetime
          employerCode
          totalPremiumAmountPaid
          hmoPlanType {
            id
            premiumDetails {
              frequency
              category
              amount
            }
          }
        }
        createdDate
        updatedDate
        creatorName

        gender
        bill {
          id
          createdDate
        }
        billStatus
        personalInformation {
          ...PersonalInformationFields
        }
        user {
          id
          email
          nonCorporateEmail
          phoneNumber
          country
        }
        registeredWith {
          id
          name
          address
        }
        isProfileMerged {
          isMerged
        }
        preauthorizationDetails {
          ...PreauthorizationDetails
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
  ${PREAUTHORIZATION_DETAILS_FRAGMENT}
  ${COVERAGE_DETAILS_FIELDS}
`;
export const CHECKIN_ENROLLEES = gql`
  mutation CheckinEnrollees($input: CheckinInput!) {
    checkinEnrollees(input: $input) {
      list {
        id
        hmos {
          list {
            checkedIn
            verificationCode
            visitationId
            provider {
              id
            }
          }
        }

        lastCheckinDate
      }
    }
  }
`;

export const LOOKUP_BY_HMO = gql`
  query LookupByHmo($input: CoverageLookupOptions!) {
    verifyEnrollee(input: $input) {
      totalCount
      list {
        id
        clinifyId
        fullName
        type
        fileNumbers {
          coverageRef
          fileNumber
          existingFamily
        }

        lastCheckinDate
        createdDate
        updatedDate

        hmos {
          list {
            id
            memberNumber
            memberPlan
            employeeNumber
            employeeType
            employeeDivision
            memberStartDate
            memberDueDate
            memberStatus
            memberPlanId
            companyName
            companyAddress
            primaryProviderName
            secondaryProviderName
            tertiaryProviderName
            primaryProviderAddress
            secondaryProviderAddress
            tertiaryProviderAddress
            capitatedMember
            retired
            occupation
            capitatedAmount
            provider {
              id
              name
            }
            memberStartDate
            memberDueDate
            employer
            checkedIn
            verificationCode
            visitationId
            parentMemberUniqueId
            memberUniqueId
          }
        }

        gender

        backgroundInformation {
          maritalStatus
        }
        user {
          id
          email
          nonCorporateEmail
          phoneNumber
          country
        }
        coverageDetails {
          ...CoverageDetailsField
        }

        personalInformation {
          ...PersonalInformationFields
        }
        hmoProfiles {
          id
          checkedIn
          verificationCode
          visitationId
          memberPlan
          memberNumber
          memberStatus
          memberPlanId
          provider {
            id
            name
            providerCode
          }
          memberStartDate
          memberDueDate
          planEligibilityText
          planEligibility
          planEnrolleeDob
          employer
          lastCheckIn {
            checkInDate
          }
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
  ${COVERAGE_DETAILS_FIELDS}
`;

export const GET_PATIENT_CONTACTS = gql`
  query GetPatientContacts($clinifyId: String!, $filterOptions: ContactFilterInput) {
    profile(clinifyId: $clinifyId) {
      id
      contacts(filterOptions: $filterOptions) {
        id
        firstName
        middleName
        lastName
        clinifyId
      }
    }
  }
`;

export const ADD_NEW_CONTACTS = gql`
  mutation AddContact($clinifyId: String!) {
    addContact(clinifyId: $clinifyId) {
      id
      clinifyId
      personalInformation {
        firstName
        middleName
        lastName
      }
    }
  }
`;

export const DELETE_USER_CONTACT = gql`
  mutation DeleteContact($clinifyId: String!) {
    deleteContact(clinifyId: $clinifyId)
  }
`;

export const GET_PATIENTS_SLIM = gql`
  query GetPatientsSlim($filterOptions: ProfilesFilterInputs) {
    profiles(filterOptions: $filterOptions) {
      list {
        id
        fullName
        clinifyId
      }
    }
  }
`;

export const PATIENT_OVERVIEW = gql`
  query PatientOverview($id: String) {
    userOverView(id: $id) {
      completion
      primaryPhoneNumber
      primaryEmail
      gender
      lastRadiology {
        requestDate
        orderedBy
        examinationType {
          examType
        }
      }
      lastLabTest {
        requestDate
        orderedBy
        testInfo {
          testName
        }
      }
      dependent {
        firstName
        lastName
        dateOfBirth
        bloodGroup
        gender
        relationship
      }
      coverage {
        provider {
          id
          name
        }
        memberNumber
        memberPlan
        memberStatus
        companyName
        companyAddress
      }
      nextOfKin {
        firstName
        lastName
        gender
        bloodGroup
        relationship
        occupation
        phoneNumber {
          countryName
          countryCode
          value
        }
        email
        address
      }
      personalInformation {
        title
        displayPictureUrl
        firstName
        lastName
        dateOfBirth
        bloodGroup
        genoType
        weight
        weightUnit
        height
        heightUnit
        address
      }
      lastAdmission {
        admissionDate
        admittedBy
        finding
        hospitalUnit
        admissionDiagnosis {
          diagnosisICD10
          diagnosisICD11
          diagnosisSNOMED
        }
      }
      allergies {
        occurenceDate
        duration
        details {
          type
          trigger
          reactions
          severeness
        }
        documentUrl
        additionalNote
      }
      lastImmunization {
        createdDate
        details {
          administeredDate
          immunizationName
          administratorName
          method
        }
      }
      lastConsultation {
        consultationDateTime
        doctorName
        provisionalDiagnosis {
          diagnosisICD10
          diagnosisICD11
          diagnosisSNOMED
        }
        finalDiagnosis {
          diagnosisICD10
          diagnosisICD11
          diagnosisSNOMED
        }
      }
      lastProcedure {
        surgeryDate
        procedureType {
          type
        }
        specialty
        operatedBy
      }
      lastAppointment {
        facilityName
        specialty
        appointmentDateTime
        doctorsName
      }
      currentMedication {
        prescribedBy
        details {
          medicationName
          datePrescribed
          purpose
        }
      }
      lastAntenatal {
        createdDate
        details {
          gestationAge
          bloodPressure
          weight
        }
      }
      lastVitalSign {
        id
        createdDate
        anthropometry {
          height
          heightUnit
          weight
          weightUnit
        }
        bloodGlucose {
          reading
          readingUnit
        }
        bloodPressure {
          diastolic
          systolic
        }
        pulseRate {
          reading
        }
        respiratoryRate {
          reading
        }
        temperature {
          reading
          readingUnit
        }
      }
    }
  }
`;

export const GET_HMO_BENEFITS = gql`
  query getHmoBenefits($profileId: String!) {
    getProfileBenefits(profileId: $profileId) {
      provider {
        id
        name
      }

      benefits {
        benefit
        balance
        limit
        quantityLimit
        quantityUsed
        visitType
        visitLimit
        visitUsed
      }
    }
  }
`;

export const GET_HMO_BENEFICIARIES = gql`
  query getHmoBeneficiaries($profileId: String!) {
    getProfileBeneficiaries(profileId: $profileId) {
      success
      errorMsg
      beneficiaries {
        birthDate
        emailAddress
        enrolleeId
        fullName
        provider {
          id
          name
        }
        phoneNumber
        picUrl
        relationship
        dateEnrolled
      }
    }
  }
`;

export const GET_DOCTORS_LIST = gql`
  query getPrivateDoctors($filterOptions: PrivateDoctorsFilterInput) {
    getAllPrivateDoctors(filterOptions: $filterOptions) {
      totalCount
      list {
        id
        
     
          createdDate
          fullName
          averageRating
          ${PERSONAL_INFORMATION_FIELDS}
        
      }
    }
  }
`;

export const GET_PATIENT_LIST = gql`
  query getAllPatient($filterOptions: ProfilesFilterInputs) {
    profiles(filterOptions: $filterOptions) {
      totalCount
      list {
        id
        clinifyId
        fullName
        user {
          id
          email
          phoneNumber
          country
        }
        personalInformation {
          ...PersonalInformationFields
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
`;

export const GET_HOSPITAL_STAFF = gql`
  query GetHospitalStaff($filterOptions: ProfilesFilterInputs) {
    profiles(filterOptions: $filterOptions) {
      totalCount
      list {
        id
        clinifyId
        fullName
        active
        type
        typeAlias
        title
        gender
        user {
          id
          email
          corporatePhoneNumber
          country
        }
        permissions {
          id
          rules {
            subject
            action
            conditions {
              organizationId
            }
          }
        }
        personalInformation {
          ...PersonalInformationFields
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
`;

export const GRANT_ORGANIZATION_PERMISSION = gql`
  mutation GrantOrganizationPermission($input: GrantOrganizationPermissionInput!) {
    grantOrganizationPermission(grantOrganizationPermissionInput: $input) {
      id
      rules {
        subject
        action
      }
    }
  }
`;

export const REVOKE_ORGANIZATION_PERMISSION = gql`
  mutation RevokeOrganizationPermission($input: RevokeOrganizationPermissionInput!) {
    revokeOrganizationPermission(revokeOrganizationPermissionInput: $input) {
      id
      rules {
        subject
        action
      }
    }
  }
`;

export const LOOKUP_USERS = gql`
  query lookupUsers($filterOptions: ProfilesFilterInputs) {
    profiles(filterOptions: $filterOptions) {
      totalCount
      list {
        id
        clinifyId
        type
        fullName
        createdDate
        user {
          id
          email
          phoneNumber
          country
        }
        personalInformation {
          ...PersonalInformationFields
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
`;

export const ALLERGY_EVENT_SUBSCRIPTION = gql`
  subscription AllergyEvent($profileId: String!) {
    AllergyEvent(profileId: $profileId)
  }
`;

export const ADMISSION_EVENT_SUBSCRIPTION = gql`
  subscription AdmissionEvent($profileId: String!) {
    AdmissionEvent(profileId: $profileId)
  }
`;

export const CONSULTATION_EVENT_SUBSCRIPTION = gql`
  subscription ConsultationEvent($profileId: String!) {
    ConsultationEvent(profileId: $profileId)
  }
`;

export const IMMUNIZATION_EVENT_SUBSCRIPTION = gql`
  subscription ImmunizationEvent($profileId: String!) {
    ImmunizationEvent(profileId: $profileId)
  }
`;

export const LABORATORY_EVENT_SUBSCRIPTION = gql`
  subscription LaboratoryEvent($profileId: String!) {
    LaboratoryEvent(profileId: $profileId)
  }
`;

export const MEDICATION_EVENT_SUBSCRIPTION = gql`
  subscription MedicationEvent($profileId: String!) {
    MedicationEvent(profileId: $profileId)
  }
`;

export const ANTINATAL_EVENT_SUBSCRIPTION = gql`
  subscription AntenatalEvent($profileId: String!) {
    AntenatalEvent(profileId: $profileId)
  }
`;

export const RADIOLOGY_EVENT_SUBSCRIPTION = gql`
  subscription RadiologyEvent($profileId: String!) {
    RadiologyEvent(profileId: $profileId)
  }
`;

export const PROCEDURE_EVENT_SUBSCRIPTION = gql`
  subscription ProcedureEvent($profileId: String!) {
    ProcedureEvent(profileId: $profileId)
  }
`;

export const VITALS_EVENT_SUBSCRIPTION = gql`
  subscription VitalsEvent($profileId: String!) {
    VitalsEvent(profileId: $profileId)
  }
`;

export const NURSING_SERVICE_EVENT_SUBSCRIPTION = gql`
  subscription NursingServiceEvent($profileId: String!) {
    NursingServiceEvent(profileId: $profileId)
  }
`;

export const REQUEST_PACKAGE_EVENT_SUBSCRIPTION = gql`
  subscription RequestPackageEvent($profileId: String!) {
    RequestPackageEvent(profileId: $profileId)
  }
`;

export const PACKAGE_EVENT_SUBSCRIPTION = gql`
  subscription PackageEvent($hospitalId: String!) {
    PackageEvent(hospitalId: $hospitalId)
  }
`;

export const PATIENT_REGISTERED_SUBS = gql`
  subscription PatientRegistered($hospitalId: String!) {
    PatientRegistered(hospitalId: $hospitalId) {
      id
      clinifyId
      fullName
      type
      createdDate
      updatedDate
      bill {
        id
        createdDate
      }
      billStatus
      user {
        id
        email
        nonCorporateEmail
        phoneNumber
        country
      }
      isProfileMerged {
        isMerged
      }
      personalInformation {
        ...PersonalInformationFields
      }
      creatorId
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
`;

export const PATIENT_ARCHIVED_SUBS = gql`
  subscription PatientArchived($hospitalId: String!) {
    PatientArchived(hospitalId: $hospitalId) {
      id
      clinifyId
      fullName
      type
      createdDate
      updatedDate
      bill {
        id
        createdDate
      }
      billStatus
      user {
        id
        email
        nonCorporateEmail
        phoneNumber
        country
      }
      isProfileMerged {
        isMerged
      }
      personalInformation {
        ...PersonalInformationFields
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
`;
//
export const PATIENT_UNARCHIVED_SUBS = gql`
  subscription PatientUnarchived($hospitalId: String!) {
    PatientUnarchived(hospitalId: $hospitalId) {
      id
      clinifyId
      fullName
      type
      createdDate
      updatedDate
      bill {
        id
        createdDate
      }

      user {
        id
        email
        nonCorporateEmail
        phoneNumber
        country
      }
      isProfileMerged {
        isMerged
      }
      personalInformation {
        ...PersonalInformationFields
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
`;

export const PATIENT_REMOVED_SUBS = gql`
  subscription PatientRemoved($hospitalId: String!) {
    PatientRemoved(hospitalId: $hospitalId) {
      id
    }
  }
`;

export const GRANT_PATIENT_ACCESS_PERMISSION = gql`
  mutation GrantPatientAccessPermission($input: GrantPatientAccessPermissionInput!) {
    grantPatientAccessPermission(grantPatientAccessPermissionInput: $input) {
      patientAccessType
      patientId
      dataAccess {
        facilityId
        staffIds
      }
    }
  }
`;

export const PATIENT_DATA_ACCESS = gql`
  query PatientDataAccess($clinifyId: String!) {
    profile(clinifyId: $clinifyId, withDetails: true) {
      id
      dataAccessType
      dataAccess {
        facilityId
        staffIds
      }
    }
  }
`;

export const GET_PROFILE_INFO = gql`
  query GetProfileInfo($clinifyId: String!) {
    getProfileInfo(clinifyId: $clinifyId) {
      id
      fullName
      user {
        id
        nonCorporateEmail
        phoneNumber
        country
      }
    }
  }
`;

export const GET_WEMA_CUSTOMER_DETAILS = gql`
  query GetWemaCustomerDetails($wemaAccountNumber: String!) {
    getWemaCustomerDetails(accountNumber: $wemaAccountNumber) {
      message
      response {
        phoneNumber
        name
        emailAddress
      }
      responseCode
      statusCode
    }
  }
`;

export const PATIENT_LOOKUP_RESULT_UPDATED_SUBS = gql`
  subscription PatientLookupResultUpdatedSubs($requestId: String!) {
    PatientLookupResultUpdated(requestId: $requestId) {
      totalCount
      list {
        id
        clinifyId
        fullName
        type
        fileNumbers {
          coverageRef
          fileNumber
          existingFamily
        }
        patientStatus
        deathDateTime
        deathLocation
        causeOfDeath
        lastCheckinDate
        coverageDetails {
          ...CoverageDetailsField
        }
        hmoProfiles {
          id
          checkedIn
          verificationCode
          visitationId
          memberPlan
          memberNumber
          memberStatus
          memberPlanId
          provider {
            id
            name
            providerCode
          }
          memberStartDate
          memberDueDate
          planEligibilityText
          planEligibility
          planEnrolleeDob
          employer
          lastCheckIn {
            checkInDate
          }
        }
        createdDate
        updatedDate

        gender
        bill {
          id
          createdDate
        }
        billStatus
        personalInformation {
          ...PersonalInformationFields
        }
        user {
          id
          email
          nonCorporateEmail
          phoneNumber
          country
        }
        registeredWith {
          id
          name
        }
        isProfileMerged {
          isMerged
        }
        preauthorizationDetails {
          ...PreauthorizationDetails
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
  ${PREAUTHORIZATION_DETAILS_FRAGMENT}
  ${COVERAGE_DETAILS_FIELDS}
`;

export const GET_FACILITY_COVERAGE_BY_TYPE = gql`
  query GetFacilityCoverageByType($type: String!) {
    getFacilityCoverageByType(type: $type) {
      id
      name
    }
  }
`;

export const SAVE_PIN = gql`
  mutation SavePin($pin: String!) {
    savePin(pin: $pin) {
      id
      hasPin
    }
  }
`;

export const UPDATE_PIN = gql`
  mutation UpdatePin($input: UpdatePinInput!) {
    updatePin(input: $input) {
      id
      hasPin
    }
  }
`;

export const MAKE_PIN_MANDATORY = gql`
  mutation MakePinMandatory($ids: [String!]!, $isPinMandatory: Boolean!) {
    makePinMandatory(ids: $ids, isPinMandatory: $isPinMandatory) {
      id
      isPinMandatory
      hasPin
    }
  }
`;

export const UPDATE_STAFF_PIN = gql`
  mutation UpdateStaffPin($staffId: String!, $pin: String!) {
    updateStaffPin(staffId: $staffId, pin: $pin) {
      id
      hasPin
    }
  }
`;

export const UPDATE_MFA_STATUS = gql`
  mutation UpdateMfaStatus($isMfaEnabled: Boolean!) {
    updateMfaStatus(isMfaEnabled: $isMfaEnabled) {
      id
      isMfaEnabled
    }
  }
`;

export const GET_PATIENT_MEDICAL_SUMMARY = gql`
  query GetPatientMedicalSummary($profileId: String!, $date: String) {
    getPatientMedicalSummary(profileId: $profileId, date: $date) {
      id
      diagnosis
      anthropometry {
        height
        heightUnit
        weight
        weightUnit
        bmi
        bsa
      }
      bloodPressure {
        id
        systolic
        diastolic
      }
      temperature {
        id
        reading
        readingUnit
      }
      pulseRate {
        id
        reading
      }
      respiratoryRate {
        id
        oxygenSaturation
        reading
      }
      bloodGlucose {
        id
        reading
        readingUnit
      }
    }
  }
`;

export const UPDATE_PATIENT_CARE_TEAM = gql`
  mutation UpdatePatientCareTeam($input: PatientCareTeamInput!) {
    updatePatientCareTeam(input: $input) {
      id
      team {
        specialistFullName
        specialistId
        specialistRole
        specialty
        specialistTitle
        rank
      }
    }
  }
`;

export const GET_BRANCH_ALIAS_PROFILES = gql`
  query GetBranchAliasProfiles {
    getBranchAliasProfiles {
      id
      hospital {
        id
        name
      }
    }
  }
`;

export const SWITCH_PROFILE = gql`
  mutation SwitchProfile($profileId: String!) {
    switchProfile(profileId: $profileId) {
      accessToken
    }
  }
`;

export const GET_ALL_PATIENT_PROFILES = gql`
  query GetAllPatientProfiles($filterOptions: AllPatientProfilesFilterInput!) {
    getAllPatientProfiles(filterOptions: $filterOptions) {
      id
      fullName
      clinifyId
      user {
        id
        phoneNumber
      }
    }
  }
`;

export const UPDATE_PATIENT_DATA_ACCESS_SETTINGS = gql`
  mutation UpdatePatientDataAccessSettings(
    $hospitalId: String!
    $profileIds: [String!]!
    $visibilityStatus: Boolean!
    $origin: String!
    $sendConfirmationEmail: Boolean
  ) {
    updatePatientDataAccessSetting(
      hospitalId: $hospitalId
      profileIds: $profileIds
      visibilityStatus: $visibilityStatus
      origin: $origin
      sendConfirmationEmail: $sendConfirmationEmail
    ) {
      id
      fullName
      clinifyId
    }
  }
`;

export const GET_TEMP_USER = gql`
  query GetTempUser($phoneNumber: String!) {
    getTempUser(phoneNumber: $phoneNumber) {
      ...TempUser
    }
  }
  ${TEMP_USER_FIELDS}
`;

export const CREATE_TEMP_USER = gql`
  mutation CreateTempUser($phoneNumber: String!, $hospitalId: String!, $amountDue: Float) {
    createTempUser(phoneNumber: $phoneNumber, hospitalId: $hospitalId, amountDue: $amountDue) {
      ...TempUser
    }
  }
  ${TEMP_USER_FIELDS}
`;

export const UPDATE_TEMP_USER = gql`
  mutation UpdateTempUser($id: String!, $hospitalId: String!) {
    updateTempUser(id: $id, hospitalId: $hospitalId) {
      ...TempUser
    }
  }
  ${TEMP_USER_FIELDS}
`;

export const UPDATE_TEMP_USER_AMOUNT_DUE = gql`
  mutation UpdateTempUserAmountDue($id: String!, $amountDue: Float!) {
    updateAmountDueOnTempUser(id: $id, amountDue: $amountDue) {
      ...TempUser
    }
  }
  ${TEMP_USER_FIELDS}
`;

export const DELETE_TEMP_USER = gql`
  mutation DeleteTempUser($id: String!) {
    deleteTempUser(id: $id) {
      ...TempUser
    }
  }
  ${TEMP_USER_FIELDS}
`;

export const UPDATE_TEMP_USER_TEMP_USER_SUBS = gql`
  subscription UpdateTempUserSubs($phoneNumber: String!) {
    TempUserUpdated(phoneNumber: $phoneNumber) {
      ...TempUser
    }
  }
  ${TEMP_USER_FIELDS}
`;
