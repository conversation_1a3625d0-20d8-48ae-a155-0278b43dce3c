import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

export const PRE_CHEMO_EDUCATION_FIELD = gql`
  fragment PreChemoEducation on PreChemoEducationModel {
    id
    preChemoChecklist
    treatmentPlan
    parentGuardianName
    drugInformationProvided
    diseaseOrDiagnosis
    dataLibrary
    handOutsProvided
    steroidEducation
    ivAccessEvaluated
    fertilityIssues
    spiritualCareService
    spiritualGroup
    financialIssues
    comfortItems
    herbalInteraction
    appointmentFlow
    clinicalProcessDiscussed
    alcoholBasedHandRub
    anaemia
    diarrhea
    hairLossSyndrome
    mouthSores
    neuropathy
    thrombocytopenia
    handAndFoot
    nauseaAndVomitting
    neutropenia
    nadir
    fatigue
    doesPatientWork
    patientWithLargeGroups
    cancerDiagnosis
    pastPresentProcedure
    radiationTherapy
    hormonalAgent
    allergies
    pastMedicalHistory
    medicationReview
    symptomsReview
    distress
    performanceStatus
    heightAndWeight
    labValues
    tumourInfo
    baselineVitalSigns
    otherDeformities
    gestationalAge
    treatmentPlanOrProtocol
    participatingInClinicalTrials
    painScore
    informedConsentForChemo
    consentGiven
    patientConsentSignature
    patientConsentSignatureType
    patientConsentSignatureDateTime
    additionalNote
    concealAdditionalNote
    documentUrl
    profileId
    profile {
      id
      clinifyId
    }
    ${AUDIT_FIELDS}
  } 
`;

export const GET_PATIENT_PRE_CHEMO_EDUCATION_CHECKLISTS = gql`
  query GetPatientPreChemoEducations($filterOptions: NursingServicesFilterInput, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      preChemoEducation(filterOptions: $filterOptions) {
        totalCount
        list {
          ...PreChemoEducation
        }
      }
    }
  }
  ${PRE_CHEMO_EDUCATION_FIELD}
`;

export const GET_PATIENT_PRE_CHEMO_EDUCATION_CHECKLIST = gql`
  query GetPatientPreChemoEducation($clinifyId: String!, $id: String!) {
    preChemoEducation(id: $id, clinifyId: $clinifyId) {
      ...PreChemoEducation
    }
  }
  ${PRE_CHEMO_EDUCATION_FIELD}
`;

export const ADD_PATIENT_PRE_CHEMO_EDUCATION_CHECKLIST = gql`
  mutation AddPreChemoEducation($id: String, $input: PreChemoEducationInput!, $pin: String) {
    addPreChemoEducation(id: $id, checklist: $input, pin: $pin) {
      ...PreChemoEducation
    }
  }
  ${PRE_CHEMO_EDUCATION_FIELD}
`;

export const PRE_CHEMO_EDUCATION_CHECKLIST_ADDED_SUBS = gql`
  subscription PreChemoEducationAddedSubs($profileId: String!) {
    PreChemoEducationAdded(profileId: $profileId) {
      ...PreChemoEducation
    }
  }
  ${PRE_CHEMO_EDUCATION_FIELD}
`;

export const UPDATE_PATIENT_PRE_CHEMO_EDUCATION_CHECKLIST = gql`
  mutation UpdatePreChemoEducation($id: String!, $input: PreChemoEducationInput!, $pin: String) {
    updatePreChemoEducation(id: $id, checklist: $input, pin: $pin) {
      ...PreChemoEducation
    }
  }
  ${PRE_CHEMO_EDUCATION_FIELD}
`;

export const PRE_CHEMO_EDUCATION_CHECKLIST_UPDATED_SUBS = gql`
  subscription PreChemoEducationUpdatedSubs($profileId: String!) {
    PreChemoEducationUpdated(profileId: $profileId) {
      ...PreChemoEducation
    }
  }
  ${PRE_CHEMO_EDUCATION_FIELD}
`;

export const DELETE_PATIENT_PRE_CHEMO_EDUCATION_CHECKLIST = gql`
  mutation DeletePreChemoEducations($ids: [String!]!, $clinifyId: String!) {
    deletePreChemoEducations(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const DELETE_PRE_CHEMO_EDUCATION_CHECKLIST_SUBS = gql`
  subscription DeletePreChemoEducationSubs($profileId: String!) {
    PreChemoEducationRemoved(profileId: $profileId) {
      id
    }
  }
`;

export const CONCEAL_PRE_CHEMO_EDUCATION_ADDITIONAL_NOTE = gql`
  mutation ConcealPreChemoEducationAdditionalNote($id: String!, $concealStatus: Boolean!) {
    concealPreChemoEducationAdditionalNote(checklistId: $id, concealState: $concealStatus) {
      id
      additionalNote
      concealAdditionalNote
    }
  }
`;

export const ADD_PRE_CHEMO_EDUCATION_SIGNATURE = gql`
  mutation AddPreChemoEducationConsentSignature($id: String!, $clinifyId: String!, $input: SignatureInput!, $pin: String) {
    addPreChemoEducationConsentSignature(id: $id, clinifyId: $clinifyId, input: $input, pin: $pin) {
      ...PreChemoEducation
      ${AUDIT_FIELDS}
    }
  }
  ${PRE_CHEMO_EDUCATION_FIELD}
`;

export const UPDATE_PRE_CHEMO_EDUCATION_SIGNATURE = gql`
  mutation UpdatePreChemoEducationConsentSignature($id: String!, $clinifyId: String!, $input: SignatureInput!, $pin: String) {
    updatePreChemoEducationConsentSignature(id: $id, clinifyId: $clinifyId, input: $input, pin: $pin) {
      ...PreChemoEducation
      ${AUDIT_FIELDS}
    }
  }
  ${PRE_CHEMO_EDUCATION_FIELD}
`;

export const DELETE_PRE_CHEMO_EDUCATION_SIGNATURE = gql`
  mutation DeletePreChemoEducationConsentSignature($id: String!, $clinifyId: String!) {
    deletePreChemoEducationConsentSignature(id: $id, clinifyId: $clinifyId) {
      ...PreChemoEducation
      ${AUDIT_FIELDS}
    }
  }
  ${PRE_CHEMO_EDUCATION_FIELD}
`;
