import { gql } from '@apollo/client';
import { INVOICE_PAYMENT_FIELDS, VIRTUAL_SERVICES_PAYMENT_FIELDS } from './payout';

const REQUEST_PAYOUT_FIELDS = gql`
  fragment RequestPayoutField on RequestPayoutModel {
    id
    requestPayoutDateTime
    initiatedBy
    createdDate
    updatedDate
    payoutStatus
    payoutDescription
    receiverAccount {
      bankName
      accountNumber
      accountName
      bankCode
      accountType
    }
    currency
    hospitalId
    receiverInitialWalletBalanceBeforePayout
    receiverInitialWalletBalanceBeforeRequest
    requestAmount
    totalCommissionFeeAmount
    transactionStartDate
    transactionEndDate
    creatorName
    lastModifierName
    createdBy {
      id
      fullName
      clinifyId
      type
    }
    virtualServicesPayments {
      ...VirtualServicesPaymentFields
    }
    invoicePayments {
      ...InvoicePaymentField
    }
    hospital {
      id
      name
      address
    }
    payout {
      id
      amountPaid
      payoutStatus
    }
    additionalNote
  }
  ${VIRTUAL_SERVICES_PAYMENT_FIELDS}
  ${INVOICE_PAYMENT_FIELDS}
`;

export const FETCH_HOSPITAL_REQUEST_PAYOUTS = gql`
  query FetchRequestPayouts($filterOptions: PayoutFilterInput!, $hospitalId: String) {
    hospital(hospitalId: $hospitalId) {
      id
      requestPayouts(filterInput: $filterOptions) {
        totalCount
        list {
          ...RequestPayoutField
        }
      }
    }
  }
  ${REQUEST_PAYOUT_FIELDS}
`;

export const FETCH_REQUEST_PAYOUT = gql`
  query FetchRequestPayout($id: String!) {
    requestPayout(id: $id) {
      ...RequestPayoutField
    }
  }
  ${REQUEST_PAYOUT_FIELDS}
`;

export const ADD_REQUEST_PAYOUT = gql`
  mutation AddRequestPayout($input: RequestPayoutInput!) {
    addRequestPayout(input: $input) {
      ...RequestPayoutField
    }
  }
  ${REQUEST_PAYOUT_FIELDS}
`;

export const EDIT_REQUEST_PAYOUT = gql`
  mutation EditRequestPayout($id: String!, $input: RequestPayoutInput!) {
    editRequestPayout(id: $id, input: $input) {
      ...RequestPayoutField
    }
  }
  ${REQUEST_PAYOUT_FIELDS}
`;

export const DELETE_REQUEST_PAYOUT = gql`
  mutation DeleteRequestPayout($ids: [String!]!) {
    deleteRequestPayouts(ids: $ids) {
      id
    }
  }
`;
