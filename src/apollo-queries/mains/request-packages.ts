import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from 'apollo-queries/fragments/audit';

export const REQUEST_PACKAGE_FIELDS = gql`
  fragment RequestPackage on RequestPackageModel {
    id
    requestDate
    packageName
    priority
    category
    orderedBy
    specialty
    rank
    department
    price
    paymentType
    patientType
    facilityName
    facilityAddress
    additionalNote
    serviceDetails {
      serviceType
      serviceName
    }
    createdDate
    updatedDate
    profileId
    profile {
      id
      clinifyId
    }
  }
`;

export const GET_REQUESTED_PACKAGES = gql`
  query GetRequestedPackages($filterOptions: RequestPackageFilterInput, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      requestPackages(filterOptions: $filterOptions) {
        totalCount
        list {
          ...RequestPackage
          bill {
            id
            createdDate
            billStatus
          }
          profile {
            coverageDetails {
              coverageType
              familyName
              companyName
              name
              memberNumber
              provider {
                id
                name
              }
            }
          }
        }
      }
    }
  }
  ${REQUEST_PACKAGE_FIELDS}
`;

export const REQUESTED_PACKAGE = gql`
  query RequestPackage($id: String!, $clinifyId: String!) {
    requestPackage(id: $id, clinifyId: $clinifyId) {
      ...RequestPackage
      ${AUDIT_FIELDS}
    }
  }
  ${REQUEST_PACKAGE_FIELDS}
`;

export const ADD_REQUEST_PACKAGE = gql`
  mutation AddRequestPackage($input: NewRequestPackageInput!, $id: String) {
    addRequestPackage(requestPackage: $input, id: $id) {
      ...RequestPackage
      ${AUDIT_FIELDS}
    }
  }
  ${REQUEST_PACKAGE_FIELDS}
`;

export const UPDATE_REQUESTED_PACKAGE = gql`
  mutation UpdateRequestPackage($input: RequestPackageInput!, $id: String!) {
    updateRequestPackage(requestPackage: $input, id: $id) {
      ...RequestPackage
      ${AUDIT_FIELDS}
    }
  }
  ${REQUEST_PACKAGE_FIELDS}
`;

export const DELETE_REQUESTED_PACKAGES = gql`
  mutation DeleteRequestPackages($ids: [String!]!, $clinifyId: String!) {
    deleteRequestPackages(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const ARCHIVE_REQUESTED_PACKAGES = gql`
  mutation ArchiveRequestPackages($ids: [String!]!, $archive: Boolean, $clinifyId: String!) {
    archiveRequestPackages(ids: $ids, archive: $archive, clinifyId: $clinifyId) {
      ...RequestPackage
      ${AUDIT_FIELDS}
    }
  }
  ${REQUEST_PACKAGE_FIELDS}
`;

export const REQUEST_PACKAGE_ADDED_SUBS = gql`
  subscription RequestPackageAddedSubs($profileId: String!) {
    RequestPackageAdded(profileId: $profileId) {
      ...RequestPackage
      ${AUDIT_FIELDS}
    }
  }
  ${REQUEST_PACKAGE_FIELDS}
`;

export const REQUEST_PACKAGE_UPDATED_SUBS = gql`
  subscription RequestPackageUpdatedSubs($profileId: String!) {
    RequestPackageUpdated(profileId: $profileId) {
      ...RequestPackage
      ${AUDIT_FIELDS}
    }
  }
  ${REQUEST_PACKAGE_FIELDS}
`;

export const DELETE_REQUEST_PACKAGE_SUBS = gql`
  subscription DeleteRequestPackageSubs($profileId: String!) {
    RequestPackageRemoved(profileId: $profileId) {
      id
    }
  }
`;

export const ARCHIVE_REQUEST_PACKAGE_SUBS = gql`
  subscription ArchiveRequestPackageSubs($profileId: String!) {
    RequestPackageArchived(profileId: $profileId) {
      ...RequestPackage
    }
  }
  ${REQUEST_PACKAGE_FIELDS}
`;

export const UNARCHIVE_REQUEST_PACKAGE_SUBS = gql`
  subscription UnarchiveRequestPackageSubs($profileId: String!) {
    RequestPackageUnarchived(profileId: $profileId) {
      ...RequestPackage
    }
  }
  ${REQUEST_PACKAGE_FIELDS}
`;
