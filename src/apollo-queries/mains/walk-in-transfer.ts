import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from 'apollo-queries/fragments/audit';

const WALK_IN_TRANSFER_FIELDS = gql`
  fragment WalkInTransfer on WalkInTransferModel {
    id
    transferDateTime
    transferredBy
    transferReason
    concealTransferReason
    transferFacilityName
    transferFacilityAddress
    documentUrl
    patientInformation {
      clinifyId
      fullName
      email
      phone
    }
    ${AUDIT_FIELDS}
  }
`;

export const GET_HOSPITAL_WALK_IN_TRANSFERS = gql`
  query GetHospitalTransferList($filterOptions: WalkInFilterInput, $hospitalId: String) {
    hospital(hospitalId: $hospitalId) {
      id
      walkInTransfers(filterOptions: $filterOptions) {
        totalCount
        list {
          ...WalkInTransfer
        }
      }
    }
  }
  ${WALK_IN_TRANSFER_FIELDS}
`;

export const ADD_WALK_IN_TRANSFER = gql`
  mutation AddWalkInTransfer($input: NewWalkInTransferInput!) {
    addWalkInTransfer(input: $input) {
      ...WalkInTransfer
      ${AUDIT_FIELDS}
    }
  }
  ${WALK_IN_TRANSFER_FIELDS}
`;

export const GET_WALK_IN_TRANSFER = gql`
  query GetWalkInTransfer($id: String!) {
    walkInTransfer(id: $id) {
      ...WalkInTransfer
      ${AUDIT_FIELDS}
    }
  }
  ${WALK_IN_TRANSFER_FIELDS}
`;

export const UPDATE_WALK_IN_TRANSFER = gql`
  mutation UpdateWalkInTransfer($input: WalkInTransferInput!, $id: String!) {
    updateWalkInTransfer(input: $input, id: $id) {
      ...WalkInTransfer
      ${AUDIT_FIELDS}
    }
  }
  ${WALK_IN_TRANSFER_FIELDS}
`;

export const DELETE_WALK_IN_TRANSFERS = gql`
  mutation DeleteWalkInTransfers($ids: [String!]!) {
    deleteWalkInTransfers(ids: $ids) {
      id
    }
  }
`;

export const ARCHIVE_WALK_IN_TRANSFERS = gql`
  mutation ArchiveWalkInTransfers($ids: [String!]!, $archive: Boolean) {
    archiveWalkInTransfers(ids: $ids, archive: $archive) {
      ...WalkInTransfer
      ${AUDIT_FIELDS}
    }
  }
  ${WALK_IN_TRANSFER_FIELDS}
`;

export const CONCEAL_TRANSFER_REASON = gql`
  mutation ConcealTransferReason($id: String!, $concealStatus: Boolean!) {
    concealTransferReason(id: $id, concealStatus: $concealStatus) {
      id
      transferReason
      concealTransferReason
    }
  }
`;

export const WALK_IN_TRANSFER_ADDED_SUBS = gql`
  subscription WalkInTransferAddedSubs($hospitalId: String!) {
    WalkInTransferAdded(hospitalId: $hospitalId) {
      ...WalkInTransfer
      ${AUDIT_FIELDS}
    }
  }
  ${WALK_IN_TRANSFER_FIELDS}
`;

export const WALK_IN_TRANSFER_UPDATED_SUBS = gql`
  subscription WalkInTransferUpdatedSubs($hospitalId: String!) {
    WalkInTransferUpdated(hospitalId: $hospitalId) {
      ...WalkInTransfer
      ${AUDIT_FIELDS}
    }
  }
  ${WALK_IN_TRANSFER_FIELDS}
`;

export const DELETE_WALK_IN_TRANSFER_SUBS = gql`
  subscription DeleteWalkInTransferSubs($hospitalId: String!) {
    WalkInTransferRemoved(hospitalId: $hospitalId) {
      id
    }
  }
`;

export const ARCHIVE_WALK_IN_TRANSFER_SUBS = gql`
  subscription ArchiveWalkInTransferSubs($hospitalId: String!) {
    WalkInTransferArchived(hospitalId: $hospitalId) {
      ...WalkInTransfer
    }
  }
  ${WALK_IN_TRANSFER_FIELDS}
`;

export const UNARCHIVE_WALK_IN_TRANSFER_SUBS = gql`
  subscription UnarchiveWalkInTransferSubs($hospitalId: String!) {
    WalkInTransferUnarchived(hospitalId: $hospitalId) {
      ...WalkInTransfer
    }
  }
  ${WALK_IN_TRANSFER_FIELDS}
`;
