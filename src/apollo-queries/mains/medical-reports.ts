import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

export const MEDICAL_REPORT_FIELDS = gql`
  fragment MedicalReport on MedicalReportModel {
    id
    createdDate
    updatedDate
    updatedBy {
      id
      fullName
      clinifyId
      type
    }
    lastModifierId
    lastModifierName
    createdBy {
      id
      fullName
      clinifyId
      type
    }
    creatorId
    creatorName
    profile {
      id
      fullName
      clinifyId
    }
    reportDate
    reportType {
      ref
      name
      itemId
    }
    patientClinifyId
    patientFullname
    patientPhone
    patientEmail
    report
    rank
    department
    doctorName
    specialty
    serviceDetails {
      priceId
      type
      name
      quantity
      pricePerUnit
      itemId
      patientType
      paymentType
      reference
    }
    facilityName
    facilityAddress
    additionalNote
    documentUrl
    hospital {
      id
      name
      address
    }
    hospitalId
    status
  }
`;

export const GET_MEDICAL_REPORTS = gql`
  query GetMedicalReports($filterOptions: MedicalReportFilterInput!) {
    medicalReports(filterOptions: $filterOptions) {
      totalCount
      list {
        ...MedicalReport
        bill {
          id
          billStatus
          createdDate
        }
        billStatus
      }
    }
  }
  ${MEDICAL_REPORT_FIELDS}
`;

export const GET_MEDICAL_REPORT = gql`
  query GetMedicalReport($id: String!) {
    medicalReport(id: $id) {
      ...MedicalReport
      ${AUDIT_FIELDS}
    }
  }
  ${MEDICAL_REPORT_FIELDS}
`;

export const ADD_MEDICAL_REPORT = gql`
  mutation AddMedicalReport($input: NewMedicalReportInput!, $pin: String) {
    addMedicalReport(input: $input, pin: $pin) {
      ...MedicalReport
      bill {
        id
        billStatus
        createdDate
      }
      billStatus
      ${AUDIT_FIELDS}
    }
  }
  ${MEDICAL_REPORT_FIELDS}
`;

export const UPDATE_MEDICAL_REPORT = gql`
  mutation UpdateMedicalReport($input: MedicalReportInput!, $pin: String) {
    updateMedicalReport(input: $input, pin: $pin) {
      ...MedicalReport
      ${AUDIT_FIELDS}
    }
  }
  ${MEDICAL_REPORT_FIELDS}
`;

export const UPDATE_MEDICAL_REPORT_BILL = gql`
  mutation UpdateMedicalReportBill($input: MedicalReportInput!) {
    updateMedicalReportBill(input: $input) {
      ...MedicalReport
      ${AUDIT_FIELDS}
    }
  }
  ${MEDICAL_REPORT_FIELDS}
`;

export const DELETE_MEDICAL_REPORT = gql`
  mutation DeleteMedicalReport($ids: [String!]!) {
    deleteMedicalReport(ids: $ids) {
      id
    }
  }
`;

export const ARCHIVE_MEDICAL_REPORT = gql`
  mutation ArchiveMedicalReport($ids: [String!]!, $archive: Boolean) {
    archiveMedicalReport(ids: $ids, archive: $archive) {
      id
    }
  }
`;

export const SEND_MEDICAL_REPORT_EMAIL = gql`
  mutation SendMedicalReportEmail($input: MedicalReportMailInput!) {
    sendMedicalReportEmail(input: $input)
  }
`;

export const MEDICAL_REPORT_ADDED_SUBS = gql`
  subscription MedicalReportAddedSubs($hospitalId: String!) {
    MedicalReportAdded(hospitalId: $hospitalId) {
      ...MedicalReport
      ${AUDIT_FIELDS}
    }
  }
  ${MEDICAL_REPORT_FIELDS}
`;

export const MEDICAL_REPORT_UPDATED_SUBS = gql`
  subscription MedicalReportUpdatedSubs($hospitalId: String!) {
    MedicalReportUpdated(hospitalId: $hospitalId) {
      ...MedicalReport
    }
  }
  ${MEDICAL_REPORT_FIELDS}
`;

export const MEDICAL_REPORT_ARCHIVED_SUBS = gql`
  subscription MedicalReportArchivedSubs($hospitalId: String!) {
    MedicalReportArchived(hospitalId: $hospitalId) {
      ...MedicalReport
    }
  }
  ${MEDICAL_REPORT_FIELDS}
`;

export const MEDICAL_REPORT_UNARCHIVED_SUBS = gql`
  subscription MedicalReportUnarchivedSubs($hospitalId: String!) {
    MedicalReportUnarchived(hospitalId: $hospitalId) {
      ...MedicalReport
    }
  }
  ${MEDICAL_REPORT_FIELDS}
`;

export const MEDICAL_REPORT_REMOVED_SUBS = gql`
  subscription MedicalReportRemovedSubs($hospitalId: String!) {
    MedicalReportRemoved(hospitalId: $hospitalId) {
      id
    }
  }
`;
