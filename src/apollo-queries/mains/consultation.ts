import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';
import { PERSONAL_INFORMATION_FIELDS } from './user';
import { PREAUTHORIZATION_DETAILS_FRAGMENT } from '../fragments/preauthorization-details';

const REFERRED_TO_FIELDS = gql`
  fragment ReferredTo on ProfileModel {
    id
    fullName
    hospital {
      id
      name
      address
    }
  }
`;

const TREATMENT_PLAN_FIELDS = gql`
  fragment TreatmentPlans on TreatmentPlanModel {
      id
      treatmentPlan
      conceal
      patientAdmitted
      concealObservationNote
      observationNote
      admissionConsent
      adverseEffectsFollowingTreatment
      stateEffects
      adverseEffectsInvestigated
      outcomeOfInvestigation
      treatmentGiven
      patientConsentSignature
      patientConsentSignatureType
      patientConsentSignatureDateTime
      treatmentStatus
    ${AUDIT_FIELDS}
  }
`;

export const CONSULTATION_FIELDS = gql`
  fragment Consultation on ConsultationModel {
    id
    consultationDateTime
    duration
    doctorName
    priority
    specialty
    class
    hmoProviderId
    serviceDetails {
      priceId
      type
      name
      quantity
      pricePerUnit
      patientType
      paymentType
      reference
    }
    isPackage
    clinicName
    clinicAddress
    complaint
    provider
    providerServiceName
    complaintHistory
    healthEducation
    systemReview
    systemReviewSmartText
    complaintGender
    complaintSmartSelection {
      positives
      negatives
      vitalSignRevied
      nurseNoteReviewed
      allOtherSystemNegative
      otherSystemNegative
      topLevelChecks
      systemChecks
      systemPositives
      systemNegatives
    }
    systemReviewSmartSelection {
      positives
      negatives
      vitalSignRevied
      nurseNoteReviewed
      allOtherSystemNegative
      otherSystemNegative
      topLevelChecks
      systemChecks
      systemPositives
      systemNegatives
    }
    category
    department
    physicalExam
    physicalExamSmartText
    physicalExamSmartSelection {
      positives
      negatives
      vitalSignRevied
      nurseNoteReviewed
      allOtherSystemNegative
      otherSystemNegative
      topLevelChecks
      systemChecks
      systemPositives
      systemNegatives
    }
    referral
    referralDate
    referralReason
    concealReferralReason
    specialtyReferredTo
    profileId
    profile {
      id
      clinifyId
      coverageDetails {
        id
        name
        coverageType
        companyName
        familyName
        memberNumber
        provider {
          id
          name
        }
      }
    }
    externalReferral
    provisionalDiagnosis {
      diagnosisICD10
      diagnosisICD11
      diagnosisSNOMED
    }
    finalDiagnosis {
      diagnosisICD10
      diagnosisICD11
      diagnosisSNOMED
    }
    consultationStartDate
    consultationEndDate
    appointmentId
    documentUrl
    createdDate
    updatedDate
    concealComplaint
    concealComplaintHistory
    concealPhysicalExam
    concealSystemReview
    concealHealthEducation
    billStatus
    audiometry
    concealAudiometry
    hospitalId
    preauthorizationDetails {
      ...PreauthorizationDetails
    }
  }
  ${PREAUTHORIZATION_DETAILS_FRAGMENT}
`;

export const CONSULTATION_LINKING_FIELDS = gql`
  fragment ConsultationLinking on ConsultationModel {
    allergies {
      id
    }
    medications {
      id
    }
    surgeries {
      id
    }
    admissions {
      id
    }
    vitals {
      id
    }
    labTests {
      id
    }
    radiology {
      id
    }
    investigations {
      id
    }
    nursingServices {
      id
    }
  }
`;

export const FETCH_PATIENT_CONSULTATIONS = gql`
  query FetchPatientConsultations($filterOptions: ConsultationFilterInput, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      consultations(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Consultation
          bill {
            id
            createdDate
          }
        }
      }
    }
  }
  ${CONSULTATION_FIELDS}
`;

// OutPatient List
export const GET_HOSPITAL_OUT_PATIENT = gql`
  query GetOutPatientList($filterOptions: ConsultationFilterInput, $hospitalId: String) {
    hospital(hospitalId: $hospitalId) {
      id
      consultations(filterOptions: $filterOptions) {
        totalCount
        list {
          profile {
              id
              fullName
              clinifyId
          }
          ...Consultation
          bill {
            id
            createdDate
          }
          ${AUDIT_FIELDS}
        }
      }
    }
  }
${CONSULTATION_FIELDS}
`;

export const FETCH_CONSULTATION = gql`
  query FetchConsultation($id: String!, $clinifyId: String!) {
    consultation(id: $id, clinifyId: $clinifyId) {
      ...Consultation
      referredTo {
        ...ReferredTo
      }
      treatmentPlans {
        ...TreatmentPlans
      }
      ...ConsultationLinking
      ${AUDIT_FIELDS}
    }
  }
  ${CONSULTATION_FIELDS}
  ${CONSULTATION_LINKING_FIELDS}
  ${TREATMENT_PLAN_FIELDS}
  ${REFERRED_TO_FIELDS}
`;

export const ADD_CONSULTATION = gql`
  mutation AddConsultation($input: NewConsultationInput!, $id: String, $treatmentPlanId: String,$pin: String) {
    addConsultation(consultation: $input, id: $id, treatmentPlanId: $treatmentPlanId, pin: $pin) {
      ...Consultation
      referredTo {
        ...ReferredTo
      }
      treatmentPlans {
        ...TreatmentPlans
      }
      bill {
        id
        createdDate
      }
      ...ConsultationLinking
      ${AUDIT_FIELDS}
    }
  }
  ${CONSULTATION_FIELDS}
  ${CONSULTATION_LINKING_FIELDS}
  ${TREATMENT_PLAN_FIELDS}
  ${REFERRED_TO_FIELDS}
`;

export const ADD_TREATMENT_PLAN = gql`
  mutation AddTreatmentPlan(
    $input: TreatmentPlanInput!
    $id: String!
    $clinifyId: String!
    $pin: String
  ) {
    addTreatmentPlan(consultationId: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...TreatmentPlans
    }
  }
  ${TREATMENT_PLAN_FIELDS}
`;

export const UPDATE_TREATMENT_PLAN = gql`
  mutation UpdateTreatmentPlan(
    $input: TreatmentPlanInput!
    $id: String!
    $clinifyId: String!
    $pin: String
  ) {
    updateTreatmentPlan(id: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...TreatmentPlans
    }
  }
  ${TREATMENT_PLAN_FIELDS}
`;

export const DELETE_TREATMENT_PLAN = gql`
  mutation DeleteTreatmentPlan($id: String!, $clinifyId: String!) {
    deleteTreatmentPlan(investigationId: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const TREATMENT_PLAN_ADDED_SUBS = gql`
  subscription TreatmentPlanAddedSubs($profileId: String!) {
    ConsultationTreatmentPlanAdded(profileId: $profileId) {
      ...TreatmentPlans
      consultationId
      ${AUDIT_FIELDS}
    }
  }
  ${TREATMENT_PLAN_FIELDS}
`;

export const TREATMENT_PLAN_UPDATED_SUBS = gql`
  subscription TreatmentPlanUpdatedSubs($profileId: String!) {
    ConsultationTreatmentPlanUpdated(profileId: $profileId) {
      ...TreatmentPlans
      ${AUDIT_FIELDS}
    }
  }
  ${TREATMENT_PLAN_FIELDS}
`;

export const DELETE_TREATMENT_PLAN_SUBS = gql`
  subscription DeleteTreatmentPlanSubs($profileId: String!) {
    ConsultationTreatmentPlanRemoved(profileId: $profileId) {
      id
      consultationId
    }
  }
`;

export const UPDATE_CONSULTATION = gql`
  mutation UpdateConsultation($id: String!, $input: ConsultationInput!, $pin: String) {
    updateConsultation(id: $id, consultation: $input, pin: $pin) {
      ...Consultation
      treatmentPlans {
        ...TreatmentPlans
      }
      referredTo {
        ...ReferredTo
      }
      ...ConsultationLinking
      hmoClaim {
        id
      }
      ${AUDIT_FIELDS}
    }
  }
  ${CONSULTATION_FIELDS}
  ${CONSULTATION_LINKING_FIELDS}
  ${TREATMENT_PLAN_FIELDS}
  ${REFERRED_TO_FIELDS}
`;

export const UPDATE_CONSULTATION_BILL = gql`
  mutation UpdateConsultationBill($id: String!, $input: ConsultationInput!) {
    updateConsultationBill(id: $id, consultation: $input) {
      ...Consultation
      treatmentPlans {
        ...TreatmentPlans
      }
      ...ConsultationLinking
      hmoClaim {
        id
      }
      ${AUDIT_FIELDS}
    }
  }
  ${CONSULTATION_FIELDS}
  ${CONSULTATION_LINKING_FIELDS}
  ${TREATMENT_PLAN_FIELDS}
`;

export const LINK_RECORDS_TO_CONSULTATION = gql`
    mutation LinkRecordsToConsultation($parentId: String!, $recordIds: [String!]!, $recordType: ConsultationLinkedRecordType!) {
        linkRecordsToConsultation(id: $parentId, recordIds: $recordIds, recordType: $recordType) {
            ...Consultation
            treatmentPlans {
                ...TreatmentPlans
            }
            ...ConsultationLinking
            ${AUDIT_FIELDS}
        }
    }
    ${CONSULTATION_FIELDS}
    ${CONSULTATION_LINKING_FIELDS}
    ${TREATMENT_PLAN_FIELDS}
`;

export const DELETE_CONSULTATION = gql`
  mutation DeleteConsultations($ids: [String!]!, $clinifyId: String!) {
    deleteConsultations(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const ARCHIVE_CONSULTATION = gql`
  mutation ArchiveConsultations($ids: [String!]!, $archive: Boolean, $clinifyId: String!) {
    archiveConsultations(ids: $ids, archive: $archive, clinifyId: $clinifyId) {
      ...Consultation
      ...ConsultationLinking
      ${AUDIT_FIELDS}
    }
  }
  ${CONSULTATION_FIELDS}
  ${CONSULTATION_LINKING_FIELDS}
`;

export const GET_HOSPITAL_REFERRALS = gql`
  query GetHospitalReferrals($filterOptions: ConsultationFilterInput) {
    hospital {
      id
      consultations(filterOptions: $filterOptions) {
        totalCount
        list {
          profile {
            id
            clinifyId
            fullName
             personalInformation {
      ...PersonalInformationFields
    }
          }
          referredTo {
            id
            fullName
            hospital {
              id
              name
              address
            }
             personalInformation {
      ...PersonalInformationFields
    }
          }
          ...Consultation
          createdBy {
            id
            fullName
            hospital {
              id
              name
            }
             personalInformation {
      ...PersonalInformationFields
    }
          }
          ${AUDIT_FIELDS}
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
  ${CONSULTATION_FIELDS}
`;

export const CONSULTATION_ADDED_SUBS = gql`
  subscription ConsultationAddedSubs($profileId: String!, $hospitalId: String!) {
    ConsultationAdded(profileId: $profileId, hospitalId: $hospitalId) {
      ...Consultation
      ...ConsultationLinking
      bill {
        id
        createdDate
      }
      ${AUDIT_FIELDS}
    }
  }
  ${CONSULTATION_FIELDS}
  ${CONSULTATION_LINKING_FIELDS}
`;

export const OUTPATIENT_CONSULTATION_ADDED_SUBS = gql`
  subscription OutPatientConsultationAddedSubs($hospitalId: String!) {
    OutpatientConsultationAdded(hospitalId: $hospitalId) {
      ...Consultation
      ...ConsultationLinking
      bill {
        id
        createdDate
      }
      ${AUDIT_FIELDS}
    }
  }
  ${CONSULTATION_FIELDS}
  ${CONSULTATION_LINKING_FIELDS}
`;

export const CONSULTATION_UPDATED_SUBS = gql`
  subscription ConsultationUpdatedSubs($profileId: String!, $hospitalId: String!) {
    ConsultationUpdated(profileId: $profileId, hospitalId: $hospitalId) {
      ...Consultation
      referredTo {
        ...ReferredTo
      }
      ...ConsultationLinking
      ${AUDIT_FIELDS}
    }
  }
  ${CONSULTATION_FIELDS}
  ${CONSULTATION_LINKING_FIELDS}
  ${REFERRED_TO_FIELDS}
`;

export const DELETE_CONSULTATION_SUBS = gql`
  subscription DeleteConsultationSubs($profileId: String!, $hospitalId: String!) {
    ConsultationRemoved(profileId: $profileId, hospitalId: $hospitalId) {
      id
    }
  }
`;

export const ARCHIVE_CONSULTATION_SUBS = gql`
  subscription ArchiveConsultationSubs($profileId: String!, $hospitalId: String!) {
    ConsultationArchived(profileId: $profileId, hospitalId: $hospitalId) {
      ...Consultation
      ...ConsultationLinking
    }
  }
  ${CONSULTATION_FIELDS}
  ${CONSULTATION_LINKING_FIELDS}
`;

export const UNARCHIVE_CONSULTATION_SUBS = gql`
  subscription UnarchiveConsultationSubs($profileId: String!, $hospitalId: String!) {
    ConsultationUnarchived(profileId: $profileId, hospitalId: $hospitalId) {
      ...Consultation
      ...ConsultationLinking
    }
  }
  ${CONSULTATION_FIELDS}
  ${CONSULTATION_LINKING_FIELDS}
`;

export const CONSULTATION_REFERRAL_SUBSCRIPTION = gql`
  subscription ReferralAdded($hospitalId: String!) {
    ReferralAdded(hospitalId: $hospitalId)
  }
`;

export const CONCEAL_CONSULTATION_COMPLAINT = gql`
  mutation ConcealConsultationComplaint($id: String!, $concealStatus: Boolean!) {
    concealConsultationComplaint(consultationId: $id, concealStatus: $concealStatus) {
      id
      concealComplaint
      complaint
    }
  }
`;

export const CONCEAL_CONSULTATION_TREATMENT_PLAN = gql`
  mutation ConcealConsultationTreatmentPlan($id: String!, $concealStatus: Boolean!) {
    concealConsultationTreatmentPlan(treatmentPlanId: $id, concealStatus: $concealStatus) {
      ...TreatmentPlans
    }
  }
  ${TREATMENT_PLAN_FIELDS}
`;

export const CONCEAL_CONSULTATION_COMPLAINT_HISTORY = gql`
  mutation ConcealConsultationComplaintHistory($id: String!, $concealStatus: Boolean!) {
    concealConsultationComplaintHistory(consultationId: $id, concealStatus: $concealStatus) {
      id
      concealComplaintHistory
      complaintHistory
    }
  }
`;

export const CONCEAL_CONSULTATION_SYSTEM_REVIEW = gql`
  mutation ConcealConsultationSystemReview($id: String!, $concealStatus: Boolean!) {
    concealConsultationSystemReview(consultationId: $id, concealStatus: $concealStatus) {
      id
      concealSystemReview
      systemReview
    }
  }
`;

export const CONCEAL_CONSULTATION_PHYSICAL_EXAM = gql`
  mutation ConcealConsultationPhysicalExam($id: String!, $concealStatus: Boolean!) {
    concealConsultationPhysicalExam(consultationId: $id, concealStatus: $concealStatus) {
      id
      concealPhysicalExam
      physicalExam
    }
  }
`;

export const CONCEAL_CONSULTATION_OBSERVATION_NOTE = gql`
  mutation ConcealObservationNote($id: String!, $concealStatus: Boolean!) {
    concealObservationNote(treatmentPlanId: $id, concealStatus: $concealStatus) {
      ...TreatmentPlans
    }
  }
  ${TREATMENT_PLAN_FIELDS}
`;
export const CONCEAL_CONSULTATION_REFERRAL_REASON = gql`
  mutation ConcealConsultationReferralReason($id: String!, $concealStatus: Boolean!) {
    concealConsultationReferralReason(consultationId: $id, concealStatus: $concealStatus) {
      id
      concealReferralReason
      referralReason
    }
  }
`;

export const CONCEAL_CONSULTATION_HEALTH_EDUCATION = gql`
  mutation ConcealHealthEducation($id: String!, $concealStatus: Boolean!) {
    concealHealthEducation(consultationId: $id, concealStatus: $concealStatus) {
      id
      concealHealthEducation
      healthEducation
    }
  }
`;

export const CONCEAL_AUDIOMETRY = gql`
  mutation ConcealAudiometry($id: String!, $concealStatus: Boolean!) {
    concealAudiometry(consultationId: $id, concealStatus: $concealStatus) {
      id
      concealAudiometry
      audiometry
    }
  }
`;

export const GET_CONSULTATION_CLAIM = gql`
  query ConsultationClaim($id: String!, $clinifyId: String!) {
    consultation(id: $id, clinifyId: $clinifyId) {
      id
      hmoClaim {
        id
      }
    }
  }
`;

export const ADD_TREATMENT_PLAN_SIGNATURE = gql`
  mutation AddTreatmentPlanConsentSignature($id: String!, $clinifyId: String!, $input: SignatureInput!, $pin: String) {
    addTreatmentPlanConsentSignature(id: $id, clinifyId: $clinifyId, input: $input, pin: $pin) {
      ...TreatmentPlans
      ${AUDIT_FIELDS}
    }
  }
  ${TREATMENT_PLAN_FIELDS}
`;

export const UPDATE_TREATMENT_PLAN_SIGNATURE = gql`
  mutation UpdateTreatmentPlanConsentSignature($id: String!, $clinifyId: String!, $input: SignatureInput!, $pin: String) {
    updateTreatmentPlanConsentSignature(id: $id, clinifyId: $clinifyId, input: $input, pin: $pin) {
      ...TreatmentPlans
      ${AUDIT_FIELDS}
    }
  }
  ${TREATMENT_PLAN_FIELDS}
`;

export const DELETE_TREATMENT_PLAN_SIGNATURE = gql`
  mutation DeleteTreatmentPlanConsentSignature($id: String!, $clinifyId: String!) {
    deleteTreatmentPlanConsentSignature(id: $id, clinifyId: $clinifyId) {
      ...TreatmentPlans
      ${AUDIT_FIELDS}
    }
  }
  ${TREATMENT_PLAN_FIELDS}
`;
