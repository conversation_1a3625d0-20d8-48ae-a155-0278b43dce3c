import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from 'apollo-queries/fragments/audit';

export const INVENTORY_FIELDS = gql`
  fragment Inventory on InventoryModel {
    id
    sn
    supplier
    invoiceNumber
    name
    type
    size
    group
    flag
    description
    category
    code
    strength
    ward
    addedBy
    addedDateTime
    batchNumber
    barcode
    expiryDate
    expiryStatus
    damagedCount
    bedNumber
    markup
    unitCost
    averageCostPrice
    colour
    model
    vin
    plateNumber
    year
    purchasedBy
    status
    unitSellingPrice
    bedAvailable
    totalCost
    totalSale
    quantityRemaining
    quantityPurchased
    quantityAvailable
    quantityDispensed
    quantityOrdered
    quantitySold
    manufacturer
    recievedDateTime
    reorderLevel
    receivedBy
    comments
    images
    section
    creatorId
    class
  }
`;

export const GET_INVENTORY_LIST = gql`
  query GetHospitalInventoryList($filterOptions: InventoryFilterInput, $hospitalId: String) {
    hospital(hospitalId: $hospitalId) {
      id
      inventories(filterOptions: $filterOptions, listing: true) {
        totalCount
        list {
          ...Inventory
        }
      }
    }
  }
  ${INVENTORY_FIELDS}
`;

export const GET_INVENTORY_LIST_WITH_AGG = gql`
  query GetHospitalInventoryListWithAgg($filterOptions: InventoryFilterInput) {
    hospital {
      id
      inventories(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Inventory
        }
      }
    }
  }
  ${INVENTORY_FIELDS}
`;

export const FETCH_INVENTORY = gql`
  query FetchInventory($id: String!, $clinifyId: String!) {
    inventory(id: $id, clinifyId: $clinifyId) {
      ...Inventory
    }
  }
  ${INVENTORY_FIELDS}
`;

export const ADD_INVENTORY = gql`
  mutation AddInventory($input: [InventoryItemInput!]!, $clinifyId: String!) {
    addInventory(input: $input, clinifyId: $clinifyId) {
      ...Inventory
    }
  }
  ${INVENTORY_FIELDS}
`;

export const UPDATE_INVENTORY = gql`
  mutation UpdateInventory($id: String!, $input: InventoryItemInput!, $clinifyId: String!) {
    updateInventory(id: $id, input: $input, clinifyId: $clinifyId) {
      ...Inventory
    }
  }
  ${INVENTORY_FIELDS}
`;

export const UPDATE_INVENTORY_IMAGES = gql`
  mutation UpdateInventoryImages($id: String!, $images: String!, $clinifyId: String!) {
    updateInventoryImages(id: $id, images: $images, clinifyId: $clinifyId) {
      ...Inventory
    }
  }
  ${INVENTORY_FIELDS}
`;

export const DELETE_INVENTORIES = gql`
  mutation DeleteInventories($ids: [String!]!, $clinifyId: String!) {
    deleteInventories(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const FETCH_INVENTORY_BY_ITEM_ID = gql`
  query getInventoryByItemIds($clinifyId: String!, $ids: [String!]!) {
    getInventoriesByIds(clinifyId: $clinifyId, ids: $ids) {
      id
      quantityRemaining
      unitSellingPrice
    }
  }
`;

export const INVENTORY_EVENT_SUBSCRIPTION = gql`
  subscription InventoryEvent($hospitalId: String!) {
    InventoryEvent(hospitalId: $hospitalId)
  }
`;

export const INVENTORY_ADDED_SUBS = gql`
  subscription InventoryAddedSubs($hospitalId: String!) {
    InventoryAdded(hospitalId: $hospitalId) {
      ...Inventory
      ${AUDIT_FIELDS}
    }
  }
  ${INVENTORY_FIELDS}
`;

export const INVENTORY_UPDATED_SUBS = gql`
  subscription InventoryUpdatedSubs($hospitalId: String!) {
    InventoryUpdated(hospitalId: $hospitalId) {
      ...Inventory
      ${AUDIT_FIELDS}
    }
  }
  ${INVENTORY_FIELDS}
`;

export const INVENTORY_REMOVED_SUBS = gql`
  subscription InventoryRemovedSubs($hospitalId: String!) {
    InventoryRemoved(hospitalId: $hospitalId) {
      id
      section
    }
  }
`;
