import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';
import { PERSONAL_INFORMATION_FIELDS } from './user';
import { PREAUTHORIZATION_DETAILS_FRAGMENT } from '../fragments/preauthorization-details';

const ADDITIONAL_NOTE_FIELDS = gql`
  fragment AdditionalNote on AdditionalNoteModel {
    id
    additionalNote
    conceal
    ${AUDIT_FIELDS}
  }
`;

export const LAB_RESULT = gql`
  fragment labResult on LabResultModel {
    id
    documentUrl
    details {
      testDate
      duration
      serviceDetails {
        priceId
        type
        name
        quantity
        pricePerUnit
        patientType
        paymentType
        reference
      }
      specimenCollected
      specimenTypes
      specimenNumber
      natureSiteOfSpecimen
      specimenReceivedDate
      collectionDate
      collectedBy
      performedBy
      performedBySignature
      performedBySignatureType
      performedBySignatureDateTime
      verifiedBy
      verifiedBySignature
      verifiedBySignatureType
      verifiedBySignatureDateTime
      resultDate
      pathologistName
      pathologistSignature
      pathologistSignatureType
      pathologistSignatureDateTime
      pathologistReport
      conclusion
      microscopicExam
      grossExam
      pertinentHistory
      reportDate
      finalReportDate
      done
      testName
      additionalNote
      testResults {
        name
        value
        unit
        dropdown
        box
        sensitivityBox
        units
        dependsOn
        range
        extraValue
        hasExtraValue
        valueTwo
        tabular
        referenceRange
      }
      extraTestResults {
        name
        value
        unit
        dropdown
        box
        sensitivityBox
        units
        dependsOn
        range
      }
    }
      facilityName
      additionalNote
      concealAdditionalNote
      facilityAddress
      ${AUDIT_FIELDS}
  }
`;

export const RADIOLOGY_RESULT = gql`
  fragment radiologyResult on RadiologyResultModel {
    id
    documentUrl
    details {
      examinationDate
      duration
      patientType
      paymentType
      serviceDetails {
        priceId
        type
        name
        quantity
        pricePerUnit
        patientType
        paymentType
        reference
      }
      radiographerName
      radiographerSignature
      radiographerSignatureType
      radiographerSignatureDateTime
      examinationNumber
      indication
      comparison
      technique
      radiographerReport
      impression
      done
      examType
      contrastConfirmed
      radiologistName
      radiologistSignature
      radiologistSignatureType
      radiologistSignatureDateTime
      verifiedBy
      verifiedBySignature
      verifiedBySignatureType
      verifiedBySignatureDateTime
    }
    facilityName
    facilityAddress
    ${AUDIT_FIELDS}
  }
`;

export const INVESTIGATION_LIST_FIELDS = gql`
  fragment investigationList on InvestigationModel {
    id
    requestType
    requestDate
    priority
    orderedBy
    clinifyId
    testInfo {
      testName
      priority
      specimen
      testCategory
      clinicalDiagnosisICD10
      clinicalDiagnosisICD11
      clinicalDiagnosisSNOMED
      loinc
      ref
      provider
      itemId
      preauthorizationDetailsId
    }
    specialty
    facilityName
    facilityAddress
    examinationType {
      ref
      examType
      priority
      loinc
      provider
      itemId
      preauthorizationDetailsId
      indication
    }
    labResult {
      ...labResult
    }
    radiologyResult {
      ...radiologyResult
    }
    clinicalHistory
    status
    isRequested
    external
    referringHospital {
      id
      name
      address
    }
    documentUrl
    radiologyContrastConfirmation
    profile {
      id
      clinifyId
      fullName
      clinifyId
      coverageDetails {
        id
        name
        coverageType
        companyName
        familyName
        memberNumber
        provider {
          id
          name
        }
      }
    }
    hospital {
      id
      name
      address
    }
    bill {
      id
      billStatus
      createdDate
    }
    preauthorizationDetails {
      id
      paCode
      paStatus
    }
  }
  ${LAB_RESULT}
  ${RADIOLOGY_RESULT}
`;

export const INVESTIGATION_FIELDS = gql`
  fragment investigation on InvestigationModel {
    id
    requestType
    requestDate
    clinifyId
    testInfo {
      testName
      priority
      testCategory
      specimen
      clinicalDiagnosisICD10
      clinicalDiagnosisICD11
      clinicalDiagnosisSNOMED
      loinc
      ref
      provider
      itemId
      preauthorizationDetailsId
    }
    profileId
    profile {
      id
      clinifyId
      fullName
      clinifyId
      personalInformation {
        ...PersonalInformationFields
      }
    }
    priority
    patientType
    hmoProviderId
    hmoClaim {
      id
    }
    clinicalDiagnosis {
      diagnosisICD10
      diagnosisICD11
      diagnosisSNOMED
    }
    serviceDetails {
      priceId
      type
      name
      quantity
      pricePerUnit
      itemId
      patientType
      paymentType
      reference
    }
    isPackage
    orderedBy
    specialty
    rank
    department
    facilityName
    facilityAddress
    external
    referringHospital {
      id
      name
      address
    }
    additionalNotes {
      ...AdditionalNote
    }
    bill {
      id
      billStatus
      createdDate
    }
    examinationType {
      ref
      priority
      examType
      loinc
      provider
      itemId
      preauthorizationDetailsId
      indication
    }
    clinicalHistory
    status
    isRequested
    documentUrl
    radiologyContrastConfirmation
    hospitalId
    hospital {
      id
      name
      address
    }
    labResult {
      ...labResult
    }
    radiologyResult {
      ...radiologyResult
    }
    preauthorizationDetails {
      ...PreauthorizationDetails
    }
    billStatus
  }
  ${LAB_RESULT}
  ${RADIOLOGY_RESULT}
  ${ADDITIONAL_NOTE_FIELDS}
  ${PERSONAL_INFORMATION_FIELDS}
  ${PREAUTHORIZATION_DETAILS_FRAGMENT}
`;

export const INVESTIGATION_FIELDS_WITHOUT_ADDITIONAL_NOTE = gql`
  fragment InvestigationNoAdditionalNote on InvestigationModel {
    id
    requestType
    requestDate
    clinifyId
    testInfo {
      testName
      priority
      testCategory
      clinicalDiagnosisICD10
      clinicalDiagnosisICD11
      clinicalDiagnosisSNOMED
      ref
      specimen
      loinc
      provider
      itemId
      preauthorizationDetailsId
    }
    priority
    serviceDetails {
      priceId
      type
      name
      quantity
      pricePerUnit
      itemId
      patientType
      paymentType
      reference
    }
    isPackage
    orderedBy
    specialty
    rank
    department
    facilityName
    facilityAddress
    external
    referringHospital {
      id
      name
      address
    }
    bill {
      id
      billStatus
      createdDate
    }
    examinationType {
      ref
      priority
      examType
      loinc
      provider
      itemId
      preauthorizationDetailsId
      indication
    }
    clinicalHistory
    status
    isRequested
    documentUrl
    radiologyContrastConfirmation
    hospital {
      id
      name
      address
    }
    labResult {
      ...labResult
    }
    radiologyResult {
      ...radiologyResult
    }
    billStatus
  }
  ${LAB_RESULT}
  ${RADIOLOGY_RESULT}
`;

export const INVESTIGATION_FIELDS_WITHOUT_PROFILE = gql`
  fragment InvestigationNoProfile on InvestigationModel {
    id
    requestType
    requestDate
    clinifyId
    testInfo {
      testName
      priority
      testCategory
      specimen
      clinicalDiagnosisICD10
      clinicalDiagnosisICD11
      clinicalDiagnosisSNOMED
      loinc
      ref
      provider
      itemId
      preauthorizationDetailsId
    }
    patientType
    paymentType
    priority
    serviceDetails {
      priceId
      type
      name
      quantity
      pricePerUnit
      itemId
      patientType
      paymentType
      reference
    }
    isPackage
    orderedBy
    specialty
    rank
    department
    facilityName
    facilityAddress
    external
    referringHospital {
      id
      name
      address
    }
    additionalNotes {
      ...AdditionalNote
    }
    bill {
      id
      billStatus
      createdDate
    }
    examinationType {
      ref
      priority
      examType
      loinc
      provider
      itemId
      preauthorizationDetailsId
      indication
    }
    clinicalHistory
    status
    isRequested
    documentUrl
    radiologyContrastConfirmation
    hospital {
      id
      name
      address
    }
    labResult {
      ...labResult
    }
    radiologyResult {
      ...radiologyResult
    }
    billStatus
  }
  ${LAB_RESULT}
  ${RADIOLOGY_RESULT}
  ${ADDITIONAL_NOTE_FIELDS}
`;

export const ADD_INVESTIGATION = gql`
  mutation AddInvestigation($input: NewInvestigationInput!, $id: String, $additionalNoteId: String, $pin: String ) {
    addInvestigation(investigation: $input, id: $id, additionalNoteId: $additionalNoteId, pin: $pin) {
      ...investigationList
      billStatus
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_LIST_FIELDS}
`;

export const ADD_ADDITIONAL_NOTE = gql`
  mutation AddAdditionalNote(
    $input: AdditionalNoteInput!
    $id: String!
    $clinifyId: String!
    $pin: String
  ) {
    addAdditionalNote(investigationId: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...AdditionalNote
    }
  }
  ${ADDITIONAL_NOTE_FIELDS}
`;

export const UPDATE_ADDITIONAL_NOTE = gql`
  mutation UpdateAdditionalNote(
    $input: AdditionalNoteInput!
    $id: String!
    $clinifyId: String!
    $pin: String
  ) {
    updateAdditionalNote(id: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...AdditionalNote
    }
  }
  ${ADDITIONAL_NOTE_FIELDS}
`;

export const CONCEAL_INVESTIGATION_ADDITIONAL_NOTE = gql`
  mutation ConcealInvestigationAdditionalNote($id: String!, $concealStatus: Boolean!) {
    concealInvestigationAdditionalNote(additionalNoteId: $id, concealStatus: $concealStatus) {
      ...AdditionalNote
    }
  }
  ${ADDITIONAL_NOTE_FIELDS}
`;

export const CONCEAL_LAB_RESULT_ADDITIONAL_NOTE = gql`
    mutation ConcealLabResultAdditionalNote($id: String!, $concealStatus: Boolean!) {
        concealLabResultAdditionalNote(investigationId: $id, concealStatus: $concealStatus) {
          ...investigation
          ${AUDIT_FIELDS}
        }
    }
    ${INVESTIGATION_FIELDS}
`;

export const DELETE_ADDITIONAL_NOTE = gql`
  mutation DeleteAdditionalNote($id: String!, $clinifyId: String!) {
    deleteAdditionalNote(investigationId: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const ADDITIONAL_NOTES_ADDED_SUBS = gql`
  subscription InvestigationAdditionalNoteAddedSubs($profileId: String!) {
    InvestigationAdditionalNoteAdded(profileId: $profileId) {
      ...AdditionalNote
      investigationId
      ${AUDIT_FIELDS}
    }
  }
  ${ADDITIONAL_NOTE_FIELDS}
`;

export const ADDITIONAL_NOTES_UPDATED_SUBS = gql`
  subscription InvestigationAdditionalNoteUpdatedSubs($profileId: String!) {
    InvestigationAdditionalNoteUpdated(profileId: $profileId) {
      ...AdditionalNote
      ${AUDIT_FIELDS}
    }
  }
  ${ADDITIONAL_NOTE_FIELDS}
`;

export const DELETE_ADDITIONAL_NOTES_SUBS = gql`
  subscription DeleteInvestigationAdditionalNoteSubs($profileId: String!) {
    InvestigationAdditionalNoteRemoved(profileId: $profileId) {
      id
      investigationId
    }
  }
`;

export const UPDATE_INVESTIGATION = gql`
  mutation UpdateInvestigation($id: String!, $input: InvestigationInput!, $pin: String){
    updateInvestigation(id: $id, investigation: $input, pin: $pin) {
      ...InvestigationNoAdditionalNote
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_FIELDS_WITHOUT_ADDITIONAL_NOTE}
`;

export const UPDATE_INVESTIGATION_BILL = gql`
  mutation UpdateInvestigationBill($id: String!, $input: InvestigationInput!){
    updateInvestigationBill(id: $id, investigation: $input) {
      ...InvestigationNoAdditionalNote
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_FIELDS_WITHOUT_ADDITIONAL_NOTE}
`;

export const DELETE_INVESTIGATION = gql`
  mutation DeleteInvestigations($ids: [String!]!, $clinifyId: String!) {
    deleteInvestigations(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const ARCHIVE_INVESTIGATION = gql`
  mutation ArchiveInvestigations($archive: Boolean, $clinifyId: String!, $ids: [String!]!) {
    archiveInvestigations(archive: $archive, clinifyId: $clinifyId, ids: $ids) {
      ...investigation
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_FIELDS}
`;

export const GET_INVESTIGATION = gql`
  query Investigation($id: String!, $clinifyId: String!) {
    investigation(id: $id,  clinifyId: $clinifyId) {
      ...investigation
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_FIELDS}
`;

export const GET_INVESTIGATION_LIST = gql`
  query GetInvestigationList($filterOptions: InvestigationFilterInput, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      investigations(filterOptions: $filterOptions) {
        totalCount
        list {
          billStatus
          ...investigationList
          ${AUDIT_FIELDS}
        }
      }
    }
  }
  ${INVESTIGATION_LIST_FIELDS}
`;

export const ADD_INV_LAB_RESULT = gql`
  mutation AddInvestigationLabResult($input: LabResultInput!, $investigationId: String!, $id: String!,, $pin: String) {
    addInvestigationLabResult(labResult: $input, investigationId: $investigationId, clinifyId: $id, pin: $pin) {
      ...investigation
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_FIELDS}
`;

export const UPDATE_INV_LAB_RESULT = gql`
  mutation UpdateInvestigationLabResult($input: LabResultInput!, $investigationId: String!, $pin: String){
    updateInvestigationLabResult(labResult: $input, investigationId: $investigationId, pin: $pin) {
      ...investigation
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_FIELDS}
`;

export const ADD_INV_RADIOLOGY_RESULT = gql`
  mutation AddInvestigationRadiologyResult($input: RadiologyResultInput!, $investigationId: String!, $id: String!, $pin: String) {
    addInvestigationRadiologyResult(radiologyResult: $input, investigationId: $investigationId, clinifyId: $id, pin: $pin) {
      ...investigation
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_FIELDS}
`;

export const UPDATE_INV_RADIOLOGY_RESULT = gql`
  mutation UpdateInvestigationRadiologyResult($input: RadiologyResultInput!, $investigationId: String!, $pin: String){
    updateInvestigationRadiologyResult(radiologyResult: $input, investigationId: $investigationId, pin: $pin) {
      ...investigation
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_FIELDS}
`;

export const UPDATE_INVESTIGATION_STATUS = gql`
  mutation UpdateInvestigationStatus($status: investigationStatus!, $id: String!, $pin: String){
    updateInvestigationStatus(status: $status, investigationId: $id, pin: $pin) {
      ...investigation
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_FIELDS}
`;

export const ADD_INVESTIGATION_WITH_LAB_RESULT = gql`
  mutation AddInvestigationWithLabResult($input: NewInvestigationWithLabResult!, $id: String, $pin: String) {
    addInvestigationWithLabResult(investigation: $input, id: $id, pin: $pin) {
      ...investigation
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_FIELDS}
`;

export const UPDATE_INVESTIGATION_WITH_LAB_RESULT = gql`
  mutation UpdateInvestigationWithLabResult($id: String!, $input: InvestigationWithLabInput!, $pin: String) {
    updateInvestigationWithLabResult(id: $id, investigation: $input, pin: $pin) {
      ...investigation
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_FIELDS}
`;

export const ADD_INVESTIGATION_WITH_RADIOLOGY_RESULT = gql`
  mutation AddInvestigationWithRadiologyResult($input: NewInvestigationWithRadioResult!, $id: String, $pin: String) {
    addInvestigationWithRadiologyResult(investigation: $input, id: $id, pin: $pin) {
      ...investigation
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_FIELDS}
`;

export const SEND_INVST_TO_PATIENT_EMAIL = gql`
  query SendInvestigationToPatientEmail($copyEmail: String, $id: String!, $origin: String!) {
    sendInvestigationToPatientEmail(copyEmail: $copyEmail, id: $id, origin: $origin) {
      sent
      email
      id
    }
  }
`;

export const UPDATE_INVESTIGATION_WITH_RADIOLOGY_RESULT = gql`
  mutation UpdateInvestigationWithRadiologyResult($id: String!, $input: InvestigationWithRadiologyInput!, $pin: String) {
    updateInvestigationWithRadiologyResult(id: $id, investigation: $input, pin: $pin) {
      ...investigation
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_FIELDS}
`;

export const GET_HOSPITAL_INVESTIGATIONS = gql`
  query hospital($filterOptions: InvestigationFilterInput, $hospitalId: String) {
    hospital(hospitalId: $hospitalId) {
      id
      investigations(filterOptions: $filterOptions) {
        totalCount
        list {
          ...investigationList
          billStatus
          ${AUDIT_FIELDS}
        }
      }
    }
  }
  ${INVESTIGATION_LIST_FIELDS}
`;

export const RADIOLOGY_ADDED_SUBS = gql`
  subscription RadiologyAddedSubs($profileId: String!) {
    RadiologyAdded(profileId: $profileId) {
      ...investigation
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_FIELDS}
`;

export const LABORATORY_ADDED_SUBS = gql`
  subscription LaboratoryAddedSubs($profileId: String!) {
    LaboratoryAdded(profileId: $profileId) {
      ...investigation
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_FIELDS}
`;

export const RADIOLOGY_UPDATED_SUBS = gql`
  subscription RadiologyUpdatedSubs($profileId: String!) {
    RadiologyUpdated(profileId: $profileId) {
      ...InvestigationNoAdditionalNote
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_FIELDS_WITHOUT_ADDITIONAL_NOTE}
`;

export const LABORATORY_UPDATED_SUBS = gql`
  subscription LaboratoryUpdatedSubs($profileId: String!) {
    LaboratoryUpdated(profileId: $profileId) {
      ...InvestigationNoAdditionalNote
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_FIELDS_WITHOUT_ADDITIONAL_NOTE}
`;

export const INVESTIGATION_ADDED_SUBS = gql`
  subscription InvestigationAddedSubs($profileId: String!, $hospitalId: String!) {
    InvestigationAdded(profileId: $profileId, hospitalId: $hospitalId) {
      ...investigationList
      billStatus
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_LIST_FIELDS}
`;

export const INVESTIGATION_UPDATED_SUBS = gql`
  subscription InvestigationUpdatedSubs($profileId: String!, $hospitalId: String!) {
    InvestigationUpdated(profileId: $profileId, hospitalId: $hospitalId) {
      ...InvestigationNoAdditionalNote
      ${AUDIT_FIELDS}
    }
  }
  ${INVESTIGATION_FIELDS_WITHOUT_ADDITIONAL_NOTE}
`;

export const DELETE_INVESTIGATION_SUBS = gql`
  subscription DeleteInvestigationSubs($profileId: String!, $hospitalId: String!) {
    InvestigationRemoved(profileId: $profileId, hospitalId: $hospitalId) {
      id
    }
  }
`;

export const ARCHIVE_INVESTIGATION_SUBS = gql`
  subscription ArchiveInvestigationSubs($profileId: String!, $hospitalId: String!) {
    InvestigationArchived(profileId: $profileId, hospitalId: $hospitalId) {
      ...investigation
    }
  }
  ${INVESTIGATION_FIELDS}
`;

export const UNARCHIVE_INVESTIGATION_SUBS = gql`
  subscription UnarchiveInvestigationSubs($profileId: String!, $hospitalId: String!) {
    InvestigationUnarchived(profileId: $profileId, hospitalId: $hospitalId) {
      ...investigation
    }
  }
  ${INVESTIGATION_FIELDS}
`;

export const INVESTIGATION_EVENT_SUBSCRIPTION = gql`
  subscription InvestigationEvent($hospitalId: String!) {
    InvestigationEvent(hospitalId: $hospitalId)
  }
`;

export const GET_INVESTIGATION_CLAIM = gql`
  query InvestigationClaim($id: String!, $clinifyId: String!) {
    investigation(id: $id, clinifyId: $clinifyId) {
      id
      hmoClaim {
        id
      }
    }
  }
`;

export const GET_LAB_RESULT_TREND = gql`
  query GetLabResultTrend($filter: LabResultTrendFilterInput!) {
    findLabResultTrend(filter: $filter) {
      resultDate
      testDate
      testName
      testResults {
        name
        extraValue
        value
        unit
        valueTwo
        hasExtraValue
        range
      }
    }
  }
`;
