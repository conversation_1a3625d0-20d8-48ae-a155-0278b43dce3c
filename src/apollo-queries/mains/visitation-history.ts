import { gql } from '@apollo/client';

export const GET_PATIENT_VISITATION_HISTORY = gql`
  query GetPatientVisitationHistory($filter: PatientVisitationHistoryFilter!) {
    getPatientVisitationHistory(filter: $filter) {
      nextVisitDate
      previousVisitDate
      admission {
        date
        dischargeDate
        hospitalName
      }
      antenatal {
        hospitalName
        date
        estimatedDateOfDelivery
        lastMenstrualPeriod
        seenBy
        gestationAge
        maternalWeight
        maternalWeightUnit
        bloodPressure
        fetalHeartRate
        symphysioHeight
        presentation
        lie
        fetalMovement
        oedema
      }
      consultation {
        date
        doctorName
        finalDiagnosis {
          diagnosisICD10
          diagnosisICD11
          diagnosisSNOMED
        }
        provisionalDiagnosis {
          diagnosisICD10
          diagnosisICD11
          diagnosisSNOMED
        }
        complaint
        complaintHistory
        hospitalName
      }
      immunization {
        hospitalName
        administeredDate
        name
        administratorName
      }
      laboratory {
        name
        hospitalName
        date
        performedBy
        requestedBy
        type
      }
      radiology {
        name
        hospitalName
        date
        performedBy
        requestedBy
        type
      }
      labourAndDelivery {
        date
        hospitalName
        seenBy
        estimatedDateOfDelivery
        gestationalAge
      }
      medicalReport {
        hospitalName
        date
        type
        doctorName
      }
      medication {
        hospitalName
        name
        consumableName
        prescribedBy
        prescriptionDate
        administrationMethod
        dosage
        dosageUnit
        frequency
        duration
      }
      nursingServices {
        hospitalName
        assistantNurseName
        nurseName
        procedureDate
        procedureName
        procedureType
      }
      postnatal {
        hospitalName
        seenBy
        deliveryDate
        visitationDate
      }
      procedure {
        hospitalName
        type
        date
        requestedBy
        operatedBy
      }
      vitalSigns {
        date
        hospitalName
        anthropometry {
          height
          heightUnit
          readingDateTime
          weight
          weightUnit
          bmi
          bsa
          isHeightCritical
          isWeightCritical
        }
        bloodGlucose {
          readingDateTime
          reading
          readingUnit
          isReadingCritical
        }
        bloodPressure {
          readingDateTime
          diastolic
          systolic
          heartRate
          isDiastolicCritical
          isSystolicCritical
          isHeartRateCritical
        }
        pain {
          type
          dateTimePainStarted
          score
        }
        pulseRate {
          readingDateTime
          reading
          isReadingCritical
        }
        respiratoryRate {
          reading
          readingDateTime
          isReadingCritical
          isOxygenSaturationCritical
        }
        takenBy
        temperature {
          readingDateTime
          reading
          readingUnit
          isReadingCritical
        }
        urineDipstick {
          readingDateTime
          blood
          glucose
          ketones
          nitrites
          protein
          urobilinogen
          leucocyte
        }
        visualAcuity {
          readingDateTime
          withGlassesLeft
          withGlassesRight
          withoutGlassesLeft
          withoutGlassesRight
        }
      }
    }
  }
`;

export const GET_PATIENT_JOURNEY = gql`
  query GetPatientJourney($patientId: String!) {
    getPatientVisitationHistorySummary(patientId: $patientId) {
      registeredDate
      registeredWith
      list {
        hospitalName
        date
        serviceTypes
      }
    }
  }
`;
