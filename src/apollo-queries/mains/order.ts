import { gql } from '@apollo/client';

export const INVENTORY_ORDER_FIELDS = gql`
  fragment InventoryOrder on InventoryOrderModel {
    id
    sn
    quantityOrdered
    addedDateTime
    deliveredDateTime
    section
    supplier
    invoiceNumber
    name
    type
    size
    bedNumber
    ward
    group
    flag
    description
    strength
    category
    code
    batchNumber
    barcode
    bedAvailable
    expiryDate
    expiryStatus
    damagedCount
    markup
    unitCost
    unitSellingPrice
    totalCost
    totalSale
    quantityRemaining
    quantityPurchased
    quantityAvailable
    quantityDispensed
    quantitySold
    manufacturer
    recievedDateTime
    reorderLevel
    receivedBy
    addedBy
    purchasedBy
    vin
    plateNumber
    year
    model
    colour
    status
    comments
    images
    addedDateTime
    creatorId
    createdDate
    creatorName
    approved
    canceled
    deliveryStatus
    quantityDelivered
    deliveredBy
    orderFrom {
      name
    }
    orderTo {
      id
      name
    }
  }
`;

export const GET_INVENTORY_ORDER_LIST = gql`
  query GetHospitalInventoryOrderList($filterOptions: InventoryOrderFilterInput) {
    hospital {
      id
      orders(filterOptions: $filterOptions) {
        totalCount
        list {
          ...InventoryOrder
        }
      }
    }
  }
  ${INVENTORY_ORDER_FIELDS}
`;

export const ADD_INVENTORY_ORDER = gql`
  mutation AddInventoryOrder($input: [InventoryOrderInput!]!) {
    addInventoryOrder(input: $input) {
      ...InventoryOrder
    }
  }
  ${INVENTORY_ORDER_FIELDS}
`;

export const FETCH_INVENTORY_ORDER = gql`
  query FetchInventoryOrder($id: String!) {
    inventoryOrder(id: $id) {
      ...InventoryOrder
    }
  }
  ${INVENTORY_ORDER_FIELDS}
`;

export const UPDATE_INVENTORY_ORDER = gql`
  mutation UpdateInventoryOrder($id: String!, $input: InventoryOrderInput!) {
    updateInventoryOrder(id: $id, input: $input) {
      ...InventoryOrder
    }
  }
  ${INVENTORY_ORDER_FIELDS}
`;

export const UPDATE_INVENTORY_ORDER_STATUS = gql`
  mutation UpdateInventoryOrderStatus($ids: [String!]!, $status: InventoryOrderStatus!) {
    updateInventoryOrderStatus(ids: $ids, status: $status) {
      ...InventoryOrder
    }
  }
  ${INVENTORY_ORDER_FIELDS}
`;

export const APPROVE_INVENTORY_ORDER = gql`
  mutation OrderRequestApproval($ids: [String!]!, $status: Boolean!) {
    orderRequestApproval(ids: $ids, status: $status) {
      ...InventoryOrder
    }
  }
  ${INVENTORY_ORDER_FIELDS}
`;

export const DELETE_INVENTORY_ORDERS = gql`
  mutation DeleteInventoryOrders($ids: [String!]!) {
    deleteInventoryOrders(ids: $ids) {
      id
    }
  }
`;

export const INVENTORY_ORDER_ADDED_SUBS = gql`
  subscription InventoryOrderAddedSubs($hospitalId: String!) {
    InventoryOrderAdded(hospitalId: $hospitalId) {
      ...InventoryOrder
    }
  }
  ${INVENTORY_ORDER_FIELDS}
`;

export const INVENTORY_ORDER_UPDATED_SUBS = gql`
  subscription InventoryOrderUpdatedSubs($hospitalId: String!) {
    InventoryOrderUpdated(hospitalId: $hospitalId) {
      ...InventoryOrder
    }
  }
  ${INVENTORY_ORDER_FIELDS}
`;

export const DELETE_INVENTORY_ORDER_SUBS = gql`
  subscription DeleteInventoryOrderSubs($hospitalId: String!) {
    InventoryOrderRemoved(hospitalId: $hospitalId) {
      id
      section
    }
  }
`;
