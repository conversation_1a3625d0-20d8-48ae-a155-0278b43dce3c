import { gql } from '@apollo/client';
import { PREAUTHORIZATION_DETAILS_FRAGMENT } from 'apollo-queries/fragments/preauthorization-details';
import { AUDIT_FIELDS } from '../fragments/audit';

export const NURSING_SERVICE_LINKING_FIELDS = gql`
  fragment NursingServiceLinking on NursingServiceModel {
    vitals {
      id
    }
    admissions {
      id
    }
    consultations {
      id
    }
    medications {
      id
    }
    surgeries {
      id
    }
    investigations {
      id
    }
    labTests {
      id
    }
    radiology {
      id
    }
    immunizations {
      id
    }
  }
`;

export const NURSING_SERVICE_DETAILS_FIELD = gql`
  fragment NursingServiceDetail on NursingServiceDetailModel {
    id
    createdDate
    updatedDate
    lastModifierId
    lastModifierName
    creatorId
    creatorName
    procedureDateTime
    procedureType
    provider
    providerServiceName
    priority
    category
    dateOfBirth
    consentGiven
    parentGuardianPresent
    parentGuardianName
    anaesthesiaGiven
    anaesthesiaType
    vitaminKGiven
    castLocation
    reasonForCasting
    isItARepeatedCasting
    hasRadiologicalInvestigationBeenDone
    bathing
    dressing
    eating
    mobility
    stairClimbing
    toiletUse
    woundLocation
    dressingType
    dressingAppearance
    dressingIntervention
    lastDressingChange
    dressingChangeDue
    painScore
    painDescriptors
    otherPainDescriptors
    signOfInfection
    whichEar
    observation
    informedConsent
    method
    otherMethods
    councelled
    typeOfInjectable
    typeOfImplant
    duration
    procedureStartDateTime
    procedureEndDateTime
    heartRateBefore
    heartRateAfter
    respiratoryRateBefore
    respiratoryRateAfter
    oxygenSaturationBefore
    oxygenSaturationAfter
    medications {
      medicationName
      prescriptionTime
      dosage
      dosageUnit
    }
    patientConsentSignature
    patientConsentSignatureType
    patientConsentSignatureDateTime
    nursingServiceId
    procedureName
    location
    procedureNote
    injectionName
    InjectionLocation
    injectionPrepared
    equipmentsSterilized
    medicalHistoryChecked
    repeatInjection
    ${AUDIT_FIELDS}
  }
`;

export const NURSING_SERVICE_FIELDS = gql`
  fragment NursingService on NursingServiceModel {
    id
    hmoProviderId
    serviceDetails {
      priceId
      type
      name
      quantity
      pricePerUnit
      itemId
      patientType
      paymentType
      reference
    }
    isPackage
    specialty
    department
    nurseName
    assistantNurseName
    facilityName
    facilityAddress
    additionalNote
    concealAdditionalNote
    billStatus
    documentUrl
    createdDate
    updatedDate
    hospitalId
    profileId
    profile {
      id
      clinifyId
      coverageDetails {
        id
        name
        coverageType
        companyName
        familyName
        memberNumber
        provider {
          id
          name
        }
      }
    }
    preauthorizationDetails {
      ...PreauthorizationDetails
    }
  }
  ${PREAUTHORIZATION_DETAILS_FRAGMENT}
`;

const NOTES_FIELDS = gql`
  fragment ProgressNotes on NursingServiceProgressNoteModel {
    id
    note
    conceal
    nursingServiceId
  }
`;

export const GET_PATIENT_NURSING_SERVICE_LIST = gql`
  query GetPatientNursingServiceList($filterOptions: NursingServicesFilterInput, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      nursingServices(filterOptions: $filterOptions) {
        totalCount
        list {
          ...NursingService
          details {
            ...NursingServiceDetail
          }
          progressNotes {
            id
            note
          }
          billing {
            id
            createdDate
            billId
          }
          billStatus
        }
      }
    }
  }
  ${NURSING_SERVICE_FIELDS}
  ${NURSING_SERVICE_DETAILS_FIELD}
`;

export const GET_PATIENT_NURSING_SERVICE = gql`
  query GetPatientNursingService($id: String!, $clinifyId: String!) {
    nursingService(id: $id, clinifyId: $clinifyId) {
      ...NursingService
      details {
        ...NursingServiceDetail
      }
      progressNotes {
        ...ProgressNotes
        ${AUDIT_FIELDS}
      }
      ...NursingServiceLinking
      ${AUDIT_FIELDS}
    }
  }
  ${NOTES_FIELDS}
  ${NURSING_SERVICE_FIELDS}
  ${NURSING_SERVICE_DETAILS_FIELD}
  ${NURSING_SERVICE_LINKING_FIELDS}
`;

export const ADD_PATIENT_NURSING_SERVICE = gql`
  mutation AddPatientNursingService($input: NewNursingServicesInput!, $id: String, $pin: String ) {
    addNursingService(nursingService: $input, id: $id, pin: $pin) {
      ...NursingService
      details {
        ...NursingServiceDetail
      }
      bill {
        id
        createdDate
      }
      hmoClaim {
        id
      }
      progressNotes {
        ...ProgressNotes
        ${AUDIT_FIELDS}
      }
      ...NursingServiceLinking
      ${AUDIT_FIELDS}
    }
  }
  ${NOTES_FIELDS}
  ${NURSING_SERVICE_FIELDS}
  ${NURSING_SERVICE_DETAILS_FIELD}
  ${NURSING_SERVICE_LINKING_FIELDS}
`;

export const UPDATE_PATIENT_NURSING_SERVICE = gql`
  mutation UpdatePatientNursingService($input: NursingServicesInput!, $id: String!, $pin: String ) {
    updateNursingService(nursingService: $input, id: $id, pin: $pin) {
      ...NursingService
      hmoClaim {
        id
      }
      ...NursingServiceLinking
      ${AUDIT_FIELDS}
    }
  }
  ${NURSING_SERVICE_FIELDS}
  ${NURSING_SERVICE_LINKING_FIELDS}
`;

export const UPDATE_PATIENT_NURSING_SERVICE_BILL = gql`
  mutation UpdatePatientNursingServiceBill($input: NursingServicesInput!, $id: String!) {
    updateNursingServiceBill(nursingService: $input, id: $id) {
      ...NursingService
      hmoClaim {
        id
      }
      ...NursingServiceLinking
      ${AUDIT_FIELDS}
    }
  }
  ${NURSING_SERVICE_FIELDS}
  ${NURSING_SERVICE_LINKING_FIELDS}
`;

export const DELETE_PATIENT_NURSING_SERVICE = gql`
  mutation DeletePatientNursingServices($ids: [String!]!, $clinifyId: String!) {
    deleteNursingServices(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const ARCHIVE_NURSING_SERVICE = gql`
  mutation ArchiveNursingServices($ids: [String!]!, $archive: Boolean, $clinifyId: String!) {
    archiveNursingServices(ids: $ids, archive: $archive, clinifyId: $clinifyId) {
      ...NursingService
      details {
        ...NursingServiceDetail
      }
      progressNotes {
        ...ProgressNotes
      }
      ${AUDIT_FIELDS}
    }
  }
  ${NOTES_FIELDS}
  ${NURSING_SERVICE_FIELDS}
  ${NURSING_SERVICE_DETAILS_FIELD}
`;

export const GET_NURSING_SERVICE_CLAIM = gql`
  query NursingServiceClaim($id: String!, $clinifyId: String!) {
    nursingService(id: $id, clinifyId: $clinifyId) {
      id
      hmoClaim {
        id
      }
    }
  }
`;

export const SAVE_NURSING_SERVICE_DETAIL = gql`
  mutation SaveNursingServicesDetail($input: NursingServicesDetailsInput!, $id: String!, $clinifyId: String!, $pin: String) { 
    saveNursingServicesDetails(input: $input, nursingServiceId: $id, clinifyId: $clinifyId, pin: $pin) {
      ...NursingServiceDetail
      ${AUDIT_FIELDS}
    }
  }
  ${NURSING_SERVICE_DETAILS_FIELD}
`;

export const UPDATE_NURSING_SERVICE_DETAIL = gql`
  mutation UpdateNursingServicesDetail($input: NursingServicesDetailsInput!, $id: String!, $clinifyId: String!, $pin: String) { 
    updateNursingServicesDetails(input: $input, id: $id, clinifyId: $clinifyId, pin: $pin) {
      ...NursingServiceDetail
      ${AUDIT_FIELDS}
    }
  }
  ${NURSING_SERVICE_DETAILS_FIELD}
`;

export const DELETE_NURSING_SERVICE_DETAIL = gql`
  mutation DeleteNursingServicesDetail($id: String!, $clinifyId: String!) {
    deleteNursingServicesDetails(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const NURSING_SERVICE_ADDED_SUBS = gql`
  subscription NursingServiceAddedSubs($profileId: String!) {
    NursingServiceAdded(profileId: $profileId) {
      ...NursingService
      details {
        ...NursingServiceDetail
      }
      bill {
        id
        createdDate
      }
      progressNotes {
        ...ProgressNotes
      }
      ...NursingServiceLinking
      ${AUDIT_FIELDS}
    }
  }
  ${NOTES_FIELDS}
  ${NURSING_SERVICE_FIELDS}
  ${NURSING_SERVICE_DETAILS_FIELD}
  ${NURSING_SERVICE_LINKING_FIELDS}
`;

export const NURSING_SERVICE_UPDATED_SUBS = gql`
  subscription NursingServiceUpdatedSubs($profileId: String!) {
    NursingServiceUpdated(profileId: $profileId) {
      ...NursingService
      ...NursingServiceLinking
      ${AUDIT_FIELDS}
    }
  }
  ${NURSING_SERVICE_FIELDS}
  ${NURSING_SERVICE_LINKING_FIELDS}
`;

export const DELETE_NURSING_SERVICE_SUBS = gql`
  subscription DeleteNursingServiceSubs($profileId: String!) {
    NursingServiceRemoved(profileId: $profileId) {
      id
    }
  }
`;

export const ARCHIVE_NURSING_SERVICE_SUBS = gql`
  subscription ArchiveNursingServiceSubs($profileId: String!) {
    NursingServiceArchived(profileId: $profileId) {
      ...NursingService
      details {
        ...NursingServiceDetail
      }
      ...NursingServiceLinking
    }
  }
  ${NURSING_SERVICE_FIELDS}
  ${NURSING_SERVICE_DETAILS_FIELD}
  ${NURSING_SERVICE_LINKING_FIELDS}
`;

export const UNARCHIVE_NURSING_SERVICE_SUBS = gql`
  subscription UnarchiveNursingServiceSubs($profileId: String!) {
    NursingServiceUnarchived(profileId: $profileId) {
      ...NursingService
      details {
        ...NursingServiceDetail
      }
      ...NursingServiceLinking
    }
  }
  ${NURSING_SERVICE_FIELDS}
  ${NURSING_SERVICE_DETAILS_FIELD}
  ${NURSING_SERVICE_LINKING_FIELDS}
`;

export const ADD_NURSING_SERVICE_SIGNATURE = gql`
  mutation AddNursingServicesConsentSignature($id: String!, $clinifyId: String!, $input: SignatureInput!, $pin: String) {
    addNursingServicesConsentSignature(id: $id, clinifyId: $clinifyId, input: $input, pin: $pin) {
      ...NursingServiceDetail
      ${AUDIT_FIELDS}
    }
  }
  ${NURSING_SERVICE_DETAILS_FIELD}
`;

export const UPDATE_NURSING_SERVICE_SIGNATURE = gql`
  mutation UpdateNursingServicesConsentSignature($id: String!, $clinifyId: String!, $input: SignatureInput!, $pin: String) {
    updateNursingServicesConsentSignature(id: $id, clinifyId: $clinifyId, input: $input, pin: $pin) {
      ...NursingServiceDetail
      ${AUDIT_FIELDS}
    }
  }
  ${NURSING_SERVICE_DETAILS_FIELD}
`;

export const DELETE_NURSING_SERVICE_SIGNATURE = gql`
  mutation DeleteNursingServicesConsentSignature($id: String!, $clinifyId: String!) {
    deleteNursingServicesConsentSignature(id: $id, clinifyId: $clinifyId) {
      ...NursingServiceDetail
      ${AUDIT_FIELDS}
    }
  }
  ${NURSING_SERVICE_DETAILS_FIELD}
`;

export const SAVE_PROGRESS_NOTES = gql`
  mutation SaveNursingServicesProgressNotes($input: NursingServiceProgressNoteInput!, $id: String!, $clinifyId: String!, $pin: String) {
    saveNursingServicesProgressNote(nursingServiceId: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...ProgressNotes
      ${AUDIT_FIELDS}
    }
  }
  ${NOTES_FIELDS}
`;

export const UPDATE_PROGRESS_NOTES = gql`
  mutation UpdateNursingServicesProgressNotes($input: NursingServiceProgressNoteInput!, $id: String!, $clinifyId: String!, $pin: String) {
    updateNursingServicesProgressNote(id: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...ProgressNotes
      ${AUDIT_FIELDS}
    }
  }
  ${NOTES_FIELDS}
`;

export const CONCEAL_PROGRESS_NOTE = gql`
  mutation ConcealNursingServicesProgressNotes($id: String!, $concealStatus: Boolean!) {
    concealNursingServicesProgressNote(id: $id, concealStatus: $concealStatus) {
      ...ProgressNotes,
      ${AUDIT_FIELDS}
    }
  }
  ${NOTES_FIELDS}
`;

export const DELETE_PROGRESS_NOTES = gql`
  mutation DeleteNursingServicesProgressNotes($id: String!, $clinifyId: String!) {
    deleteNursingServicesProgressNote(id: $id, clinifyId: $clinifyId) {
      id
      nursingServiceId
    }
  }
`;

export const NURSING_SERVICE_DETAILS_ADDED_SUBS = gql`
  subscription NursingServiceDetailsAddedSubs($profileId: String!) {
    NursingServiceDetailsAdded(profileId: $profileId) {
      ...NursingServiceDetail
      ${AUDIT_FIELDS}
    }
  }
  ${NURSING_SERVICE_DETAILS_FIELD}
`;

export const NURSING_SERVICE_DETAILS_UPDATED_SUBS = gql`
  subscription NursingServiceDetailsUpdatedSubs($profileId: String!) {
    NursingServiceDetailsUpdated(profileId: $profileId) {
      ...NursingServiceDetail
      ${AUDIT_FIELDS}
    }
  }
  ${NURSING_SERVICE_DETAILS_FIELD}
`;

export const NURSING_SERVICE_DETAILS_REMOVED_SUBS = gql`
  subscription NursingServiceDetailsRemovedSubs($profileId: String!) {
    NursingServiceDetailsRemoved(profileId: $profileId) {
      id
      nursingServiceId
    }
  }
`;

export const NURSING_SERVICE_PROGRESS_NOTE_ADDED_SUBS = gql`
  subscription NursingServiceProgressNoteAddedSubs($profileId: String!) {
    NursingServiceProgressNoteAdded(profileId: $profileId) {
      ...ProgressNotes
      ${AUDIT_FIELDS}
    }
  }
  ${NOTES_FIELDS}
`;

export const NURSING_SERVICE_PROGRESS_NOTE_UPDATED_SUBS = gql`
  subscription NursingServiceProgressNoteUpdatedSubs($profileId: String!) {
    NursingServiceProgressNoteUpdated(profileId: $profileId) {
      ...ProgressNotes
      ${AUDIT_FIELDS}
    }
  }
  ${NOTES_FIELDS}
`;

export const NURSING_SERVICE_PROGRESS_NOTE_REMOVED_SUBS = gql`
  subscription NursingServiceProgressNoteRemovedSubs($profileId: String!) {
    NursingServiceProgressNoteRemoved(profileId: $profileId) {
      id
      nursingServiceId
    }
  }
`;

export const CONCEAL_NURSING_SERVICE_ADDITIONAL_NOTE = gql`
  mutation ConcealNursingAdditionalNote($id: String!, $concealStatus: Boolean!) {
    concealNursingServiceAdditionalNote(id: $id, concealStatus: $concealStatus) {
      id
      concealAdditionalNote
      additionalNote
    }
  }
`;

export const LINK_RECORDS_TO_NURSING_SERVICE = gql`
  mutation LinkRecordsToNursingService($parentId: String!, $recordIds: [String!]!, $recordType: NursingServiceLinkRecordType!) {
    linkRecordsToNursingService(id: $parentId, recordIds: $recordIds, recordType: $recordType) {
      ...NursingService
      details {
        ...NursingServiceDetail
      }
      ...NursingServiceLinking
      ${AUDIT_FIELDS}
    }
  }
  ${NURSING_SERVICE_FIELDS}
  ${NURSING_SERVICE_DETAILS_FIELD}
  ${NURSING_SERVICE_LINKING_FIELDS}
`;
