import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

export const MEDICATION_BUNDLE_ITEM_FRAGMENT = gql`
  fragment MedicationBundleItem on MedicationBundleItemModel {
    id
    administrationMethod
    bank
    bundleDate
    createdDate
    creatorId
    creatorName
    dosage
    dosageUnit
    duration
    frequency
    lastModifierId
    lastModifierName
    medicationCategory
    drugInventoryId
    inventoryClass
    medicationConsumables {
      drugInventoryId
      name
      quantity
      unitPrice
      inventoryClass
    }
    priceDetails {
      type
      name
      pricePerUnit
      patientType
      paymentType
      quantity
    }
    provider
    medicationName
    option
    purpose
    quantity
    unitPrice
    updatedDate
    prescriptionNote
    medicationType
    diagnosis {
      diagnosisICD10
      diagnosisICD11
      diagnosisSNOMED
    }
    ${AUDIT_FIELDS}
  }
`;

export const MEDICATION_BUNDLE_FRAGMENT = gql`
  fragment MedicationBundle on MedicationBundleModel {
    id
    bundleName
    clinifyId
    createdDate
    updatedDate
    creatorId
    creatorName
    documentUrl
    lastModifierId
    lastModifierName
    additionalNote
    ${AUDIT_FIELDS}
  }
`;

export const GET_MEDICATION_BUNDLE_LIST = gql`
  query GetMedicationBundleList($filterOptions: FilterInput, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      medicationBundles(filterOptions: $filterOptions) {
        totalCount
        list {
          ...MedicationBundle
          medicationBundleItems {
            ...MedicationBundleItem
          }
        }
      }
    }
  }
  ${MEDICATION_BUNDLE_FRAGMENT}
  ${MEDICATION_BUNDLE_ITEM_FRAGMENT}
`;

export const FETCH_MEDICATION_BUNDLE = gql`
  query FetchMedicationBundle($id: String!) {
    medicationBundle(id: $id) {
      ...MedicationBundle
      medicationBundleItems {
        ...MedicationBundleItem
      }
    }
  }
  ${MEDICATION_BUNDLE_FRAGMENT}
  ${MEDICATION_BUNDLE_ITEM_FRAGMENT}
`;

export const GET_SAVED_MEDICATION_BUNDLES = gql`
  query GetSavedMedicationBundles {
    getSavedMedicationBundles {
      id
      bundleName
    }
  }
`;

export const ADD_MEDICATION_BUNDLE = gql`
  mutation AddMedicationBundle($input: NewMedicationBundleInput!) {
    addMedicationBundle(medicationBundle: $input) {
      ...MedicationBundle
      medicationBundleItems {
        ...MedicationBundleItem
      }
    }
  }
  ${MEDICATION_BUNDLE_FRAGMENT}
  ${MEDICATION_BUNDLE_ITEM_FRAGMENT}
`;

export const UPDATE_MEDICATION_BUNDLE = gql`
  mutation UpdateMedicationBundle($id: String!, $input: MedicationBundleInput!) {
    updateMedicationBundle(id: $id, medicationBundle: $input) {
      ...MedicationBundle
      medicationBundleItems {
        ...MedicationBundleItem
      }
    }
  }
  ${MEDICATION_BUNDLE_FRAGMENT}
  ${MEDICATION_BUNDLE_ITEM_FRAGMENT}
`;

export const DELETE_MEDICATION_BUNDLE = gql`
  mutation DeleteMedicationBundle($ids: [String!]!) {
    deleteMedicationBundles(ids: $ids) {
      id
    }
  }
`;

export const SAVE_MEDICATION_BUNDLE_ITEM = gql`
  mutation SaveMedicationBundleItem($id: String!, $input: MedicationBundleItemInput!) {
    addMedicationBundleItem(medicationBundleId: $id, input: $input) {
      ...MedicationBundleItem
      medicationBundleId
    }
  }
  ${MEDICATION_BUNDLE_ITEM_FRAGMENT}
`;

export const UPDATE_MEDICATION_BUNDLE_ITEM = gql`
  mutation UpdateMedicationBundleItem($id: String!, $input: MedicationBundleItemInput!) {
    updateMedicationBundleItem(medicationBundleItemId: $id, input: $input) {
      ...MedicationBundleItem
      medicationBundleId
    }
  }
  ${MEDICATION_BUNDLE_ITEM_FRAGMENT}
`;

export const DELETE_MEDICATION_BUNDLE_ITEM = gql`
  mutation DeleteMedicationBundleItem($id: String!) {
    deleteMedicationBundleItem(id: $id) {
      id
      medicationBundleId
    }
  }
`;

export const ARCHIVE_MEDICATION_BUNDLE = gql`
  mutation ArchiveMedicationBundleItem($ids: [String!]!, $archive: Boolean) {
    archiveMedicationBundles(ids: $ids, archive: $archive) {
      ...MedicationBundle
    }
  }
  ${MEDICATION_BUNDLE_FRAGMENT}
`;

export const MEDICATION_BUNDLE_ADDED_SUBS = gql`
  subscription MedicationBundleAdded($profileId: String!) {
    MedicationBundleAdded(profileId: $profileId) {
      ...MedicationBundle
      medicationBundleItems {
        ...MedicationBundleItem
      }
    }
  }
  ${MEDICATION_BUNDLE_FRAGMENT}
  ${MEDICATION_BUNDLE_ITEM_FRAGMENT}
`;

export const MEDICATION_BUNDLE_UPDATED_SUBS = gql`
  subscription MedicationBundleUpdated($profileId: String!) {
    MedicationBundleUpdated(profileId: $profileId) {
      ...MedicationBundle
      medicationBundleItems {
        ...MedicationBundleItem
      }
    }
  }
  ${MEDICATION_BUNDLE_FRAGMENT}
  ${MEDICATION_BUNDLE_ITEM_FRAGMENT}
`;

export const MEDICATION_BUNDLE_REMOVED_SUBS = gql`
  subscription MedicationBundleRemoved($profileId: String!) {
    MedicationBundleRemoved(profileId: $profileId) {
      id
    }
  }
`;

export const MEDICATION_BUNDLE_ARCHIVED_SUBS = gql`
  subscription MedicationBundleArchived($profileId: String!) {
    MedicationBundleArchived(profileId: $profileId) {
      ...MedicationBundle
      medicationBundleItems {
        ...MedicationBundleItem
      }
    }
  }
  ${MEDICATION_BUNDLE_FRAGMENT}
  ${MEDICATION_BUNDLE_ITEM_FRAGMENT}
`;

export const MEDICATION_BUNDLE_UNARCHIVED_SUBS = gql`
  subscription MedicationBundleUnarchived($profileId: String!) {
    MedicationBundleUnarchived(profileId: $profileId) {
      ...MedicationBundle
      medicationBundleItems {
        ...MedicationBundleItem
      }
    }
  }
  ${MEDICATION_BUNDLE_FRAGMENT}
  ${MEDICATION_BUNDLE_ITEM_FRAGMENT}
`;

export const MEDICATION_BUNDLE_ITEM_ADDED_SUBS = gql`
  subscription MedicationBundleItemAdded($profileId: String!) {
    MedicationBundleItemAdded(profileId: $profileId) {
      ...MedicationBundleItem
      medicationBundleId
    }
  }
  ${MEDICATION_BUNDLE_ITEM_FRAGMENT}
`;

export const MEDICATION_BUNDLE_ITEM_UPDATED_SUBS = gql`
  subscription MedicationBundleItemUpdated($profileId: String!) {
    MedicationBundleItemUpdated(profileId: $profileId) {
      ...MedicationBundleItem
      medicationBundleId
    }
  }
  ${MEDICATION_BUNDLE_ITEM_FRAGMENT}
`;

export const MEDICATION_BUNDLE_ITEM_REMOVED_SUBS = gql`
  subscription MedicationBundleItemRemoved($profileId: String!) {
    MedicationBundleItemRemoved(profileId: $profileId) {
      id
      medicationBundleId
    }
  }
`;
