import { gql } from '@apollo/client';
import { AUDIT_FIELDS_WITH_ID, UPDATED_BY } from '../fragments/audit';
import { FACILITY_BILLING_INFORMATION_FRAGMENT } from '../fragments/hospital';

/** ORG BILLS */

export const SUB_BILL_FIELDS = gql`
  fragment SubBill on SubBillDetailsModel {
    id
    amountDue
    amountOwing
    unitPrice
    amountOutstanding
    amountPaid
    discountCode
    amount
    discountAmount
    discountPercentage
    vatAmount
    vatPercentage
    patientType
    paymentType
    serviceType
    serviceName
    quantity
    reference
    creatorName
    lastModifierName
    createdDate
    updatedDate
  }
`;

export const ORG_BILL_DETAIL_FIELDS = gql`
  fragment BillDetail on BillDetailsModel {
    id
    amountDue
    amountOwing
    unitPrice
    amountOutstanding
    amountPaid
    discountCode
    amount
    discountAmount
    discountPercentage
    vatAmount
    vatPercentage
    professionalFeeAmount
    professionalFeePercentage
    bankName
    accountNumber
    reference
    walletTransactionRef
    billType
    billName
    serviceType
    serviceName
    subServiceType
    serviceDetails {
      serviceType
      serviceName
    }
    splitPayment
    additionalPayments {
      splitAmount
      splitPaymentType
      splitBankName
      splitAccountNumber
    }
    description
    patientType
    paymentType
    paymentMethod
    quantity
    splitReference
    status
    excluded
    manuallyCreated
    subBills {
      ...SubBill
    }
    createdBy {
      id
      clinifyId
      fullName
    }
    creatorName
    lastModifierName
    createdDate
    updatedDate
    billId
    diagnosis {
      diagnosisICD10
      diagnosisICD11
      diagnosisSNOMED
    }
    admissionSeverity
  }
  ${SUB_BILL_FIELDS}
`;

export const BILL_FIELDS = gql`
  fragment BillFields on BillModel {
    id
    totalAmount
    unitPrice
    totalAccumulativePaid
    amountPayable
    amountPaid
    discountAmount
    vatAmount
    professionalFeeAmount
    invoiceNumber
    amountDue
    amountOwning
    amountUnderpaid
    amountOverpaid
    hospitalName
    hospitalAddress
    amountOutstanding
    reference
    serviceType
    description
    paymentStatus
    billStatus
    autoGenerated
    additionalNote
    raisedBy
    collectedBy
    collectionDateTime
    billingDateTime
    recalled
    isPackage
    details {
      ...BillDetail
    }
    senderProfile {
      id
    }
    senderHospital {
      id
      name
      address
      facilityLogo
      website
      phoneNumber {
        countryCode
        value
      }
      billingInformation {
        ...FacilityBillingInformation
      }
    }
    receiverProfile {
      id
      clinifyId
      type
      fullName
      coverageDetails {
        id
        name
        coverageType
        companyName
        familyName
        memberNumber
        provider {
          id
          name
        }
      }
    }
    receiverHospital {
      id
    }
    patientInformation {
      clinifyId
      fullName
      email
      phone
    }
    createdBy {
      id
      type
      clinifyId
    }
    creatorName
    lastModifierName
    creatorId
    createdDate
    updatedDate
    isWalkInBill
    virtualServicesPayment {
      id
      amountDue
      amountPaid
      commissionFeeAmount
      commissionFeePercentage
      commissionPayer
      controlledCommissionFee
      paymentStatus
      payoutStatus
      virtualBankAccount {
        id
        bank
        accountName
        accountNumber
      }
    }
  }
  ${ORG_BILL_DETAIL_FIELDS}
  ${FACILITY_BILLING_INFORMATION_FRAGMENT}
`;

export const BILL_WITHOUT_RAISED_BY = gql`
  fragment Bill on BillModel {
    id
    totalAmount
    unitPrice
    totalAccumulativePaid
    amountPayable
    amountPaid
    discountAmount
    vatAmount
    professionalFeeAmount
    invoiceNumber
    amountDue
    amountOwning
    amountUnderpaid
    amountOverpaid
    hospitalName
    hospitalAddress
    amountOutstanding
    reference
    serviceType
    description
    paymentStatus
    billStatus
    autoGenerated
    additionalNote
    raisedBy
    billingDateTime
    recalled
    collectedBy
    collectionDateTime
    details {
      ...BillDetail
    }
    senderProfile {
      id
    }
    senderHospital {
      id
    }
    receiverProfile {
      id
      clinifyId
      type
      fullName
    }
    receiverHospital {
      id
    }
    creatorName
    lastModifierName
    creatorId
    createdDate
    updatedDate
    virtualServicesPayment {
      id
      amountDue
      amountPaid
      commissionFeeAmount
      commissionFeePercentage
      commissionPayer
      controlledCommissionFee
      paymentStatus
      payoutStatus
      virtualBankAccount {
        id
        bank
        accountName
        accountNumber
      }
    }
  }
  ${ORG_BILL_DETAIL_FIELDS}
`;

export const PAY_BILL = gql`
  mutation PayBill($detail: PayBillInput!) {
    payBill(detail: $detail) {
      id
      billStatus
      amountPaid
      totalAmount
      amountPayable
      reversedAmount
      currency
      serviceType
      isDeleted
      paymentStatus
      commissionPercent
    }
  }
`;

export const SEND_BILL = gql`
  mutation SendBill($bill: CreateBillInput!) {
    sendBill(bill: $bill) {
      id
      amountPaid
      totalAmount
      amountPayable
      reversedAmount
      currency
      serviceType
      isDeleted
      paymentStatus
      commissionPercent
    }
  }
`;

export const GENERATE_BILL_REFRENCE = gql`
  mutation GenerateBillReference($referenceDetail: GenerateTransactionReferenceInput!) {
    generateTransactionReference(referenceDetail: $referenceDetail) {
      id
    }
  }
`;

export const GET_ALL_BILL = gql`
  query GetAllUserBills($filterOptions: BillFilter) {
    getAllUserBills(filter: $filterOptions) {
      totalCount
      list {
        ...BillFields
      }
    }
  }
  ${BILL_FIELDS}
`;

export const GET_ALL_WALLET_TRANSACTIONS = gql`
  query GetAllUserWalletTransactions($filterOptions: WalletTransactionFilter) {
    getUserWalletTransactions(filter: $filterOptions) {
      totalCount
      list {
        id
        createdDate
        amount
        transactionType
        transactionStatus
        transactionDetails
        currency
        amountSent
        description
        senderWallet {
          profile {
            clinifyId
            fullName
          }
        }
        receiverWallet {
          profile {
            clinifyId
            fullName
          }
        }
        currency
      }
    }
  }
`;

export const GET_WALLET_TRANSACTIONS = gql`
  query GetWalletTransaction($id: String!) {
    getWalletTransaction(transactionId: $id) {
      id
      createdDate
      amount
      transactionType
      transactionStatus
      transactionDetails
      currency
      amountSent
      description
      senderWallet {
        profile {
          id
          clinifyId
          fullName
        }
        hospital {
          id
          clinifyId
          name
        }
      }
      receiverWallet {
        profile {
          id
          clinifyId
          fullName
        }
        hospital {
          id
          clinifyId
          name
        }
      }
      senderBankDetails {
        accountNumber
        accountName
        bankName
      }
      currency
    }
  }
`;

export const ARCHIVE_WALLET_TRANSACTIONS = gql`
  mutation ArchiveWalletTransaction($ids: [String!]!, $archive: Boolean) {
    archiveWalletTransaction(ids: $ids, archive: $archive) {
      id
      createdDate
      amount
      transactionType
      transactionStatus
      transactionDetails
      currency
      amountSent
      description
      senderWallet {
        profile {
          clinifyId
          fullName
        }
      }
      receiverWallet {
        profile {
          clinifyId
          fullName
        }
      }
      currency
    }
  }
`;

export const GET_WALLET_BALANCE = gql`
  query GetwalletBalance {
    getWalletBalance {
      totalBalance
      availableBalance
    }
  }
`;

export const GET_CLINIFY_USER_WALLET_BALANCE = gql`
  query GetClinifyUserWalletBalance($patientClinifyId: String!) {
    getClinifyUserWalletBalance(patientClinifyId: $patientClinifyId) {
      totalBalance
      availableBalance
    }
  }
`;

export const GET_WALLET = gql`
  query Getwallet($patientClinifyId: String) {
    getWallet(patientClinifyId: $patientClinifyId) {
      id
      totalSent
      totalReceived
      currency
      balance
    }
  }
`;

export const GET_ALL_CARDS = gql`
  query GetAllCard {
    getAllCards {
      id
      expiryDate
      bank
      lastFourDigits
      isDefault
      brand
    }
  }
`;

export const CHARGE_CARD = gql`
  mutation ChargeCard($ChargeCardInput: ChargeCardInput!) {
    chargeCard(ChargeCardInput: $ChargeCardInput) {
      status
      message
    }
  }
`;

export const CREATE_PAYMENT = gql`
  mutation CreatePayment($transactionDetails: CreatePaymentInput!) {
    createPayment(transactionDetails: $transactionDetails) {
      id
      senderInitialBalance
      amount
      currency
      transactionType
      description
      transactionStatus
    }
  }
`;

export const UPDATE_BILL = gql`
  mutation UpdateBill($updateBillInput: UpdateBillInput!) {
    updateBill(update: $updateBillInput) {
      id
      totalAmount
      amountPayable
      discountAmount
    }
  }
`;

export const DELETE_BILL = gql`
  mutation DeleteBill($passCode: String!, $id: String!) {
    deleteBill(passCode: $passCode, id: $id) {
      id
      totalAmount
      amountPayable
      discountAmount
    }
  }
`;

export const DELETE_CARD = gql`
  mutation DeleteCard($card: DeleteCardInput!) {
    deleteCard(card: $card) {
      id
      lastFourDigits
      brand
    }
  }
`;

export const PAY_ORG_BILL = gql`
  mutation PayOrgBill($detail: PayBillInput!) {
    payOrgBill(detail: $detail) {
      ...BillFields
      ${UPDATED_BY}
    }
  }
  ${BILL_FIELDS}
`;

export const GET_ORG_BILL = gql`
  query getBill($id: String!, $clinifyId: String!) {
    getBill(id: $id, clinifyId: $clinifyId) {
      ...BillFields
      ${UPDATED_BY}
    }
  }
  ${BILL_FIELDS}
`;

export const GET_HOSPITAL_BILL_FOR_PATIENT = gql`
  query getHospitalBillForPatient($filterOptions: BillFilter) {
    hospital {
      id
      bills(filterOptions: $filterOptions) {
        totalCount
        list {
          ...BillFields
        }
      }
    }
  }
  ${BILL_FIELDS}
`;

export const ADD_ORG_BILL = gql`
  mutation addOrgBill(
    $input: addOrgBillInput!
    $wallet: ChargePatientWalletOrgBillInput
    $id: String
  ) {
    addOrgBill(bill: $input, wallet: $wallet, id: $id) {
      ...BillFields
    }
  }
  ${BILL_FIELDS}
`;

export const ADD_PATIENT_WALK_IN_BILL = gql`
  mutation AddWalkInHospitalBill($input: NewWalkInHospitalBillInput!) {
    addWalkInHospitalBill(input: $input) {
      ...BillFields
    }
  }
  ${BILL_FIELDS}
`;

export const UPDATE_ORG_BILL = gql`
  mutation updateOrgBill($input: UpdateOrgBillInput!, $id: String!) {
    updateOrgBill(UpdateOrgBillInput: $input, id: $id) {
      ...BillFields
      ${UPDATED_BY}
    }
  }
  ${BILL_FIELDS}
`;

export const ARCHIVE_ORG_BILL = gql`
  mutation archiveOrgBills($archive: Boolean!, $ids: [String!]!) {
    archiveOrgBills(archive: $archive, ids: $ids) {
      ...BillFields
    }
  }
  ${BILL_FIELDS}
`;

export const DELETE_ORG_BILL = gql`
  mutation deleteOrgBills($ids: [String!]!) {
    deleteOrgBills(ids: $ids) {
      id
    }
  }
`;

export const RECALL_ORG_BILL = gql`
  mutation recallOrgBill($recalled: Boolean, $id: String!) {
    recallOrgBill(recalled: $recalled, id: $id) {
      id
      recalled
      ${AUDIT_FIELDS_WITH_ID}
    }
  }
`;

export const CANCEL_ORG_BILL = gql`
  mutation cancelOrgBill($id: String!) {
    cancelOrgBill(id: $id) {
      id
      billStatus
    }
  }
`;

export const UNCANCEL_ORG_BILL = gql`
  mutation unCancelOrgBill($id: String!) {
    unCancelOrgBill(id: $id) {
      id
      billStatus
    }
  }
`;

/** Bill Details */

export const UPDATE_ORG_AUTO_BILL_DETAILS = gql`
  mutation updateBillDetail(
    $inputs: [BillDetailInput!]!
    $wallet: ChargePatientWalletOrgBillInput
    $id: String!
  ) {
    updateBillDetail(billDetails: $inputs, wallet: $wallet, billId: $id) {
      ...BillFields
      ${UPDATED_BY}
    }
  }
  ${BILL_FIELDS}
`;

export const CHARGE_PATIENT_WALLET = gql`
  mutation ChargePatientWallet($input: ChargePatientWalletInput!) {
    chargePatientWallet(input: $input) {
      id
      createdDate
      amount
      transactionType
      transactionStatus
      transactionDetails
      currency
      amountSent
      description
      currency
    }
  }
`;

export const EXCLUDE_BILL_ITEM = gql`
  mutation ExcludeBillDetail($billId: String!, $inputs: [excludeBillDetailInput!]!) {
    excludeBillDetail(billId: $billId, details: $inputs) {
      id
      details {
        id
        excluded
        subBills {
          id
        }
      }
    }
  }
`;

export const SEND_BILL_TO_PATIENT = gql`
  query SendBillToPatient($id: String!, $copyEmail: String, $origin: String!) {
    sendBillToPatient(id: $id, copyEmail: $copyEmail, origin: $origin) {
      sent
      email
      id
    }
  }
`;

export const SEND_RECEIPT_TO_PATIENT = gql`
  query SendReceiptToPatient(
    $id: String!
    $copyEmail: String
    $origin: String!
    $receiptSize: String!
  ) {
    sendReceiptToPatient(
      id: $id
      copyEmail: $copyEmail
      origin: $origin
      receiptSize: $receiptSize
    ) {
      sent
      email
      id
    }
  }
`;

export const COMPLETE_BILL_PAYMENT = gql`
  mutation CompleteBillPayment($id: String!) {
    completeBillPayment(id: $id) {
      ...BillFields
      ${UPDATED_BY}
    }
  }
  ${BILL_FIELDS}
`;
