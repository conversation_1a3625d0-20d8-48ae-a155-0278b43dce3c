import { gql } from '@apollo/client';

export const INVENTORY_SUPPLY_FIELDS = gql`
  fragment InventorySupply on InventorySupplyModel {
    id
    sn
    addedDateTime
    deliveredDateTime
    section
    supplier
    invoiceNumber
    name
    type
    size
    bedNumber
    ward
    group
    flag
    description
    strength
    category
    code
    batchNumber
    barcode
    bedAvailable
    expiryDate
    expiryStatus
    damagedCount
    markup
    unitCost
    unitSellingPrice
    totalCost
    totalSale
    quantityRemaining
    quantityPurchased
    quantityAvailable
    quantityDispensed
    quantitySold
    manufacturer
    recievedDateTime
    reorderLevel
    receivedBy
    addedBy
    purchasedBy
    vin
    plateNumber
    year
    model
    colour
    status
    comments
    images
    addedDateTime
    creatorId
    createdDate
    creatorName
    quantityOrdered
    canceled
    orderStatus
    quantityDelivered
    deliveredBy
    hospital {
      name
    }
    supplyTo {
      name
    }
    order {
      id
      creatorName
      createdDate
    }
  }
`;

export const GET_INVENTORY_SUPPLY_LIST = gql`
  query GetHospitalInventorySupplyList($filterOptions: InventoryOrderFilterInput) {
    hospital {
      id
      inventorySupplies(filter: $filterOptions) {
        totalCount
        list {
          ...InventorySupply
        }
      }
    }
  }
  ${INVENTORY_SUPPLY_FIELDS}
`;

export const UPDATE_INVENTORY_SUPPLY = gql`
  mutation UpdateInventorySupply($id: String!, $input: InventorySupplyInput!) {
    updateInventorySupply(id: $id, input: $input) {
      ...InventorySupply
    }
  }
  ${INVENTORY_SUPPLY_FIELDS}
`;

export const UPDATE_INVENTORY_SUPPLY_STATUS = gql`
  mutation UpdateInventorySupplyStatus($id: String!, $status: InventorySupplyStatus!) {
    updateInventorySupplyStatus(id: $id, status: $status) {
      ...InventorySupply
    }
  }
  ${INVENTORY_SUPPLY_FIELDS}
`;

export const INVENTORY_SUPPLY_ADDED_SUBS = gql`
  subscription InventorySupplyAddedSubs($hospitalId: String!) {
    InventorySupplyAdded(hospitalId: $hospitalId) {
      ...InventorySupply
    }
  }
  ${INVENTORY_SUPPLY_FIELDS}
`;

export const INVENTORY_SUPPLY_UPDATED_SUBS = gql`
  subscription InventorySupplyUpdatedSubs($hospitalId: String!) {
    InventorySupplyUpdated(hospitalId: $hospitalId) {
      ...InventorySupply
    }
  }
  ${INVENTORY_SUPPLY_FIELDS}
`;
