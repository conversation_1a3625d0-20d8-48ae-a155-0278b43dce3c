import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from 'apollo-queries/fragments/audit';

const WALK_IN_REFERRAL_FIELDS = gql`
  fragment WalkInReferral on WalkInReferralModel {
    id
    referralDateTime
    referredBy
    referralReason
    concealReferralReason
    referralFacilityName
    referralFacilityAddress
    documentUrl
    patientInformation {
      clinifyId
      fullName
      email
      phone
    }
    ${AUDIT_FIELDS}
  }
`;

export const GET_HOSPITAL_WALK_IN_REFERRALS = gql`
  query GetHospitalReferralList($filterOptions: WalkInFilterInput, $hospitalId: String) {
    hospital(hospitalId: $hospitalId) {
      id
      walkInReferrals(filterOptions: $filterOptions) {
        totalCount
        list {
          ...WalkInReferral
        }
      }
    }
  }
  ${WALK_IN_REFERRAL_FIELDS}
`;

export const ADD_WALK_IN_REFERRAL = gql`
  mutation AddWalkInReferral($input: NewWalkInReferralInput!) {
    addWalkInReferral(input: $input) {
      ...WalkInReferral
      ${AUDIT_FIELDS}
    }
  }
  ${WALK_IN_REFERRAL_FIELDS}
`;

export const GET_WALK_IN_REFERRAL = gql`
  query GetWalkInReferral($id: String!) {
    walkInReferral(id: $id) {
      ...WalkInReferral
      ${AUDIT_FIELDS}
    }
  }
  ${WALK_IN_REFERRAL_FIELDS}
`;

export const UPDATE_WALK_IN_REFERRAL = gql`
  mutation UpdateWalkInReferral($input: WalkInReferralInput!, $id: String!) {
    updateWalkInReferral(input: $input, id: $id) {
      ...WalkInReferral
      ${AUDIT_FIELDS}
    }
  }
  ${WALK_IN_REFERRAL_FIELDS}
`;

export const DELETE_WALK_IN_REFERRALS = gql`
  mutation DeleteWalkInReferrals($ids: [String!]!) {
    deleteWalkInReferrals(ids: $ids) {
      id
    }
  }
`;

export const ARCHIVE_WALK_IN_REFERRALS = gql`
  mutation ArchiveWalkInReferrals($ids: [String!]!, $archive: Boolean) {
    archiveWalkInReferrals(ids: $ids, archive: $archive) {
      ...WalkInReferral
      ${AUDIT_FIELDS}
    }
  }
  ${WALK_IN_REFERRAL_FIELDS}
`;

export const CONCEAL_REFERRAL_REASON = gql`
  mutation ConcealReferralReason($id: String!, $concealStatus: Boolean!) {
    concealReferralReason(id: $id, concealStatus: $concealStatus) {
      id
      referralReason
      concealReferralReason
    }
  }
`;

export const WALK_IN_REFERRAL_ADDED_SUBS = gql`
  subscription WalkInReferralAddedSubs($hospitalId: String!) {
    WalkInReferralAdded(hospitalId: $hospitalId) {
      ...WalkInReferral
      ${AUDIT_FIELDS}
    }
  }
  ${WALK_IN_REFERRAL_FIELDS}
`;

export const WALK_IN_REFERRAL_UPDATED_SUBS = gql`
  subscription WalkInReferralUpdatedSubs($hospitalId: String!) {
    WalkInReferralUpdated(hospitalId: $hospitalId) {
      ...WalkInReferral
      ${AUDIT_FIELDS}
    }
  }
  ${WALK_IN_REFERRAL_FIELDS}
`;

export const DELETE_WALK_IN_REFERRAL_SUBS = gql`
  subscription DeleteWalkInReferralSubs($hospitalId: String!) {
    WalkInReferralRemoved(hospitalId: $hospitalId) {
      id
    }
  }
`;

export const ARCHIVE_WALK_IN_REFERRAL_SUBS = gql`
  subscription ArchiveWalkInReferralSubs($hospitalId: String!) {
    WalkInReferralArchived(hospitalId: $hospitalId) {
      ...WalkInReferral
    }
  }
  ${WALK_IN_REFERRAL_FIELDS}
`;

export const UNARCHIVE_WALK_IN_REFERRAL_SUBS = gql`
  subscription UnarchiveWalkInReferralSubs($hospitalId: String!) {
    WalkInReferralUnarchived(hospitalId: $hospitalId) {
      ...WalkInReferral
    }
  }
  ${WALK_IN_REFERRAL_FIELDS}
`;
