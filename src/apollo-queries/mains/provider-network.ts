import { gql } from '@apollo/client';
import { PERSONAL_INFORMATION_FIELDS } from './user';

export const GRANT_ORGANIZATION_PERMISSION_FOR_FACILITY = gql`
  mutation GrantOrganizationPermissionForFacility(
    $input: GrantOrganizationPermissionInput!
    $hospitalId: String!
  ) {
    grantOrganizationPermissionForFacility(
      grantOrganizationPermissionInput: $input
      hospitalId: $hospitalId
    ) {
      id
      rules {
        subject
        action
      }
    }
  }
`;

export const REVOKE_ORGANIZATION_PERMISSION_FOR_FACILITY = gql`
  mutation RevokeOrganizationPermissionForFacility(
    $hospitalId: String!
    $input: RevokeOrganizationPermissionInput!
  ) {
    revokeOrganizationPermissionForFacility(
      hospitalId: $hospitalId
      revokeOrganizationPermissionInput: $input
    ) {
      id
      rules {
        subject
        action
      }
    }
  }
`;

export const FETCH_PROVIDER_STAFFS = gql`
  query FetchProviderStaffs($hospitalId: String!, $input: ProfilesFilterInputs!) {
    fetchProviderStaffs(hospitalId: $hospitalId, input: $input) {
      totalCount
      list {
        id
        clinifyId
        fullName
        active
        type
        typeAlias
        title
        gender
        user {
          id
          email
          corporatePhoneNumber
          country
        }
        permissions {
          id
          rules {
            subject
            action
            conditions {
              organizationId
            }
          }
        }
        personalInformation {
          ...PersonalInformationFields
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
`;

export const FETCH_PROVIDER_USER_TYPES = gql`
  query FetchProviderUserTypes($hospitalId: String!) {
    fetchProviderUserTypes(hospitalId: $hospitalId)
  }
`;

export const BULK_CREATE_AND_ASSIGN_TO_HOSPITAL_FOR_FACILITY = gql`
  mutation BulkCreateAndAssignToHospitalForFacility(
    $createAndAssignToHospitalInput: [CreateAndAssignToHospitalInput!]!
    $hospitalId: String!
  ) {
    bulkCreateAndAssignToHospitalForFacility(
      createAndAssignToHospitalInput: $createAndAssignToHospitalInput
      hospitalId: $hospitalId
    ) {
      id
      clinifyId
      active
      fullName
      type
      gender
      personalInformation {
        ...PersonalInformationFields
      }
      user {
        id
        email
        corporatePhoneNumber
        country
      }
      active
      permissions {
        id
        rules {
          subject
          action
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
`;

export const ACTIVATE_HOSPITAL_STAFF_FOR_FACILITY = gql`
  mutation ActivateHospitalStaffForFacility(
    $active: Boolean
    $hospitalId: String!
    $profileIds: [String!]!
  ) {
    activateHospitalStaffForFacility(
      active: $active
      hospitalId: $hospitalId
      profileIds: $profileIds
    ) {
      id
      clinifyId
      active
      fullName
      type
      gender
      personalInformation {
        ...PersonalInformationFields
      }
      user {
        id
        email
        corporatePhoneNumber
        country
      }
      active
      permissions {
        id
        rules {
          subject
          action
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
`;

export const DELETE_HOSPITAL_STAFF_FOR_FACILITY = gql`
  mutation DeleteHospitalStaffForFacility($hospitalId: String!, $profileIds: [String!]!) {
    deleteHospitalStaffForFacility(hospitalId: $hospitalId, profileIds: $profileIds) {
      id
      clinifyId
      active
      fullName
      type
      personalInformation {
        ...PersonalInformationFields
      }
      user {
        id
        email
        corporatePhoneNumber
        country
      }
      permissions {
        id
        rules {
          subject
          action
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
`;
