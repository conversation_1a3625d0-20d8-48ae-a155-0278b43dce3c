import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from 'apollo-queries/fragments/audit';
import { PREAUTHORIZATION_DETAILS_FRAGMENT } from '../fragments/preauthorization-details';

export const ANTENATAL_DETAIL_FIELD = gql`
  fragment AntenatalDetail on AntenatalDetailsModel {
    createdDate
    updatedDate
    lastModifierId
    lastModifierName
    creatorId
    creatorName
    id
    priority
    category
    gravidity
    clinicalDiagnosis {
      diagnosisICD10
      diagnosisICD11
      diagnosisSNOMED
    }
    parity
    visitationDateTime
    position
    presentation
    specifyBreechPresentation
    fetalMovement
    gestationAge
    fetalHeartRate
    bloodPressure
    weight
    weightUnit
    symphysioHeight
    heightUnit
    visitationNote
    lie
    oedama
    specifyOedama
    concealVisitationNote
    treatmentPlan
    concealTreatmentPlan
    grade
    seenBy
    specialty
    rank
    department
    itemId
    provider
    estimatedDateOfDelivery
    lastMenstrualPeriod
    counselledFGM
    counselledMaternalNutrition
    counselledFamilyPlanning
    counselledLabourBirthPreparedness
    counselledEarlyInitiationBreastFeeding
    counselledExclusiveBreastFeeding
    counselledPostpartumFamilyPlanning
    givenLlin
    outcomeOfVisit
    ancHivIAndIiScreening
    hiv
    ancHepatitisBScreening
    hbvAntibody
    ancHepatitisCScreening
    hcvAntibody
    ancGenotype
    genotype
    ancVdrl
    vdrl
    ancPcvAndHb
    invPcv
    invPcvRange
    invHb
    invHbRange
    ancRandomBloodSugar
    randomBloodSugar
    randomBloodSugarUnit
    randomBloodSugarRange
    ancFastingBloodSugar
    fastingBloodSugar
    fastingBloodSugarUnit
    fastingBloodSugarRange
    ancBloodGrouping
    bloodGrouping
    bloodGroupingRange
    rhesusFactor
    rhesusFactorRange
    ancUrinalysis
    colour
    appearanceClarity
    ph
    specificGravity
    glucose
    ketones
    blood
    protein
    bilirubin
    uribilinogen
    nitrites
    leukocyteEsterase
    rbcs
    wbcs
    epithelialCells
    casts
    crystals
    bacteria
    yeast
    ipt1
    ipt1Ega
    ipt2
    ipt2Ega
    ipt3
    ipt3Ega
    ipt4
    ipt4Ega
    hematinics
    hematinicsEga
    albendazole
    albendazoleEga
    tetanusToxoid1   
    tetanusToid1Ega   
    tetanusToxoid2   
    tetanusToid2Ega   
    tetanusToxoid3   
    tetanusToid3Ega
    tetanusToxoid4
    tetanusToid4Ega
    tetanusToxoid5
    tetanusToid5Ega
    preauthorizationDetails {
      ...PreauthorizationDetails
    }
    ${AUDIT_FIELDS}
  }
  ${PREAUTHORIZATION_DETAILS_FRAGMENT}
`;

export const ANTENATAL_FIELDS = gql`
  fragment Antenatal on AntenatalModel {
    id
    createdDate
    updatedDate
    lastModifierId
    lastModifierName
    creatorId
    creatorName
    concealAdditionalNote
    documentUrl
    hospitalId
    hospitalName
    hospitalAddress
    additionalNote
    appointmentId
    hmoProviderId
    serviceDetails {
      priceId
      type
      name
      quantity
      pricePerUnit
      itemId
      paymentType
      patientType
      reference
    }
    isPackage
    details {
      ...AntenatalDetail
    }
    profileId
    profile {
      id
      clinifyId
      coverageDetails {
        id
        name
        coverageType
        companyName
        familyName
        memberNumber
        provider {
          id
          name
        }
      }
    }
    billStatus
    ${AUDIT_FIELDS}
  }
  ${ANTENATAL_DETAIL_FIELD}
`;

export const ANTENATAL_LINKING_FIELDS = gql`
  fragment AntenatalLinking on AntenatalModel {
    medications {
      id
    }
    vitals {
      id
    }
    investigations {
      id
    }
    labTests {
      id
    }
    radiology {
      id
    }
    nursingServices {
      id
    }
  }
`;

export const GET_PATIENT_ANTENENATAL_LIST = gql`
  query GetPatientAntenatalList($filterOptions: AntenatalFilterOptions, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      antenatals(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Antenatal
          billing {
            id
            createdDate
            billId
          }
          billStatus
        }
      }
    }
  }
  ${ANTENATAL_FIELDS}
`;

export const GET_PATIENT_ANTENATAL = gql`
  query GetPatientAntenatal($clinifyId: String!, $id: String!) {
    antenatal(id: $id, clinifyId: $clinifyId) {
      ...Antenatal
      ...AntenatalLinking
    }
  }
  ${ANTENATAL_FIELDS}
  ${ANTENATAL_LINKING_FIELDS}
`;

export const ADD_PATIENT_ANTENATAL = gql`
  mutation AddPatientAntenatal($id: String, $input: NewAntenatalInput!, $pin: String ) {
    addAntenatal(antenatal: $input, id: $id, pin: $pin) {
      ...Antenatal
      ${AUDIT_FIELDS}
    }
  }
  ${ANTENATAL_FIELDS}
`;

export const ANTENATAL_ADDED_SUBS = gql`
  subscription AntenatalAddedSubs($profileId: String!, $hospitalId: String!) {
    AntenatalAdded(profileId: $profileId, hospitalId: $hospitalId) {
      ...Antenatal
    }
  }
  ${ANTENATAL_FIELDS}
`;

export const UPDATE_PATIENT_ANTENATAL = gql`
  mutation UpdatePatientAntenatal($id: String!, $input: AntenatalInput!, $pin: String){
    updateAntenatal(id: $id, antenatal: $input, pin: $pin){
      ...Antenatal
      ...AntenatalLinking
      ${AUDIT_FIELDS}
    }
  }
  ${ANTENATAL_FIELDS}
  ${ANTENATAL_LINKING_FIELDS}
`;

export const UPDATE_PATIENT_ANTENATAL_BILL = gql`
  mutation UpdatePatientAntenatalBill($id: String!, $input: AntenatalInput!){
    updateAntenatalBill(id: $id, antenatal: $input){
      ...Antenatal
      ...AntenatalLinking
      ${AUDIT_FIELDS}
    }
  }
  ${ANTENATAL_FIELDS}
  ${ANTENATAL_LINKING_FIELDS}
`;

export const ANTENATAL_UPDATED_SUBS = gql`
  subscription AntenatalUpdatedSubs($profileId: String!, $hospitalId: String!) {
    AntenatalUpdated(profileId: $profileId, hospitalId: $hospitalId) {
      ...Antenatal
      ...AntenatalLinking
      ${AUDIT_FIELDS}
    }
  }
  ${ANTENATAL_FIELDS}
  ${ANTENATAL_LINKING_FIELDS}
`;

export const ARCHIVE_PATIENT_ANTENATAL = gql`
  mutation ArchivePatientAntenatal($archive: Boolean, $ids: [String!]!, $clinifyId: String!) {
    archiveAntenatals(ids: $ids, archive: $archive, clinifyId: $clinifyId){
      ...Antenatal
      ...AntenatalLinking
      ${AUDIT_FIELDS}
    }
  }
  ${ANTENATAL_FIELDS}
  ${ANTENATAL_LINKING_FIELDS}
`;

export const ARCHIVE_ANTENATAL_SUBS = gql`
  subscription ArchiveAntenatalSubs($profileId: String!, $hospitalId: String!) {
    AntenatalArchived(profileId: $profileId, hospitalId: $hospitalId) {
      ...Antenatal
      ...AntenatalLinking
    }
  }
  ${ANTENATAL_FIELDS}
  ${ANTENATAL_LINKING_FIELDS}
`;

export const UNARCHIVE_ANTENATAL_SUBS = gql`
  subscription UnarchiveAntenatalSubs($profileId: String!, $hospitalId: String!) {
    AntenatalUnarchived(profileId: $profileId, hospitalId: $hospitalId) {
      ...Antenatal
      ...AntenatalLinking
    }
  }
  ${ANTENATAL_FIELDS}
  ${ANTENATAL_LINKING_FIELDS}
`;

export const DELETE_PATIENT_ANTENATAL = gql`
  mutation DeletePatientAntenatal($ids: [String!]!, $clinifyId: String!) {
    deleteAntenatals(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const DELETE_ANTENATAL_SUBS = gql`
  subscription DeleteAntenatalSubs($hospitalId: String!, $profileId: String!) {
    AntenatalRemoved(profileId: $profileId, hospitalId: $hospitalId) {
      id
    }
  }
`;

export const CONCEAL_ANTENATAL_ADDITIONAL_NOTE = gql`
  mutation ConcealAntenatalAdditionalNote($id: String!, $concealStatus: Boolean!) {
    concealAntenatalAdditionalNote(antenatalId: $id, concealState: $concealStatus) {
      id
      concealAdditionalNote
      additionalNote
    }
  }
`;

// --> Antenatal Detail <--

export const SAVE_ANTENATAL_DETAIL = gql`
  mutation SaveAntenatlDetail(
    $input: AntenatalDetailsInput!
    $id: String!
    $clinifyId: String!
    $pin: String
  ) {
    saveAntenatalDetail(input: $input, antenatalId: $id, clinifyId: $clinifyId, pin: $pin) {
      ...AntenatalDetail
      antenatal {
        id
        serviceDetails {
          priceId
          type
          name
          quantity
          pricePerUnit
          itemId
          paymentType
          patientType
          reference
        }
      }
    }
  }
  ${ANTENATAL_DETAIL_FIELD}
`;

export const ADD_ANTENATAL_DETAIL_SUB = gql`
  subscription AntenatalDetailAdded($profileId: String!) {
    AntenatalDetailAdded(profileId: $profileId) {
      ...AntenatalDetail
      antenatalId
    }
  }
  ${ANTENATAL_DETAIL_FIELD}
`;

export const UPDATE_ANTENATAL_DETAIL = gql`
  mutation UpdateAntenatalDetail(
    $clinifyId: String!
    $id: String!
    $input: AntenatalDetailsInput!
    $pin: String
  ) {
    updateAntenatalDetail(antenatalDetailId: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...AntenatalDetail
    }
  }
  ${ANTENATAL_DETAIL_FIELD}
`;

export const UPDATE_ANTENATAL_DETAIL_SUB = gql`
  subscription AntenatalDetailUpdated($profileId: String!) {
    AntenatalDetailUpdated(profileId: $profileId) {
      ...AntenatalDetail
      antenatalId
    }
  }
  ${ANTENATAL_DETAIL_FIELD}
`;

export const DELETE_ANTENATAL_DETAIL = gql`
  mutation DeleteAntenatalDetail($clinifyId: String!, $id: String!) {
    deleteAntenatalDetail(clinifyId: $clinifyId, id: $id) {
      id
    }
  }
`;

export const ANTENATAL_DETAILS_REMOVED_SUB = gql`
  subscription AntenatalDetailRemoved($profileId: String!) {
    AntenatalDetailRemoved(profileId: $profileId) {
      id
      antenatalId
    }
  }
`;

export const CONCEAL_VISITATION_NOTE = gql`
  mutation ConcealVisitationNote($id: String!, $concealStatus: Boolean!) {
    concealVisitationNote(antenatalDetailsId: $id, concealState: $concealStatus) {
      id
      concealVisitationNote
      visitationNote
    }
  }
`;

export const CONCEAL_ANTENATAL_TREATMENT_PLAN = gql`
  mutation ConcealAntenatalTreatmentPlan($id: String!, $concealStatus: Boolean!) {
    concealAntenatalTreatmentPlan(antenatalDetailsId: $id, concealState: $concealStatus) {
      id
      treatmentPlan
      concealTreatmentPlan
    }
  }
`;

export const LINK_RECORDS_TO_ANTENATAL = gql`
    mutation LinkRecordsToAntenatal($parentId: String!, $recordIds: [String!]!, $recordType: AntenatalLinkedRecordType!) {
        linkRecordsToAntenatal(id: $parentId, recordIds: $recordIds, recordType: $recordType) {
            ...Antenatal
            ...AntenatalLinking
            ${AUDIT_FIELDS}
        }
    }
    ${ANTENATAL_FIELDS}
    ${ANTENATAL_LINKING_FIELDS}
`;

// -----> Partograph <------

export const PARTOGRAPH_FIELD = gql`
  fragment Partograph on PartographModel {
    id
    notes
    fhr {
      readingDate
      value
      time
    }
    amnoiticFluid {
      readingDate
      value
      moulding
      time
    }
    cervixDecent {
      readingDate
      cervix
      decent
      period
      time
    }
    contraction {
      readingDate
      value
      duration
      time
    }
    drugIv {
      readingDate
      drugName
      time
    }
    oxytocin {
      readingDate
      value
      drops
      time
    }
    pulseBP {
      readingDate
      pulse
      bpSystolic
      bpDiastolic
      time
    }
    temperature {
      readingDate
      readingDate
      value
      time
      unit
    }
    urine {
      readingDate
      protein
      acetone
      volume
      time
    }
    profileId
    profile {
      id
      clinifyId
    }
    notes
    facilityName
    facilityAddress
    ${AUDIT_FIELDS}
  } 
`;

export const GET_PATIENT_PARTOGRAPH_LIST = gql`
  query GetPatientPartographList($filterOptions: AntenatalFilterOptions, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      partographs(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Partograph
        }
      }
    }
  }
  ${PARTOGRAPH_FIELD}
`;

export const GET_PATIENT_PARTOGRAPH = gql`
  query GetPatientPartograph($clinifyId: String!, $id: String!) {
    partograph(id: $id, clinifyId: $clinifyId) {
      ...Partograph
    }
  }
  ${PARTOGRAPH_FIELD}
`;

export const ADD_PATIENT_PARTOGRAPH = gql`
  mutation AddPatientPartograph($id: String, $input: PartographInput!, $pin: String) {
    addPartograph(id: $id, partograph: $input, pin: $pin) {
      ...Partograph
    }
  }
  ${PARTOGRAPH_FIELD}
`;

export const PARTOGRAPH_ADDED_SUBS = gql`
  subscription PartographAddedSubs($profileId: String!) {
    PartographAdded(profileId: $profileId) {
      ...Partograph
    }
  }
  ${PARTOGRAPH_FIELD}
`;

export const UPDATE_PATIENT_PARTOGRAPH = gql`
  mutation UpdatePatientPartograph($id: String!, $input: PartographInput!, $pin: String) {
    updatePartograph(id: $id, partograph: $input, pin: $pin) {
      ...Partograph
    }
  }
  ${PARTOGRAPH_FIELD}
`;

export const PARTOGRAPH_UPDATED_SUBS = gql`
  subscription PartographUpdatedSubs($profileId: String!) {
    PartographUpdated(profileId: $profileId) {
      ...Partograph
    }
  }
  ${PARTOGRAPH_FIELD}
`;

export const DELETE_PATIENT_PARTOGRAPH = gql`
  mutation DeletePatientPartograph($ids: [String!]!, $clinifyId: String!) {
    deletePartographs(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const DELETE_PARTOGRAPH_SUBS = gql`
  subscription DeletePartographSubs($profileId: String!) {
    PartographRemoved(profileId: $profileId) {
      id
    }
  }
`;

export const GET_ANTENATAL_CLAIM = gql`
  query AntenatalClaim($id: String!, $clinifyId: String!) {
    antenatal(id: $id, clinifyId: $clinifyId) {
      id
      hmoClaim {
        id
      }
    }
  }
`;
