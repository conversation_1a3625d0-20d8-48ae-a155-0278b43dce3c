import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';
import { PREAUTHORIZATION_DETAILS_FRAGMENT } from '../fragments/preauthorization-details';

export const OPERATION_NOTES_FIELDS = gql`
  fragment OperationNotes on OperationNoteModel {
      id
      operationNote
      postOperationNote
      conceal
      ${AUDIT_FIELDS}
  }
`;

export const SURGERY_FIELDS = gql`
  fragment Surgery on SurgeryModel {
    id
    surgeryDate
    duration
    patientConsentSignature
    patientConsentSignatureType
    patientConsentSignatureDateTime
    procedureType {
      ref
      type
      priority
      provider
      itemId
      preauthorizationDetailsId
    }
    rank
    reason
    assistantSurgeon
    requestedBy
    specialty
    facilityName
    facilityAddress
    operatedBy
    patientConsent
    hmoProviderId
    serviceDetails {
      priceId
      type
      name
      quantity
      pricePerUnit
      itemId
      patientType
      paymentType
      reference
    }
    isPackage
    operatingRoomNurse
    anesthetistName
    anesthesia
    visitingSpecialistName
    visitingFacilityName
    documentUrl
    surgeryStartDate
    surgeryEndDate
    appointmentId
    createdDate
    updatedDate
    department
    billStatus
    hospitalId
    profileId
    profile {
      id
      clinifyId
      coverageDetails {
        id
        name
        coverageType
        companyName
        familyName
        memberNumber
        provider {
          id
          name
        }
      }
    }
    preauthorizationDetails {
      ...PreauthorizationDetails
    }
  }
  ${PREAUTHORIZATION_DETAILS_FRAGMENT}
`;

export const SURGERY_LINKING_FIELDS = gql`
  fragment SurgeryLinking on SurgeryModel {
    medications {
      id
    }
    vitals {
      id
    }
    investigations {
      id
    }
    labTests {
      id
    }
    radiology {
      id
    }
    nursingServices {
      id
    }
  }
`;

export const ADD_PATIENT_SURGERY = gql`
  mutation AddPatientSurgery(
    $input: NewSurgeryInput!
    $id: String
    $operationNoteId: String
    $pin: String
  ) {
    addSurgery(surgery: $input, id: $id, operationNoteId: $operationNoteId, pin: $pin) {
      ...Surgery
      bill {
        id
        billStatus
        createdDate
      }
      billStatus
    }
  }
  ${SURGERY_FIELDS}
`;

export const ADD_OPERATION_NOTE = gql`
  mutation AddOperationNote(
    $input: OperationNoteInput!
    $id: String!
    $clinifyId: String!
    $pin: String
  ) {
    addOperationNote(consultationId: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...OperationNotes
    }
  }
  ${OPERATION_NOTES_FIELDS}
`;

export const UPDATE_OPERATION_NOTE = gql`
  mutation UpdateOperationNote(
    $input: OperationNoteInput!
    $id: String!
    $clinifyId: String!
    $pin: String
  ) {
    updateOperationNote(id: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...OperationNotes
    }
  }
  ${OPERATION_NOTES_FIELDS}
`;

export const OPERATION_NOTES_ADDED_SUBS = gql`
  subscription OperationNoteAddedSubs($profileId: String!) {
    ProcedureOperationNoteAdded(profileId: $profileId) {
      ...OperationNotes
      surgeryId
      ${AUDIT_FIELDS}
    }
  }
  ${OPERATION_NOTES_FIELDS}
`;

export const OPERATION_NOTES_UPDATED_SUBS = gql`
  subscription OperationNoteUpdatedSubs($profileId: String!) {
    ProcedureOperationNoteUpdated(profileId: $profileId) {
      ...OperationNotes
      ${AUDIT_FIELDS}
    }
  }
  ${OPERATION_NOTES_FIELDS}
`;

export const DELETE_OPERATION_NOTES_SUBS = gql`
  subscription DeleteOperationNoteSubs($profileId: String!) {
    ProcedureOperationNoteRemoved(profileId: $profileId) {
      id
      surgeryId
    }
  }
`;

export const DELETE_OPERATION_NOTE = gql`
  mutation deleteOperationNote($id: String!, $clinifyId: String!) {
    deleteOperationNote(investigationId: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const UPDATE_SURGERY = gql`
  mutation UpdateSurgery($input: SurgeryInput!, $id: String!, $pin: String) {
    updateSurgery(surgery: $input, id: $id, pin: $pin) {
      ...Surgery
      ...SurgeryLinking
      operationNotes {
        ...OperationNotes
      }
      ${AUDIT_FIELDS}
    }
  }
  ${SURGERY_FIELDS}
  ${OPERATION_NOTES_FIELDS}
  ${SURGERY_LINKING_FIELDS}
`;

export const UPDATE_SURGERY_BILL = gql`
  mutation UpdateSurgeryBill($input: SurgeryInput!, $id: String!) {
    updateSurgeryBill(surgery: $input, id: $id) {
      ...Surgery
      ...SurgeryLinking
      operationNotes {
        ...OperationNotes
      }
      ${AUDIT_FIELDS}
    }
  }
  ${SURGERY_FIELDS}
  ${OPERATION_NOTES_FIELDS}
  ${SURGERY_LINKING_FIELDS}
`;

export const DELETE_SURGERY = gql`
  mutation DeleteSurgeries($ids: [String!]!, $clinifyId: String!) {
    deleteSurgeries(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const ARCHIVE_SURGERY = gql`
  mutation ArchiveSurgery($ids: [String!]!, $archive: Boolean, $clinifyId: String!) {
    archiveSurgeries(ids: $ids, archive: $archive, clinifyId: $clinifyId) {
      ...Surgery
      ...SurgeryLinking
    }
  }
  ${SURGERY_FIELDS}
  ${SURGERY_LINKING_FIELDS}
`;

export const GET_PATIENT_SURGERY_LIST = gql`
  query GetPatientSurgeryList($filterOptions: SurgeryFilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      surgeries(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Surgery
          bill {
            id
            billStatus
            createdDate
          }
          billStatus
        }
      }
    }
  }
  ${SURGERY_FIELDS}
`;

export const GET_SURGERY = gql`
  query GetSurgery($id: String!, $clinifyId: String!) {
    surgery(id: $id, clinifyId: $clinifyId) {
      ...Surgery
      ...SurgeryLinking
      operationNotes {
        ...OperationNotes
      }
      ${AUDIT_FIELDS}
    }
  }
  ${OPERATION_NOTES_FIELDS}
  ${SURGERY_FIELDS}
  ${SURGERY_LINKING_FIELDS}
`;

export const SURGERY_ADDED_SUBS = gql`
  subscription ProcedureAddedSubs($profileId: String!, $hospitalId: String!) {
    ProcedureAdded(profileId: $profileId, hospitalId: $hospitalId) {
      ...Surgery
      bill {
        id
        billStatus
        createdDate
      }
      billStatus
      ${AUDIT_FIELDS}
    }
  }
  ${SURGERY_FIELDS}
`;

export const SURGERY_UPDATED_SUBS = gql`
  subscription ProcedureUpdatedSubs($profileId: String!) {
    ProcedureUpdated(profileId: $profileId) {
      ...Surgery
      ${AUDIT_FIELDS}
    }
  }
  ${SURGERY_FIELDS}
`;

export const DELETE_SURGERY_SUBS = gql`
  subscription DeleteProcedureSubs($profileId: String!) {
    ProcedureRemoved(profileId: $profileId) {
      id
    }
  }
`;

export const ARCHIVE_SURGERY_SUBS = gql`
  subscription ArchiveProcedureSubs($profileId: String!) {
    ProcedureArchived(profileId: $profileId) {
      ...Surgery
    }
  }
  ${SURGERY_FIELDS}
`;

export const UNARCHIVE_SURGERY_SUBS = gql`
  subscription UnarchiveProcedureSubs($profileId: String!) {
    ProcedureUnarchived(profileId: $profileId) {
      ...Surgery
    }
  }
  ${SURGERY_FIELDS}
`;

export const CONCEAL_OPERATION_NOTE = gql`
  mutation ConcealOperationNote($id: String!, $concealStatus: Boolean!) {
    concealOperationNote(operationNoteId: $id, concealState: $concealStatus) {
      ...OperationNotes
        ${AUDIT_FIELDS}
    }
  }
  ${OPERATION_NOTES_FIELDS}
`;

export const LINK_RECORDS_TO_SURGERY = gql`
   mutation LinkRecordsToSurgery($parentId: String!, $recordIds: [String!]!, $recordType: SurgeryLinkedRecordType!) {
      linkRecordsToSurgery(id: $parentId, recordIds: $recordIds, recordType: $recordType) {
            ...Surgery
            ...SurgeryLinking
            ${AUDIT_FIELDS}
        }
    }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
    ${SURGERY_FIELDS}
    ${SURGERY_LINKING_FIELDS}
`;

export const PRE_OPERATION_CHECKLIST_FIELD = gql`
  fragment PreOperationChecklist on PreOperationChecklistModel {
    id
    operationDate
    preSurgeryReadiness
    completeConsent
    bloodProductConent
    alternateForm
    historyAvailable
    allergiesMarked
    bpmhComplete
    vitalsComplete
    heightWeightDoc
    anaestheticHistory
    completeConsult
    aroDocument
    interpreterNeeded
    fallsRisk
    bariatricRequired
    vteProphylaxis
    presurgicalScrub
    patientPreparation
    idBand
    facilityDetails
    allergyBand
    tsinBand
    preprocedureCollected
    implants
    belongingsDocumented
    cbap
    verifiedSurgicalSite
    nutritionStatusDoc
    preoperationMedications
    orStaffReviewed
    surgicalSafetyChecklist
    patientConfirmation
    isSiteMarked
    anaesthesiaMedicationCheck
    pulseOximeterFunctioning
    knownAllergy
    difficultAirway
    riskOfBloodLoss
    teamMembersIntroduced
    pantientNameAndProcedure
    antibioticBeenGiven
    criticalSteps
    casePeriod
    anticipatedBloodLoss
    patientSpecificConcern
    specifyPatientConcern
    sterilityConfirmed
    equipmentIssues
    specifyEquipmentIssues
    essentialImagingDisplayed
    confirmedProcedureName
    confirmSpecimensLabeled
    confirmInstrumentCount
    equipmentProblemsToAddress
    recoveryKeyConcerns
    urinaryOutputAmount
    urinaryOutputAmountUnit
    catheterDateTime
    denturePlateRemoved
    contactLensRemoved
    prosthesisRemoved
    jewelryRemoved
    shavedSurgicalSite
    lastMealDateTime
    bowelEmptied
    preoperationMedicationDateTime
    medicationProfileAvailable
    identifiedPatient
    patientCaseFileAvailable
    assessedDvtRisk
    equipmentImplantAvailable
    verifiedAvailabilityOrderedBloodProduct
    additionalNote
    concealAdditionalNote
    profile {
      id
      clinifyId
    }
    ${AUDIT_FIELDS}
  } 
`;

export const GET_PATIENT_PRE_OPERATION_CHECKLISTS = gql`
  query GetPatientPreOperationChecklists($filterOptions: SurgeryFilterInput, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      preOperationChecklist(filterOptions: $filterOptions) {
        totalCount
        list {
          ...PreOperationChecklist
        }
      }
    }
  }
  ${PRE_OPERATION_CHECKLIST_FIELD}
`;

export const GET_PATIENT_PRE_OPERATION_CHECKLIST = gql`
  query GetPatientPreOperationChecklist($clinifyId: String!, $id: String!) {
    preOperationChecklist(id: $id, clinifyId: $clinifyId) {
      ...PreOperationChecklist
    }
  }
  ${PRE_OPERATION_CHECKLIST_FIELD}
`;

export const ADD_PATIENT_PRE_OPERATION_CHECKLIST = gql`
  mutation AddPatientPreOperationChecklist(
    $id: String
    $input: PreOperationChecklistInput!
    $pin: String
  ) {
    addPreOperationChecklist(id: $id, checklist: $input, pin: $pin) {
      ...PreOperationChecklist
    }
  }
  ${PRE_OPERATION_CHECKLIST_FIELD}
`;

export const PRE_OPERATION_CHECKLIST_ADDED_SUBS = gql`
  subscription PreOperationChecklistAddedSubs($profileId: String!) {
    PreOperationChecklistAdded(profileId: $profileId) {
      ...PreOperationChecklist
    }
  }
  ${PRE_OPERATION_CHECKLIST_FIELD}
`;

export const UPDATE_PATIENT_PRE_OPERATION_CHECKLIST = gql`
  mutation UpdatePatientPreOperationChecklist(
    $id: String!
    $input: PreOperationChecklistInput!
    $pin: String
  ) {
    updatePreOperationChecklist(id: $id, checklist: $input, pin: $pin) {
      ...PreOperationChecklist
    }
  }
  ${PRE_OPERATION_CHECKLIST_FIELD}
`;

export const PRE_OPERATION_CHECKLIST_UPDATED_SUBS = gql`
  subscription PreOperationChecklistUpdatedSubs($profileId: String!) {
    PreOperationChecklistUpdated(profileId: $profileId) {
      ...PreOperationChecklist
    }
  }
  ${PRE_OPERATION_CHECKLIST_FIELD}
`;

export const DELETE_PATIENT_PRE_OPERATION_CHECKLIST = gql`
  mutation DeletePatientPreOperationChecklist($ids: [String!]!, $clinifyId: String!) {
    deletePreOperationChecklists(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const DELETE_PRE_OPERATION_CHECKLIST_SUBS = gql`
  subscription DeletePreOperationChecklistSubs($profileId: String!) {
    PreOperationChecklistRemoved(profileId: $profileId) {
      id
    }
  }
`;

export const CONCEAL_PRE_CHECKLIST_ADDITIONAL_NOTE = gql`
  mutation ConcealPreCheclistAdditionalNote($id: String!, $concealStatus: Boolean!) {
    concealPreCheclistAdditionalNote(checklistId: $id, concealState: $concealStatus) {
      id
      additionalNote
      concealAdditionalNote
    }
  }
`;

export const ANAESTHESIA_CHECKLIST_FIELD = gql`
  fragment Anaesthesia on AnaesthesiaModel {
    id
    drugsGiven {
      readingDate
      time
      drugName
      dosage
      dosageUnit
      routeOfAdministration
    }
    pulseAndBp {
      readingDate
      time
      pulse
      bpSystolic
      bpDiastolic
    }
    eventTimeGraph {
      readingDate
      eventName
      time
    }
    oxygenLevel {
      readingDate
      time
      ecgRythm
      saturated
      airFGF
      endTidal
      oxygenFi
      agentFe
      airwayPressure
      airwayPressureUnit
    }
    additionalOxygenLevel {
      name
      value {
        time
        volume
      }
    }
    fluids {
      readingDate
      time
      ivFluids
      bloodLoss
      urineOutput
    }
    inOt
    rapidSequence
    inhalational
    equipmentCheck
    preOxygenation
    vascularAccessSite
    preInduction
    postInduction
    nasalSpecs
    faceMask
    iGel
    lma
    endatrachealTube
    size {
      tick
      value
    }
    cmToLips {
      tick
      value
    }
    cuff
    amoured
    oralNasal
    grade
    difficult
    bougie
    mcCoy
    bougieILMA
    fibreOptic
    mcIntosh
    capnograph
    auscultation
    nmj {
      tick
      site
    }
    tempProbe
    respiration
    mode
    tv
    frequency
    circuit
    other
    eyesCovered
    pressurePointsPadded
    urinaryCatheter
    nasogastricTube
    warmingBlanket
    position
    arms
    intravenousFluidsGiven
    localAnaestheticTechnique
    clinicalAdverseEvent
    createdDate
    updatedDate
    creatorName
    lastModifierName
    profile {
      id
      clinifyId
    }
  }
`;

export const GET_PATIENT_ANAESTHESIA_CHECKLISTS = gql`
  query GetPatientAnaesthesiaChecklists($filterOptions: SurgeryFilterInput, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      anaesthesiaChecklist(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Anaesthesia
        }
      }
    }
  }
  ${ANAESTHESIA_CHECKLIST_FIELD}
`;

export const ADD_PATIENT_ANAESTHESIA_CHECKLIST = gql`
  mutation AddAnaesthesiaChecklist($id: String, $input: AnaesthesiaInput!, $pin: String) {
    addAnaesthesiaChecklist(id: $id, checklist: $input, pin: $pin) {
      ...Anaesthesia
    }
  }
  ${ANAESTHESIA_CHECKLIST_FIELD}
`;

export const ANAESTHESIA_CHECKLIST_ADDED_SUBS = gql`
  subscription AnaesthesiaAddedSubs($profileId: String!) {
    AnaesthesiaAdded(profileId: $profileId) {
      ...Anaesthesia
    }
  }
  ${ANAESTHESIA_CHECKLIST_FIELD}
`;

export const UPDATE_PATIENT_ANAESTHESIA_CHECKLIST = gql`
  mutation UpdateAnaesthesiaChecklist($id: String!, $input: AnaesthesiaInput!, $pin: String) {
    updateAnaesthesiaChecklist(id: $id, checklist: $input, pin: $pin) {
      ...Anaesthesia
    }
  }
  ${ANAESTHESIA_CHECKLIST_FIELD}
`;

export const ANAESTHESIA_CHECKLIST_UPDATED_SUBS = gql`
  subscription AnaesthesiaUpdatedSubs($profileId: String!) {
    AnaesthesiaUpdated(profileId: $profileId) {
      ...Anaesthesia
    }
  }
  ${ANAESTHESIA_CHECKLIST_FIELD}
`;

export const DELETE_PATIENT_ANAESTHESIA_CHECKLIST = gql`
  mutation DeleteAnaesthesiaChecklists($ids: [String!]!, $clinifyId: String!) {
    deleteAnaesthesiaChecklists(ids: $ids, clinifyId: $clinifyId) {
      ...Anaesthesia
    }
  }
  ${ANAESTHESIA_CHECKLIST_FIELD}
`;

export const DELETE_ANAESTHESIA_CHECKLIST_SUBS = gql`
  subscription DeleteAnaesthesiaSubs($profileId: String!) {
    AnaesthesiaRemoved(profileId: $profileId) {
      id
    }
  }
`;

export const GET_SURGERY_CLAIM = gql`
  query GetSurgeryClaim($id: String!, $clinifyId: String!) {
    surgery(id: $id, clinifyId: $clinifyId) {
      id
      hmoClaim {
        id
      }
    }
  }
`;

export const ADD_SURGERY_SIGNATURE = gql`
  mutation AddProcedureConsentSignature($id: String!, $clinifyId: String!, $input: SignatureInput!, $pin: String) {
    addProcedureConsentSignature(id: $id, clinifyId: $clinifyId, input: $input, pin: $pin) {
      ...Surgery
      ${AUDIT_FIELDS}
    }
  }
  ${SURGERY_FIELDS}
`;

export const UPDATE_SURGERY_SIGNATURE = gql`
  mutation UpdateProcedureConsentSignature($id: String!, $clinifyId: String!, $input: SignatureInput!, $pin: String) {
    updateProcedureConsentSignature(id: $id, clinifyId: $clinifyId, input: $input, pin: $pin) {
      ...Surgery
      ${AUDIT_FIELDS}
    }
  }
  ${SURGERY_FIELDS}
`;

export const DELETE_SURGERY_SIGNATURE = gql`
  mutation DeleteProcedureConsentSignature($id: String!, $clinifyId: String!) {
    deleteProcedureConsentSignature(id: $id, clinifyId: $clinifyId) {
      ...Surgery
      ${AUDIT_FIELDS}
    }
  }
  ${SURGERY_FIELDS}
`;
