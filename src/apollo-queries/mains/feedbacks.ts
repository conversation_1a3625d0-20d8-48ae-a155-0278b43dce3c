import { gql } from '@apollo/client';

const FEEDBACK_MODEL = gql`
  fragment Feedback on FeedbackModel {
    id
    feedbackDateTime
    title
    message
    comment
    category
    isResolved
    documentUrl
    createdDate
    creatorName
    reviewerName
    hospital {
      id
      name
    }
    createdBy {
      id
      fullName
    }
  }
`;

export const ADD_FEEDBACK = gql`
  mutation AddFeedback($input: NewFeedbackInput!) {
    addFeedback(input: $input) {
      ...Feedback
    }
  }
  ${FEEDBACK_MODEL}
`;

export const GET_FEEDBACKS = gql`
  query GetFeedbacks($filterOptions: FeedbackFilterOptions) {
    feedbacks(filterOptions: $filterOptions) {
      totalCount
      list {
        ...Feedback
      }
    }
  }
  ${FEEDBACK_MODEL}
`;

export const GET_FEEDBACK = gql`
  query GetFeedback($id: String!) {
    feedback(id: $id) {
      ...Feedback
    }
  }
  ${FEEDBACK_MODEL}
`;

export const UPDATE_FEEDBACK = gql`
  mutation UpdateFeedback($input: UpdateFeedbackInput!) {
    updateFeedback(input: $input) {
      ...Feedback
    }
  }
  ${FEEDBACK_MODEL}
`;

export const DELETE_FEEDBACK = gql`
  mutation DeleteFeedback($ids: [String!]!) {
    deleteFeedbacks(ids: $ids) {
      id
    }
  }
`;

export const ARCHIVE_FEEDBACKS = gql`
  mutation ArchiveFeedbacks($ids: [String!]!, $archive: Boolean!) {
    archiveFeedbacks(ids: $ids, archive: $archive) {
      ...Feedback
    }
  }
  ${FEEDBACK_MODEL}
`;

export const UPDATE_FEEDBACK_COMMENT = gql`
  mutation UpdateFeedbackComment($input: UpdateFeedbackCommentInput!) {
    updateFeedbackComment(input: $input) {
      ...Feedback
    }
  }
  ${FEEDBACK_MODEL}
`;

export const UPDATE_FEEDBACK_STATUS = gql`
  mutation UpdateFeedbackStatus($input: UpdateFeedbackStatusInput!) {
    updateFeedbackStatus(input: $input) {
      ...Feedback
    }
  }
  ${FEEDBACK_MODEL}
`;

export const GET_FEEDBACK_SUMMARY = gql`
  query GetFeedbackSummary($filterOptions: FeedbackFilterOptions!) {
    feedbackSummary(filterOptions: $filterOptions) {
      totalCount
      totalResolved
      totalUnresolved
      totalProviderSubmitted
    }
  }
`;

export const FEEDBACK_ADDED_SUBS = gql`
  subscription FeedbackAddedSubs($hospitalId: String!, $hmoProviderId: String) {
    FeedbackAdded(hospitalId: $hospitalId, hmoProviderId: $hmoProviderId) {
      ...Feedback
    }
  }
  ${FEEDBACK_MODEL}
`;

export const FEEDBACK_UPDATED_SUBS = gql`
  subscription FeedbackUpdatedSubs($hospitalId: String!, $hmoProviderId: String) {
    FeedbackUpdated(hospitalId: $hospitalId, hmoProviderId: $hmoProviderId) {
      ...Feedback
    }
  }
  ${FEEDBACK_MODEL}
`;

export const FEEDBACK_REMOVED_SUBS = gql`
  subscription FeedbackRemovedSubs($hospitalId: String!, $hmoProviderId: String) {
    FeedbackRemoved(hospitalId: $hospitalId, hmoProviderId: $hmoProviderId) {
      id
    }
  }
`;

export const FEEDBACK_ARCHIVED_SUBS = gql`
  subscription FeedbackArchivedSubs($hospitalId: String!, $hmoProviderId: String) {
    FeedbackArchived(hospitalId: $hospitalId, hmoProviderId: $hmoProviderId) {
      ...Feedback
    }
  }
  ${FEEDBACK_MODEL}
`;
