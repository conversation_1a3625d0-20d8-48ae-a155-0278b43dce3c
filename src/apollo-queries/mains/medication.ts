import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';
import { PREAUTHORIZATION_DETAILS_FRAGMENT } from '../fragments/preauthorization-details';
import { ADMINISTRATION_NOTE_FRAGMENT } from '../fragments/medication';
import { ONCOLOGY_CHEMO_DRUG } from './oncology-history';

export const DISPENSE_REGISTER_FIELDS = gql`
  fragment DispenseRegister on DispenseRegistersModel {
    id
    details {
      medicationName
      reference
      periodName
      periods {
        no
        values
        count
        audits {
          fullName
          dateTime
          desc
          checkId
          profileId
        }
      }
    }
    creator
    updater
    updatedDate
    createdDate
  }
`;

export const MEDICATION_OXYGEN_THERAPY_FIELDS = gql`
  fragment MedicationOxygenTherapy on OxygenTherapyModel {
    id
    createdDate
    creator
    medicationDetailId
    updatedDate
    updater
    details {
      administeredBy
      administrationDateTime
      administrationHour
      saturation
      therapyType
    }
  }
`;

export const MEDICATION_DETAILS_FIELD = gql`
  fragment MedicationDetail on MedicationDetailsModel {
    id
    datePrescribed
    duration
    medicationName
    medicationCategory
    purpose
    administrationMethod
    dosage
    dosageUnit
    prescriptionNote
    concealPrescriptionNote
    type
    quantity
    startDate
    endDate
    bank
    drugInventoryId
    option
    unitPrice
    medicationStatus
    provider
    fromBundle
    medicationConsumables {
      name
      drugInventoryId
      unitPrice
      quantity
      inventoryClass
    }
    priceDetails {
      type
      name
      pricePerUnit
      patientType
      paymentType
      quantity
    }
    discontinue
    discontinueReason
    adverseEffectsFollowingMedication
    stateEffects
    adverseEffectsInvestigated
    outcomeOfInvestigation
    refillNumber
    frequency
    isPackage
    hospitalId
    medicationType
    preauthorizationDetails {
      ...PreauthorizationDetails
    }
    diagnosis {
      diagnosisICD10
      diagnosisICD11
      diagnosisSNOMED
    }
    chemoDrugs {
      ...OncologyChemoDrug
      oncologyConsultationHistory {
        id
        chemoComments {
          cycleNumber
          section
          comment
          creatorId
          creatorName
          createdDate
          lastModifierId
          lastModifierName
          updatedDate
        }
      }
    }
    inventoryClass
  }
  ${ONCOLOGY_CHEMO_DRUG}
  ${PREAUTHORIZATION_DETAILS_FRAGMENT}
`;

export const MEDICATION_INFUSION = gql`
  fragment MedicationInfusion on MedicationInfusionModel {
    id
    creator
    updater
    createdDate
    updatedDate
    medicationDetailId
    details {
      typeOrStrength
      volume
      drugName
      dosage
      route
      dripRate
      batchNumber
      prescribedBy
      administrationDateTime
      administeredBy
      checkedBy
    }
  }
`;

export const SHORT_PROFILE = gql`
  fragment ShortProfile on ProfileModel {
    fullName
    personalInformation {
      address
    }
    user {
      phoneNumber
    }
  }
`;

export const DISPENSE_DETAILS_FIELD = gql`
  fragment DispenseDetail on DispenseDetailsModel {
    id
    dispenseDate
    dispensedBy
    medicationName
    medicationDetailId
    dispenseNote
    concealDispenseNote
    hmoProviderId
    hospitalId
    dispenseServiceDetails {
      priceId
      type
      name
      quantity
      pricePerUnit
      patientType
      paymentType
    }
    facilityName
    facilityAddress
    medicationId
    billingId
    quantityRemaining
    quantityDispensed
    dispenseConsumables {
      name
      drugInventoryId
      quantityConsumed
      quantityRemaining
      inventoryClass
    }
    option
    hmoClaimId
    inventoryClass
    hmoClaim {
      id
      claimDate
      enrolleeNumber
        utilizations {
            id
            type
            price
        }
    }
    ${AUDIT_FIELDS}
  }
`;

export const MEDICATION_FIELDS = gql`
  fragment Medication on MedicationModel {
    id
    prescribedBy
    rank
    department
    specialty
    verificationCode
    details {
      ...MedicationDetail
    }
    administrationNotes {
      ...AdministrationNote
    }
    dispenseIds
    hospitalName
    hospitalAddress
    setReminder
    reminderStartDate
    reminderEndDate
    medicationStartTime
    medicationEndTime
    interval
    intervalUnit
    remindMe
    documentUrl
    additionalNote
    concealAdditionalNote
    createdDate
    updatedDate
    clinifyId
    billStatus
    hospitalId
    profileId
    profile {
      id
      clinifyId
      fullName
      coverageDetails {
        id
        name
        coverageType
        companyName
        familyName
        memberNumber
        provider {
          id
          name
        }
      }
    }
  }
  ${MEDICATION_DETAILS_FIELD}
  ${ADMINISTRATION_NOTE_FRAGMENT}
`;

export const FETCH_DISPENSE_DETAILS = gql`
  query DispenseDetails($parentRecordId: String!) {
    getDispenseDetails(medicationId: $parentRecordId) {
      ...DispenseDetail
    }
  }
  ${DISPENSE_DETAILS_FIELD}
`;

export const ADD_PATIENT_MEDICATION = gql`
  mutation AddPatientMedication($input: NewMedicationInput!, $id: String, $pin: String) {
    addMedication(medication: $input, id: $id, pin: $pin) {
      ...Medication
      billing {
        id
        createdDate
        billId
      }
      dispenseDetails {
        id
        medicationId
      }
      billStatus
      ${AUDIT_FIELDS}
    }
  }
  ${MEDICATION_FIELDS}
`;

export const GET_PATIENT_MEDICATION_LIST = gql`
  query GetPatientMedicationList($filterOptions: MedFilterOptions, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      medications(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Medication
          dispenseDetails {
            id
            medicationName
            medicationId
            facilityName
          }
          billing {
            id
            createdDate
            billId
          }
          billStatus
        }
      }
    }
  }
  ${MEDICATION_FIELDS}
`;

export const DELETE_PATIENT_MEDICATION = gql`
  mutation DeletePatientMedications($ids: [String!]!, $clinifyId: String!) {
    deleteMedications(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const SAVE_MEDICATION_DETAIL = gql`
  mutation SaveMedicationDetail(
    $clinifyId: String!
    $id: String!
    $input: MedicationDetailsInput!
     $pin: String
  ) {
    saveMedicationDetail(medicationId: $id, clinifyId: $clinifyId, input: $input, pin: $pin) {
      ...MedicationDetail
      dispenseRegister {
        ...DispenseRegister
      }
      oxygenTherapy {
        ...MedicationOxygenTherapy
      }
      infusion {
        ...MedicationInfusion
      }
      ${AUDIT_FIELDS}
    }
  }
  ${MEDICATION_DETAILS_FIELD}
  ${DISPENSE_REGISTER_FIELDS}
  ${MEDICATION_OXYGEN_THERAPY_FIELDS}
  ${MEDICATION_INFUSION}
`;

export const UPDATE_MEDICATION_DETAIL = gql`
  mutation UpdateMedicationDetail(
    $clinifyId: String!
    $id: String!
    $input: MedicationDetailsInput!
     $pin: String
  ) {
    updateMedicationDetail(medicationId: $id, clinifyId: $clinifyId, input: $input, pin: $pin) {
      ...MedicationDetail
      dispenseRegister {
        ...DispenseRegister
      }
      dispenseDetails {
        ...DispenseDetail
      }
      ${AUDIT_FIELDS}
    }
  }
  ${MEDICATION_DETAILS_FIELD}
  ${DISPENSE_REGISTER_FIELDS}
  ${DISPENSE_DETAILS_FIELD}
`;

export const UPDATE_MEDICATION_STATUS = gql`
  mutation UpdateMedicationStatus($id: String!, $input: String!, $pin: String) {
    updateMedicationStatus(medicationDetailId: $id, status: $input, pin: $pin) {
      id
      medicationStatus
    }
  }
`;

export const DELETE_MEDICATION_DETAIL = gql`
  mutation DeleteMedicationDetail($clinifyId: String!, $id: String!) {
    deleteMedicationDetail(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const DELETE_MULTIPLE_MEDICATION_DETAIL = gql`
  mutation DeleteMultipleMedicationDetails($clinifyId: String!, $ids: [String!]!) {
    deleteMultipleMedicationDetails(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const ADD_MEDICATION_DETAILS_SUB = gql`
  subscription MedicationDetailsAdded($profileId: String!) {
    MedicationDetailsAdded(profileId: $profileId) {
      ...MedicationDetail
      dispenseRegister {
        ...DispenseRegister
      }
      oxygenTherapy {
        ...MedicationOxygenTherapy
      }
      infusion {
        ...MedicationInfusion
      }
      medicationId
      ${AUDIT_FIELDS}
    }
  }
  ${MEDICATION_DETAILS_FIELD}
  ${DISPENSE_REGISTER_FIELDS}
  ${MEDICATION_OXYGEN_THERAPY_FIELDS}
  ${MEDICATION_INFUSION}
`;

export const UPDATE_MEDICATION_DETAILS_SUB = gql`
  subscription MedicationDetailsUpdated($profileId: String!) {
    MedicationDetailsUpdated(profileId: $profileId) {
      ...MedicationDetail
      dispenseRegister {
        ...DispenseRegister
      }
      dispenseDetails {
        ...DispenseDetail
      }
      ${AUDIT_FIELDS}
    }
  }
  ${MEDICATION_DETAILS_FIELD}
  ${DISPENSE_REGISTER_FIELDS}
  ${DISPENSE_DETAILS_FIELD}
`;

export const MEDICATION_DETAILS_REMOVED_SUB = gql`
  subscription MedicationDetailsRemoved($profileId: String!) {
    MedicationDetailsRemoved(profileId: $profileId) {
      id
      medicationId
    }
  }
`;

export const UPDATE_DISPENSE_DETAIL = gql`
  mutation UpdateDispenseDetail($id: String!, $input: DispenseDetailsInput!, $pin: String) {
    updateDispenseDetails(id: $id, dispenseDetail: $input, pin: $pin) {
      ...DispenseDetail
      ${AUDIT_FIELDS}
    }
  }
  ${DISPENSE_DETAILS_FIELD}
`;

export const DELETE_DISPENSE_DETAIL = gql`
  mutation DeleteDispenseDetail($id: String!, $clinifyId: String!) {
    deleteDispenseDetail(id: $id, clinifyId: $clinifyId) {
      id
      medicationId
      billingId
    }
  }
`;

export const SAVE_DISPENSE_DETAIL = gql`
  mutation SaveDispenseDetails($id: String!, $input: NewDispenseDetailsInput!, $pin: String) {
    saveDispenseDetail(medicationId: $id, input: $input, pin: $pin) {
      ...DispenseDetail
      medication {
        id
        billing {
          id
          createdDate
          billId
        }
        dispenseIds
        billStatus
      }
      ${AUDIT_FIELDS}
    }
  }
  ${DISPENSE_DETAILS_FIELD}
`;

export const DISPENSE_DETAIL_ADDED_SUBS = gql`
  subscription DispenseMedicationAddedSubs($profileId: String!, $hospitalId: String!) {
    DispenseMedicationAdded(profileId: $profileId, hospitalId: $hospitalId) {
      ...DispenseDetail
      createdBy {
        id
        hospitalId
      }
      medication {
        id
        billing {
          id
          createdDate
          billId
        }
        dispenseIds
        billStatus
      }
      ${AUDIT_FIELDS}
    }
  }
  ${DISPENSE_DETAILS_FIELD}
`;

export const DISPENSE_DETAIL_UPDATED_SUBS = gql`
  subscription DispenseMedicationUpdatedSubs($profileId: String!, $hospitalId: String!) {
    DispenseMedicationUpdated(profileId: $profileId, hospitalId: $hospitalId) {
      ...DispenseDetail
      ${AUDIT_FIELDS}
    }
  }
  ${DISPENSE_DETAILS_FIELD}
`;

export const DISPENSE_DETAIL_DELETED_SUBS = gql`
  subscription DispenseMedicationRemovedSubs($profileId: String!, $hospitalId: String!) {
    DispenseMedicationRemoved(profileId: $profileId, hospitalId: $hospitalId) {
      id
      medicationId
      billingId
      medication {
        id
        dispenseIds
      }
    }
  }
`;

export const ARCHIVE_PATIENT_MEDICATION = gql`
  mutation ArchivePatientMedication($ids: [String!]!, $archive: Boolean, $clinifyId: String!) {
    archiveMedications(ids: $ids, archive: $archive, clinifyId: $clinifyId) {
      ...Medication
      ${AUDIT_FIELDS}
    }
  }
  ${MEDICATION_FIELDS}
`;

export const UPDATE_PATIENT_MEDICATION = gql`
  mutation UpdatePatientMedication($id: String!, $input: MedicationInput!, $pin: String) {
    updateMedication(id: $id, medication: $input, pin: $pin) {
      ...Medication
      details {
        ...MedicationDetail
        ${AUDIT_FIELDS}
      }
      ${AUDIT_FIELDS}
    }
  }
  ${MEDICATION_FIELDS}
`;

export const GET_PATIENT_MEDICATION = gql`
  query GetPatientMedication($id: String!, $clinifyId: String!) {
    medication(id: $id, clinifyId: $clinifyId) {
      ...Medication
      details {
        ...MedicationDetail
        dispenseRegister {
        ...DispenseRegister
        }
        oxygenTherapy {
          ...MedicationOxygenTherapy
        }
        infusion {
          ...MedicationInfusion
        }
        ${AUDIT_FIELDS}
      }
      ${AUDIT_FIELDS}
    }
  }
  ${MEDICATION_FIELDS}
  ${DISPENSE_REGISTER_FIELDS}
  ${MEDICATION_OXYGEN_THERAPY_FIELDS}
  ${MEDICATION_INFUSION}
`;

export const GET_HOSPITAL_PRESCRIPTION = gql`
  query GetHospitalPrescription($filterOptions: MedFilterOptions, $hospitalId: String) {
    hospital(hospitalId: $hospitalId) {
      id
      medications(filterOptions: $filterOptions) {
        totalCount
        list {
          profile {
            id
            clinifyId
            fullName
          }
          billing {
            id
            billId
            createdDate
          }
          billStatus
          ...Medication
          dispenseDetails {
            id
            medicationName
            medicationId
            facilityName
          }
        }
      }
    }
  }
  ${MEDICATION_FIELDS}
`;

export const MEDICATION_ADDED_SUBS = gql`
  subscription MedicationAddedSubs($profileId: String!, $hospitalId: String!) {
    MedicationAdded(profileId: $profileId, hospitalId: $hospitalId) {
      ...Medication
      billing {
        id
        createdDate
        billId
      }
      dispenseDetails {
        id
        medicationId
      }
      billStatus
      ${AUDIT_FIELDS}
    }
  }
  ${MEDICATION_FIELDS}
`;

export const MEDICATION_UPDATED_SUBS = gql`
  subscription MedicationUpdatedSubs($profileId: String!, $hospitalId: String!) {
    MedicationUpdated(profileId: $profileId, hospitalId: $hospitalId) {
      ...Medication
      ${AUDIT_FIELDS}
    }
  }
  ${MEDICATION_FIELDS}
`;

export const DELETE_MEDICATION_SUBS = gql`
  subscription DeleteMedicationSubs($profileId: String!, $hospitalId: String!) {
    MedicationRemoved(profileId: $profileId, hospitalId: $hospitalId) {
      id
    }
  }
`;

export const ARCHIVE_MEDICATION_SUBS = gql`
  subscription ArchiveMedicationSubs($profileId: String!, $hospitalId: String!) {
    MedicationArchived(profileId: $profileId, hospitalId: $hospitalId) {
      ...Medication
    }
  }
  ${MEDICATION_FIELDS}
`;

export const UNARCHIVE_MEDICATION_SUBS = gql`
  subscription UnarchiveMedicationSubs($profileId: String!, $hospitalId: String!) {
    MedicationUnarchived(profileId: $profileId, hospitalId: $hospitalId) {
      ...Medication
    }
  }
  ${MEDICATION_FIELDS}
`;

export const PRESCRIPTION_EVENT_SUBSCRIPTION = gql`
  subscription PrescriptionEvent($hospitalId: String!) {
    PrescriptionEvent(hospitalId: $hospitalId)
  }
`;

export const CONCEAL_MEDICATION_ADDITIONAL_NOTE = gql`
  mutation ConcealMedicationAdditionalNote($id: String!, $concealStatus: Boolean!) {
    concealMedicationAdditionalNote(medicationId: $id, concealState: $concealStatus) {
      id
      concealAdditionalNote
      additionalNote
    }
  }
`;

export const CONCEAL_PRESCRIPTION_NOTE = gql`
  mutation ConcealPrescriptionNote($id: String!, $concealStatus: Boolean!) {
    concealPrescriptionNote(medicationDetailsId: $id, concealState: $concealStatus) {
      id
      concealPrescriptionNote
      prescriptionNote
    }
  }
`;

export const CONCEAL_DISPENSE_NOTE = gql`
    mutation ConcealDispenseNote($id: String!, $concealStatus: Boolean!) {
        concealDispenseNote(dispenseDetailsId: $id, concealStatus: $concealStatus) {
          id
          concealDispenseNote
          dispenseNote
          ${AUDIT_FIELDS}
        }
    }
`;

export const UPDATE_DISPENSE_REGISTER = gql`
  mutation UpdateMedicationDispenseRegister(
    $input: UpdateDispenseRegisterInput!
    $id: String!
    $pin: String
  ) {
    updateMedicationDispenseRegister(input: $input, id: $id, pin: $pin) {
      ...DispenseRegister
    }
  }
  ${DISPENSE_REGISTER_FIELDS}
`;

export const SEND_MEDICATION_EMAIL = gql`
  query SendMedicationToPatientEmail($id: String!, $origin: String!) {
    sendMedicationToPatientEmail(id: $id, origin: $origin) {
      sent
    }
  }
`;

export const ADD_ADMINISTRATION_NOTE = gql`
  mutation AddAdministrationNote(
    $input: AdministrationNoteInput!
    $id: String!
    $clinifyId: String!
    $pin: String
  ) {
    addAdministrationNote(medicationId: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...AdministrationNote
    }
  }
  ${ADMINISTRATION_NOTE_FRAGMENT}
`;

export const UPDATE_ADMINISTRATION_NOTE = gql`
  mutation UpdateAdministrationNote(
    $input: AdministrationNoteInput!
    $id: String!
    $clinifyId: String!
    $pin: String
  ) {
    updateAdministrationNote(id: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...AdministrationNote
    }
  }
  ${ADMINISTRATION_NOTE_FRAGMENT}
`;

export const DELETE_ADMINISTRATION_NOTE = gql`
  mutation DeleteAdministrationNote($id: String!, $clinifyId: String!) {
    deleteAdministrationNote(administrationNoteId: $id, clinifyId: $clinifyId) {
      id
      medicationId
    }
  }
`;

export const CONCEAL_ADMINISTRATION_NOTE = gql`
  mutation ConcealAdministrationNote($id: String!, $concealStatus: Boolean!) {
    concealAdministrationNote(administrationNoteId: $id, concealStatus: $concealStatus) {
      ...AdministrationNote
    }
  }
  ${ADMINISTRATION_NOTE_FRAGMENT}
`;

export const ADMINISTRATION_NOTE_ADDED_SUBS = gql`
  subscription AdministrationNoteAddedSubs($profileId: String!) {
    AdministrationNoteAdded(profileId: $profileId) {
      ...AdministrationNote
    }
  }
  ${ADMINISTRATION_NOTE_FRAGMENT}
`;

export const ADMINISTRATION_NOTE_UPDATED_SUBS = gql`
  subscription AdministrationNoteUpdatedSubs($profileId: String!) {
    AdministrationNoteUpdated(profileId: $profileId) {
      ...AdministrationNote
    }
  }
  ${ADMINISTRATION_NOTE_FRAGMENT}
`;

export const ADMINISTRATION_NOTE_DELETED_SUBS = gql`
  subscription AdministrationNoteDeletedSubs($profileId: String!) {
    AdministrationNoteRemoved(profileId: $profileId) {
      id
      medicationId
    }
  }
`;

export const FETCH_PRESCRIPTION_TRENDS = gql`
  query GetPatientPrescriptionTrends($filter: MedsHistoryFilter!) {
    getPatientMedsHistory(filter: $filter) {
      totalCount
      list {
        duration
        dosage
        dosageUnit
        prescribedBy
        administrationMethod
        name
        frequency
        consumableName
        prescriptionDate
      }
    }
  }
`;

export const UPDATE_MEDICATION_OXYGEN_THERAPY = gql`
  mutation UpdateMedicationOxygenTherapy($id: String!, $input: OxygenTherapyInput!, $pin: String) {
    updateMedicationOxygenTherapy(id: $id, input: $input, pin: $pin) {
      ...MedicationOxygenTherapy
    }
  }
  ${MEDICATION_OXYGEN_THERAPY_FIELDS}
`;

export const UPDATE_MEDICATION_INFUSION = gql`
  mutation UpdateMedicationInfusion($id: String!, $input: MedicationInfusionInput!, $pin: String) {
    updateMedicationInfusion(id: $id, input: $input, pin: $pin) {
      ...MedicationInfusion
    }
  }
  ${MEDICATION_INFUSION}
`;
