import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

export const POST_OPERATION_CHECKLIST_FIELD = gql`
  fragment PostOperation on PostOperationModel {
    id
    serviceStartDateTime
    chartType
    surgeryStartDateTime
    surgeryEndDateTime
    dietOrderSheet {
      checked
      orderName
      date
      checkedBy
    }
    ambutationOrderSheet {
      checked
      orderName
      date
      checkedBy
    }
    urethralCatheterizationOrderSheet {
      checked
      orderName
      date
      checkedBy
    }
    fluidTherapyOrderSheet {
      checked
      orderName
      date
      checkedBy
    }
    antiBioticsOrderSheet {
      checked
      orderName
      date
      checkedBy
    }
    analgesicOrderSheet {
      checked
      orderName
      date
      checkedBy
      title
      names
      isMultiple
    }
    clexane40mgFor3DaysOrderSheet {
      checked
      orderName
      date
      checkedBy
    }
    vitalSigns {
      timeIn
      systolic
      diastolic
      pulseRate
      oxygenSaturation
      respiratoryRate
      temperature
      temperatureUnit
      nausea
      painScore
      state
    }
    medications {
      medicationName
      prescriptionTime
      dosage
      dosageUnit
    }
    discontinueMedication
    urineOutput {
      timeIn
      output
    }
    linesDrainsTubes {
      intravenousInfusions
      others
      dressingChecked
      dressingCheckedTime
      drainsChecked
      drainsCheckedTime
      catheterChecked
      catheterCheckedTime
      quantityDrained
    }
    newOrders {
      checked
      orderName
      date
      checkedBy
    }
    SurgeonName
    surgeonNameSignature
    surgeonNameSignatureType
    surgeonNameSignatureDateTime
    SurgeonSpecialty
    SurgeonAssistantName
    surgeonAssistantNameSignature
    surgeonAssistantNameSignatureType
    surgeonAssistantNameSignatureDateTime
    operatingRoomNurse
    operatingRoomNurseSignature
    operatingRoomNurseSignatureType
    operatingRoomNurseSignatureDateTime
    anesthetistName
    anesthetistNameSignature
    anesthetistNameSignatureType
    anesthetistNameSignatureDateTime
    recoveryNurse
    recoveryNurseSignature
    recoveryNurseSignatureType
    recoveryNurseSignatureDateTime
    visitingSpecialistName
    visitingSpecialistSignature
    visitingSpecialistSignatureType
    visitingSpecialistSignatureDateTime
    visitingFacilityName
    additionalNote
    concealAdditionalNote
    profileId
    profile {
      id
      clinifyId
    }
    ${AUDIT_FIELDS}
  } 
`;

export const GET_PATIENT_POST_OPERATION_CHECKLISTS = gql`
  query GetPatientPostOperations($filterOptions: NursingServicesFilterInput, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      postOperationChecklist(filterOptions: $filterOptions) {
        totalCount
        list {
          ...PostOperation
        }
      }
    }
  }
  ${POST_OPERATION_CHECKLIST_FIELD}
`;

export const GET_PATIENT_POST_OPERATION_CHECKLIST = gql`
  query GetPatientPostOperation($clinifyId: String!, $id: String!) {
    postOperationChecklist(id: $id, clinifyId: $clinifyId) {
      ...PostOperation
    }
  }
  ${POST_OPERATION_CHECKLIST_FIELD}
`;

export const ADD_PATIENT_POST_OPERATION_CHECKLIST = gql`
  mutation AddPostOperationChecklist(
    $id: String
    $input: PostOperationChecklistInput!
    $pin: String
  ) {
    addPostOperationChecklist(id: $id, checklist: $input, pin: $pin) {
      ...PostOperation
    }
  }
  ${POST_OPERATION_CHECKLIST_FIELD}
`;

export const POST_OPERATION_CHECKLIST_ADDED_SUBS = gql`
  subscription PostOperationChecklistAddedSubs($profileId: String!) {
    PostOperationChecklistAdded(profileId: $profileId) {
      ...PostOperation
    }
  }
  ${POST_OPERATION_CHECKLIST_FIELD}
`;

export const UPDATE_PATIENT_POST_OPERATION_CHECKLIST = gql`
  mutation UpdatePostOperationChecklist(
    $id: String!
    $input: PostOperationChecklistInput!
    $pin: String
  ) {
    updatePostOperationChecklist(id: $id, checklist: $input, pin: $pin) {
      ...PostOperation
    }
  }
  ${POST_OPERATION_CHECKLIST_FIELD}
`;

export const POST_OPERATION_CHECKLIST_UPDATED_SUBS = gql`
  subscription PostOperationChecklistUpdatedSubs($profileId: String!) {
    PostOperationChecklistUpdated(profileId: $profileId) {
      ...PostOperation
    }
  }
  ${POST_OPERATION_CHECKLIST_FIELD}
`;

export const DELETE_PATIENT_POST_OPERATION_CHECKLIST = gql`
  mutation DeletePostOperationChecklists($ids: [String!]!, $clinifyId: String!) {
    deletePostOperationChecklists(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const DELETE_POST_OPERATION_CHECKLIST_SUBS = gql`
  subscription DeletePostOperationChecklistSubs($profileId: String!) {
    PostOperationChecklistRemoved(profileId: $profileId) {
      id
    }
  }
`;

export const CONCEAL_POST_CHECKLIST_ADDITIONAL_NOTE = gql`
  mutation ConcealPostChecklistAdditionalNote($id: String!, $concealStatus: Boolean!) {
    concealPostChecklistAdditionalNote(checklistId: $id, concealState: $concealStatus) {
      id
      additionalNote
      concealAdditionalNote
    }
  }
`;
