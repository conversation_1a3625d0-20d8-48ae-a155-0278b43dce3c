import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

export const VIRTUAL_CARE_APPOINTMENT_FIELDS = gql`
  fragment VirtualCareAppointment on VirtualCareAppointmentModel {
    id
    createdDate
    updatedDate
    liveSessionUrl
    category
    appointmentDate
    startDateTime
    endDateTime
    priority
    duration
    patientType
    paymentType
    hospital {
      id
      name
      address
    }
    specialists {
      id
      fullName
      type
      personalInformation {
        id
        speciality
      }
    }
    patientProfile {
      id
      fullName
      gender
      clinifyId
      personalInformation {
        id
        firstName
        middleName
        lastName
        dateOfBirth
        displayPictureUrl
        bloodGroup
      }
      user {
        id
        nonCorporateEmail
        phoneNumber
        country
      }
    }
  }
`;

export const APPOINTMENT_FIELDS = gql`
  fragment Appointment on OrganisationAppointmentModel {
    id
    hospital {
      id
      name
      address
    }
    profile {
      id
      fullName
      clinifyId
    }
    patientInformation {
      clinifyId
      fullName
      email
      phone
    }
    specialist {
      id
      clinifyId
      type
      fullName
    }
    createdDate
    updatedDate
    status
    category
    liveSessionUrl
    rank
    reason
    appointmentDateTime
    startDateTime
    endDateTime
    specialty
    role
    paymentType
    serviceDetails {
      type
      name
    }
    patientType
    duration
    confirmedBy
    deliveryMethod
    urgency
    additionalNote
    recordType
    documentUrl
  }
`;

export const FETCH_APPOINTMENTS = gql`
  query FetchAppointments($filterOptions: OrganisationAppointmentFilterInput, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      appointments(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Appointment
          patientConfirmation
        }
      }
    }
  }
  ${APPOINTMENT_FIELDS}
`;

export const FETCH_APPOINTMENT = gql`
  query FetchAppointment($id: String!, $filterInput: fetchAppointmentFilterInput ) {
    appointment(id: $id, filterInput: $filterInput) {
      ...Appointment
      ${AUDIT_FIELDS}
    }
  }
  ${APPOINTMENT_FIELDS}
`;

export const ADD_APPOINTMENT = gql`
  mutation AddAppointment($input: NewOrganisationAppointmentInput!) {
    addAppointment(input: $input) {
      ...Appointment
    }
  }
  ${APPOINTMENT_FIELDS}
`;

export const ADD_APPOINTMENT_SUB = gql`
  subscription AddAppointmentSub($profileId: String!, $hospitalId: String!) {
    AppointmentAdded(profileId: $profileId, hospitalId: $hospitalId) {
      ...Appointment
    }
  }
  ${APPOINTMENT_FIELDS}
`;

export const UPDATE_APPOINTMENT_SUB = gql`
  subscription UpdateAppointmentSub($profileId: String!, $hospitalId: String!) {
    AppointmentUpdated(profileId: $profileId, hospitalId: $hospitalId) {
      id
      specialist {
        id
        clinifyId
        type
        fullName
      }
      createdDate
      updatedDate
      status
      category
      rank
      reason
      appointmentDateTime
      startDateTime
      endDateTime
      specialty
      paymentType
      patientType
      duration
      confirmedBy
      deliveryMethod
      urgency
      additionalNote
    }
  }
`;

export const UPDATE_APPOINTMENT = gql`
  mutation UpdateAppointment($id: String!, $input: UpdateOrganisationAppointmentInput!) {
    updateAppointment(input: $input, id: $id) {
      ...Appointment
      ${AUDIT_FIELDS}
    }
  }
  ${APPOINTMENT_FIELDS}
`;

export const UPDATE_APPOINTMENT_TIME = gql`
  mutation UpdateAppointmentTime($id: String!, $input:UpdateOrganisationAppointmentTimeInput!) {
    updateAppointmentTime(id: $id, input: $input) {
      ...Appointment
      ${AUDIT_FIELDS}
    }
  }
  ${APPOINTMENT_FIELDS}
`;

export const UPDATE_APPOINTMENT_STATUS = gql`
  mutation UpdateAppointmentStatus($id: String!, $status: OrganisationApointmentStatus!) {
    updateAppointmentStatus(id: $id, status: $status) {
      ...Appointment
      ${AUDIT_FIELDS}
    }
  }
  ${APPOINTMENT_FIELDS}
`;

export const DELETE_APPOINTMENT = gql`
  mutation DeleteAppointments($ids: [String!]!) {
    deleteAppointments(ids: $ids) {
      id
    }
  }
`;
export const GET_APPOINTMENT_UNAVAILABILITY = gql`
  query GetAppointmentUnavailability($profileId: String!) {
    getAppointmentUnavailability(profileId: $profileId) {
      id
      list {
        startDate
        endDate
        isAllDay
        reason
      }
    }
  }
`;
export const UPDATE_APPOINTMENT_UNAVAILABILITY = gql`
  mutation UpdateAppointmentUnavailability(
    $input: [ProfileUnavailableDatesInput!]
    $profileId: String
  ) {
    updateAppointmentUnavailability(input: $input, profileId: $profileId) {
      id
      list {
        startDate
        endDate
        isAllDay
        reason
      }
    }
  }
`;

export const UPDATE_APPOINTMENT_UNAVAILABILITY_SUB = gql`
  subscription AppointmentUnvailabilityUpdatedSub($hospitalId: String!) {
    AppointmentUnvailabilityUpdated(hospitalId: $hospitalId) {
      id
      list {
        startDate
        endDate
        isAllDay
        reason
      }
    }
  }
`;

export const DELETE_APPOINTMENTS_SUB = gql`
  subscription DeleteAppointmentSub($profileId: String!, $hospitalId: String!) {
    AppointmentsRemoved(profileId: $profileId, hospitalId: $hospitalId) {
      ...Appointment
    }
  }
  ${APPOINTMENT_FIELDS}
`;

export const ARCHIVE_APPOINTMENT_SUB = gql`
  subscription ArchiveAppointmentsSub($profileId: String!, $hospitalId: String!) {
    AppointmentsArchived(profileId: $profileId, hospitalId: $hospitalId) {
      ...Appointment
    }
  }
  ${APPOINTMENT_FIELDS}
`;

export const UNARCHIVE_APPOINTMENT_SUB = gql`
  subscription UnarchiveAppointmentsSub($profileId: String!, $hospitalId: String!) {
    AppointmentsUnarchived(profileId: $profileId, hospitalId: $hospitalId) {
      ...Appointment
    }
  }
  ${APPOINTMENT_FIELDS}
`;

export const ARCHIVE_APPOINTMENT = gql`
  mutation ArchiveAppointments($ids: [String!]!, $archive: Boolean!) {
    archiveAppointments(ids: $ids, archive: $archive) {
      ...Appointment
    }
  }
  ${APPOINTMENT_FIELDS}
`;

export const APPOINTMENT_EVENT_SUBSCRIPTION = gql`
  subscription AppointmentEvent($hospitalId: String!, $profileId: String!) {
    AppointmentEvent(hospitalId: $hospitalId, profileId: $profileId)
  }
`;

export const GET_HOSPITAL_VIRTUAL_CARE_APPOINTMENTS = gql`
  query GetHospitalVirtualCareAppointments(
    $filterOptions: VirtualCareAppointmentFilterInput!
    $hospitalId: String
  ) {
    hospital(hospitalId: $hospitalId) {
      id
      virtualCareAppointments(filterInput: $filterOptions) {
        totalCount
        list {
          ...VirtualCareAppointment
        }
      }
    }
  }
  ${VIRTUAL_CARE_APPOINTMENT_FIELDS}
`;

export const GET_PATIENT_VIRTUAL_CARE_APPOINTMENTS = gql`
  query GetPatientVirtualCareAppointments(
    $filterOptions: VirtualCareAppointmentFilterInput!
    $id: String!
  ) {
    profile(clinifyId: $id) {
      id
      virtualCareAppointments(filterInput: $filterOptions) {
        totalCount
        list {
          ...VirtualCareAppointment
        }
      }
    }
  }
  ${VIRTUAL_CARE_APPOINTMENT_FIELDS}
`;

export const GET_VIRTUAL_CARE_APPOINTMENT = gql`
  query GetVirtualCareAppointment($id: String!) {
    virtualCareAppointment(id: $id) {
      ...VirtualCareAppointment
    }
  }
  ${VIRTUAL_CARE_APPOINTMENT_FIELDS}
`;

export const UPDATE_VIRTUAL_CARE_APPOINTMENT = gql`
  mutation UpdateVirtualCareAppointment($id: String!, $input: EditVirtualCareAppointmentInput!) {
    updateVirtualCareAppointment(id: $id, input: $input) {
      ...VirtualCareAppointment
    }
  }
  ${VIRTUAL_CARE_APPOINTMENT_FIELDS}
`;

export const DELETE_VIRTUAL_CARE_APPOINTMENTS = gql`
  mutation DeleteVirtualCareAppointments($ids: [String!]!) {
    deleteVirtualCareAppointments(ids: $ids) {
      id
    }
  }
`;

export const ARCHIVE_VIRTUAL_CARE_APPOINTMENTS = gql`
  mutation ArchiveVirtualCareAppointments($ids: [String!]!, $archive: Boolean!) {
    archiveVirtualCareAppointments(ids: $ids, archive: $archive) {
      ...VirtualCareAppointment
    }
  }
  ${VIRTUAL_CARE_APPOINTMENT_FIELDS}
`;

export const VIRTUAL_CARE_APPOINTMENT_ADDED_SUBS = gql`
  subscription VirtualCareAppointmentAddedSubs($hospitalId: String, $profileId: String!) {
    VirtualCareAppointmentAdded(hospitalId: $hospitalId, profileId: $profileId) {
      ...VirtualCareAppointment
    }
  }
  ${VIRTUAL_CARE_APPOINTMENT_FIELDS}
`;

export const VIRTUAL_CARE_APPOINTMENT_UPDATED_SUBS = gql`
  subscription VirtualCareAppointmentUpdatedSubs($hospitalId: String, $profileId: String!) {
    VirtualCareAppointmentUpdated(hospitalId: $hospitalId, profileId: $profileId) {
      ...VirtualCareAppointment
    }
  }
  ${VIRTUAL_CARE_APPOINTMENT_FIELDS}
`;

export const VIRTUAL_CARE_APPOINTMENT_REMOVED_SUBS = gql`
  subscription VirtualCareAppointmentRemovedSubs($hospitalId: String, $profileId: String!) {
    VirtualCareAppointmentRemoved(hospitalId: $hospitalId, profileId: $profileId) {
      id
    }
  }
`;

export const VIRTUAL_CARE_APPOINTMENT_ARCHIVED_SUBS = gql`
  subscription VirtualCareAppointmentArchivedSubs($hospitalId: String, $profileId: String!) {
    VirtualCareAppointmentArchived(hospitalId: $hospitalId, profileId: $profileId) {
      ...VirtualCareAppointment
    }
  }
  ${VIRTUAL_CARE_APPOINTMENT_FIELDS}
`;

export const VIRTUAL_CARE_APPOINTMENT_UNARCHIVED_SUBS = gql`
  subscription VirtualCareAppointmentUnarchivedSubs($hospitalId: String, $profileId: String!) {
    VirtualCareAppointmentArchived(hospitalId: $hospitalId, profileId: $profileId) {
      ...VirtualCareAppointment
    }
  }
  ${VIRTUAL_CARE_APPOINTMENT_FIELDS}
`;

export const APPOINTMENT_CONFIRMATION_SUBS = gql`
  subscription AppointmentConfirmationSubs($hospitalId: String!, $profileId: String!) {
    AppointmentConfirmation(hospitalId: $hospitalId, profileId: $profileId) {
      id
      hospitalId
      profileId
      patientConfirmation
    }
  }
`;

export const GET_APPOINTMENT_SERVICE_TYPES = gql`
  query GetAppointmentServiceTypes($dateRange: DateRangeInput) {
    getAppointmentServiceTypes(dateRange: $dateRange)
  }
`;
