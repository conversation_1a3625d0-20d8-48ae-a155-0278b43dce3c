import { gql } from '@apollo/client';
import { PREAUTHORIZATION_DETAILS_FRAGMENT } from 'apollo-queries/fragments/preauthorization-details';
import { AUDIT_FIELDS } from '../fragments/audit';

export const REQUEST_PROCEDURE_FIELDS = gql`
  fragment RequestProcedure on RequestProcedureModel {
    id
    surgeryDate
    duration
    patientConsentSignature
    patientConsentSignatureType
    patientConsentSignatureDateTime
    procedureType {
      ref
      type
      priority
      provider
      itemId
      preauthorizationDetailsId
    }
    rank
    reason
    requestedBy
    specialty
    facilityName
    facilityAddress
    operatedBy
    patientConsent
    serviceDetails {
      priceId
      type
      name
      quantity
      pricePerUnit
      itemId
      patientType
      paymentType
      reference
    }
    isPackage
    documentUrl
    surgeryStartDate
    surgeryEndDate
    appointmentId
    createdDate
    updatedDate
    department
    billStatus
    hospitalId
    profileId
    profile {
      id
      clinifyId
      coverageDetails {
        id
        name
        coverageType
        companyName
        familyName
        memberNumber
        provider {
          id
          name
        }
      }
    }
    preauthorizationDetails {
      ...PreauthorizationDetails
    }
  }
  ${PREAUTHORIZATION_DETAILS_FRAGMENT}
`;

export const GET_REQUEST_PROCEDURES = gql`
  query GetRequestProcedures($filterOptions: RequestProcedureFilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      requestProcedures(filterOptions: $filterOptions) {
        totalCount
        list {
          ...RequestProcedure
          bill {
            id
            billStatus
            createdDate
          }
          billStatus
        }
      }
    }
  }
  ${REQUEST_PROCEDURE_FIELDS}
`;

export const ADD_REQUEST_PROCEDURE = gql`
  mutation AddRequestProcedure($input: NewRequestProcedureInput!, $id: String, $pin: String) {
    addRequestProcedure(input: $input, id: $id, pin: $pin) {
      ...RequestProcedure
      bill {
        id
        billStatus
        createdDate
      }
      billStatus
    }
  }
  ${REQUEST_PROCEDURE_FIELDS}
`;

export const UPDATE_REQUEST_PROCEDURE = gql`
  mutation UpdateRequestProcedure($input: RequestProcedureInput!, $id: String!, $pin: String) {
    updateRequestProcedure(input: $input, id: $id, pin: $pin) {
      ...RequestProcedure
      ${AUDIT_FIELDS}
    }
  }
  ${REQUEST_PROCEDURE_FIELDS}
`;

export const UPDATE_REQUEST_PROCEDURE_BILL = gql`
  mutation UpdateRequestProcedureBill($input: RequestProcedureInput!, $id: String!) {
    updateRequestProcedureBill(input: $input, id: $id) {
      ...RequestProcedure
      ${AUDIT_FIELDS}
    }
  }
  ${REQUEST_PROCEDURE_FIELDS}
`;

export const DELETE_REQUEST_PROCEDURE = gql`
  mutation DeleteRequestProcedures($ids: [String!]!, $clinifyId: String!) {
    deleteRequestProcedures(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const ARCHIVE_REQUEST_PROCEDURE = gql`
  mutation ArchiveRequestProcedures($ids: [String!]!, $archive: Boolean, $clinifyId: String!) {
    archiveRequestProcedures(ids: $ids, archive: $archive, clinifyId: $clinifyId) {
      ...RequestProcedure
    }
  }
  ${REQUEST_PROCEDURE_FIELDS}
`;

export const GET_REQUEST_PROCEDURE = gql`
  query GetRequestProcedure($id: String!, $clinifyId: String!) {
    requestProcedure(id: $id, clinifyId: $clinifyId) {
      ...RequestProcedure
      ${AUDIT_FIELDS}
    }
  }
  ${REQUEST_PROCEDURE_FIELDS}
`;

export const GET_REQUEST_PROCEDURE_CLAIM = gql`
  query RequestProcedureClaim($id: String!, $clinifyId: String!) {
    requestProcedure(id: $id, clinifyId: $clinifyId) {
      id
      hmoClaim {
        id
      }
    }
  }
`;

export const REQUEST_PROCEDURE_ADDED_SUBS = gql`
  subscription RequestProcedureAddedSubs($profileId: String!) {
    RequestProcedureAdded(profileId: $profileId) {
      ...RequestProcedure
      bill {
        id
        billStatus
        createdDate
      }
      billStatus
      ${AUDIT_FIELDS}
    }
  }
  ${REQUEST_PROCEDURE_FIELDS}
`;

export const REQUEST_PROCEDURE_UPDATED_SUBS = gql`
  subscription RequestProcedureUpdatedSubs($profileId: String!) {
    RequestProcedureUpdated(profileId: $profileId) {
      ...RequestProcedure
      ${AUDIT_FIELDS}
    }
  }
  ${REQUEST_PROCEDURE_FIELDS}
`;

export const DELETE_REQUEST_PROCEDURE_SUBS = gql`
  subscription DeleteProcedureRemovedSubs($profileId: String!) {
    RequestProcedureRemoved(profileId: $profileId) {
      id
    }
  }
`;

export const ARCHIVE_REQUEST_PROCEDURE_SUBS = gql`
  subscription ArchiveRequestProcedureSubs($profileId: String!) {
    RequestProcedureArchived(profileId: $profileId) {
      ...RequestProcedure
    }
  }
  ${REQUEST_PROCEDURE_FIELDS}
`;

export const UNARCHIVE_REQUEST_PROCEDURE_SUBS = gql`
  subscription UnarchiveRequestProcedureSubs($profileId: String!) {
    RequestProcedureUnarchived(profileId: $profileId) {
      ...RequestProcedure
    }
  }
  ${REQUEST_PROCEDURE_FIELDS}
`;
