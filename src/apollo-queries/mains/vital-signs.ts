import { gql } from '@apollo/client';
import { AUDIT_FIELDS, AUDIT_FIELDS_WITH_ID } from '../fragments/audit';
import { ANTHROPOMETRY_FIELDS } from '../subs/anthropometry';
import { BLOOD_GLUCOSE_FIELDS } from '../subs/bloodGlucose';
import { BLOOD_PRESSURE_FIELDS } from '../subs/bloodPressure';
import { PULSE_RATE_FIELDS } from '../subs/pulseRate';
import { RESPIRATORY_RATE_FIELDS } from '../subs/respiratoryRate';
import { TEMPERATURE_FIELDS } from '../subs/temperature';
import { URINE_DIPSTICK_FIELDS } from '../subs/urineDipstick';
import { VISUAL_ACUITY_FIELDS } from '../subs/visualAcuity';
import { PAIN_FIELDS } from '../subs/pain';

export const VITAL_FIELDS = gql`
  fragment Vital on VitalModel {
    id
    hospitalName
    hospitalAddress
    documentUrl
    createdDate
    updatedDate
    profileId
    profile {
      id
      clinifyId
    }
  }
`;

export const GET_PATIENT_VITAL_SIGNS_LIST = gql`
  query GetPatientVitalSignsList($filterOptions: VitalFilterInput, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      vitals(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Vital
          anthropometry {
            ...Anthropometry
            ${AUDIT_FIELDS}
          }
          bloodGlucose {
            ...BloodGlucose
            ${AUDIT_FIELDS}
          }
          bloodPressure {
            ...BloodPressure
            ${AUDIT_FIELDS}
          }
          pulseRate {
            ...PulseRate
            ${AUDIT_FIELDS}
          }
          respiratoryRate {
            ...RespiratoryRate
            ${AUDIT_FIELDS}
          }
          temperature {
            ...Temperature
            ${AUDIT_FIELDS}
          }
          visualAcuity {
            ...VisualAcuity
            ${AUDIT_FIELDS}
          }
          urineDipstick {
            ...UrineDipstick
            ${AUDIT_FIELDS}
          }
          pain {
            ...Pain
            ${AUDIT_FIELDS}
          }
          createdDate
        }
      }
    }
  }
  ${VITAL_FIELDS}
  ${ANTHROPOMETRY_FIELDS}
  ${BLOOD_PRESSURE_FIELDS}
  ${BLOOD_GLUCOSE_FIELDS}
  ${TEMPERATURE_FIELDS}
  ${PULSE_RATE_FIELDS}
  ${RESPIRATORY_RATE_FIELDS}
  ${TEMPERATURE_FIELDS}
  ${VISUAL_ACUITY_FIELDS}
  ${URINE_DIPSTICK_FIELDS}
  ${PAIN_FIELDS}
`;

export const ADD_VITAL_SIGNS = gql`
  mutation AddVital($input: NewVitalInput!, $id: String) {
    addVital(vital: $input, id: $id) {
      ...Vital
      anthropometry {
        ...Anthropometry
        ${AUDIT_FIELDS}
      }
      bloodGlucose {
        ...BloodGlucose
        ${AUDIT_FIELDS}
      }
      bloodPressure {
        ...BloodPressure
        ${AUDIT_FIELDS}
      }
      pulseRate {
        ...PulseRate
        ${AUDIT_FIELDS}
      }
      respiratoryRate {
        ...RespiratoryRate
        ${AUDIT_FIELDS}
      }
      temperature {
        ...Temperature
        ${AUDIT_FIELDS}
      }
      visualAcuity {
        ...VisualAcuity
        ${AUDIT_FIELDS}
      }
      urineDipstick {
        ...UrineDipstick
        ${AUDIT_FIELDS}
      }
      createdDate
    }
  }
  ${VITAL_FIELDS}
  ${ANTHROPOMETRY_FIELDS}
  ${BLOOD_PRESSURE_FIELDS}
  ${BLOOD_GLUCOSE_FIELDS}
  ${TEMPERATURE_FIELDS}
  ${PULSE_RATE_FIELDS}
  ${RESPIRATORY_RATE_FIELDS}
  ${TEMPERATURE_FIELDS}
  ${VISUAL_ACUITY_FIELDS}
  ${URINE_DIPSTICK_FIELDS}
`;

export const UPDATE_VITAL_SIGNS = gql`
  mutation UpdateVital($input: UpdateVitalInput!, $id: String!) {
    updateVital(id: $id, vital: $input) {
      ...Vital
      ${AUDIT_FIELDS}
    }
  }
  ${VITAL_FIELDS}
`;

export const DELETE_VITAL_SIGNS = gql`
  mutation DeleteVitals($ids: [String!]!, $clinifyId: String!) {
    deleteVitals(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const ARCHIVE_VITAL_SIGNS = gql`
  mutation ArchiveVitals($ids: [String!]!, $archive: Boolean, $clinifyId: String!) {
    archiveVitals(ids: $ids, archive: $archive, clinifyId: $clinifyId) {
      ...Vital
      ${AUDIT_FIELDS}
    }
  }
  ${VITAL_FIELDS}
`;

export const GET_VITAL_SIGN = gql`
  query GetVitalList($id: String!, $clinifyId: String!) {
    vital(id: $id, clinifyId: $clinifyId) {
      ...Vital
      ${AUDIT_FIELDS_WITH_ID}
    }
  }
  ${VITAL_FIELDS}
`;

export const GET_ALL_VITAL_SIGN = gql`
  query GetAllVitals($id: String!, $clinifyId: String!) {
    getVitalById(id: $id, clinifyId: $clinifyId) {
      id
      anthropometry {
        readingDateTime
        id
        height
        heightUnit
        weight
        weightUnit
        bmi
        bsa
        creatorName
        isHeightCritical
        isWeightCritical
        additionalNote
      }
      bloodPressure {
        id
        readingDateTime
        systolic
        diastolic
        meanArterialPressure
        heartRate
        fetalHeartRate
        creatorName
        isSystolicCritical
        isDiastolicCritical
        isHeartRateCritical
        isFetalHeartRateCritical
        additionalNote
      }
      temperature {
        id
        reading
        readingUnit
        readingDateTime
        creatorName
        isReadingCritical
        additionalNote
      }
      pulseRate {
        id
        reading
        readingDateTime
        creatorName
        isReadingCritical
        additionalNote
      }
      respiratoryRate {
        id
        reading
        readingDateTime
        oxygenSaturation
        creatorName
        isReadingCritical
        isOxygenSaturationCritical
        additionalNote
      }
      bloodGlucose {
        id
        reading
        readingUnit
        readingDateTime
        creatorName
        isReadingCritical
        additionalNote
      }
      pain {
        id
        dateTimePainStarted
        score
        creatorName
        additionalNote
      }
    }
  }
`;

const ALL_VITALS_FRAGMENT = gql`
  fragment AllVitalsFragment on AllVitalResponse {
    vitalId
    creatorId
    anthropometry {
      ...Anthropometry
      creatorName
      ${AUDIT_FIELDS}
    }
    bloodPressure {
      ...BloodPressure
      creatorName
      ${AUDIT_FIELDS}
    }
    pulseRate {
      ...PulseRate
      creatorName
      ${AUDIT_FIELDS}
    }
    respiratoryRate {
      ...RespiratoryRate
      creatorName
      ${AUDIT_FIELDS}
    }
    temperature {
      ...Temperature
      creatorName
      ${AUDIT_FIELDS}
    }
    bloodGlucose {
      ...BloodGlucose
      creatorName
      ${AUDIT_FIELDS}
    }
    pain {
      ...Pain
      creatorName
      ${AUDIT_FIELDS}
    }
  }
  ${ANTHROPOMETRY_FIELDS}
  ${BLOOD_PRESSURE_FIELDS}
  ${PULSE_RATE_FIELDS}
  ${RESPIRATORY_RATE_FIELDS}
  ${TEMPERATURE_FIELDS}
  ${BLOOD_GLUCOSE_FIELDS}
  ${PAIN_FIELDS}
`;

export const INSERT_ALL_VITALS = gql`
  mutation InsertAllVitals($id: String!, $input: AllVitalsInput!, $clinifyId: String!) {
    insertAllVitals(vitalId: $id, input: $input, clinifyId: $clinifyId) {
      ...AllVitalsFragment
    }
  }
  ${ALL_VITALS_FRAGMENT}
`;

export const UPDATE_ALL_VITALS = gql`
  mutation UpdateAllVitals($id: String!, $input: AllVitalsInput!, $clinifyId: String!) {
    editAllVitals(vitalId: $id, input: $input, clinifyId: $clinifyId) {
      ...AllVitalsFragment
    }
  }
  ${ALL_VITALS_FRAGMENT}
`;

export const DELETE_ALL_VITALS = gql`
  mutation DeleteAllVitals($id: String!, $input: DeleteAllVitalsInput!, $clinifyId: String!) {
    deleteAllVitals(vitalId: $id, input: $input, clinifyId: $clinifyId) {
      anthropometryId
      bloodGlucoseId
      bloodPressureId
      pulseRateId
      respiratoryRateId
      temperatureId
      vitalsId
    }
  }
`;

export const ALL_VITALS_INSERTED_SUBS = gql`
  subscription AllVitalsAddedSubs($profileId: String!) {
    AllVitalsInserted(profileId: $profileId) {
      ...AllVitalsFragment
    }
  }
  ${ALL_VITALS_FRAGMENT}
`;

export const ALL_VITALS_UPDATED_SUBS = gql`
  subscription AllVitalsUpdatedSubs($profileId: String!) {
    AllVitalsUpdated(profileId: $profileId) {
      ...AllVitalsFragment
    }
  }
  ${ALL_VITALS_FRAGMENT}
`;

export const ALL_VITALS_REMOVED_SUBS = gql`
  subscription AllVitalsRemovedSubs($profileId: String!) {
    AllVitalsRemoved(profileId: $profileId) {
      vitalsId
      temperatureId
      respiratoryRateId
      pulseRateId
      bloodPressureId
      bloodGlucoseId
      anthropometryId
      painId
    }
  }
`;

export const VITAL_ADDED_SUBS = gql`
  subscription VitalAddedSubs($profileId: String!) {
    VitalsAdded(profileId: $profileId) {
      ...Vital
      anthropometry {
        ...Anthropometry
        ${AUDIT_FIELDS}
      }
      bloodGlucose {
        ...BloodGlucose
        ${AUDIT_FIELDS}
      }
      bloodPressure {
        ...BloodPressure
        ${AUDIT_FIELDS}
      }
      pulseRate {
        ...PulseRate
        ${AUDIT_FIELDS}
      }
      respiratoryRate {
        ...RespiratoryRate
        ${AUDIT_FIELDS}
      }
      temperature {
        ...Temperature
        ${AUDIT_FIELDS}
      }
      visualAcuity {
        ...VisualAcuity
        ${AUDIT_FIELDS}
      }
      urineDipstick {
        ...UrineDipstick
        ${AUDIT_FIELDS}
      }
      createdDate
    }
  }
  ${VITAL_FIELDS}
  ${ANTHROPOMETRY_FIELDS}
  ${BLOOD_PRESSURE_FIELDS}
  ${BLOOD_GLUCOSE_FIELDS}
  ${TEMPERATURE_FIELDS}
  ${PULSE_RATE_FIELDS}
  ${RESPIRATORY_RATE_FIELDS}
  ${TEMPERATURE_FIELDS}
  ${VISUAL_ACUITY_FIELDS}
  ${URINE_DIPSTICK_FIELDS}
`;

export const VITAL_UPDATED_SUBS = gql`
  subscription VitalUpdatedSubs($profileId: String!) {
    VitalsUpdated(profileId: $profileId) {
      ...Vital
      ${AUDIT_FIELDS}
    }
  }
  ${VITAL_FIELDS}
`;

export const DELETE_VITAL_SUBS = gql`
  subscription DeleteVitalSubs($profileId: String!) {
    VitalsRemoved(profileId: $profileId) {
      id
    }
  }
`;

export const ARCHIVE_VITAL_SUBS = gql`
  subscription ArchiveVitalSubs($profileId: String!) {
    VitalsArchived(profileId: $profileId) {
      ...Vital
    }
  }
  ${VITAL_FIELDS}
`;

export const UNARCHIVE_VITAL_SUBS = gql`
  subscription UnarchiveVitalSubs($profileId: String!) {
    VitalsUnarchived(profileId: $profileId) {
      ...Vital
    }
  }
  ${VITAL_FIELDS}
`;
