import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from 'apollo-queries/fragments/audit';

export const SUPPLY_FIELDS = gql`
  fragment Supply on SupplyModel {
    id
    items {
      id
      name
      type
      addedBy
      category
      addedDateTime
      size
      description
      code
      batchNumber
      totalQuantity
      quantityRemaining
      quantityOutstanding
      quantityTransferred
      quantityReturned
      quantityConsumed
      quantityRecalled
      quantityDestroyed
    }
  }
`;

export const ITEM_FIELDS = gql`
  fragment items on SupplyItemModel {
    id
    name
    type
    addedBy
    category
    addedDateTime
    size
    description
    code
    batchNumber
    totalQuantity
    quantityRemaining
    quantityOutstanding
    quantityTransferred
    quantityReturned
    quantityConsumed
    quantityRecalled
    quantityDestroyed
  }
`;

export const FETCH_SUPPLIES = gql`
  query FetchSupplies($filterOptions: SupplyFilter) {
    hospital {
      id
      supplies(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Supply
        }
      }
    }
  }
  ${SUPPLY_FIELDS}
`;

export const FETCH_SUPPLY = gql`
  query FetchSupply($id: String!) {
    supply(id: $id) {
      ...Supply
      items {
        ${AUDIT_FIELDS}
        code
      }
      ${AUDIT_FIELDS}
    }
  }
  ${SUPPLY_FIELDS}
`;

export const ADD_SUPPLY = gql`
  mutation AddSupply($input: AddSupplyInput!) {
    addSupply(input: $input) {
      ...Supply
    }
  }
  ${SUPPLY_FIELDS}
`;

export const ADD_SUPPLY_ITEM = gql`
  mutation AddSupplyItem($input: SupplyItemInput!, $supplyId: String!) {
    addSupplyItem(input: $input, supplyId: $supplyId) {
      ...items
    }
  }
  ${ITEM_FIELDS}
`;

export const UPDATE_SUPPLY_ITEM = gql`
  mutation UpdateSupplyItem($id: String!, $input: SupplyItemInput! ) {
    updateSupplyItem(id: $id, input: $input ) {
      ...items
      ${AUDIT_FIELDS}
    }
  }
  ${ITEM_FIELDS}
`;

export const UPDATE_SUPPLY = gql`
  mutation UpdateSupply($input: SupplyItemInput!, $id: String!) {
    updateSupplyItem(id: $id, input: $input ) {
      ...items
      ${AUDIT_FIELDS}
    }
  }
  ${SUPPLY_FIELDS}
`;

export const DELETE_SUPPLIES = gql`
  mutation DeleteSupplies($ids: [String!]!) {
    deleteSupplies(ids: $ids) {
      id
    }
  }
`;

export const DELETE_SUPPLY_ITEM = gql`
  mutation DeleteSupplyItem($id: String!) {
    deleteSupplyItem(id: $id) {
      id
    }
  }
`;

export const ARCHIVE_SUPPLIES = gql`
  mutation ArchiveSupplies($ids: [String!]!, $archive: Boolean!) {
    archiveSupplies(ids: $ids, archive: $archive) {
      ...Supply
    }
  }
  ${SUPPLY_FIELDS}
`;

export const FETCH_SUPPLY_ITEM = gql`
  query SupplyItem($id: String!) {
    supply(id: $id) {
      ...Supply
    }
  }
  ${ITEM_FIELDS}
`;
