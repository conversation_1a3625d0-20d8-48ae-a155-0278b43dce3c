import { gql } from '@apollo/client';

const INVOICE_PAYMENT_FRAGMENT = gql`
  fragment InvoicePaymentFields on InvoicePaymentModel {
    id
    invoiceId
    amountPaid
    paymentMethod
    paymentStatus
    amountDue
    commissionFeeAmount
    commissionFeePercentage
    createdDate
    updatedDate
    payoutStatus
    commissionPayer
    discountAmount
  }
`;

export const INVOICE_ITEM_FIELDS = gql`
  fragment InvoiceItemFields on InvoiceItemModel {
    id
    description
    updatedDate
    createdDate
    invoiceType
    discountAmount
    discountPercentage
    invoiceId
    quantity
    unitPrice
  }
`;

export const INVOICE_FIELDS = gql`
  fragment InvoiceFields on InvoiceModel {
    id
    createdDate
    updatedDate
    discountPercentage
    discountAmount
    profileId
    employerId
    creatorId
    description
    subTotal
    totalAmount
    createdBy {
      id
      fullName
      clinifyId
      type
    }
    lastModifierId
    updatedBy {
      id
      fullName
      clinifyId
      type
    }
    additionalNote
    amountPaid
    bankTransactionIds
    dueDate
    invoiceReference
    invoiceStatus
    issueDate
    paymentDate
    sponsorName
    sponsorRef
    nextYearlyPremium
    sponsorLivesCovered
    agencyLivesCovered
    sponsorPremiumPerLives
    recipient {
      address
      email
      name
      phone
      clinifyId
    }
    senderHospitalId
    senderHospital {
      id
      name
      address
      facilityLogo
      website
    }
    professionalFeeAmount
    professionalFeePercentage
    controlledDiscount
    controlledProfessionalFee
    vatPercentage
    vatAmount
    controlledVat
    virtualAccount {
      id
      currency
      updatedDate
      createdDate
      accountName
      accountNumber
      bank
      expiryDate
    }
    employeesDetails {
      enrolleeId
      fullName
      planCategory
      planType
      planAmount
      paymentFrequency
      planStartDate
      planDueDate
      planStatus
      isCovered
      dependents {
        fullName
        enrolleeId
        relationship
      }
    }
    sponsorDetails {
      status
      fullName
      isCovered
      memberNumber
      planDueDate
      planStartDate
    }
    paymentFrequency
    plasticIdCardCount
    plasticIdCardAmount
    laminatedIdCardCount
    laminatedIdCardAmount
    creatorName
    lastModifierName
    periodStartDate
    periodEndDate
    nextYearlyPremium
  }
`;

export const GET_INVOICE = gql`
  query GetInvoice($id: String!) {
    getInvoice(id: $id) {
      ...InvoiceFields
      employer {
        id
        employerName
        employees {
          id
          firstName
          middleName
          lastName
          planCategory
          hmoProfile {
            id
            memberNumber
            hmoPlanType {
              id
              premiumDetails {
                frequency
                category
                amount
              }
              status
            }
          }
        }
      }
      invoiceItems {
        ...InvoiceItemFields
        createdBy {
          id
          fullName
          type
        }
        updatedBy {
          id
          fullName
          type
        }
        creatorName
        lastModifierName
      }
      invoicePayments {
        ...InvoicePaymentFields
        createdBy {
          id
          fullName
          type
        }
        updatedBy {
          id
          fullName
          type
        }
        creatorName
        lastModifierName
      }
      createdBy {
        id
        fullName
        type
      }
      updatedBy {
        id
        fullName
        type
      }
      creatorName
      lastModifierName
    }
  }
  ${INVOICE_FIELDS}
  ${INVOICE_ITEM_FIELDS}
  ${INVOICE_PAYMENT_FRAGMENT}
`;

export const GET_HOSPITAL_INVOICES = gql`
  query GetHospitalInvoices($hospitalId: String, $filterOptions: InvoiceListFilterInput) {
    hospital(hospitalId: $hospitalId) {
      id
      invoices(filterOptions: $filterOptions) {
        totalCount
        list {
          ...InvoiceFields
          invoiceItems {
            ...InvoiceItemFields
          }
          employer {
            id
            employerName
            employees {
              id
              firstName
              middleName
              lastName
              planCategory
              paymentFrequency
              planStartDate
              planDueDate
              hmoPlanType {
                id
                premiumDetails {
                  frequency
                  category
                  amount
                }
              }
              hmoProfile {
                id
                memberNumber
                memberStatus
                planCategory
              }
              dependants {
                id
                firstName
                middleName
                lastName
                relationship
                hmoProfile {
                  id
                  memberNumber
                }
              }
            }
          }
          invoicePayments {
            id
            paymentStatus
            paymentMethod
            amountPaid
            payoutStatus
            amountDue
          }
          createdBy {
            id
            fullName
            type
          }
        }
      }
    }
  }
  ${INVOICE_FIELDS}
  ${INVOICE_ITEM_FIELDS}
`;

export const GET_PROFILE_INVOICE = gql`
  query GetProfileInvoice($filterOptions: InvoiceListFilterInput, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      invoices(filterOptions: $filterOptions) {
        totalCount
        list {
          ...InvoiceFields
          invoiceItems {
            ...InvoiceItemFields
          }
          invoicePayments {
            id
            paymentStatus
            paymentMethod
            amountPaid
            payoutStatus
          }
          createdBy {
            id
            fullName
            type
          }
        }
      }
    }
  }
  ${INVOICE_FIELDS}
  ${INVOICE_ITEM_FIELDS}
`;

export const CREATE_INVOICE = gql`
  mutation CreateInvoice($input: InvoiceInput!, $virtualAccountProvider: VirtualAccountProvider) {
    addInvoice(input: $input, virtualAccountProvider: $virtualAccountProvider) {
      ...InvoiceFields
      invoiceItems {
        ...InvoiceItemFields
      }
      createdBy {
        id
        fullName
        type
      }
    }
  }
  ${INVOICE_FIELDS}
  ${INVOICE_ITEM_FIELDS}
`;

export const EDIT_INVOICE = gql`
  mutation EditInvoice($id: String!, $input: EditInvoiceInput!) {
    updateInvoice(id: $id, input: $input) {
      ...InvoiceFields
      invoiceItems {
        ...InvoiceItemFields
        createdBy {
          id
          fullName
          type
        }
        updatedBy {
          id
          fullName
          type
        }
        creatorName
        lastModifierName
      }
      invoicePayments {
        ...InvoicePaymentFields
        createdBy {
          id
          fullName
          type
        }
        updatedBy {
          id
          fullName
          type
        }
        creatorName
        lastModifierName
      }
      lastModifierName
    }
  }
  ${INVOICE_FIELDS}
  ${INVOICE_ITEM_FIELDS}
  ${INVOICE_PAYMENT_FRAGMENT}
`;

export const FETCH_INVOICE_ITEMS = gql`
  query FetchInvoiceItems($id: String!) {
    getInvoiceItems(id: $id) {
      ...InvoiceItemFields
      createdBy {
        id
        fullName
        type
      }
      updatedBy {
        id
        fullName
        type
      }
      creatorName
      lastModifierName
    }
  }
  ${INVOICE_ITEM_FIELDS}
`;

export const ADD_INVOICE_ITEM = gql`
  mutation AddInvoiceItem($id: String!, $input: InvoiceItemInput!) {
    addInvoiceItem(invoiceId: $id, input: $input) {
      ...InvoiceItemFields
      createdBy {
        id
        fullName
        type
      }
      updatedBy {
        id
        fullName
        type
      }
      creatorName
      lastModifierName
    }
  }
  ${INVOICE_ITEM_FIELDS}
`;

export const EDIT_INVOICE_ITEM = gql`
  mutation EditInvoiceItem($id: String!, $input: InvoiceItemInput!) {
    editInvoiceItem(id: $id, input: $input) {
      ...InvoiceItemFields
      updatedBy {
        id
        fullName
        type
      }
    }
  }
  ${INVOICE_ITEM_FIELDS}
`;

export const DELETE_INVOICE_ITEM = gql`
  mutation DeleteInvoiceItem($id: String!) {
    deleteInvoiceItem(id: $id) {
      id
    }
  }
`;

export const ARCHIVE_INVOICES = gql`
  mutation ArchiveInvoices($ids: [String!]!, $archive: Boolean!) {
    archiveInvoices(ids: $ids, archive: $archive) {
      id
    }
  }
`;

export const DELETE_INVOICE = gql`
  mutation DeleteInvoice($ids: [String!]!) {
    deleteInvoices(ids: $ids) {
      id
    }
  }
`;

export const GET_INVOICES_SUMMARY = gql`
  query GetInvoicesSummary($filterOptions: InvoiceListFilterInput!) {
    getInvoiceListSummary(filterOptions: $filterOptions) {
      totalInvoices
      totalOverdue
      totalPaid
      totalPending
      totalDraft
    }
  }
`;

export const ATTACH_VIRTUAL_ACCOUNT_TO_INVOICE = gql`
  mutation AttachVirtualAccountToInvoice(
    $id: String!
    $virtualAccountProvider: VirtualAccountProvider
  ) {
    attachVirtualAccountToInvoice(id: $id, virtualAccountProvider: $virtualAccountProvider) {
      ...InvoiceFields
    }
  }
  ${INVOICE_FIELDS}
`;

export const CANCEL_INVOICE = gql`
  mutation CancelInvoice($id: String!) {
    cancelInvoice(id: $id) {
      id
      invoiceStatus
    }
  }
`;

export const MARK_CANCELLED_INVOICE_AS_PENDING = gql`
  mutation MarkCancelledInvoiceAsPending($id: String!) {
    markCancelledInvoiceAsPending(id: $id) {
      id
      invoiceStatus
    }
  }
`;

export const SEND_INVOICE_TO_RECIPIENT = gql`
  mutation SendInvoiceToRecipient($id: String!) {
    sendInvoiceToRecipientEmail(id: $id) {
      sent
    }
  }
`;

export const ADD_INVOICE_PAYMENT_METHOD = gql`
  mutation AddInvoicePaymentMethod($id: String!, $input: InvoicePaymentInput!) {
    addInvoicePaymentMethod(invoiceId: $id, input: $input) {
      ...InvoicePaymentFields
      createdBy {
        id
        fullName
        type
      }
      updatedBy {
        id
        fullName
        type
      }
      creatorName
      lastModifierName
    }
  }
  ${INVOICE_PAYMENT_FRAGMENT}
`;

export const UPDATE_INVOICE_PAYMENT_METHOD = gql`
  mutation UpdateInvoicePaymentMethod($id: String!, $input: InvoicePaymentInput!) {
    updateInvoicePaymentMethod(id: $id, input: $input) {
      ...InvoicePaymentFields
      createdBy {
        id
        fullName
        type
      }
      creatorName
      updatedBy {
        id
        fullName
        type
      }
      lastModifierName
    }
  }
  ${INVOICE_PAYMENT_FRAGMENT}
`;

export const DELETE_INVOICE_PAYMENT_METHOD = gql`
  mutation DeleteInvoicePaymentMethod($id: String!) {
    deleteInvoicePaymentMethod(id: $id) {
      id
    }
  }
`;

export const ADD_INVOICE_SUBS = gql`
  subscription InvoiceAddSubs($hospitalId: String!, $profileId: String!) {
    InvoiceAdded(hospitalId: $hospitalId, profileId: $profileId) {
      ...InvoiceFields
      invoiceItems {
        ...InvoiceItemFields
      }
      invoicePayments {
        id
        paymentStatus
        paymentMethod
        amountPaid
        payoutStatus
      }
      createdBy {
        id
        fullName
        type
      }
    }
  }
  ${INVOICE_FIELDS}
  ${INVOICE_ITEM_FIELDS}
`;

export const UPDATE_INVOICE_SUBS = gql`
  subscription InvoiceUpdateSubs($hospitalId: String!, $profileId: String!) {
    InvoiceUpdated(hospitalId: $hospitalId, profileId: $profileId) {
      ...InvoiceFields
      invoiceItems {
        ...InvoiceItemFields
      }
      lastModifierName
    }
  }
  ${INVOICE_FIELDS}
  ${INVOICE_ITEM_FIELDS}
`;

export const UPDATE_INVOICE_STATUS_SUBS = gql`
  subscription InvoiceStatusUpdatedSubs($hospitalId: String!, $profileId: String!) {
    InvoiceStatusUpdated(hospitalId: $hospitalId, profileId: $profileId) {
      id
      invoiceStatus
      virtualAccountId
      virtualAccount {
        id
        bank
        accountName
        accountNumber
      }
    }
  }
`;

export const INVOICE_ACCOUNT_ADDED_SUBS = gql`
  subscription InvoiceAccountAddedSubs($hospitalId: String!, $profileId: String!) {
    InvoiceAccountAdded(hospitalId: $hospitalId, profileId: $profileId) {
      id
      issueDate
      invoiceStatus
      virtualAccountId
      virtualAccount {
        id
        currency
        updatedDate
        createdDate
        accountName
        accountNumber
        bank
        expiryDate
      }
    }
  }
`;

export const DELETE_INVOICE_SUBS = gql`
  subscription InvoiceDeletedSubs($hospitalId: String!, $profileId: String!) {
    InvoiceRemoved(hospitalId: $hospitalId, profileId: $profileId) {
      id
    }
  }
`;

export const ARCHIVE_INVOICE_SUBS = gql`
  subscription ArchiveInvoiceSubs($hospitalId: String!, $profileId: String!) {
    InvoiceArchived(hospitalId: $hospitalId, profileId: $profileId) {
      ...InvoiceFields
      invoiceItems {
        ...InvoiceItemFields
        createdBy {
          id
          fullName
          type
        }
        updatedBy {
          id
          fullName
          type
        }
        creatorName
        lastModifierName
      }
      createdBy {
        id
        fullName
        type
      }
      updatedBy {
        id
        fullName
        type
      }
      creatorName
      lastModifierName
    }
  }
  ${INVOICE_FIELDS}
  ${INVOICE_ITEM_FIELDS}
`;

export const UNARCHIVE_INVOICE_SUBS = gql`
  subscription UnarchiveInvoiceSubs($hospitalId: String!, $profileId: String!) {
    InvoiceUnarchived(hospitalId: $hospitalId, profileId: $profileId) {
      ...InvoiceFields
      invoiceItems {
        ...InvoiceItemFields
        createdBy {
          id
          fullName
          type
        }
        updatedBy {
          id
          fullName
          type
        }
        creatorName
        lastModifierName
      }
      createdBy {
        id
        fullName
        type
      }
      updatedBy {
        id
        fullName
        type
      }
      creatorName
      lastModifierName
    }
  }
  ${INVOICE_FIELDS}
  ${INVOICE_ITEM_FIELDS}
`;

export const ADD_INVOICE_ITEM_SUBS = gql`
  subscription AddInvoiceItemSubs($hospitalId: String!, $profileId: String!) {
    InvoiceItemAdded(hospitalId: $hospitalId, profileId: $profileId) {
      ...InvoiceItemFields
      createdBy {
        id
        fullName
        type
      }
      updatedBy {
        id
        fullName
        type
      }
      creatorName
      lastModifierName
    }
  }
  ${INVOICE_ITEM_FIELDS}
`;

export const UPDATE_INVOICE_ITEMS_SUBS = gql`
  subscription UpdateInvoiceItemSubs($hospitalId: String!, $profileId: String!) {
    InvoiceItemUpdated(hospitalId: $hospitalId, profileId: $profileId) {
      ...InvoiceItemFields
      createdBy {
        id
        fullName
        type
      }
      updatedBy {
        id
        fullName
        type
      }
      creatorName
      lastModifierName
    }
  }
  ${INVOICE_ITEM_FIELDS}
`;

export const DELETE_INVOICE_ITEMS_SUBS = gql`
  subscription DeleteInvoiceItemSubs($hospitalId: String!, $profileId: String!) {
    InvoiceItemRemoved(hospitalId: $hospitalId, profileId: $profileId) {
      id
      invoiceId
    }
  }
`;

export const ADD_INVOICE_PAYMENT_SUBS = gql`
  subscription AddInvoicePaymentSubs($hospitalId: String!, $profileId: String!) {
    InvoicePaymentAdded(hospitalId: $hospitalId, profileId: $profileId) {
      ...InvoicePaymentFields
      createdBy {
        id
        fullName
        type
      }
      updatedBy {
        id
        fullName
        type
      }
      creatorName
      lastModifierName
    }
  }
  ${INVOICE_PAYMENT_FRAGMENT}
`;

export const UPDATE_INVOICE_PAYMENT_SUBS = gql`
  subscription UpdateInvoicePaymentSubs($hospitalId: String!, $profileId: String!) {
    InvoicePaymentUpdated(hospitalId: $hospitalId, profileId: $profileId) {
      ...InvoicePaymentFields
      createdBy {
        id
        fullName
        type
      }
      updatedBy {
        id
        fullName
        type
      }
      creatorName
      lastModifierName
    }
  }
  ${INVOICE_PAYMENT_FRAGMENT}
`;

export const DELETE_INVOICE_PAYMENT_SUBS = gql`
  subscription DeleteInvoicePaymentSubs($hospitalId: String!, $profileId: String!) {
    InvoicePaymentRemoved(hospitalId: $hospitalId, profileId: $profileId) {
      id
      invoiceId
    }
  }
`;

export const PAY_INVOICE_WITH_WALLET = gql`
  mutation PayInvoiceWithWallet($invoiceId: String!, $passcode: String!) {
    payInvoiceWithWallet(invoiceId: $invoiceId, passcode: $passcode) {
      invoicePayments
      success
    }
  }
`;

export const PAY_INVOICE_WITH_WALLET_SUBS = gql`
  subscription PayInvoiceWithWalletSubs($profileId: String!) {
    PayInvoiceWithWallet(profileId: $profileId) {
      invoicePayments
      success
    }
  }
`;

export const UPDATE_EMPLOYEES_COVERED_IN_INVOICE = gql`
  mutation UpdateEmployeesCoveredInInvoice(
    $id: String!
    $enrolleeIds: [String!]!
    $isCovered: Boolean!
  ) {
    updateEmployeesDetails(invoiceId: $id, enrolleeIds: $enrolleeIds, isCovered: $isCovered) {
      id
      employeesDetails {
        enrolleeId
        fullName
        planCategory
        planType
        planAmount
        paymentFrequency
        planStartDate
        planDueDate
        planStatus
        isCovered
        dependents {
          fullName
          enrolleeId
          relationship
        }
      }
    }
  }
`;

export const GET_SPONSORED_ENROLLEES = gql`
  query GetSponsoredHmoProfiles($sponsorName: String!) {
    getSponsoredHmoProfiles(sponsorName: $sponsorName) {
      fullName
      isCovered
      memberNumber
      planDueDate
      planStartDate
      status
    }
  }
`;
