import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

const QUESTIONNAIRE_DATA_FRAGMENTS = gql`
  fragment QuestionnaireDataFragment on QuestionnaireData {
    employmentStatus
    gender
    highestFormalEducationalLevel
    maritalStatus
    numberOfHouseholdMembers
    numberOfMattresses
    numberOfMobilePhones
    numberOfRooms
    numberOfTVs
    numberOfVehicles
    occupationalGroup
    occupation
    primaryCookingImplement
    relationshipToHouseholdHead
    typeOfRoof
    typeOfToilet
    questionnaireScore
  }
`;

const HMO_FIELDS = gql`
  fragment HmoField on HmoProfileModel {
    id
    memberNumber
    memberPlan
    employeeNumber
    memberStartDate
    memberStatus
    companyName
    companyAddress
    primaryProviderName
    secondaryProviderName
    tertiaryProviderName
    primaryProviderAddress
    secondaryProviderAddress
    tertiaryProviderAddress
    memberUniqueId
    parentMemberUniqueId
    memberPlanId
    provider {
      id
      name
    }
    profile {
      id
      clinifyId
    }
    ${AUDIT_FIELDS}
  }
`;

const HMO_FIELDS_NAME_ONLY = gql`
  fragment HmoFieldNameOnly on HmoProfileModel {
    id
    provider {
      id
      name
    }
    memberNumber
    memberUniqueId
    memberPlan
    memberPlanId
    primaryProviderId
    primaryProviderName
  }
`;

export const GET_HMOS = gql`
  query GetUserHmos($filterInput: HmoProfileFilterInput, $id: String!) {
    profile(clinifyId: $id) {
      id
      hmos(filterOptions: $filterInput) {
        totalCount
        list {
          ...HmoField
        }
      }
    }
  }
  ${HMO_FIELDS}
`;

export const GET_HMOS_WITH_PLAN_ELIGIBILITY = gql`
  query GetUserHmoWithPlanEligibility($filterInput: HmoProfileFilterInput, $id: String!) {
    profile(clinifyId: $id) {
      id
      hmos(filterOptions: $filterInput, queryEndpoint: true) {
        totalCount
        list {
          id
          provider {
            id
            name
          }
          planEligibility
        }
      }
    }
  }
`;

export const GET_COVERAGE_INFORMATION_WITH_PLAN_ELIGIBILITY = gql`
  query GetCoverageInformationWithPlanEligibility($id: String!) {
    profile(clinifyId: $id) {
      id
      coverageInformations(filterInput: { skip: 0, take: 5 }) {
        list {
          id
          name
          companyName
          familyName
          coverageType
          hmoProfile {
            id
            planEligibility
            memberNumber
            memberStatus
            memberPlan
            memberPlanId
            planCategory
            primaryProviderId
            provider {
              id
              name
              providerCode
            }
            lastCheckIn {
              verificationCode
            }
          }
        }
      }
    }
  }
`;

export const GET_HMOS_NAME_ONLY = gql`
  query GetUserHmosNameOnly($clinifyId: String!) {
    profile(clinifyId: $clinifyId) {
      id
      hmos {
        list {
          ...HmoFieldNameOnly
        }
      }
    }
  }
  ${HMO_FIELDS_NAME_ONLY}
`;

export const SYNC_HMO_PROFILE = gql`
  mutation SyncHmoProfile($clinifyId: String!) {
    syncProfileWithHmo(clinifyId: $clinifyId) {
      ...HmoField
    }
  }
  ${HMO_FIELDS}
`;

export const UPDATE_HMO = gql`
  mutation UpdateHmo($id: String!, $input: HmoProfileInput!) {
    updateMyHmos(hmoProfileInput: $input, hmoProfileId: $id) {
      ...HmoField
    }
  }
  ${HMO_FIELDS}
`;

export const SAVE_HMO = gql`
  mutation SaveUserHmos($input: HmoProfileInput!, $id: String) {
    saveMyHmos(hmoProfileInput: $input, id: $id) {
      ...HmoField
    }
  }
  ${HMO_FIELDS}
`;

export const DELETE_MY_HMO = gql`
  mutation DeleteMyHmo($id: String!) {
    deleteMyHmo(id: $id) {
      id
    }
  }
`;

export const GET_ALL_HMOS = gql`
  query FetchHmoProviders {
    fetchHmoProviders {
      list {
        id
        name
      }
    }
  }
`;
export const GET_UTIILIZATION_TYPES = gql`
  query GetUtilizationTypes(
    $providerId: String!
    $utilizationId: String!
    $clinifyId: String!
    $visitationType: String!
    $facilityId: String
  ) {
    getUtilizationTypes(
      providerId: $providerId
      utilizationId: $utilizationId
      clinifyId: $clinifyId
      visitationType: $visitationType
      facilityId: $facilityId
    ) {
      list {
        label
        value
      }
    }
  }
`;

export const GET_UTIILIZATION_CATEGORIES = gql`
  query GetUtilizationCategories(
    $providerId: String!
    $clinifyId: String!
    $visitationType: String!
    $facilityId: String
  ) {
    getUtilizationCategories(
      providerId: $providerId
      clinifyId: $clinifyId
      visitationType: $visitationType
      facilityId: $facilityId
    ) {
      list {
        label
        value
      }
    }
  }
`;

export const GET_VISITATION_TYPES = gql`
  query GetVisitationTypes(
    $providerId: String!
    $clinifyId: String!
    $hospitalId: String
    $externalPlanTypeId: String
  ) {
    getVisitationTypes(
      providerId: $providerId
      clinifyId: $clinifyId
      hospitalId: $hospitalId
      externalPlanTypeId: $externalPlanTypeId
    ) {
      list {
        label
        value
      }
    }
  }
`;

export const GET_HMO_PROVIDER_BY_FACILITY_ID = gql`
  query GetHmoProviderByFacilityId {
    getHmoProviderByFacilityId {
      totalCount
      list {
        id
        name
      }
    }
  }
`;

export const BULK_ACTIVATE_HMO_PROFILE = gql`
  mutation BulkActivateHmoProfile($ids: [String!]!) {
    bulkActivateHmoProfiles(hmoProfileIds: $ids) {
      id
      memberStatus
    }
  }
`;

export const PRINCIPAL_MEMBERSHIP_NO_GENERATOR = gql`
  mutation PrincipalMembershipNoGenerator($providerCode: Float, $id: String) {
    principalMembershipNoGenerator(providerCode: $providerCode, id: $id)
  }
`;

export const GET_REGISTERED_ENROLLEES_METRICS = gql`
  query GetRegisteredEnrolleesMetrics($input: RegisteredEnrolleesMetricsInput!) {
    registeredEnrolleesMetrics(input: $input) {
      totalEnrollees
      totalFacilitiesEnrolledIn
      totalCommissionPayable
    }
  }
`;

export const UPDATE_HMO_PROFILE_QUESTIONNAIRE_DATA = gql`
  mutation UpdateHmoProfileQuestionnaireData(
    $coverageInformationId: String!
    $input: QuestionnaireInput!
  ) {
    updateQuestionnaireData(
      coverageInformationId: $coverageInformationId
      questionnaireData: $input
    ) {
      id
      questionnaireData {
        ...QuestionnaireDataFragment
      }
    }
  }
  ${QUESTIONNAIRE_DATA_FRAGMENTS}
`;

export const BULK_REGISTER_ENROLLEES = gql`
  mutation BulkRegisterEnrollees($input: BulkEnrolleesRegistrationInput!) {
    bulkRegisterEnrollees(input: $input) {
      jobId
      totalEnrollees
      status
      batchId
    }
  }
`;

export const BULK_REGISTRATION_STATUS_UPDATE_SUBS = gql`
  subscription BulkRegistrationStatusUpdateSubs($profileId: String!) {
    BulkRegistrationStatusUpdate(profileId: $profileId) {
      failed
      successful
      total
      failedEnrollees {
        code
        index
        reason
        enrolleeData {
          firstName
          lastName
          email
          phoneNumber
        }
      }
      successfulEnrollees {
        enrolleeId
        index
        enrolleeData {
          firstName
          lastName
          email
          phoneNumber
        }
      }
    }
  }
`;
