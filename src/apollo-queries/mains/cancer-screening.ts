import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

export const CANCER_SCREENING_FIELD = gql`
  fragment CancerScreening on CancerScreeningModel {
    id
    screeningToBeDone
    screeningType
    seenRiskAssessmentForm
    mammogramDate
    breastUltrasoundDate
    cervicalScreeningDate
    clinicalBreastExaminationDate
    digitalRectalExaminationDate
    prostrateSpecificAntigenDate
    faecalImmunochemicalOccultDate
    colonoscopyDateTime
    hpvDnaDate
    lastHpvVaccineDate
    otherScreeningInformation
    lastMenstrualPeriod
    existingPastMedicalCondition
    currentDrugs
    pregnancies
    terminationsOrMiscarriages
    contraceptiveMethod
    tubalLitigation
    vasectomy
    pastBiopsies
    otherMedicalInformation
    pastResultsAvailable
    specifyPastResult
    procedureAndFindingDocumented
    laboratoryFormRequested
    planOrRecommendations
    otherComments
    followUpAppointmentWithResult
    recallAppointment
    additionalNote
    concealAdditionalNote
    documentUrl
    profileId
    profile {
      id
      clinifyId
    }
    ${AUDIT_FIELDS}
  } 
`;

export const GET_PATIENT_CANCER_SCREENING_CHECKLISTS = gql`
  query GetPatientCancerScreenings($filterOptions: NursingServicesFilterInput, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      cancerScreening(filterOptions: $filterOptions) {
        totalCount
        list {
          ...CancerScreening
        }
      }
    }
  }
  ${CANCER_SCREENING_FIELD}
`;

export const GET_PATIENT_CANCER_SCREENING_CHECKLIST = gql`
  query GetPatientCancerScreening($clinifyId: String!, $id: String!) {
    cancerScreening(id: $id, clinifyId: $clinifyId) {
      ...CancerScreening
    }
  }
  ${CANCER_SCREENING_FIELD}
`;

export const ADD_PATIENT_CANCER_SCREENING_CHECKLIST = gql`
  mutation AddCancerScreening($id: String, $input: CancerScreeningInput!, $pin: String) {
    addCancerScreening(id: $id, checklist: $input, pin: $pin) {
      ...CancerScreening
    }
  }
  ${CANCER_SCREENING_FIELD}
`;

export const CANCER_SCREENING_CHECKLIST_ADDED_SUBS = gql`
  subscription CancerScreeningAddedSubs($profileId: String!) {
    CancerScreeningAdded(profileId: $profileId) {
      ...CancerScreening
    }
  }
  ${CANCER_SCREENING_FIELD}
`;

export const UPDATE_PATIENT_CANCER_SCREENING_CHECKLIST = gql`
  mutation UpdateCancerScreening($id: String!, $input: CancerScreeningInput!, $pin: String) {
    updateCancerScreening(id: $id, checklist: $input, pin: $pin) {
      ...CancerScreening
    }
  }
  ${CANCER_SCREENING_FIELD}
`;

export const CANCER_SCREENING_CHECKLIST_UPDATED_SUBS = gql`
  subscription CancerScreeningUpdatedSubs($profileId: String!) {
    CancerScreeningUpdated(profileId: $profileId) {
      ...CancerScreening
    }
  }
  ${CANCER_SCREENING_FIELD}
`;

export const DELETE_PATIENT_CANCER_SCREENING_CHECKLIST = gql`
  mutation DeleteCancerScreenings($ids: [String!]!, $clinifyId: String!) {
    deleteCancerScreenings(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const DELETE_CANCER_SCREENING_CHECKLIST_SUBS = gql`
  subscription DeleteCancerScreeningSubs($profileId: String!) {
    CancerScreeningRemoved(profileId: $profileId) {
      id
    }
  }
`;

export const CONCEAL_CANCER_SCREENING_ADDITIONAL_NOTE = gql`
  mutation ConcealCancerScreeningAdditionalNote($id: String!, $concealStatus: Boolean!) {
    concealCancerScreeningAdditionalNote(checklistId: $id, concealState: $concealStatus) {
      id
      additionalNote
      concealAdditionalNote
    }
  }
`;
