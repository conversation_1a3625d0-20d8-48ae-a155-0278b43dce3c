import { gql } from '@apollo/client';

const AUDIT_FIELDS = `
  creatorName
  lastModifierName
`;

const BASE_HANDOVER_NOTE = gql`
  fragment BaseHandoverNote on HandoverNoteModel {
    id
    name
    handoverDateTime
    updatedDate
    createdDate
    hospitalId
    creatorId
    department
    handoverById
    specialty
  }
`;

export const BASE_HANDOVER_NOTE_ITEM = gql`
  fragment BaseHandoverNoteItem on HandoverNoteItemModel {
    id
    createdDate
    updatedDate
    admissionWard
    patientProfileId
    patientInformation {
      fullName
      clinifyId
      phone
      email
    }
    note
    priority
    handoverNoteId
    creatorId
  }
`;

export const BASE_HANDOVER_STAFF = gql`
  fragment BaseHandoverStaff on HandoverStaffModel {
    id
    status
    currentShift
    nextShift
    staffProfileId
    staffProfile {
      fullName
    }
    handoverNoteId
    createdDate
    updatedDate
    creatorId
  }
`;

export const BASE_HANDOVER_ADDITIONAL_NOTE = gql`
  fragment BaseHandoverAdditionalNote on HandoverAdditionalNoteModel {
    id
    additionalNote
    handoverNoteId
    creatorId
    createdDate
    updatedDate
  }
`;

export const GET_HANDOVER_NOTE = gql`
  query GetHandoverNote($id: String!) {
    handoverNote(id: $id) {
      ...BaseHandoverNote
      handoverBy {
        id
        fullName
      }
      items {
        ...BaseHandoverNoteItem
        ${AUDIT_FIELDS}
      }
      staffs {
        ...BaseHandoverStaff
        ${AUDIT_FIELDS}
      }
      additionalNotes {
        ...BaseHandoverAdditionalNote
        ${AUDIT_FIELDS}
      }
      hospital {
        id
        name
        address
      }
      ${AUDIT_FIELDS}
    }
  }
  ${BASE_HANDOVER_NOTE}
  ${BASE_HANDOVER_NOTE_ITEM}
  ${BASE_HANDOVER_STAFF}
  ${BASE_HANDOVER_ADDITIONAL_NOTE}
`;

export const GET_HANDOVER_NOTES = gql`
  query GetHandoverNotes($filterOptions: HandoverNoteFilterInput!) {
    handoverNotes(filterOptions: $filterOptions) {
      totalCount
      list {
        ...BaseHandoverNote
        staffs {
          id
          status
          staffProfile {
            fullName
          }
        }
        handoverBy {
          id
          fullName
        }
        items {
          id
          note
          patientProfileId
          patientInformation {
            clinifyId
            fullName
          }
        }
      }
    }
  }
  ${BASE_HANDOVER_NOTE}
`;

export const ADD_HANDOVER_NOTE = gql`
  mutation AddHandoverNote($input: CreateHandoverNoteInput!) {
    addHandoverNote(input: $input) {
      ...BaseHandoverNote
      handoverBy {
        id
        fullName
      }
      items {
        ...BaseHandoverNoteItem
        ${AUDIT_FIELDS}
      }
      staffs {
        ...BaseHandoverStaff
        ${AUDIT_FIELDS}
      }
      additionalNotes {
        ...BaseHandoverAdditionalNote
        ${AUDIT_FIELDS}
      }
      ${AUDIT_FIELDS}
    }
  }
  ${BASE_HANDOVER_NOTE}
  ${BASE_HANDOVER_NOTE_ITEM}
  ${BASE_HANDOVER_STAFF}
  ${BASE_HANDOVER_ADDITIONAL_NOTE}
`;

export const EDIT_HANDOVER_NOTE = gql`
  mutation EditHandoverNote($id: String!, $input: EditHandoverNoteInput!) {
    updateHandoverNote(id: $id, input: $input) {
      ...BaseHandoverNote
      handoverBy {
        id
        fullName
      }
      lastModifierName
    }
  }
  ${BASE_HANDOVER_NOTE}
`;

export const DELETE_HANDOVER_NOTES = gql`
  mutation DeleteHandoverNotes($ids: [String!]!) {
    deleteHandoverNotes(ids: $ids) {
      id
    }
  }
`;

export const ARCHIVE_HANDOVER_NOTES = gql`
  mutation ArchiveHandoverNotes($ids: [String!]!, $archive: Boolean!) {
    archiveHandoverNotes(ids: $ids, archive: $archive) {
      id
    }
  }
`;

export const ADD_HANDOVER_NOTE_ITEM = gql`
  mutation AddHandoverNoteItem($id: String!, $input: HandoverNoteItemInput!) {
    addHandoverNoteItem(id: $id, input: $input) {
      ...BaseHandoverNoteItem
      ${AUDIT_FIELDS}
    }
  }
  ${BASE_HANDOVER_NOTE_ITEM}
`;

export const EDIT_HANDOVER_NOTE_ITEM = gql`
  mutation EditHandoverNoteItem($id: String!, $input: HandoverNoteItemInput!) {
    updateHandoverNoteItem(id: $id, input: $input) {
      ...BaseHandoverNoteItem
      lastModifierName
    }
  }
  ${BASE_HANDOVER_NOTE_ITEM}
`;

export const DELETE_HANDOVER_NOTE_ITEM = gql`
  mutation DeleteHandoverNoteItem($id: String!) {
    deleteHandoverNoteItem(id: $id) {
      id
    }
  }
`;

export const ADD_HANDOVER_ADDITIONAL_NOTE = gql`
  mutation AddHandoverAdditionalNote($id: String!, $input: HandoverAdditionalNoteInput!) {
    addHandoverAdditionalNote(id: $id, input: $input) {
      ...BaseHandoverAdditionalNote
      ${AUDIT_FIELDS}
    }
  }
  ${BASE_HANDOVER_ADDITIONAL_NOTE}
`;

export const EDIT_HANDOVER_ADDITIONAL_NOTE = gql`
  mutation EditHandoverAdditionalNote($id: String!, $input: HandoverAdditionalNoteInput!) {
    updateHandoverAdditionalNote(id: $id, input: $input) {
      ...BaseHandoverAdditionalNote
      lastModifierName
    }
  }
  ${BASE_HANDOVER_ADDITIONAL_NOTE}
`;

export const DELETE_HANDOVER_ADDITIONAL_NOTE = gql`
  mutation DeleteHandoverAdditionalNote($id: String!) {
    deleteHandoverAdditionalNote(id: $id) {
      id
    }
  }
`;

export const ADD_HANDOVER_STAFF = gql`
  mutation AddHandoverStaff($id: String!, $input: HandoverStaffInput!) {
    addHandoverStaff(id: $id, input: $input) {
      ...BaseHandoverStaff
      ${AUDIT_FIELDS}
    }
  }
  ${BASE_HANDOVER_STAFF}
`;

export const UPDATE_HANDOVER_STAFF = gql`
  mutation UpdateHandoverStaff($id: String!, $input: HandoverStaffInput!) {
    updateHandoverStaff(id: $id, input: $input) {
      ...BaseHandoverStaff
      ${AUDIT_FIELDS}
    }
  }
  ${BASE_HANDOVER_STAFF}
`;

export const DELETE_HANDOVER_STAFF = gql`
  mutation DeleteHandoverStaff($id: String!) {
    deleteHandoverStaff(id: $id) {
      id
    }
  }
`;

export const HANDOVER_NOTE_ADDED_SUBS = gql`
  subscription HandoverNoteAddedSubs($profileId: String!, $hospitalId: String, $userType: UserType!) {
    HandoverNoteAdded(profileId: $profileId, hospitalId: $hospitalId, userType: $userType) {
      ...BaseHandoverNote
      handoverBy {
        id
        fullName
      }
      items {
        ...BaseHandoverNoteItem
        ${AUDIT_FIELDS}
      }
      staffs {
        ...BaseHandoverStaff
        ${AUDIT_FIELDS}
      }
      additionalNotes {
        ...BaseHandoverAdditionalNote
        ${AUDIT_FIELDS}
      }
      ${AUDIT_FIELDS}
    }
  }
  ${BASE_HANDOVER_NOTE}
  ${BASE_HANDOVER_NOTE_ITEM}
  ${BASE_HANDOVER_ADDITIONAL_NOTE}
`;

export const HANDOVER_NOTE_UPDATED_SUBS = gql`
  subscription HandoverNoteUpdatedSubs(
    $profileId: String!
    $hospitalId: String
    $userType: UserType!
  ) {
    HandoverNoteUpdated(profileId: $profileId, hospitalId: $hospitalId, userType: $userType) {
      ...BaseHandoverNote
      handoverBy {
        id
        fullName
      }
      lastModifierName
    }
  }
  ${BASE_HANDOVER_NOTE}
`;

export const HANDOVER_NOTE_REMOVED_SUBS = gql`
  subscription HandoverNoteRemovedSubs(
    $profileId: String!
    $hospitalId: String
    $userType: UserType!
  ) {
    HandoverNoteRemoved(profileId: $profileId, hospitalId: $hospitalId, userType: $userType) {
      id
    }
  }
`;

export const HANDOVER_NOTE_ARCHIVED_SUBS = gql`
  subscription HandoverNoteArchivedSubs(
    $profileId: String!
    $hospitalId: String
    $userType: UserType!
  ) {
    HandoverNoteArchived(profileId: $profileId, hospitalId: $hospitalId, userType: $userType) {
      ...BaseHandoverNote
      staffs {
        id
        staffProfile {
          fullName
        }
      }
      handoverBy {
        id
        fullName
      }
      items {
        id
        patientProfileId
        patientInformation {
          clinifyId
          fullName
        }
      }
    }
  }
  ${BASE_HANDOVER_NOTE}
`;

export const HANDOVER_NOTE_UNARCHIVED_SUBS = gql`
  subscription HandoverNoteUnarchivedSubs(
    $profileId: String!
    $hospitalId: String
    $userType: UserType!
  ) {
    HandoverNoteUnarchived(profileId: $profileId, hospitalId: $hospitalId, userType: $userType) {
      ...BaseHandoverNote
      staffs {
        id
        staffProfile {
          fullName
        }
      }
      handoverBy {
        id
        fullName
      }
      items {
        id
        patientProfileId
        patientInformation {
          clinifyId
          fullName
        }
      }
    }
  }
  ${BASE_HANDOVER_NOTE}
`;

export const HANDOVER_NOTE_ITEM_ADDED_SUBS = gql`
  subscription HandoverNoteItemAddedSubs($profileId: String!, $hospitalId: String, $userType: UserType!) {
    HandoverNoteItemAdded(profileId: $profileId, hospitalId: $hospitalId, userType: $userType) {
      ...BaseHandoverNoteItem
      ${AUDIT_FIELDS}
    }
  }
  ${BASE_HANDOVER_NOTE_ITEM}
`;

export const HANDOVER_NOTE_ITEM_UPDATED_SUBS = gql`
  subscription HandoverNoteItemUpdatedSubs(
    $profileId: String!
    $hospitalId: String
    $userType: UserType!
  ) {
    HandoverNoteItemUpdated(profileId: $profileId, hospitalId: $hospitalId, userType: $userType) {
      ...BaseHandoverNoteItem
      lastModifierName
    }
  }
  ${BASE_HANDOVER_NOTE_ITEM}
`;

export const HANDOVER_NOTE_ITEM_DELETED_SUBS = gql`
  subscription HandoverNoteItemDeletedSubs(
    $profileId: String!
    $hospitalId: String
    $userType: UserType!
  ) {
    HandoverNoteItemRemoved(profileId: $profileId, hospitalId: $hospitalId, userType: $userType) {
      id
      handoverNoteId
    }
  }
`;

export const HANDOVER_ADDITIONAL_NOTE_ADDED_SUBS = gql`
  subscription HandoverAdditionalNoteAddedSubs($profileId: String!, $hospitalId: String, $userType: UserType!) {
    HandoverAdditionalNoteAdded(profileId: $profileId, hospitalId: $hospitalId, userType: $userType) {
      ...BaseHandoverAdditionalNote
      ${AUDIT_FIELDS}
    }
  }
  ${BASE_HANDOVER_ADDITIONAL_NOTE}
`;

export const HANDOVER_ADDITIONAL_NOTE_UPDATED_SUBS = gql`
  subscription HandoverAdditionalNoteUpdatedSubs(
    $profileId: String!
    $hospitalId: String
    $userType: UserType!
  ) {
    HandoverAdditionalNoteUpdated(
      profileId: $profileId
      hospitalId: $hospitalId
      userType: $userType
    ) {
      ...BaseHandoverAdditionalNote
      lastModifierName
    }
  }
  ${BASE_HANDOVER_ADDITIONAL_NOTE}
`;

export const HANDOVER_ADDITIONAL_NOTE_REMOVED_SUBS = gql`
  subscription HandoverAdditionalNoteRemovedSubs(
    $profileId: String!
    $hospitalId: String
    $userType: UserType!
  ) {
    HandoverAdditionalNoteRemoved(
      profileId: $profileId
      hospitalId: $hospitalId
      userType: $userType
    ) {
      id
      handoverNoteId
    }
  }
`;

export const HANDOVER_STAFF_ADDED_SUBS = gql`
  subscription HandoverStaffAddedSubs($profileId: String!, $hospitalId: String, $userType: UserType!) {
    HandoverStaffAdded(profileId: $profileId, hospitalId: $hospitalId, userType: $userType) {
      ...BaseHandoverStaff
      ${AUDIT_FIELDS}
    }
  }
  ${BASE_HANDOVER_STAFF}
`;

export const HANDOVER_STAFF_UPDATED_SUBS = gql`
  subscription HandoverStaffUpdatedSubs($profileId: String!, $hospitalId: String, $userType: UserType!) {
    HandoverStaffUpdated(profileId: $profileId, hospitalId: $hospitalId, userType: $userType) {
      ...BaseHandoverStaff
      ${AUDIT_FIELDS}
    }
  }
  ${BASE_HANDOVER_STAFF}
`;

export const HANDOVER_STAFF_DELETED_SUBS = gql`
  subscription HandoverStaffDeletedSubs(
    $profileId: String!
    $hospitalId: String
    $userType: UserType!
  ) {
    HandoverStaffRemoved(profileId: $profileId, hospitalId: $hospitalId, userType: $userType) {
      id
      handoverNoteId
    }
  }
`;
