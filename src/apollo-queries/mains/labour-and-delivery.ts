import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from 'apollo-queries/fragments/audit';
import { PREAUTHORIZATION_DETAILS_FRAGMENT } from 'apollo-queries/fragments/preauthorization-details';

const LABOUR_AND_DELIVERY_FIELDS = gql`
  fragment LabourAndDelivery on LabourDeliveryModel {
    id
    provider
    visitationDateTime
    gestationAge
    estimatedDeliveryDate
    induction
    inductionDateTime
    inducedBy
    inducedMethod
    preTerm
    bloodGroup
    decisionSeekingCare
    modeOfTransportation
    postAbortionCare
    admittedForComplicationsOfUnsafeAbortion
    abortion
    parity
    membranesRuptureDateTime
    firstStageStartDateTime
    firstStageDuration
    firstStageSupportPersonPresent
    firstStageEpiduralGiven
    partographUsed
    firstStageMaternalBloodPressure
    firstStageFetalHeartRate
    firstStageFetalMonitoring
    firstStagePresentation
    firstStageSpecifyBreech
    firstStagePosition
    firstStageLie
    firstStageFetalMovement
    augmentation
    augmentationMethod
    firstStageMotherStatus
    firstStageMdaConducted
    firstStageMotherCauseOfDeath
    firstStageBabyStatus
    firstStageDoctorName
    firstStageSpecialty
    firstStageRank
    firstStageDepartment
    firstStageNurseName
    firstStageMidWife
    firstStageAdditionalNote
    concealFirstStageAdditionalNote
    secondStageStartDateTime
    secondStageBabyDeliveryDateTime
    secondStageDuration
    secondStageSupportPersonPresent
    secondStageEpiduralGiven
    secondStageDeliveryMethod
    placeOfBirth
    referredOut
    admitted
    discharged
    secondStageMaternalBloodPressure
    secondStageFetalHeartRate
    secondStagePresentation
    secondStageSpecifyBreech
    secondStagePosition
    secondStageLie
    secondStageFetalMovement
    episiotomy
    gender
    liveBirthWeight
    birthWeight
    birthWeightUnit
    notBreathingOrCrying
    babyResuscitated
    cordClampTime
    chxGelApplied
    secondStageMotherStatus
    secondStageMdaConducted
    secondStageMotherCauseOfDeath
    secondStageBabyStatus
    stillBirth
    liveHivBirth
    deliveredBy
    secondStageDoctorName
    secondStageSpecialty
    secondStageRank
    secondStageDepartment
    secondStageNurseName
    secondStageMidWife
    secondStageMarternalComplication
    secondStageAdditionalNote
    concealSecondStageAdditionalNote
    thirdStageStartDateTime
    placentaDateTime
    thirdStageDuration
    placentaDeliveryMethod
    placentaComplete
    configuration
    bloodLossEstimate
    osytocinReceiveed
    misoprostolReceived
    receivedMgso4WithEclampsia
    cervicalTear
    perinealLaceration
    birthInjury
    otherTrauma
    repair
    specifyRepair
    anesthesiaType
    anesthesiaGiven
    thirdStageMotherStatus
    thirdStageMdaConducted
    thirdStageMotherCauseOfDeath
    thirdStageBabyStatus
    thirdStageDoctorName
    thirdStageSpecialty
    thirdStageRank
    thirdStageDepartment
    thirdStageNurseName
    thirdStageMidWife
    thirdStageAdditionalNote
    concealThirdStageAdditionalNote
    additionalNote
    concealAdditionalNote
    appointmentId
    hmoProviderId

    typeOfClient
    currentPregnancyNumber
    firstStageMaternalTemperature
    firstStageMaternalTemperatureUnit
    firstStageMaternalPulseRate
    firstStageMaternalRespiratoryRate
    secondStageMaternalTemperature
    secondStageMaternalTemperatureUnit
    secondStageMaternalPulseRate
    secondStageMaternalRespiratoryRate
    secondStageMouthAndNoseSuctioned
    babyCauseOfDeath
    secondStageHeadCircumferenceOfBaby
    secondStageChestCircumferenceOfBaby
    secondStageBodyLengthOfBaby
    secondStageBabyRespiratoryRate
    secondStageBabyHeartRate
    secondStageTemperatureAt1hr
    secondStageTemperatureAt1hrUnit
    fourthStagePostpartumComplications
    babyPutToBreast
    fourthStageTimeOfInitiatingBreastfeeding
    fourthStagePostDeliveryMassageGiven
    fourthStageCervicalTear
    fourthStagePerinealLaceration
    fourthStageBirthInjury
    fourthStageVulvaCareGiven
    fourthStageOtherTrauma
    fourthStageRepair
    fourthStageSpecifyRepair
    fourthStageAnesthesiaType
    fourthStageAnesthesiaGiven
    fourthStageVitaminKGivenToBaby
    fourthStageVitaminKDoseGivenToBaby
    fourthStageMaternalTemperature
    fourthStageMaternalTemperatureUnit
    fourthStageMaternalPulseRate
    fourthStageBabyTemperature
    fourthStageBabyTemperatureUnit
    fourthStageBabyHeartRate
    fourthStageBabyRespiratoryRate
    fourthStageStatusOfMother
    fourthStageMdaConducted
    fourthStageMotherCauseOfDeath
    fourthStageStatusOfBaby
    fourthStagePostpartumDepression
    dangerSigns
    firstDoseOfAntibioticsAndReferred
    neonatalTetanus
    neonatalJaundice
    admittedOnKMC
    dischargedAfterKMC
    givenLlin
    birthCertificateIssued
    birthCertificateCollected
    fourthStageApgarScoreA
    fourthStageApgarScoreP
    fourthStageApgarScoreG
    fourthStageApgarScoreApperance
    fourthStageApgarScoreR
    fourthStageApgarScore
    fourthStageCordCare
    fourthStageGeneralConditionOfTheBaby
    fourthStageGeneralConditionOfTheMother
    fourthStageDoctorName
    fourthStageSpecialty
    fourthStageRank
    fourthStageDepartment
    fourthStageNurseName
    fourthStageMidWife
    fourthStageAdditionalNote
    concealFourthStageCordCare
    concealFourthStageAdditionalNote
    concealFourthStageGeneralConditionOfTheBaby
    concealFourthStageGeneralConditionOfTheMother
    serviceDetails {
      priceId
      type
      name
      quantity
      pricePerUnit
      paymentType
      patientType
      reference
    }
    isPackage
    billStatus
    facilityName
    facilityAddress
    documentUrl
    createdDate
    updatedDate
    hospitalId
    profileId
    profile {
      id
      clinifyId
      coverageDetails {
        id
        name
        coverageType
        companyName
        familyName
        memberNumber
        provider {
          id
          name
        }
      }
    }
    preauthorizationDetails {
      ...PreauthorizationDetails
    }
  }
  ${PREAUTHORIZATION_DETAILS_FRAGMENT}
`;

export const GET_LABOUR_AND_DELIVERY_LIST = gql`
  query GetLabourAndDeliveries($filterOptions: LabourDeliveryFilterOptions, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      labourAndDeliveries(filterOptions: $filterOptions) {
        totalCount
        list {
          ...LabourAndDelivery
          bill {
            id
            createdDate
          }
          billStatus
        }
      }
    }
  }
  ${LABOUR_AND_DELIVERY_FIELDS}
`;

export const GET_LABOUR_AND_DELIVERY = gql`
  query GetLabourAndDelivery($id: String!, $clinifyId: String!) {
    labourAndDelivery(id: $id, clinifyId: $clinifyId) {
      ...LabourAndDelivery
      ${AUDIT_FIELDS}
    }
  }
  ${LABOUR_AND_DELIVERY_FIELDS}
`;

export const ADD_LABOUR_AND_DELIVERY = gql`
  mutation AddLabourAndDelivery($input: NewLabourDeliveryInput!, $id: String,$pin: String) {
    addLabourAndDelivery(labourDelivery: $input, id: $id,pin: $pin) {
      ...LabourAndDelivery
      billStatus
      bill {
        id
        createdDate
      }
      hmoClaim{
        id
      }
      ${AUDIT_FIELDS}
    }
  }
  ${LABOUR_AND_DELIVERY_FIELDS}
`;

export const UPDATE_LABOUR_AND_DELIVERY = gql`
  mutation UpdateLabourAndDelivery($id: String!, $input: LabourDeliveryInput!,$pin: String) {
    updateLabourAndDelivery(id: $id, labourDelivery: $input,pin: $pin) {
      ...LabourAndDelivery
      hmoClaim{
        id
      }
      ${AUDIT_FIELDS}
    }
  }
  ${LABOUR_AND_DELIVERY_FIELDS}
`;

export const UPDATE_LABOUR_AND_DELIVERY_BILL = gql`
  mutation UpdateLabourAndDeliveryBill($id: String!, $input: LabourDeliveryInput!) {
    updateLabourAndDeliveryBill(id: $id, labourDelivery: $input) {
      ...LabourAndDelivery
      hmoClaim{
        id
      }
      ${AUDIT_FIELDS}
    }
  }
  ${LABOUR_AND_DELIVERY_FIELDS}
`;

export const DELETE_LABOUR_AND_DELIVERY = gql`
  mutation DeleteLabourAndDeliverys($ids: [String!]!, $clinifyId: String!) {
    deleteLabourAndDeliverys(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const ARCHIVE_LABOUR_AND_DELIVERY = gql`
  mutation ArchiveLabourAndDeliverys($ids: [String!]!, $archive: Boolean, $clinifyId: String!) {
    archiveLabourAndDeliverys(ids: $ids, archive: $archive, clinifyId: $clinifyId) {
      ...LabourAndDelivery
      ${AUDIT_FIELDS}
    }
  }
  ${LABOUR_AND_DELIVERY_FIELDS}
`;

export const LABOUR_AND_DELIVERY_ADDED_SUBS = gql`
  subscription LabourAndDeliveryAddedSubs($profileId: String!) {
    LabourAndDeliveryAdded(profileId: $profileId) {
      ...LabourAndDelivery
      billStatus
      bill {
        id
        createdDate
      }
      ${AUDIT_FIELDS}
    }
  }
  ${LABOUR_AND_DELIVERY_FIELDS}
`;

export const LABOUR_AND_DELIVERY_UPDATED_SUBS = gql`
  subscription LabourAndDeliveryUpdatedSubs($profileId: String!) {
    LabourAndDeliveryUpdated(profileId: $profileId) {
      ...LabourAndDelivery
      ${AUDIT_FIELDS}
    }
  }
  ${LABOUR_AND_DELIVERY_FIELDS}
`;

export const DELETE_LABOUR_AND_DELIVERY_SUBS = gql`
  subscription DeleteLabourAndDeliverySubs($profileId: String!) {
    LabourAndDeliveryRemoved(profileId: $profileId) {
      id
    }
  }
`;

export const ARCHIVE_LABOUR_AND_DELIVERY_SUBS = gql`
  subscription ArchiveLabourAndDeliverySubs($profileId: String!) {
    LabourAndDeliveryArchived(profileId: $profileId) {
      ...LabourAndDelivery
    }
  }
  ${LABOUR_AND_DELIVERY_FIELDS}
`;

export const UNARCHIVE_LABOUR_AND_DELIVERY_SUBS = gql`
  subscription UnarchiveLabourAndDeliverySubs($profileId: String!) {
    LabourAndDeliveryUnarchived(profileId: $profileId) {
      ...LabourAndDelivery
    }
  }
  ${LABOUR_AND_DELIVERY_FIELDS}
`;

export const CONCEAL_LABOUR_AND_DELIVERY_FIRST = gql`
  mutation ConcealLabourAndDeliveryFirstStageAdditionalNote(
    $id: String!
    $concealStatus: Boolean!
  ) {
    concealLabourAndDeliveryFirstStageAdditionalNote(id: $id, concealState: $concealStatus) {
      id
      concealFirstStageAdditionalNote
      firstStageAdditionalNote
    }
  }
`;

export const CONCEAL_LABOUR_AND_DELIVERY_SECOND = gql`
  mutation ConcealLabourAndDeliverySecondStageAdditionalNote(
    $id: String!
    $concealStatus: Boolean!
  ) {
    concealLabourAndDeliverySecondStageAdditionalNote(id: $id, concealState: $concealStatus) {
      id
      concealSecondStageAdditionalNote
      secondStageAdditionalNote
    }
  }
`;

export const CONCEAL_LABOUR_AND_DELIVERY_THIRD = gql`
  mutation ConcealLabourAndDeliveryThirdStageAdditionalNote(
    $id: String!
    $concealStatus: Boolean!
  ) {
    concealLabourAndDeliveryThirdStageAdditionalNote(id: $id, concealState: $concealStatus) {
      id
      concealThirdStageAdditionalNote
      thirdStageAdditionalNote
    }
  }
`;

export const CONCEAL_LABOUR_AND_DELIVERY_ADDITIONAL_NOTE = gql`
  mutation ConcealLabourAndDeliveryAdditionalNote($id: String!, $concealStatus: Boolean!) {
    concealLabourAndDeliveryAdditionalNote(id: $id, concealState: $concealStatus) {
      id
      concealAdditionalNote
      additionalNote
    }
  }
`;

export const CONCEAL_LABOUR_AND_DELIVERY_FOURTH_STAGE_ADDITIONAL_NOTE = gql`
  mutation ConcealLabourAndDeliveryFourthStageAdditionalNote(
    $id: String!
    $concealStatus: Boolean!
  ) {
    concealLabourAndDeliveryFourthStageAdditionalNote(id: $id, concealState: $concealStatus) {
      id
      concealFourthStageAdditionalNote
      fourthStageAdditionalNote
    }
  }
`;

export const CONCEAL_LABOUR_AND_DELIVERY_FOURTH_STAGE_GENERAL_CONDITION_OF_THE_BABY = gql`
  mutation ConcealLabourAndDeliveryFourthStageGeneralConditionOfTheBaby(
    $id: String!
    $concealStatus: Boolean!
  ) {
    concealLabourAndDeliveryFourthStageGeneralConditionOfTheBaby(
      id: $id
      concealState: $concealStatus
    ) {
      id
      concealFourthStageGeneralConditionOfTheBaby
      fourthStageGeneralConditionOfTheBaby
    }
  }
`;

export const CONCEAL_LABOUR_AND_DELIVERY_FOURTH_STAGE_GENERAL_CONDITION_OF_THE_MOTHER = gql`
  mutation ConcealLabourAndDeliveryFourthStageGeneralConditionOfTheMother(
    $id: String!
    $concealStatus: Boolean!
  ) {
    concealLabourAndDeliveryFourthStageGeneralConditionOfTheMother(
      id: $id
      concealState: $concealStatus
    ) {
      id
      concealFourthStageGeneralConditionOfTheMother
      fourthStageGeneralConditionOfTheMother
    }
  }
`;

export const CONCEAL_LABOUR_AND_DELIVERY_FOURTH_STAGE_CORD_CARE = gql`
  mutation ConcealLabourAndDeliveryFourthStageCordCare($id: String!, $concealStatus: Boolean!) {
    concealLabourAndDeliveryFourthStageCordCare(id: $id, concealState: $concealStatus) {
      id
      concealFourthStageCordCare
      fourthStageCordCare
    }
  }
`;

export const GET_LABOUR_AND_DELIVERY_CLAIM = gql`
  query LabourAndDeliveryClaim($id: String!, $clinifyId: String!) {
    labourAndDelivery(id: $id, clinifyId: $clinifyId) {
      id
      hmoClaim {
        id
      }
    }
  }
`;
