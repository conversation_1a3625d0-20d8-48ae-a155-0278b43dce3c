import { gql } from '@apollo/client';

export const GET_PATIENT_NOTIFICATIONS = gql`
  query GetPatientNotifications($profileId: String, $options: NotificationFilterInput) {
    getNotifications(profileId: $profileId, filterOptions: $options) {
      list {
        id
        title
        tag
        description
        profileId
        metaData {
          id
          clinifyId
          specialty
          provider
          department
          hospitalId
          profileId
          recordType
          recordId
        }
        createdDate
        isSeen
      }
      unreadCount
    }
  }
`;

export const SUBSCRIBE_FACILITY_UPDATE = gql`
  subscription NotificationFacilitySubscription {
    NotificationFacilitySubscription {
      triggeredBy
      data {
        id
        title
        tag
        description
        profileId
        metaData {
          clinifyId
          specialty
          provider
          department
        }
        createdDate
      }
    }
  }
`;

export const MARK_NOTIFICATION_AS_SEEN = gql`
  mutation MarkNotificationSAsSeen($notificationIds: [String!]!) {
    markNotificationsAsSeen(notificationIds: $notificationIds)
  }
`;

export const MARK_ALL_NOTIFICATION_AS_SEEN = gql`
  mutation MarkAllNotificationAsSeen($profileId: String) {
    markAllNotificationsAsSeen(profileId: $profileId)
  }
`;
