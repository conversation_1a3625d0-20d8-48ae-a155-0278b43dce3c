import { gql } from '@apollo/client';

export const GET_PROFILE_PREFERENCES = gql`
  query GetMyProfilePreferences {
    getMyProfilePreference {
      id
      autoProcessClaims
      autoProcessPreauthorizations
    }
  }
`;

export const UPDATE_MY_PROFILE_PREFERENCE = gql`
  mutation UpdateMyProfilePreferences($input: UpdateProfilePreferenceInput!) {
    updateMyProfilePreference(input: $input) {
      id
      autoProcessClaims
      autoProcessPreauthorizations
    }
  }
`;
