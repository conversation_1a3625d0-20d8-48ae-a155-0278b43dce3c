import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';
import { PREAUTHORIZATION_DETAILS_FRAGMENT } from '../fragments/preauthorization-details';

export const IMMUNIZATION_DETAILS_FIELDS = gql`
  fragment ImunizationDetail on ImmunizationDetailModel {
    id
    code
    administrationType
    period
    adverseEffectsFollowingImmunization
    provider
    administrationSource
    duration
    immunizationName
    priority
    administeredDate
    expiryDate
    outcomeOfInvestigation
    class
    dosageUnit
    stateEffects
    batchNumber
    category
    quantity
    adverseEffectsInvestigated
    dosage
    method
    administratorName
    itemId
    preauthorizationDetails {
      ...PreauthorizationDetails
    }
    ${AUDIT_FIELDS}
  }
  ${PREAUTHORIZATION_DETAILS_FRAGMENT}
`;

export const IMMUNIZATION_FIELDS = gql`
  fragment Immunization on ImmunizationModel {
    id
    details {
      ...ImunizationDetail
    }
    appointmentId
    remindMe
    hospitalName
    hospitalAddress
    additionalNote
    hmoProviderId
    serviceDetails {
      priceId
      type
      name
      quantity
      pricePerUnit
      itemId
      paymentType
      patientType
      reference
    }
    isPackage
    documentUrl
    createdDate
    updatedDate
    concealAdditionalNote
    hospitalId
    profileId
    profile {
      id
      clinifyId
      coverageDetails {
        id
        name
        coverageType
        companyName
        familyName
        memberNumber
        provider {
          id
          name
        }
      }
    }
    billStatus
  }
  ${IMMUNIZATION_DETAILS_FIELDS}
`;

export const GET_IMMUNIZATIONS = gql`
  query GetImmunizations($filterOptions: ImmunizationFilterInput, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      immunizations(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Immunization
          billing {
            id
            createdDate
            billId
          }
          billStatus
        }
      }
    }
  }
  ${IMMUNIZATION_FIELDS}
`;

export const GET_IMMUNIZATION = gql`
  query GetImmunization($id: String!, $clinifyId: String!) {
    immunization(id: $id, clinifyId: $clinifyId) {
      ...Immunization
      ${AUDIT_FIELDS}
    }
  }
  ${IMMUNIZATION_FIELDS}
`;

export const UPDATE_IMMUNIZATION = gql`
  mutation UpdateImmunization($id: String!, $input: ImmunizationInput!, $pin: String) {
    updateImmunization(id: $id, immunization: $input, pin: $pin) {
      ...Immunization
      hmoClaim{
        id
      }
      ${AUDIT_FIELDS}
    }
  }
  ${IMMUNIZATION_FIELDS}
`;

export const UPDATE_IMMUNIZATION_BILL = gql`
  mutation UpdateImmunizationBill($id: String!, $input: ImmunizationInput!) {
    updateImmunizationBill(id: $id, immunization: $input) {
      ...Immunization
      hmoClaim{
        id
      }
      ${AUDIT_FIELDS}
    }
  }
  ${IMMUNIZATION_FIELDS}
`;

export const DELETE_IMMUNIZATION = gql`
  mutation DeleteImmunizations($ids: [String!]!, $clinifyId: String!) {
    deleteImmunizations(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const ARCHIVE_IMMUNIZATION = gql`
  mutation ArchiveImmunizations($ids: [String!]!, $archive: Boolean, $clinifyId: String!) {
    archiveImmunizations(ids: $ids, archive: $archive, clinifyId: $clinifyId) {
      ...Immunization
      ${AUDIT_FIELDS}
    }
  }
  ${IMMUNIZATION_FIELDS}
`;

export const ADD_IMMUNIZATION = gql`
  mutation AddImmunization($input: NewImmunizationInput!, $id: String, $pin: String) {
    addImmunization(immunization: $input, id: $id, pin: $pin) {
      ...Immunization
      hmoClaim{
        id
      }
      ${AUDIT_FIELDS}
    }
  }
  ${IMMUNIZATION_FIELDS}
`;

export const IMMUNIZATION_ADDED_SUBS = gql`
  subscription ImmunizationAddedSubs($profileId: String!, $hospitalId: String!) {
    ImmunizationAdded(profileId: $profileId, hospitalId: $hospitalId) {
      ...Immunization
      ${AUDIT_FIELDS}
    }
  }
  ${IMMUNIZATION_FIELDS}
`;

export const IMMUNIZATION_UPDATED_SUBS = gql`
  subscription ImmunizationUpdatedSubs($profileId: String!) {
    ImmunizationUpdated(profileId: $profileId) {
      ...Immunization
      ${AUDIT_FIELDS}
    }
  }
  ${IMMUNIZATION_FIELDS}
`;

export const DELETE_IMMUNIZATION_SUBS = gql`
  subscription DeleteImmunizationSubs($profileId: String!) {
    ImmunizationRemoved(profileId: $profileId) {
      id
    }
  }
`;

export const ARCHIVE_IMMUNIZATION_SUBS = gql`
  subscription ArchiveImmunizationSubs($profileId: String!) {
    ImmunizationArchived(profileId: $profileId) {
      ...Immunization
    }
  }
  ${IMMUNIZATION_FIELDS}
`;

export const UNARCHIVE_IMMUNIZATION_SUBS = gql`
  subscription UnarchiveImmunizationSubs($profileId: String!) {
    ImmunizationUnarchived(profileId: $profileId) {
      ...Immunization
    }
  }
  ${IMMUNIZATION_FIELDS}
`;

export const CONCEAL_IMMUNIZATION_ADDITIONAL_NOTE = gql`
  mutation ConcealImmunizationAdditionalNote($id: String!, $concealStatus: Boolean!) {
    concealImmunizationAdditionalNote(id: $id, concealState: $concealStatus) {
      id
      concealAdditionalNote
      additionalNote
    }
  }
`;

export const GET_IMMUNIZATION_CLAIM = gql`
  query ImmunizationClaim($id: String!, $clinifyId: String!) {
    immunization(id: $id, clinifyId: $clinifyId) {
      id
      hmoClaim {
        id
      }
    }
  }
`;

export const SAVE_IMMUNIZATION_SERVICE_DETAIL = gql`
  mutation SaveImmunizationDetail($input: ImmunizationDetailInput!, $id: String!, $clinifyId: String!,  $pin: String) { 
    saveImmunizationDetail(input: $input, immunizationId: $id, clinifyId: $clinifyId, pin: $pin) {
      ...ImunizationDetail
      immunization {
        id
        serviceDetails {
          priceId
          type
          name
          quantity
          pricePerUnit
          itemId
          paymentType
          patientType
          reference
        }
      }
      ${AUDIT_FIELDS}
    }
  }
  ${IMMUNIZATION_DETAILS_FIELDS}
`;

export const UPDATE_IMMUNIZATION_SERVICE_DETAIL = gql`
  mutation UpdateImmunizationDetail($input: ImmunizationDetailInput!, $id: String!, $clinifyId: String!, $pin: String) { 
    updateImmunizationDetail(input: $input, id: $id, clinifyId: $clinifyId, pin: $pin) {
      ...ImunizationDetail
      ${AUDIT_FIELDS}
    }
  }
  ${IMMUNIZATION_DETAILS_FIELDS}
`;

export const DELETE_IMMUNIZATION_SERVICE_DETAIL = gql`
  mutation DeleteImmunizationDetail($id: String!, $clinifyId: String!) {
    deleteImmunizationDetail(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const ADD_IMMUNIZATION_DETAIL_SUB = gql`
  subscription ImmunizationDetailAdded($profileId: String!) {
    ImmunizationDetailAdded(profileId: $profileId) {
      ...ImunizationDetail
      immunizationId
    }
  }
  ${IMMUNIZATION_DETAILS_FIELDS}
`;

export const UPDATE_IMMUNIZATION_DETAIL_SUB = gql`
  subscription ImmunizationDetailUpdated($profileId: String!) {
    ImmunizationDetailUpdated(profileId: $profileId) {
      ...ImunizationDetail
      immunizationId
    }
  }
  ${IMMUNIZATION_DETAILS_FIELDS}
`;

export const IMMUNIZATION_DETAILS_REMOVED_SUB = gql`
  subscription ImmunizationDetailRemoved($profileId: String!) {
    ImmunizationDetailRemoved(profileId: $profileId) {
      id
      immunizationId
    }
  }
`;
