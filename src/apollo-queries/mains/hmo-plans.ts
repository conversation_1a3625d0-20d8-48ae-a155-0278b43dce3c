import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from 'apollo-queries/fragments/audit';

const HMO_PLAN_BENEFIT_MODEL = gql`
  fragment HmoPlanBenefit on HmoPlanBenefitModel {
    id
    name
    code
    limit
    visitLimit
    annualLimitPerPerson
    waitingPeriodDays
    planTypeId
    utilisationCategory
    visitTypeId
    visitType {
      id
      name
    }
    utilisationTypes {
      name
      annualLimitPerPerson
      benefitCategory
      benefitCoverage
      benefitLimit
      code
      id
      price
      bandBPrice
      bandCPrice
      visitLimit
      waitingPeriodDays
      quantity
    }
    createdDate
  }
`;

const HMO_PLAN_TYPE_FIELDS = gql`
  fragment HmoPlanType on HmoPlanTypeModel {
    id
    name
    premiumCountry
    status
    planDateTime
    plannedBy
    isExternalPlan
    isSponsor
    premiumDetails {
      amount
      category
      frequency
      administrationAgency
      commissionRate
      referralCommissionRate
      providerLedCommissionRate
      individualCommissionRate
      enrollmentCommissionRate {
        name
        id
        commissionRate
        referralCommissionRate
      }
    }
    benefits {
      ...HmoPlanBenefit
    }
    ${AUDIT_FIELDS}
  }
  ${HMO_PLAN_BENEFIT_MODEL}
`;

const HMO_PLAN_TYPE_LIST_FIELDS = gql`
  fragment HmoPlanTypeListFields on HmoPlanTypeModel {
    id
    name
    premiumCountry
    status
    premiumDetails {
      amount
      category
      frequency
      administrationAgency
      commissionRate
      referralCommissionRate
      providerLedCommissionRate
      individualCommissionRate
      enrollmentCommissionRate {
        name
        id
        commissionRate
        referralCommissionRate
      }
    }
    planDateTime
    plannedBy
    beneficiariesCount
    ${AUDIT_FIELDS}
  }
`;

export const ADD_HMO_PLANS = gql`
  mutation AddHmoPlan($providerId: String!, $input: NewHmoPlanInput!) {
    addHmoPlanType(providerId: $providerId, input: $input) {
      ...HmoPlanType
    }
  }
  ${HMO_PLAN_TYPE_FIELDS}
`;

export const DUPLICATE_HMO_PLAN = gql`
  mutation DuplicateHmoPlan($planTypeId: String!) {
    duplicateHmoPlanType(id: $planTypeId) {
      ...HmoPlanType
    }
  }
  ${HMO_PLAN_TYPE_FIELDS}
`;

export const GET_HMO_PLANS = gql`
  query GetHmoPlanList($hospitalId: String, $filterOptions: HmoPlanFilterOptions!) {
    hospital(hospitalId: $hospitalId) {
      id
      hmoPlanTypes(filterOptions: $filterOptions) {
        totalCount
        list {
          ...HmoPlanTypeListFields
        }
      }
    }
  }
  ${HMO_PLAN_TYPE_LIST_FIELDS}
`;

export const GET_HMO_PLAN = gql`
  query GetHmoPlan($id: String!) {
    hmoPlanType(planTypeId: $id) {
      ...HmoPlanType
    }
  }
  ${HMO_PLAN_TYPE_FIELDS}
`;

export const UPDATE_HMO_PLAN = gql`
  mutation UpdateHmoPlan($input: HmoPlanInput!) {
    updateHmoPlanType(input: $input) {
      ...HmoPlanType
    }
  }
  ${HMO_PLAN_TYPE_FIELDS}
`;

export const UPDATE_HMO_PLAN_BENEFITS = gql`
  mutation UpdateHmoPlanBenefits(
    $providerId: String!
    $planTypeId: String!
    $input: [UpdatePlanTypeBenefit!]!
  ) {
    updateHmoPlanBenefits(providerId: $providerId, planTypeId: $planTypeId, input: $input) {
      ...HmoPlanBenefit
    }
  }
  ${HMO_PLAN_BENEFIT_MODEL}
`;

export const GET_EXTERNAL_PLAN_TYPE_WITH_BENEFITS = gql`
  query GetExternalPlanTypeWithBenefits($hmoProviderId: String!) {
    getExternalPlanType(hmoProviderId: $hmoProviderId, fetchBenefits: true) {
      ...HmoPlanType
    }
  }
  ${HMO_PLAN_TYPE_FIELDS}
`;

export const UPDATE_CUSTOM_PLAN_TYPE_BY_HOSPITAL_ID = gql`
  mutation UpdateCustomPlanTypePriceByHospitalId(
    $hospitalId: String!
    $planTypeId: String!
    $input: [BenefitCustomPriceInput!]!
  ) {
    updateCustomPlanTypePriceByHospitalId(
      hospitalId: $hospitalId
      planTypeId: $planTypeId
      inputs: $input
    ) {
      hospitalId
      benefitId
      price
      utilisationCode
    }
  }
`;

export const GET_CUSTOM_PLAN_TYPE_PRICE_BY_HOSPITAL_ID = gql`
  query GetCustomPlanTypePriceByHospitalId($hospitalId: String!, $planTypeId: String!) {
    getCustomPlanTypePriceByHospitalId(hospitalId: $hospitalId, planTypeId: $planTypeId) {
      hospitalId
      benefitId
      price
      utilisationCode
    }
  }
`;

export const DELETE_HMO_PLANS = gql`
  mutation DeleteHmoPlan($ids: [String!]!) {
    deleteHmoPlanTypes(ids: $ids) {
      id
    }
  }
`;

export const ARCHIVE_HMO_PLANS = gql`
  mutation ArchiveHmoPlans($ids: [String!]!, $archive: Boolean!) {
    archiveHmoPlanTypes(ids: $ids, archive: $archive) {
      ...HmoPlanType
    }
  }
  ${HMO_PLAN_TYPE_FIELDS}
`;

export const FETCH_PLAN_BENEFICIARIES_BY_PROVIDER_ID = gql`
  query FetchPlanBeneficiariesByProviderId(
    $providerId: String!
    $filterOptions: HmoPlanBeneficiariesFilterOptions
  ) {
    fetchPlanBeneficiariesByProviderId(providerId: $providerId, filterOptions: $filterOptions) {
      list {
        id
        memberNumber
        profile {
          id
          fullName
          clinifyId
        }
      }
      totalCount
    }
  }
`;

export const ADD_BENEFICIARIES_TO_PLAN = gql`
  mutation AddBeneficiariesToHmoPlan($planId: String!, $beneficiaries: [String!]!) {
    addBeneficiariesToPlan(planId: $planId, beneficiaries: $beneficiaries) {
      id
    }
  }
`;

export const REMOVE_BENEFICIARIES_FROM_PLAN = gql`
  mutation RemoveBeneficiariesFromHmoPlan($planId: String!, $beneficiaries: [String!]!) {
    removeBeneficiariesFromPlan(planId: $planId, beneficiaries: $beneficiaries) {
      id
    }
  }
`;

export const REMOVE_HMO_PLAN_BENEFITS = gql`
  mutation RemoveHmoPlanBenefits(
    $planTypeId: String!
    $benefitIds: [String!]!
    $utilizationTypeIds: [String]!
  ) {
    deleteHmoPlanBenefits(
      benefitIds: $benefitIds
      planTypeId: $planTypeId
      utilizationTypeIds: $utilizationTypeIds
    ) {
      ...HmoPlanType
    }
  }
  ${HMO_PLAN_TYPE_FIELDS}
`;
