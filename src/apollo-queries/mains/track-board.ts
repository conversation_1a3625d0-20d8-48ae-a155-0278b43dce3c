import { gql } from '@apollo/client';

export const FETCH_TRACK_BOARD_DATA = gql`
  query FetchTrackBoardData($filterOptions: TrackboardFilter!) {
    fetchTrackboardData(filter: $filterOptions) {
      totalCount
      list {
        admissionWard
        roomNumber
        severness
        priority
        daysAdmitted
        admissionStatus
        admissionDiagnosis {
          diagnosisICD10
          diagnosisICD11
          diagnosisSNOMED
        }
        admittedBy
        admittingDoctor
        admissionDateTime
        dischargeDateTime
        dischargedBy
        painScore
        dischargeStatus
        allergies
        patientDetails {
          id
          fullName
          gender
          clinifyId
          patientStatus
          dateOfBirth
          registrationDate
        }
        labTests {
          testNames
          status
          testResult {
            testName
            done
            testDate
            results
          }
        }
        radExam {
          examTypes
          status
          examResult {
            examType
            done
            examDate
          }
        }
        waitingInfo {
          status
          waitTime
          emergencyType
          resuscitationActionPlan
          createdDate
          updatedDate
        }
        dependents {
          title
          firstName
          middleName
          lastName
        }
        bloodTransfusion {
          date
          bloodGroup
          genoType
        }
        careTeam {
          specialistFullName
          specialty
        }
        prescriptions {
          name
          dispenseCount
        }
        procedures {
          date
          types
        }
        vitalDetails {
          anthropometry {
            height
            heightUnit
            weight
            weightUnit
            bmi
            bsa
          }
          bloodPressure {
            systolic
            diastolic
          }
          bloodGlucose {
            reading
          }
          pulseRate {
            reading
          }
          respiratoryRate {
            reading
            spO2
          }
          temperature {
            reading
            readingUnit
          }
          pain {
            score
          }
        }
      }
    }
  }
`;
