import { gql } from '@apollo/client';
import { PERSONAL_INFORMATION_FIELDS } from './user';

export const WAITING_LIST_FIELDS = gql`
  fragment Waiter on WaitingListModel {
    id
    status
    visitationReason
    waitStatus
    visitType
    appointmentBooked
    vitalsTaken
    vitalsTakenBy
    arrivalDateTime
    transferReason
    emergencyType
    resuscitationActionPlan
    waitTime
    priority
    patientType
    paymentType
    serviceDetails {
      priceId
      type
      name
      quantity
      pricePerUnit
    }
    patientId
    patientDisplayPic
    patientClinifyId
    patientFullName
    patientTitle
    patientFirstName
    patientMiddleName
    patientLastName
    patientDateOfBirth
    patientGender
    patientBloodGroup
    patientPhoneNumber
    patientCountry
    patientEmail
    createdDate
    updatedDate
    coverageNames
    coverageTypes
    enrolleeIds
    checkedInBy
    checkedOutBy
    appointmentDateTime
    assignedTo {
      id
      fullName
      clinifyId
      type
      personalInformation {
        ...PersonalInformationFields
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
`;

export const CHECKIN_PATIENT_SUB = gql`
  subscription CheckinPatientSub($hospitalId: String!) {
    WaiterAdded(hospitalId: $hospitalId) {
      ...Waiter
    }
  }
  ${WAITING_LIST_FIELDS}
`;

export const CHECKIN_PATIENT = gql`
  mutation CheckInPatient($input: WaitingListInput!) {
    addPatientToWaitingList(input: $input) {
      ...Waiter
    }
  }
  ${WAITING_LIST_FIELDS}
`;

export const WAITERS_REMOVED_SUB = gql`
  subscription WaitersRemovedSub($hospitalId: String!) {
    WaitersRemoved(hospitalId: $hospitalId) {
      ...Waiter
    }
  }
  ${WAITING_LIST_FIELDS}
`;

export const DELETE_WAITERS = gql`
  mutation DeleteWaiters($ids: [String!]!) {
    deleteWaitingListItems(ids: $ids) {
      ...Waiter
    }
  }
  ${WAITING_LIST_FIELDS}
`;

export const WAITERS_CHECKED_IN_OUT_SUB = gql`
  subscription WaitersCheckedInOrOutSub($hospitalId: String!) {
    WaitersCheckedInOrOut(hospitalId: $hospitalId) {
      ...Waiter
    }
  }
  ${WAITING_LIST_FIELDS}
`;

export const CHECK_WAITERS_IN_OUT = gql`
  mutation CheckWaitersInOrOut($ids: [String!]!, $status: CheckInOrOutStatus!) {
    checkWaitersInOrOut(ids: $ids, status: $status) {
      ...Waiter
    }
  }
  ${WAITING_LIST_FIELDS}
`;

export const WAITERS_ARCHIVED_SUB = gql`
  subscription WaitersArchivedSub($hospitalId: String!) {
    WaitersArchived(hospitalId: $hospitalId) {
      ...Waiter
    }
  }
  ${WAITING_LIST_FIELDS}
`;

export const WAITERS_UNARCHIVED_SUB = gql`
  subscription WaitersUnarchivedSub($hospitalId: String!) {
    WaitersUnarchived(hospitalId: $hospitalId) {
      ...Waiter
    }
  }
  ${WAITING_LIST_FIELDS}
`;

export const ARCHIVE_WAITER = gql`
  mutation ArchiveWaiters($ids: [String!]!, $archive: Boolean!) {
    archiveWaitingListItems(archive: $archive, ids: $ids) {
      ...Waiter
    }
  }
  ${WAITING_LIST_FIELDS}
`;

export const UPDATE_WAITER_SUB = gql`
  subscription UpdateWaiterSub($hospitalId: String!) {
    WaiterUpdated(hospitalId: $hospitalId) {
      ...Waiter
    }
  }
  ${WAITING_LIST_FIELDS}
`;

export const UPDATE_WAITER = gql`
  mutation UpdateWaiter($input: WaitingListUpdateInput!, $id: String!) {
    updateWaitingList(input: $input, id: $id) {
      ...Waiter
    }
  }
  ${WAITING_LIST_FIELDS}
`;

export const WAITING_LIST = gql`
  query WaitingList($filterOptions: WaitingListFilterInput!) {
    waitingList(filterOptions: $filterOptions) {
      totalCount
      list {
        ...Waiter
      }
    }
  }
  ${WAITING_LIST_FIELDS}
`;

export const ADD_APPOINTMENT_TO_WAITING_LIST = gql`
  mutation AddAppointmentToWaitingList($ids: [String!]!) {
    addAppointmentToWaitingList(ids: $ids) {
      ...Waiter
    }
  }
  ${WAITING_LIST_FIELDS}
`;

export const WAITING_LIST_EVENT_SUBSCRIPTION = gql`
  subscription WaiterEvent($profileId: String!, $hospitalId: String!) {
    WaiterEvent(profileId: $profileId, hospitalId: $hospitalId)
  }
`;
