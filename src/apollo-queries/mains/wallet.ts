import { gql } from '@apollo/client';

const VIRTUAL_ACCOUNT_FIELD = gql`
  fragment VirtualAccountFragment on VirtualBankAccountModel {
    id
    currency
    accountName
    accountNumber
    transactionType
    virtualAccountType
    createdDate
    updatedDate
    bank
    expiryDate
    hmoId
    hmo {
      id
      name
    }
  }
`;

const PAYMENT_DEPOSIT_FIELD = gql`
  fragment PaymentDepositFragment on PaymentDepositModel {
    id
    depositDate
    depositMethod
    currency
    profileId
    hospitalId
    amountDeposited
    amountUsed
    additionalNote
    collectedById
    creatorId
    lastModifierId
    description
    additionalNote
    createdDate
    updatedDate
    autoGenerated
    amountRefunded
    finalDepositBalance
    initialDepositBalance
    isManualRefund
    refundDate
    refundedBy
  }
`;

const WALLET_TRANSACTION_FIELD = gql`
  fragment WalletTransactionFragment on WalletTransactionModel {
    id
    createdDate
    amount
    transactionType
    transactionStatus
    transactionDetails
    currency
    amountSent
    description
    currency
  }
`;

export const GET_TOPUP_VIRTUAL_ACCOUNT = gql`
  query GetTopupVirtualAccount($clinifyId: String!, $input: VirtualBankAccountKYCInput) {
    getVirtualAccountForWalletTopup(clinifyId: $clinifyId, input: $input) {
      id
      accountNumber
      accountName
      currency
      bank
      walletId
      bvn
      phoneNumber
    }
  }
`;

export const UPDATE_KYC_DETAILS = gql`
  mutation UpdateKYCDetails($accountNumber: String!, $input: VirtualBankAccountKYCInput!) {
    updateKYCDetails(input: $input, accountNumber: $accountNumber) {
      id
      accountNumber
      accountName
      currency
      bank
      walletId
      bvn
      phoneNumber
    }
  }
`;

export const GET_HOSPITAL_VIRTUAL_ACCOUNTS = gql`
  query HospitalVirtualAccounts {
    hospital {
      id
      virtualAccounts {
        ...VirtualAccountFragment
      }
    }
  }
  ${VIRTUAL_ACCOUNT_FIELD}
`;

export const WALLET_BALANCE_SUBS = gql`
  subscription WalletBalance($walletId: String!) {
    WalletBalance(walletId: $walletId) {
      id
      balance
    }
  }
`;

export const GET_DEPOSIT_SUMMARY = gql`
  query GetDepositSummary($profileId: String!) {
    paymentDepositSummary(profileId: $profileId) {
      balance
      totalDeposited
      totalUsed
      currency
      totalRefunded
    }
  }
`;

export const GET_PATIENT_DEPOSITS = gql`
  query GetPatientDeposits($filterOptions: PaymentDepositFilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      paymentDeposits(filterOptions: $filterOptions) {
        totalCount
        list {
          ...PaymentDepositFragment
          collectedBy {
            id
            fullName
          }
          withdrawnBy {
            id
            fullName
          }
        }
      }
    }
  }
  ${PAYMENT_DEPOSIT_FIELD}
`;

export const GET_PAYMENT_DEPOSIT = gql`
  query GetPaymentDeposit($id: String!, $clinifyId: String!) {
    paymentDeposit(id: $id, clinifyId: $clinifyId) {
      ...PaymentDepositFragment
      creatorName
      lastModifierName
      collectedBy {
        id
        fullName
      }
      withdrawnBy {
        id
        fullName
      }
      hospital {
        id
        name
        address
      }
    }
  }
  ${PAYMENT_DEPOSIT_FIELD}
`;

export const ADD_PAYMENT_DEPOSIT = gql`
  mutation AddPaymentDeposit($input: PaymentDepositInput!) {
    addPaymentDeposit(input: $input) {
      ...PaymentDepositFragment
      creatorName
    }
  }
  ${PAYMENT_DEPOSIT_FIELD}
`;

export const ADD_PAYMENT_DEPOSIT_SUBS = gql`
  subscription AddPaymentDepositSubs($hospitalId: String!, $profileId: String!) {
    PaymentDepositAdded(hospitalId: $hospitalId, profileId: $profileId) {
      ...PaymentDepositFragment
      collectedBy {
        id
        fullName
      }
      withdrawnBy {
        id
        fullName
      }
    }
  }
  ${PAYMENT_DEPOSIT_FIELD}
`;

export const ADD_DEPOSIT_REFUND_SUBS = gql`
  subscription DepositRefundAddedSubs($hospitalId: String!, $profileId: String!) {
    RefundDepositAdded(hospitalId: $hospitalId, profileId: $profileId) {
      ...PaymentDepositFragment
      collectedBy {
        id
        fullName
      }
      withdrawnBy {
        id
        fullName
      }
    }
  }
  ${PAYMENT_DEPOSIT_FIELD}
`;

export const UPDATE_DEPOSIT_REFUND_SUBS = gql`
  subscription UpdateDepositRefundSubs($hospitalId: String!, $profileId: String!) {
    RefundDepositUpdated(hospitalId: $hospitalId, profileId: $profileId) {
      ...PaymentDepositFragment
    }
  }
  ${PAYMENT_DEPOSIT_FIELD}
`;

export const DELETE_PAYMENT_DEPOSIT_SUBS = gql`
  subscription PaymentDepositRemovedSubs($hospitalId: String!, $profileId: String!) {
    PaymentDepositRemoved(hospitalId: $hospitalId, profileId: $profileId) {
      id
      amountDeposited
      amountUsed
      profileId
      hospitalId
      currency
      autoGenerated
      isManualRefund
      amountRefunded
    }
  }
`;

export const UPDATE_PAYMENT_DEPOSIT = gql`
  mutation UpdatePaymentDeposit($id: String!, $input: EditPaymentDepositInput!) {
    updatePaymentDeposit(input: $input, id: $id) {
      ...PaymentDepositFragment
    }
  }
  ${PAYMENT_DEPOSIT_FIELD}
`;

export const ARCHIVE_PAYMENT_DEPOSIT = gql`
  mutation ArchivePaymentDeposit($ids: [String!]!, $archive: Boolean!) {
    archivePaymentDeposits(ids: $ids, archive: $archive) {
      ...PaymentDepositFragment
    }
  }
  ${PAYMENT_DEPOSIT_FIELD}
`;

export const DELETE_PAYMENT_DEPOSITS = gql`
  mutation DeletePaymentDeposit($ids: [String!]!) {
    deletePaymentDeposits(ids: $ids) {
      id
    }
  }
`;

export const SEND_FUNDS_TO_WALLET = gql`
  mutation SendFundsToWallet($input: SendFundsInput!) {
    sendFundsToWallet(input: $input) {
      ...WalletTransactionFragment
      senderWallet {
        profile {
          clinifyId
          fullName
        }
      }
      receiverWallet {
        profile {
          clinifyId
          fullName
        }
      }
    }
  }
  ${WALLET_TRANSACTION_FIELD}
`;

export const ADD_DEPOSIT_REFUND = gql`
  mutation AddDepositRefund($input: PaymentDepositRefundInput!) {
    addPaymentDepositRefund(input: $input) {
      ...PaymentDepositFragment
      collectedBy {
        id
        fullName
      }
      withdrawnBy {
        id
        fullName
      }
    }
  }
  ${PAYMENT_DEPOSIT_FIELD}
`;

export const UPDATE_DEPOSIT_REFUND = gql`
  mutation UpdateDepositRefund($input: EditPaymentDepositRefundInput!, $id: String!) {
    updatePaymentDepositRefund(input: $input, id: $id) {
      ...PaymentDepositFragment
    }
  }
  ${PAYMENT_DEPOSIT_FIELD}
`;

export const GET_WALLET_SUMMARY = gql`
  query GetWalletSummary($walletId: String!) {
    getWalletSummary(walletId: $walletId) {
      totalWalletBalance
      availableWalletBalance
      totalWalletWithdrawal
      totalWalletRefund
      totalReceived
    }
  }
`;
