import { gql } from '@apollo/client';

const EMPLOYEE_DEPENDENT_FRAGMENT = gql`
  fragment EmployeeDependentFields on EmployeeDependantModel {
    id
    firstName
    middleName
    lastName
    relationship
    title
    employeeId
    lastModifierName
    creatorName
    createdDate
    updatedDate
    displayPictureUrl
    hmoProfile {
      id
      memberNumber
      primaryProviderId
      primaryProviderAddress
      primaryProviderName
    }
    profile {
      id
      gender
      title
      details {
        id
        dateOfBirth
      }
      user {
        nonCorporateEmail
        phoneNumber
      }
    }
  }
`;

const EMPLOYEE_FRAGMENT = gql`
  fragment EmployeeFields on EmployeeModel {
    id
    title
    firstName
    middleName
    lastName
    jobTitle
    department
    employeeId
    planCategory
    employeeType
    hmoPlanType {
      id
      name
      planCode
      premiumDetails {
        frequency
        category
        amount
      }
    }
    hmoPlanTypeId
    paymentFrequency
    paymentDate
    planStartDate
    planDueDate
    employerId
    enrolledBy
    createdDate
    updatedDate
    creatorName
    lastModifierName
    creatorId
    lastModifierId
    displayPictureUrl
  }
`;

const EMPLOYER_FRAGMENT = gql`
  fragment EmployerFields on EmployerModel {
    id
    employerName
    employerAddress
    country
    state
    localGovernmentArea
    ward
    displayUrl
    employerPrimaryEmailAddress
    employerPrimaryPhoneNumber {
      countryCode
      countryName
      value
    }
    employerSecondaryPhoneNumber {
      value
      countryCode
      countryName
    }
    employerSecondaryEmailAddress
    contactPersonTitle
    contactPersonFirstName
    contactPersonMiddleName
    contactPersonLastName
    contactPersonPhoneNumber {
      countryCode
      countryName
      value
    }
    contactPersonEmailAddress
    contactPersonAltTitle
    contactPersonAltFirstName
    contactPersonAltMiddleName
    contactPersonAltLastName
    contactPersonAltPhoneNumber {
      countryCode
      countryName
      value
    }
    contactPersonAltEmailAddress
    numberOfEmployees
    planAmount
    planAmountFlag
    planAgent
    paymentFrequency
    planGroup
    planSubGroup
    additionalNote
    employerNumber
    employerPlanCode
    referrer
    referrerCode
    referrerNumber
    commissionRate
    commissionPayable
    tpaNonTpaCommissionRate
    tpaNonTpaCommissionPayable
    selectedMemberPlans {
      id
      name
      frequency
    }
    createdDate
    updatedDate
    creatorName
    lastModifierName
    creatorId
    lastModifierId
    employees {
      ...EmployeeFields
    }
    hmoProvider {
      id
      name
    }
    hmoProviderId
  }
  ${EMPLOYEE_FRAGMENT}
`;

export const GET_EMPLOYER = gql`
  query GetEmployer($id: String!) {
    employer(id: $id) {
      ...EmployerFields
      employees {
        ...EmployeeFields
        hmoProfile {
          id
          provider {
            id
            name
          }
          primaryProviderId
          primaryProviderAddress
          primaryProviderName
          memberNumber
          memberStatus
          enrollmentDateTime
          paymentDateTime
          premiumCollected
          registrationSource
          administrationAgency
          commissionPayable
          commissionRate
          tpaNonTpaCommissionRate
          tpaNonTpaCommissionPayable
          enrollmentAgent
          enrollmentAgency
          planCategory
          salesWing
          sponsorType
          sponsorName
          referrer
          referrerCode
          referrerNumber
          referrerCommissionRate
          referrerCommissionPayable
          activationDatetime
          totalPremiumAmountPaid
          hmoPlanType {
            id
            name
            planCode
            premiumDetails {
              frequency
              category
              amount
            }
          }
        }
        profile {
          id
          gender
          title
          details {
            id
            dateOfBirth
          }
          user {
            nonCorporateEmail
            phoneNumber
          }
        }
        dependants {
          ...EmployeeDependentFields
        }
      }
    }
  }
  ${EMPLOYER_FRAGMENT}
  ${EMPLOYEE_FRAGMENT}
  ${EMPLOYEE_DEPENDENT_FRAGMENT}
`;

export const GET_EMPLOYER_BY_IDS = gql`
  query GetEmployersByIds($ids: [String!]!) {
    employersByIds(ids: $ids) {
      ...EmployerFields
      employees {
        ...EmployeeFields
        hmoProfile {
          id
          provider {
            id
            name
          }
          primaryProviderId
          primaryProviderAddress
          primaryProviderName
          memberNumber
          memberStatus
          enrollmentDateTime
          paymentDateTime
          premiumCollected
          registrationSource
          administrationAgency
          commissionPayable
          commissionRate
          tpaNonTpaCommissionRate
          tpaNonTpaCommissionPayable
          enrollmentAgent
          enrollmentAgency
          planCategory
          salesWing
          sponsorType
          sponsorName
          hmoPlanType {
            id
            premiumDetails {
              frequency
              category
              amount
            }
          }
        }
        profile {
          id
          gender
          title
          details {
            id
            dateOfBirth
          }
          user {
            nonCorporateEmail
            phoneNumber
          }
        }
        dependants {
          ...EmployeeDependentFields
        }
      }
    }
  }
  ${EMPLOYER_FRAGMENT}
  ${EMPLOYEE_FRAGMENT}
  ${EMPLOYEE_DEPENDENT_FRAGMENT}
`;

export const GET_EMPLOYERS = gql`
  query GetEmployers($filterOptions: EmployerFilterInput!) {
    employers(filter: $filterOptions) {
      totalCount
      list {
        ...EmployerFields
      }
    }
  }
  ${EMPLOYER_FRAGMENT}
`;

export const CREATE_EMPLOYER = gql`
  mutation AddEmployer($input: CreateEmployerInput!) {
    addEmployer(input: $input) {
      ...EmployerFields
    }
  }
  ${EMPLOYER_FRAGMENT}
`;

export const EDIT_EMPLOYER = gql`
  mutation EditEmployer($id: String!, $input: UpdateEmployerInput!) {
    updateEmployer(id: $id, input: $input) {
      ...EmployerFields
      employees {
        ...EmployeeFields
      }
    }
  }
  ${EMPLOYER_FRAGMENT}
  ${EMPLOYEE_FRAGMENT}
`;

export const DELETE_EMPLOYERS = gql`
  mutation DeleteEmployers($ids: [String!]!) {
    deleteEmployers(ids: $ids) {
      id
    }
  }
`;

export const ARCHIVE_EMPLOYERS = gql`
  mutation ArchiveEmployer($ids: [String!]!, $archive: Boolean!) {
    archiveEmployers(ids: $ids, status: $archive) {
      ...EmployerFields
    }
  }
  ${EMPLOYER_FRAGMENT}
`;

export const GET_EMPLOYERS_HMO_PLAN_TYPES = gql`
  query GetEmployerHmoPlanTypes($employerId: String!) {
    employerHmoPlanTypes(employerId: $employerId) {
      id
      name
      premiumDetails {
        amount
        category
        frequency
        administrationAgency
        commissionRate
        enrollmentCommissionRate {
          id
          name
          commissionRate
        }
      }
    }
  }
`;

export const GET_EMPLOYER_DETAILS = gql`
  query GetEmployerDetails($id: String!) {
    employer(id: $id) {
      id
      employerName
      employerAddress
      selectedMemberPlans {
        id
        name
        frequency
      }
    }
  }
`;

export const UPDATE_EMPLOYEES = gql`
  mutation UpdateEmployees($input: [UpdateEmployeeInput!]!) {
    updateEmployees(inputs: $input) {
      ...EmployeeFields
    }
  }
  ${EMPLOYEE_FRAGMENT}
`;

export const ADD_EMPLOYEES = gql`
  mutation AddEmployees($input: [CreateEmployeeInput!]!, $employerId: String!) {
    addEmployees(inputs: $input, employerId: $employerId) {
      ...EmployeeFields
      hmoProfile {
        id
        primaryProviderId
        primaryProviderAddress
        primaryProviderName
        provider {
          id
          name
        }
        memberNumber
        memberStatus
        enrollmentDateTime
        paymentDateTime
        premiumCollected
        registrationSource
        administrationAgency
        commissionRate
        commissionPayable
        tpaNonTpaCommissionRate
        tpaNonTpaCommissionPayable
        enrollmentAgent
        enrollmentAgency
        planCategory
        salesWing
        sponsorType
        sponsorName
        hmoPlanType {
          id
          premiumDetails {
            frequency
            category
            amount
          }
        }
      }
      profile {
        id
        gender
        title
        details {
          id
          dateOfBirth
        }
        user {
          nonCorporateEmail
          phoneNumber
        }
      }
      dependants {
        ...EmployeeDependentFields
      }
    }
  }
  ${EMPLOYEE_FRAGMENT}
  ${EMPLOYEE_DEPENDENT_FRAGMENT}
`;

export const DELETE_EMPLOYEES = gql`
  mutation DeleteEmployees($ids: [String!]!) {
    deleteEmployees(ids: $ids) {
      id
      employerId
    }
  }
`;

export const ADD_EMPLOYEE_DEPENDENT = gql`
  mutation AddEmployeeDependent($employeeId: String!, $input: [CreateEmployerDependantInput!]!) {
    addDependants(employeeId: $employeeId, inputs: $input) {
      ...EmployeeDependentFields
    }
  }
  ${EMPLOYEE_DEPENDENT_FRAGMENT}
`;

export const UPDATE_EMPLOYEE_DEPENDENT = gql`
  mutation UpdateEmployeeDependent($employeeId: String!, $input: [UpdateEmployerDependantInput!]!) {
    updateDependants(employeeId: $employeeId, inputs: $input) {
      ...EmployeeDependentFields
    }
  }
  ${EMPLOYEE_DEPENDENT_FRAGMENT}
`;

export const DELETE_EMPLOYEE_DEPENDENT = gql`
  mutation DeleteEmployeeDependent($ids: [String!]!) {
    deleteDependants(ids: $ids) {
      id
      employeeId
    }
  }
`;

export const GET_PATIENT_REGISTRATION_SUMMARY_METRICS = gql`
  query GetPatientRegistrationSummaryMetrics($filterOptions: ProfilesFilterInputs!) {
    getPatientRegistrationSummary(filterOptions: $filterOptions) {
      totalCommissionPayable
      totalEnrollees
      totalAgents
      totalOfficers
      totalEmployers
      totalProviders
      totalEnrolled
    }
  }
`;
