import { gql } from '@apollo/client';
import { PREAUTHORIZATION_DETAILS_FRAGMENT } from 'apollo-queries/fragments/preauthorization-details';
import { AUDIT_FIELDS } from '../fragments/audit';

const ONCOLOGY_CONSULTATION_REGISTER_FIELDS = gql`
  fragment OncologyConsultationRegister on OncologyConsultationRegisterModel {
    id
    creatorName
    lastModifierName
    treatmentChart {
      noOfCycles
      periods {
        day
        cycles {
          cycleNo
          value
        }
      }
    }
    therapyChart {
      noOfCycles
      periods {
        day
        cycles {
          cycleNo
          value
        }
      }
    }
    createdDate
    updatedDate
  }
`;

const ONCOLOGY_TREATMENT_PLAN_FIELDS = gql`
  fragment OncologyTreatmentPlan on OncologyTreatmentPlanModel {
      id
      treatmentPlan
      conceal
      patientAdmitted
      concealObservationNote
      observationNote
      admissionConsent
      adverseEffectsFollowingTreatment
      stateEffects
      adverseEffectsInvestigated
      outcomeOfInvestigation
      treatmentGiven
      patientConsentSignature
      patientConsentSignatureType
      patientConsentSignatureDateTime
      treatmentStatus
    ${AUDIT_FIELDS}
  }
`;

const ONCOLOGY_CONSULTATION_HISTORY = gql`
  fragment OncologyConsultationHistory on OncologyConsultationHistoryModel {
    id
    consultationDateTime
    duration
    priority
    category
    doctorName
    specialty
    rank
    department
    consultationStartDate
    consultationEndDate
    initialDiagnosisICD10
    initialDiagnosisICD11
    initialDiagnosisSNOMED
    finalDiagnosisICD10
    finalDiagnosisICD11
    finalDiagnosisSNOMED
    diagnosisDateTime
    diagnosedBy
    additionalNote
    stageDiagnosisICD10
    stageDiagnosisICD11
    stageDiagnosisSNOMED
    stageDiagnosisDateTime
    classification
    stage
    stageDate
    histopathologicType
    stageTiming
    primaryTumor
    residualTumor
    tumorDetails {
      size
      laterality
    }
    lymphovascularInvasion
    regionalLymphNodes
    numberOfNodes
    distantMetastasis
    grade
    stageStatus
    cancerType
    progression
    relapse
    remission
    stageTreatmentType
    stageAdditionalNote
    nuclearGradeOrPleomorphism
    mitoticCountScore
    tubuleFormation
    stageGroupingScore
    scarffBloomRichardsonScore
    nottinghamModificationSbrGrade
    stageLymphovascularInvasion
    stageHistopathologicType
    diagnosticInformation
    typeOfSpecimen
    stagingRole
    stagingDate
    treatmentType
    treatmentSite
    intentOfTreatment
    lineOfTreatment
    concurrentTreatment
    treatmentPlanProvider
    treatmentDepartment
    treatmentStatus
    treatmentPriority
    treatmentInterval
    treatmentStartDate
    treatmentEndDate
    treatmentCycleDays
    treatmentCycleNumber
    treatmentPatientType
    treatmentAdverseReaction
    treatmentSpecificReaction
    treatmentOutcome
    treatmentResponse
    treatmentFollowupDate
    treatmentAdditionalNote
    therapyType
    therapySite
    intentOfTherapy
    lineOfTherapy
    concurrentTherapy
    therapyPlanProvider
    therapyDepartment
    therapyStatus
    therapyPriority
    therapyInterval
    therapyStartDate
    therapyEndDate
    therapyCycleDays
    therapyCycleNumber
    therapyPatientType
    therapyAdverseReaction
    therapySpecificReaction
    therapyOutcome
    therapyResponse
    therapyFollowupDate
    therapyAdditionalNote
    nottinghamGradeAbove
    estrogenReceptorExpression
    erPercentagePositive
    erAllredScore
    progesteroneReceptor
    prPercentagePositive
    prAllredScore
    overallHer2Status
    ihcScore
    fishResult
    fishCopyNumber
    her2OrCep17Ratio
    circulatingTumorCells
    complaintGender
    complaintSmartText
    complaintSmartSelection {
      positives
      negatives
      vitalSignRevied
      nurseNoteReviewed
      allOtherSystemNegative
      otherSystemNegative
      topLevelChecks
      systemChecks
      systemPositives
      systemNegatives
    }
    systemReviewSmartText
    systemReviewSmartSelection {
      positives
      negatives
      vitalSignRevied
      nurseNoteReviewed
      allOtherSystemNegative
      otherSystemNegative
      topLevelChecks
      systemChecks
      systemPositives
      systemNegatives
    }
    physicalExamSmartText
    physicalExamSmartSelection {
      positives
      negatives
      vitalSignRevied
      nurseNoteReviewed
      allOtherSystemNegative
      otherSystemNegative
      topLevelChecks
      systemChecks
      systemPositives
      systemNegatives
    }
    complaint
    concealComplaint
    complaintHistory
    concealComplaintHistory
    systemReview
    concealSystemReview
    physicalExam
    concealPhysicalExam
    audiometry
    concealAudiometry
    healthEducation
    concealHealthEducation
    oncologyRegister {
      ...OncologyConsultationRegister
    }
    profileId
    profile {
      id
      clinifyId
      coverageDetails {
        id
        name
        coverageType
        companyName
        familyName
        memberNumber
        provider {
          id
          name
        }
      }
    }
    hmoProviderId
    serviceDetails {
      priceId
      type
      name
      quantity
      pricePerUnit
      patientType
      paymentType
      reference
    }
    hospitalId
    facilityName
    facilityAddress
    billStatus
    isPackage
    appointmentId
    documentUrl
    laboratoryTestVerified
    radiologyExaminationVerified
    chemoNote
    concealChemoNote
    preauthorizationDetails {
      ...PreauthorizationDetails
    }
    ${AUDIT_FIELDS}
  }
  ${ONCOLOGY_CONSULTATION_REGISTER_FIELDS}
  ${PREAUTHORIZATION_DETAILS_FRAGMENT}
`;

export const ONCOLOGY_CONSULTATION_LINKING_FIELDS = gql`
  fragment OncologyConsultationLinking on OncologyConsultationHistoryModel {
    allergies {
      id
    }
    medications {
      id
    }
    surgeries {
      id
    }
    admissions {
      id
    }
    vitals {
      id
    }
    labTests {
      id
    }
    radiology {
      id
    }
    investigations {
      id
    }
    nursingServices {
      id
    }
  }
`;

export const ONCOLOGY_CHEMO_DRUG = gql`
  fragment OncologyChemoDrug on OncologyChemoDrugModel {
    id
    drugName
    route
    infusionUsed
    dosage
    dosagePercentage
    totalDose
    adjustedDose
    quantity
    day
    cycleNumber
    drugId
    note
    section
    frequency
    combinationGroupName
    combinationName
    chemoDiagnosis
    inventoryClass
    medicationDetailsId
    investigationDetails {
      investigationName
      investigationType
      investigationPerformed
      investigationVerified
    }
    administrationRegister {
      administeredBy
      administratorId
      administrationDateTime
      period
    }
  }
`;

export const GET_ONCOLOGY_CONSULTATION_HISTORIES = gql`
  query GetOncologyConsultationHistories($filterOptions: FilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      oncologyConsultationHistories(filterOptions: $filterOptions) {
        totalCount
        list {
          ...OncologyConsultationHistory
          bill {
            id
            createdDate
          }
        }
      }
    }
  }
  ${ONCOLOGY_CONSULTATION_HISTORY}
`;

export const GET_ONCOLOGY_CONSULTATION_HISTORY = gql`
  query GetOncologyConsultationHistory($id: String!, $clinifyId: String!) {
    oncologyConsultationHistory(id: $id, clinifyId: $clinifyId) {
      ...OncologyConsultationHistory
      oncologyChemoDrugs {
        ...OncologyChemoDrug
      }
      treatmentPlans {
        ...OncologyTreatmentPlan
      }
      ...OncologyConsultationLinking
    }
  }
  ${ONCOLOGY_CONSULTATION_HISTORY}
  ${ONCOLOGY_CHEMO_DRUG}
  ${ONCOLOGY_CONSULTATION_LINKING_FIELDS}
  ${ONCOLOGY_TREATMENT_PLAN_FIELDS}
`;

export const ADD_ONCOLOGY_CONSULTATION_HISTORY = gql`
  mutation AddOncologyConsultationHistory(
    $input: NewOncologyConsultationInput!
    $id: String
    $pin: String
    $prescribe: Boolean
  ) {
    addOncologyConsultationHistory(input: $input, id: $id, pin: $pin, prescribe: $prescribe) {
      ...OncologyConsultationHistory
      treatmentPlans {
        ...OncologyTreatmentPlan
      }
      bill {
        id
        createdDate
      }
      ...OncologyConsultationLinking
    }
  }
  ${ONCOLOGY_CONSULTATION_HISTORY}
  ${ONCOLOGY_CONSULTATION_LINKING_FIELDS}
  ${ONCOLOGY_TREATMENT_PLAN_FIELDS}
`;

export const UPDATE_ONCOLOGY_CONSULTATION_HISTORY = gql`
  mutation UpdateOncologyConsultationHistory(
    $input: OncologyConsultationInput!
    $id: String!
    $pin: String
    $prescribe: Boolean
  ) {
    updateOncologyConsultationHistory(input: $input, id: $id, pin: $pin, prescribe: $prescribe) {
      ...OncologyConsultationHistory
      treatmentPlans {
        ...OncologyTreatmentPlan
      }
      hmoClaim {
        id
      }
      oncologyChemoDrugs {
        ...OncologyChemoDrug
      }
      ...OncologyConsultationLinking
    }
  }
  ${ONCOLOGY_CONSULTATION_HISTORY}
  ${ONCOLOGY_CHEMO_DRUG}
  ${ONCOLOGY_CONSULTATION_LINKING_FIELDS}
  ${ONCOLOGY_TREATMENT_PLAN_FIELDS}
`;

export const UPDATE_ONCOLOGY_CONSULTATION_HISTORY_BILL = gql`
  mutation UpdateOncologyConsultationHistoryBill($input: OncologyConsultationInput!, $id: String!) {
    updateOncologyConsultationHistoryBill(input: $input, id: $id) {
      ...OncologyConsultationHistory
      treatmentPlans {
        ...OncologyTreatmentPlan
      }
      hmoClaim {
        id
      }
      ...OncologyConsultationLinking
    }
  }
  ${ONCOLOGY_CONSULTATION_HISTORY}
  ${ONCOLOGY_CONSULTATION_LINKING_FIELDS}
  ${ONCOLOGY_TREATMENT_PLAN_FIELDS}
`;

export const DELETE_ONCOLOGY_CONSULTATION_HISTORY = gql`
  mutation DeleteOncologyConsultationHistory($ids: [String!]!, $clinifyId: String!) {
    deleteOncologyConsultationHistory(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const UPDATE_ONCOLOGY_CONSULTATION_CHART = gql`
  mutation UpdateOncologyConsultationRegister(
    $clinifyId: String!
    $input: OncologyRegisterChart!
    $chartType: String!
    $id: String!
    $pin: String
  ) {
    updateOncologyConsultationRegister(
      clinifyId: $clinifyId
      input: $input
      chartType: $chartType
      id: $id
      pin: $pin
    ) {
      ...OncologyConsultationRegister
    }
  }
  ${ONCOLOGY_CONSULTATION_REGISTER_FIELDS}
`;

export const ARCHIVE_ONCOLOGY_CONSULTATION_HISTORY = gql`
  mutation ArchiveOncologyConsultationHistory(
    $ids: [String!]!
    $archive: Boolean
    $clinifyId: String!
  ) {
    archiveOncologyConsultationHistory(ids: $ids, archive: $archive, clinifyId: $clinifyId) {
      ...OncologyConsultationHistory
      ...OncologyConsultationLinking
    }
  }
  ${ONCOLOGY_CONSULTATION_HISTORY}
  ${ONCOLOGY_CONSULTATION_LINKING_FIELDS}
`;

export const GET_ONCOLOGY_CONSULTATION_HISTORY_CLAIM = gql`
  query OncologyConsultationHistoryClaim($id: String!, $clinifyId: String!) {
    oncologyConsultationHistory(id: $id, clinifyId: $clinifyId) {
      id
      hmoClaim {
        id
      }
    }
  }
`;

export const LINK_RECORDS_TO_ONCOLOGY_CONSULTATION = gql`
    mutation LinkRecordsToOncologyConsultation($parentId: String!, $recordIds: [String!]!, $recordType: OncologyConsultationLinkedRecordType!) {
      linkRecordsToOncologyConsultation(id: $parentId, recordIds: $recordIds, recordType: $recordType) {
        ...OncologyConsultationHistory
        treatmentPlans {
          ...OncologyTreatmentPlan
        }
        ...OncologyConsultationLinking
        ${AUDIT_FIELDS}
      }
    }
    ${ONCOLOGY_CONSULTATION_HISTORY}
    ${ONCOLOGY_CONSULTATION_LINKING_FIELDS}
    ${ONCOLOGY_TREATMENT_PLAN_FIELDS}
`;

export const ONCOLOGY_CONSULTATION_HISTORY_ADDED_SUBS = gql`
  subscription OncologyConsultationAddedSubs($profileId: String!) {
    OncologyConsultationAdded(profileId: $profileId) {
      ...OncologyConsultationHistory
      ...OncologyConsultationLinking
      bill {
        id
        createdDate
      }
    }
  }
  ${ONCOLOGY_CONSULTATION_HISTORY}
  ${ONCOLOGY_CONSULTATION_LINKING_FIELDS}
`;

export const ONCOLOGY_CONSULTATION_HISTORY_UPDATED_SUBS = gql`
  subscription OncologyConsultationUpdatedSubs($profileId: String!) {
    OncologyConsultationUpdated(profileId: $profileId) {
      ...OncologyConsultationHistory
      ...OncologyConsultationLinking
    }
  }
  ${ONCOLOGY_CONSULTATION_HISTORY}
  ${ONCOLOGY_CONSULTATION_LINKING_FIELDS}
`;

export const DELETE_ONCOLOGY_CONSULTATION_HISTORY_SUBS = gql`
  subscription DeleteOncologyConsultationSubs($profileId: String!) {
    OncologyConsultationRemoved(profileId: $profileId) {
      id
    }
  }
`;

export const ARCHIVE_ONCOLOGY_CONSULTATION_HISTORY_SUBS = gql`
  subscription ArchiveOncologyConsultationSubs($profileId: String!) {
    OncologyConsultationArchived(profileId: $profileId) {
      ...OncologyConsultationHistory
      ...OncologyConsultationLinking
    }
  }
  ${ONCOLOGY_CONSULTATION_HISTORY}
  ${ONCOLOGY_CONSULTATION_LINKING_FIELDS}
`;

export const UNARCHIVE_ONCOLOGY_CONSULTATION_HISTORY_SUBS = gql`
  subscription UnarchiveOncologyConsultationSubs($profileId: String!) {
    OncologyConsultationUnarchived(profileId: $profileId) {
      ...OncologyConsultationHistory
      ...OncologyConsultationLinking
    }
  }
  ${ONCOLOGY_CONSULTATION_HISTORY}
  ${ONCOLOGY_CONSULTATION_LINKING_FIELDS}
`;

export const ONCOLOGY_CONSULTATION_CHART_UPDATED_SUBS = gql`
  subscription OncologyConsultationChartUpdatedSubs($profileId: String!) {
    OncologyConsultationChartUpdated(profileId: $profileId) {
      ...OncologyConsultationRegister
    }
  }
  ${ONCOLOGY_CONSULTATION_REGISTER_FIELDS}
`;

export const CONCEAL_ONCOLOGY_CONSULTATION_COMPLAINT = gql`
  mutation ConcealOncologyConsultationComplaint($id: String!, $concealStatus: Boolean!) {
    concealOncologyConsultationComplaint(oncologyId: $id, concealStatus: $concealStatus) {
      id
      concealComplaint
      complaint
    }
  }
`;

export const CONCEAL_ONCOLOGY_CONSULTATION_COMPLAINT_HISTORY = gql`
  mutation ConcealOncologyConsultationComplaintHistory($id: String!, $concealStatus: Boolean!) {
    concealOncologyConsultationComplaintHistory(oncologyId: $id, concealStatus: $concealStatus) {
      id
      concealComplaintHistory
      complaintHistory
    }
  }
`;

export const CONCEAL_ONCOLOGY_CONSULTATION_SYSTEM_REVIEW = gql`
  mutation ConcealOncologyConsultationSystemReview($id: String!, $concealStatus: Boolean!) {
    concealOncologyConsultationSystemReview(oncologyId: $id, concealStatus: $concealStatus) {
      id
      concealSystemReview
      systemReview
    }
  }
`;

export const CONCEAL_ONCOLOGY_CONSULTATION_PHYSICAL_EXAM = gql`
  mutation ConcealOncologyConsultationPhysicalExam($id: String!, $concealStatus: Boolean!) {
    concealOncologyConsultationPhysicalExam(oncologyId: $id, concealStatus: $concealStatus) {
      id
      concealPhysicalExam
      physicalExam
    }
  }
`;

export const CONCEAL_ONCOLOGY_CONSULTATION_AUDIOMETRY = gql`
  mutation ConcealOncologyConsultationAudiometry($id: String!, $concealStatus: Boolean!) {
    concealOncologyConsultationAudiometry(oncologyId: $id, concealStatus: $concealStatus) {
      id
      concealAudiometry
      audiometry
    }
  }
`;

export const CONCEAL_ONCOLOGY_CONSULTATION_HEALTH_EDUCATION = gql`
  mutation ConcealOncologyConsultationHealthEducation($id: String!, $concealStatus: Boolean!) {
    concealOncologyConsultationHealthEducation(oncologyId: $id, concealStatus: $concealStatus) {
      id
      concealHealthEducation
      healthEducation
    }
  }
`;

export const CONCEAL_ONCOLOGY_CONSULTATION_CHEMO_NOTE = gql`
  mutation ConcealOncologyConsultationChemoNote($id: String!, $concealStatus: Boolean!) {
    concealOncologyConsultationChemoNote(oncologyId: $id, concealStatus: $concealStatus) {
      id
      concealChemoNote
      chemoNote
    }
  }
`;

export const ADMINISTER_CHEMO_DRUG = gql`
  mutation AdministerChemoDrug(
    $id: String!
    $period: String!
    $status: Boolean!
    $clinifyId: String!
    $pin: String
  ) {
    administerChemoDrug(
      id: $id
      period: $period
      status: $status
      clinifyId: $clinifyId
      pin: $pin
    ) {
      ...OncologyChemoDrug
    }
  }
  ${ONCOLOGY_CHEMO_DRUG}
`;

export const ONCOLOGY_CHEMO_DRUG_UPDATED_SUBS = gql`
  subscription ChemoDrugUpdatedSubs($profileId: String!) {
    ChemoDrugUpdated(profileId: $profileId) {
      ...OncologyChemoDrug
      oncologyConsultationHistory {
        id
        chemoComments {
          cycleNumber
          section
          comment
          creatorId
          creatorName
          createdDate
          lastModifierId
          lastModifierName
          updatedDate
        }
      }
    }
  }
  ${ONCOLOGY_CHEMO_DRUG}
`;

export const ADD_ONCOLOGY_CONSULTATION_TREATMENT_PLAN = gql`
  mutation AddOncologyConsultationTreatmentPlan(
    $input: OncologyTreatmentPlanInput!
    $id: String!
    $clinifyId: String!
    $pin: String
  ) {
    addOncologyConsultationTreatmentPlan(
      consultationId: $id
      input: $input
      clinifyId: $clinifyId
      pin: $pin
    ) {
      ...OncologyTreatmentPlan
    }
  }
  ${ONCOLOGY_TREATMENT_PLAN_FIELDS}
`;

export const UPDATE_ONCOLOGY_CONSULTATION_TREATMENT_PLAN = gql`
  mutation UpdateOncologyConsultationTreatmentPlan(
    $input: OncologyTreatmentPlanInput!
    $id: String!
    $clinifyId: String!
    $pin: String
  ) {
    updateOncologyConsultationTreatmentPlan(
      id: $id
      input: $input
      clinifyId: $clinifyId
      pin: $pin
    ) {
      ...OncologyTreatmentPlan
    }
  }
  ${ONCOLOGY_TREATMENT_PLAN_FIELDS}
`;

export const DELETE_ONCOLOGY_CONSULTATION_TREATMENT_PLAN = gql`
  mutation DeleteOncologyConsultationTreatmentPlan($id: String!, $clinifyId: String!) {
    deleteOncologyConsultationTreatmentPlan(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const CONCEAL_ONCOLOGY_CONSULTATION_TREATMENT_PLAN = gql`
  mutation ConcealOncologyConsultationTreatmentPlan($id: String!, $concealStatus: Boolean!) {
    concealOncologyConsultationTreatmentPlan(treatmentPlanId: $id, concealStatus: $concealStatus) {
      ...OncologyTreatmentPlan
    }
  }
  ${ONCOLOGY_TREATMENT_PLAN_FIELDS}
`;

export const CONCEAL_ONCOLOGY_CONSULTATION_OBSERVATION_NOTE = gql`
  mutation ConcealOncologyTreatmentPlanObservationNote($id: String!, $concealStatus: Boolean!) {
    concealOncologyTreatmentPlanObservationNote(
      treatmentPlanId: $id
      concealStatus: $concealStatus
    ) {
      ...OncologyTreatmentPlan
    }
  }
  ${ONCOLOGY_TREATMENT_PLAN_FIELDS}
`;

export const ADD_ONCOLOGY_CONSULTATION_TREATMENT_PLAN_SIGNATURE = gql`
  mutation AddOncologyTreatmentPlanConsentSignature($id: String!, $clinifyId: String!, $input: SignatureInput!, $pin: String) {
    addOncologyTreatmentPlanConsentSignature(id: $id, clinifyId: $clinifyId, input: $input, pin: $pin) {
      ...OncologyTreatmentPlan
      ${AUDIT_FIELDS}
    }
  }
  ${ONCOLOGY_TREATMENT_PLAN_FIELDS}
`;

export const UPDATE_ONCOLOGY_CONSULTATION_TREATMENT_PLAN_SIGNATURE = gql`
  mutation UpdateOncologyTreatmentPlanConsentSignature($id: String!, $clinifyId: String!, $input: SignatureInput!, $pin: String) {
    updateOncologyTreatmentPlanConsentSignature(id: $id, clinifyId: $clinifyId, input: $input, pin: $pin) {
      ...OncologyTreatmentPlan
      ${AUDIT_FIELDS}
    }
  }
  ${ONCOLOGY_TREATMENT_PLAN_FIELDS}
`;

export const DELETE_ONCOLOGY_CONSULTATION_TREATMENT_PLAN_SIGNATURE = gql`
  mutation DeleteOncologyTreatmentPlanConsentSignature($id: String!, $clinifyId: String!) {
    deleteOncologyTreatmentPlanConsentSignature(id: $id, clinifyId: $clinifyId) {
      ...OncologyTreatmentPlan
      ${AUDIT_FIELDS}
    }
  }
  ${ONCOLOGY_TREATMENT_PLAN_FIELDS}
`;

export const ONCOLOGY_CONSULTATION_TREATMENT_PLAN_ADDED_SUBS = gql`
  subscription OncologyConsultationTreatmentPlanAddedSubs($profileId: String!) {
    OncologyConsultationTreatmentPlanAdded(profileId: $profileId) {
      ...OncologyTreatmentPlan
      oncologyConsultationHistoryId
      ${AUDIT_FIELDS}
    }
  }
  ${ONCOLOGY_TREATMENT_PLAN_FIELDS}
`;

export const ONCOLOGY_CONSULTATION_TREATMENT_PLAN_UPDATED_SUBS = gql`
  subscription OncologyConsultationTreatmentPlanUpdatedSubs($profileId: String!) {
    OncologyConsultationTreatmentPlanUpdated(profileId: $profileId) {
      ...OncologyTreatmentPlan
      ${AUDIT_FIELDS}
    }
  }
  ${ONCOLOGY_TREATMENT_PLAN_FIELDS}
`;

export const ONCOLOGY_CONSULTATION_DELETE_TREATMENT_PLAN_SUBS = gql`
  subscription DeleteOncologyConsultationTreatmentPlanSubs($profileId: String!) {
    OncologyConsultationTreatmentPlanRemoved(profileId: $profileId) {
      id
      oncologyConsultationHistoryId
    }
  }
`;

export const UPDATE_CHEMO_COMMENT = gql`
  mutation UpdateChemoComment(
    $chemoDrugId: String!
    $clinifyId: String!
    $input: OncologyChemoCommentInput!
    $pin: String
  ) {
    updateChemoComment(chemoDrugId: $chemoDrugId, clinifyId: $clinifyId, input: $input, pin: $pin) {
      ...OncologyChemoDrug
      oncologyConsultationHistory {
        id
        chemoComments {
          cycleNumber
          section
          comment
          creatorId
          creatorName
          createdDate
          lastModifierId
          lastModifierName
          updatedDate
        }
      }
    }
  }
  ${ONCOLOGY_CHEMO_DRUG}
`;
