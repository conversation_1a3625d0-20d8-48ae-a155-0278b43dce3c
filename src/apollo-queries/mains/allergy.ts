import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

export const ALLERGY_FIELDS = gql`
  fragment Allergy on AllergyModel {
    id
    occurenceDate
    duration
    hospitalName
    hospitalAddress
    doctorName
    specialty
    rank
    department
    details {
      type
      trigger
      reactions
      severeness
      clinicalDiagnosis {
        diagnosisICD10
        diagnosisICD11
        diagnosisSNOMED
      }
    }
    profileId
    profile {
      id
      clinifyId
    }
    documentUrl
    concealAdditionalNote
    additionalNote
    createdDate
    updatedDate
  }
`;

export const ALLERGY_LINKING_FIELDS = gql`
  fragment AllergyLinking on AllergyModel {
    medications {
      id
    }
  }
`;

export const GET_PATIENT_ALLERGY_LIST = gql`
  query GetPatientAllergyList($filterOptions: AllergyFilterInput, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      allergies(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Allergy
        }
      }
    }
  }
  ${ALLERGY_FIELDS}
`;

export const GET_PATIENT_ALLERGY = gql`
  query GetPatientAllergy($id: String!, $clinifyId: String!) {
    allergy(id: $id, clinifyId: $clinifyId) {
      ...Allergy
      ...AllergyLinking
      ${AUDIT_FIELDS}
    }
  }
  ${ALLERGY_FIELDS}
  ${ALLERGY_LINKING_FIELDS}
`;

export const ADD_PATIENT_ALLERGY = gql`
  mutation AddPatientAllergy($input: NewAllergyInput!, $id: String) {
    addAllergy(allergy: $input, id: $id) {
      ...Allergy
      ...AllergyLinking
      ${AUDIT_FIELDS}
    }
  }
  ${ALLERGY_FIELDS}
  ${ALLERGY_LINKING_FIELDS}
`;

export const LINK_RECORDS_TO_ALLERGY = gql`
    mutation LinkRecordsToAllergy($parentId: String!, $recordIds: [String!]!, $recordType: AllergyLinkedRecordType!) {
        linkRecordsToAllergy(id: $parentId, recordIds: $recordIds, recordType: $recordType) {
            ...Allergy
            ...AllergyLinking
            ${AUDIT_FIELDS}
        }
    }
    ${ALLERGY_FIELDS}
    ${ALLERGY_LINKING_FIELDS}
`;

export const DELETE_PATIENT_ALLERGY = gql`
  mutation DeletePatientAllergies($ids: [String!]!, $clinifyId: String!) {
    deleteAllergies(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const ARCHIVE_ALLERGY = gql`
  mutation ArchiveAllergy($ids: [String!]!, $archive: Boolean, $clinifyId: String!) {
    archiveAllergies(ids: $ids, archive: $archive, clinifyId: $clinifyId) {
      ...Allergy
      ...AllergyLinking
      ${AUDIT_FIELDS}
    }
  }
  ${ALLERGY_FIELDS}
  ${ALLERGY_LINKING_FIELDS}
`;

export const UPDATE_PATIENT_ALLERGY = gql`
  mutation UpdatePatientAllergy($input: AllergyInput!, $id: String!) {
    updateAllergy(allergy: $input, id: $id) {
      ...Allergy
      ...AllergyLinking
      ${AUDIT_FIELDS}
    }
  }
  ${ALLERGY_FIELDS}
  ${ALLERGY_LINKING_FIELDS}
`;

export const ALLERGY_ADDED_SUBS = gql`
  subscription AllergyAddedSubs($profileId: String!) {
    AllergyAdded(profileId: $profileId) {
      ...Allergy
      ${AUDIT_FIELDS}
    }
  }
  ${ALLERGY_FIELDS}
`;

export const ALLERGY_UPDATED_SUBS = gql`
  subscription AllergyUpdatedSubs($profileId: String!) {
    AllergyUpdated(profileId: $profileId) {
      ...Allergy
      ...AllergyLinking
      ${AUDIT_FIELDS}
    }
  }
  ${ALLERGY_FIELDS}
  ${ALLERGY_LINKING_FIELDS}
`;

export const DELETE_ALLERGY_SUBS = gql`
  subscription DeleteAllergySubs($profileId: String!) {
    AllergyRemoved(profileId: $profileId) {
      id
    }
  }
`;

export const ARCHIVE_ALLERGY_SUBS = gql`
  subscription ArchiveAllergySubs($profileId: String!) {
    AllergyArchived(profileId: $profileId) {
      ...Allergy
      ...AllergyLinking
    }
  }
  ${ALLERGY_FIELDS}
  ${ALLERGY_LINKING_FIELDS}
`;

export const UNARCHIVE_ALLERGY_SUBS = gql`
  subscription UnarchiveAllergySubs($profileId: String!) {
    AllergyUnarchived(profileId: $profileId) {
      ...Allergy
      ...AllergyLinking
    }
  }
  ${ALLERGY_FIELDS}
  ${ALLERGY_LINKING_FIELDS}
`;

export const CONCEAL_ALLERGY_ADDITIONAL_NOTE = gql`
  mutation ConcealAllergyAdditionalNote($id: String!, $concealStatus: Boolean!) {
    concealAllergyAdditionalNote(id: $id, concealStatus: $concealStatus) {
      id
      concealAdditionalNote
      additionalNote
    }
  }
`;
