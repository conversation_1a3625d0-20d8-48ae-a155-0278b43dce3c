import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from 'apollo-queries/fragments/audit';
import { PREAUTHORIZATION_DETAILS_FRAGMENT } from 'apollo-queries/fragments/preauthorization-details';

const POSTNATAL_FIELDS = gql`
  fragment Postnatal on PostnatalModel {
    id
    provider
    visitationDateTime
    seenBy
    deliveryDate
    hasMenstruationStarted
    menstruationStartDate
    uterus
    breastFeeding
    familyPlanning
    familyPlanningMethod
    contraceptivePillCount
    typeOfImplant
    nameOfInjectable
    firstTimeFpUser
    sourceOfReferral
    familyPlanningClientType
    vulva
    vitaminAGivenToBaby
    vitaminADoseGivenToBaby
    vitaminADoseUnitGivenToBaby
    babyComplaint
    stateBabyComplaint
    visitationNote
    concealVisitationNote
    treatmentPlan
    concealTreatmentPlan
    clinicalDiagnosis {
      diagnosisICD10
      diagnosisICD11
      diagnosisSNOMED
    }
    rank
    department
    healthEducation
    concealHealthEducation
    additionalNote
    concealAdditionalNote
    postpartumDepression
    appointmentId
    hmoProviderId
    serviceDetails {
      priceId
      type
      name
      quantity
      pricePerUnit
      paymentType
      patientType
      reference
    }
    isPackage
    billStatus
    facilityName
    facilityAddress
    documentUrl
    createdDate
    updatedDate
    hospitalId
    profileId
    profile {
      id
      clinifyId
      coverageDetails {
        id
        name
        coverageType
        companyName
        familyName
        memberNumber
        provider {
          id
          name
        }
      }
    }
    preauthorizationDetails {
      ...PreauthorizationDetails
    }
  }
  ${PREAUTHORIZATION_DETAILS_FRAGMENT}
`;

export const GET_POSTNATAL_LIST = gql`
  query GetPostnatals($filterOptions: PostnatalFilterOptions, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      postnatals(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Postnatal
          bill {
            id
            createdDate
          }
          billStatus
        }
      }
    }
  }
  ${POSTNATAL_FIELDS}
`;

export const GET_POSTNATAL = gql`
  query GetPostnatal($id: String!, $clinifyId: String!) {
    postnatal(id: $id, clinifyId: $clinifyId) {
      ...Postnatal
      ${AUDIT_FIELDS}
    }
  }
  ${POSTNATAL_FIELDS}
`;

export const ADD_POSTNATAL = gql`
  mutation AddPostnatal($input: NewPostnatalInput!, $id: String,$pin: String) {
    addPostnatal(postnatal: $input, id: $id,pin: $pin) {
      ...Postnatal
      billStatus
      bill {
        id
        createdDate
      }
      hmoClaim{
        id
      }
      ${AUDIT_FIELDS}
    }
  }
  ${POSTNATAL_FIELDS}
`;

export const UPDATE_POSTNATAL = gql`
  mutation UpdatePostnatal($id: String!, $input: PostnatalInput!, $pin: String) {
    updatePostnatal(id: $id, postnatal: $input,pin: $pin) {
      ...Postnatal
      hmoClaim{
        id
      }
      ${AUDIT_FIELDS}
    }
  }
  ${POSTNATAL_FIELDS}
`;

export const UPDATE_POSTNATAL_BILL = gql`
  mutation UpdatePostnatalBill($id: String!, $input: PostnatalInput!) {
    updatePostnatalBill(id: $id, postnatal: $input) {
      ...Postnatal
      hmoClaim{
        id
      }
      ${AUDIT_FIELDS}
    }
  }
  ${POSTNATAL_FIELDS}
`;

export const DELETE_POSTNATAL = gql`
  mutation DeletePostnatals($ids: [String!]!, $clinifyId: String!) {
    deletePostnatals(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const ARCHIVE_POSTNATAL = gql`
  mutation ArchivePostnatals($ids: [String!]!, $archive: Boolean, $clinifyId: String!) {
    archivePostnatals(ids: $ids, archive: $archive, clinifyId: $clinifyId) {
      ...Postnatal
      ${AUDIT_FIELDS}
    }
  }
  ${POSTNATAL_FIELDS}
`;

export const POSTNATAL_ADDED_SUBS = gql`
  subscription PostnatalAddedSubs($profileId: String!) {
    PostnatalAdded(profileId: $profileId) {
      ...Postnatal
      billStatus
      bill {
        id
        createdDate
      }
      ${AUDIT_FIELDS}
    }
  }
  ${POSTNATAL_FIELDS}
`;

export const POSTNATAL_UPDATED_SUBS = gql`
  subscription PostnatalUpdatedSubs($profileId: String!) {
    PostnatalUpdated(profileId: $profileId) {
      ...Postnatal
      ${AUDIT_FIELDS}
    }
  }
  ${POSTNATAL_FIELDS}
`;

export const DELETE_POSTNATAL_SUBS = gql`
  subscription DeletePostnatalSubs($profileId: String!) {
    PostnatalRemoved(profileId: $profileId) {
      id
    }
  }
`;

export const ARCHIVE_POSTNATAL_SUBS = gql`
  subscription ArchivePostnatalSubs($profileId: String!) {
    PostnatalArchived(profileId: $profileId) {
      ...Postnatal
    }
  }
  ${POSTNATAL_FIELDS}
`;

export const UNARCHIVE_POSTNATAL_SUBS = gql`
  subscription UnarchivePostnatalSubs($profileId: String!) {
    PostnatalUnarchived(profileId: $profileId) {
      ...Postnatal
    }
  }
  ${POSTNATAL_FIELDS}
`;

export const CONCEAL_POSTNATAL_VISITATION_NOTE = gql`
  mutation ConcealPostnatalVisitationNote($id: String!, $concealStatus: Boolean!) {
    concealPostnatalVisitationNote(id: $id, concealState: $concealStatus) {
      id
      concealVisitationNote
      visitationNote
    }
  }
`;

export const CONCEAL_POSTNATAL_HEALTH_EDUCATION = gql`
  mutation ConcealPostnatalHealthEducation($id: String!, $concealStatus: Boolean!) {
    concealPostnatalHealthEducation(id: $id, concealState: $concealStatus) {
      id
      concealHealthEducation
      healthEducation
    }
  }
`;

export const CONCEAL_POSTNATAL_ADDITIONAL_NOTE = gql`
  mutation ConcealPostnatalAdditionalNote($id: String!, $concealStatus: Boolean!) {
    concealPostnatalAdditionalNote(id: $id, concealState: $concealStatus) {
      id
      concealAdditionalNote
      additionalNote
    }
  }
`;

export const CONCEAL_POSTNATAL_TREATMENT_PLAN = gql`
  mutation ConcealPostnatalTreatmentPlan($id: String!, $concealStatus: Boolean!) {
    concealPostnatalTreatmentPlan(id: $id, concealState: $concealStatus) {
      id
      concealTreatmentPlan
      treatmentPlan
    }
  }
`;

export const GET_POSTNATAL_CLAIM = gql`
  query PostnatalClaim($id: String!, $clinifyId: String!) {
    postnatal(id: $id, clinifyId: $clinifyId) {
      id
      hmoClaim {
        id
      }
    }
  }
`;
