import { gql } from '@apollo/client';
import { AUDIT_FIELDS } from '../fragments/audit';

const QUESTIONNAIRE_DATA_FRAGMENTS = gql`
  fragment QuestionnaireDataFragment on QuestionnaireData {
    employmentStatus
    gender
    highestFormalEducationalLevel
    maritalStatus
    numberOfHouseholdMembers
    numberOfMattresses
    numberOfMobilePhones
    numberOfRooms
    numberOfTVs
    numberOfVehicles
    occupationalGroup
    primaryCookingImplement
    relationshipToHouseholdHead
    typeOfRoof
    typeOfToilet
    questionnaireScore
  }
`;

export const COVERAGE_INFORMATION_FIELDS = gql`
  fragment CoverageInformationField on CoverageInformationModel {
    id
    coverageType
    companyName
    companyAddress
    familyName
    familyAddress
    name
    contactAddress
    memberNumber
    memberPlan
    memberDueDate
    memberStatus
    hmoProfile {
      id
      memberNumber
      memberPlan
      employeeNumber
      employeeType
      employeeDivision
      memberPlanGroup
      memberPlanSubGroup
      capturedDate
      capturedBy
      enrolledBy
      enrollmentDateTime
      paymentDateTime
      paymentFrequency
      memberStartDate
      memberDueDate
      memberStatus
      companyName
      companyAddress
      memberUniqueId
      parentMemberUniqueId
      primaryProviderName
      secondaryProviderName
      tertiaryProviderName
      primaryProviderAddress
      secondaryProviderAddress
      tertiaryProviderAddress
      capitatedMember
      retired
      occupation
      capitatedAmount
      planEligibility
      terminationDate
      premiumCollected
      premiumOutstanding
      registrationSource
      administrationAgency
      commissionRate
      commissionPayable
      tpaNonTpaCommissionRate
      tpaNonTpaCommissionPayable
      enrollmentAgent
      enrollmentAgency
      planCategory
      salesWing
      sponsorType
      sponsorName
      referrer
      referrerCode
      referrerNumber
      referrerCommissionRate
      referrerCommissionPayable
      activationDatetime
      employerCode
      totalPremiumAmountPaid
      provider {
        id
        name
      }
    }
    fileNumber
    existingFamily
    ${AUDIT_FIELDS}
  }
`;

export const COVERAGE_DETAILS_FIELDS = gql`
  fragment CoverageDetailsField on CoverageDetailsInputType {
    id
    coverageType
    companyName
    companyAddress
    familyName
    familyAddress
    name
    contactAddress
    memberNumber
    memberPlan
    employeeNumber
    capturedDate
    capturedBy
    enrolledBy
    enrollmentDateTime
    paymentDateTime
    paymentFrequency
    memberStartDate
    memberDueDate
    memberStatus
    companyName
    companyAddress
    memberUniqueId
    parentMemberUniqueId
    primaryProviderId
    primaryProviderName
    secondaryProviderName
    tertiaryProviderName
    primaryProviderAddress
    secondaryProviderAddress
    tertiaryProviderAddress
    capitatedMember
    capitatedAmount
    employeeType
    employeeDivision
    occupation
    retired
    memberPlanGroup
    memberPlanSubGroup
    terminationDate
    premiumCollected
    premiumOutstanding
    registrationSource
    administrationAgency
    commissionRate
    commissionPayable
    tpaNonTpaCommissionRate
    tpaNonTpaCommissionPayable
    enrollmentAgent
    enrollmentAgency
    planCategory
    salesWing
    sponsorType
    sponsorName
    referrer
    referrerCode
    referrerNumber
    referrerCommissionRate
    referrerCommissionPayable
    employerCode
    activationDatetime
    totalPremiumAmountPaid
    questionnaireData {
      ...QuestionnaireDataFragment
    }
    provider {
      id
      name
    }
  }
  ${QUESTIONNAIRE_DATA_FRAGMENTS}
`;

export const GET_COVERAGE_INFORMATION = gql`
  query GetCoverageInformation($id: String!) {
    coverageInformation(id: $id) {
      ...CoverageInformationField
    }
  }
  ${COVERAGE_INFORMATION_FIELDS}
`;

export const GET_COVERAGE_INFORMATIONS = gql`
  query GetCoverageInformations($filterInput: ProfileInfosFilterInput!, $id: String!) {
    profile(clinifyId: $id) {
      id
      coverageInformations(filterInput: $filterInput) {
        totalCount
        list {
          ...CoverageInformationField
        }
      }
    }
  }
  ${COVERAGE_INFORMATION_FIELDS}
`;

export const ADD_COVERAGE_INFORMATION = gql`
  mutation AddCoverageInformation($input: CoverageDetailsInput!) {
    addCoverageInformation(input: $input) {
      ...CoverageInformationField
    }
  }
  ${COVERAGE_INFORMATION_FIELDS}
`;

export const UPDATE_COVERAGE_INFORMATION = gql`
  mutation UpdateCoverageInformation($id: String!, $input: CoverageDetailsInput!) {
    updateCoverageInformation(id: $id, input: $input) {
      ...CoverageInformationField
    }
  }
  ${COVERAGE_INFORMATION_FIELDS}
`;

export const DELETE_COVERAGE_INFORMATION = gql`
  mutation DeleteCoverageInformation($id: String!) {
    deleteCoverageInformation(id: $id) {
      ...CoverageInformationField
    }
  }
  ${COVERAGE_INFORMATION_FIELDS}
`;

export const COVERAGE_INFORMATION_ADDED_SUBS = gql`
  subscription CoverageAddedSubs($profileId: String!) {
    CoverageInformationAdded(profileId: $profileId) {
      ...HmoField
    }
  }
  ${COVERAGE_INFORMATION_FIELDS}
`;

export const COVERAGE_INFORMATION_UPDATED_SUBS = gql`
  subscription CoverageUpdatedSubs($profileId: String!) {
    CoverageInformationUpdated(profileId: $profileId) {
      ...HmoField
    }
  }
  ${COVERAGE_INFORMATION_FIELDS}
`;

export const DELETE_COVERAGE_INFORMATION_SUBS = gql`
  subscription DeleteCoverageSubs($profileId: String!) {
    CoverageInformationRemoved(profileId: $profileId) {
      id
    }
  }
`;

export const GET_COVERAGE_NAME_LIST = gql`
  query GetCoverageNameList($coverageType: String!) {
    getCoverageNameList(coverageType: $coverageType)
  }
`;

export const GET_ENROLLED_ENROLLEES = gql`
  query GetEnrolledEnrollees($dateRange: DateRangeInput!) {
    getEnrolledEnrollees(dateRange: $dateRange) {
      memberNumber
      membershipNumber
      planCategory
      title
      fullName
      companyName
      gender
      phoneNumber
      dateOfBirth
      maritalStatus
      relationship
      memberStatus
      premiumCollected
      enrollmentDateTime
      createdDate
      memberStartDate
      memberDueDate
      renewalCount
      providerName
      ownership
      tariffBand
      enrolledBy
      agentName
      agentPhoneNumber
      salesWing
      channelDivisions
      enrollmentAgent
      enrollmentAgency
      administrationAgency
      memberPlan
      paymentFrequency
      registrationSource
      lga
      commissionRate
      commissionPayable
      tpaNonTpaCommissionRate
      tpaNonTpaCommissionPayable
      referrer
      referrerNumber
      referrerCommissionRate
      referrerCommissionPayable
    }
  }
`;
