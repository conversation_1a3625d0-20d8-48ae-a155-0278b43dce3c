import { gql } from '@apollo/client';

const BANK_ACCOUNT_TRANSACTION_FIELDS = gql`
  fragment BankAccountTransactionFields on BankAccountTransactionModel {
    id
    transactionDateTime
    description
    reference
    amount
    transactionType
    transactionStatus
    sender {
      name
      bankName
      bankCode
      accountNumber
    }
    receiver {
      name
      accountNumber
      bankName
      bankCode
    }
    hospital {
      id
      name
      address
    }
    bill {
      id
      billingDateTime
      billStatus
    }
    invoice {
      id
      issueDate
      invoiceStatus
      invoiceReference
      recipient {
        name
        clinifyId
        email
        address
        phone
      }
      invoicePayments {
        id
        paymentStatus
        amountPaid
      }
    }
  }
`;

const TRANSFER_FUND_FIELDS = gql`
  fragment TransferFundFields on TransferFundModel {
    id
    createdBy {
      id
      fullName
      type
      clinifyId
      title
    }
    amount
    createdDate
    updatedDate
    destinationAccountName
    destinationAccountNumber
    destinationBankCode
    destinationBankName
    narration
    originatorName
    sourceAccountNumber
    transferReference
    transferStatus
    additionalNote
    serviceChargeAmount
    hospital {
      id
      name
      address
    }
  }
`;

export const FETCH_BANK_TRANSACTIONS = gql`
  query FetchBankTransactions($filterOptions: BankTransactionFilterInput!, $hospitalId: String) {
    fetchBankAccountTransactions(filterOptions: $filterOptions, hospitalId: $hospitalId) {
      totalCount
      list {
        ...BankAccountTransactionFields
      }
    }
  }
  ${BANK_ACCOUNT_TRANSACTION_FIELDS}
`;

export const FETCH_BANK_TRANSACTION = gql`
  query FetchBankTransaction($id: String!) {
    fetchBankAccountTransaction(id: $id) {
      ...BankAccountTransactionFields
    }
  }
  ${BANK_ACCOUNT_TRANSACTION_FIELDS}
`;

export const GET_BANK_TRANSACTIONS_SUMMARY = gql`
  query GetBankTransactionsSummary($options: BankTransactionFilterInput!) {
    getBankTransactionsSummary(filterOptions: $options) {
      totalTransactions
      totalTransactionsAmount
      totalInvoicePayments
      totalInvoicePaymentsAmount
      totalBillPayments
      totalBillPaymentsAmount
    }
  }
`;

export const GET_TRANSFER_FUNDS = gql`
  query GetTransferFunds($filterOptions: TransferFundFilterInput!, $hmoProviderId: String!) {
    transferFunds(filterInput: $filterOptions, hmoProviderId: $hmoProviderId) {
      totalCount
      list {
        ...TransferFundFields
      }
    }
  }
  ${TRANSFER_FUND_FIELDS}
`;

export const GET_TRANSFER_FUND = gql`
  query GetTransferFund($id: String!) {
    transferFund(id: $id) {
      ...TransferFundFields
    }
  }
  ${TRANSFER_FUND_FIELDS}
`;
