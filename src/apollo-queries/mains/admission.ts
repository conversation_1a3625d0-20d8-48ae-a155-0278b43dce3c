import { gql } from '@apollo/client';
import { AUDIT_FIELDS, AUDIT_FIELDS_WITH_ID } from '../fragments/audit';
import { PREAUTHORIZATION_DETAILS_FRAGMENT } from '../fragments/preauthorization-details';

const BLOOD_TRANSFUSION_FIELDS = gql`
  fragment BloodTransfusions on BloodTransfusionModel {
    id
    transfusionDateTime
    transfusionOrderGiven
    transfusionDoctor
    transfusionNurse
    patientBloodGroup
    patientGenoType
    crossMatchingTime
    bloodLabel
    bloodProduct
    expiryDate
    donorBloodType
    bloodPint
    lengthOfTransfusion
    transfusionStartDateTime
    transfusionEndDateTime
    adverseReaction
    reaction
    transfusionNote
    patientConsent
    consentReason
    bloodSource
    bloodDonorStatus
    concealTransfusionNote
    postTransfusionFBC
    concealPostTransfusionFBC
    diuretic
    diureticType
    patientConsentSignature
    patientConsentSignatureType
    patientConsentSignatureDateTime
  }
`;

export const FETCH_BLOOD_TRANSFUSIONS = gql`
  query BloodTransfusions($parentRecordId: String!) {
    admissionBloodTransfusions(admissionId: $parentRecordId) {
      ...BloodTransfusions
      ${AUDIT_FIELDS}
    }
  }
  ${BLOOD_TRANSFUSION_FIELDS}
`;

export const SAVE_BLOOD_TRANSFUSION = gql`
  mutation SaveBloodTransfusion($input: BloodTransfusionInput!, $id: String!, $clinifyId: String!,$pin: String) {
    saveBloodTransfusion(admissionId: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...BloodTransfusions
      ${AUDIT_FIELDS}
    }
  }
  ${BLOOD_TRANSFUSION_FIELDS}
`;

export const UPDATE_BLOOD_TRANSFUSION = gql`
  mutation UpdateBloodTransfusion($input: BloodTransfusionInput!, $id: String!, $clinifyId: String!, $pin: String) {
    updateBloodTransfusion(id: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...BloodTransfusions
      ${AUDIT_FIELDS}
    }
  }
  ${BLOOD_TRANSFUSION_FIELDS}
`;

export const CONCEAL_TRANSFUSION_NOTE = gql`
  mutation ConcealTransfusionNote($id: String!, $concealStatus: Boolean!) {
    concealTransfusionNote(bloodTransfusionId: $id, concealStatus: $concealStatus) {
      id
      concealTransfusionNote
      transfusionNote
    }
  }
`;

export const CONCEAL_POST_TRANSFUSION_FBC = gql`
  mutation ConcealPostTransfusionFBC($id: String!, $concealStatus: Boolean!) {
    concealPostTransfusionFBC(id: $id, concealStatus: $concealStatus) {
      id
      concealPostTransfusionFBC
      postTransfusionFBC
    }
  }
`;

export const DELETE_BLOOD_TRANSFUSION = gql`
  mutation DeleteBloodTransfusion($id: String!, $clinifyId: String!) {
    deleteBloodTransfusion(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const BLOOD_TRANSFUSION_ADDED_SUBS = gql`
  subscription BloodTransfusionAddedSubs($profileId: String!) {
    BloodTransfusionAdded(profileId: $profileId) {
      ...BloodTransfusions
      admissionId
      ${AUDIT_FIELDS}
    }
  }
  ${BLOOD_TRANSFUSION_FIELDS}
`;

export const BLOOD_TRANSFUSION_UPDATED_SUBS = gql`
  subscription BloodTransfusionUpdatedSubs($profileId: String!) {
    BloodTransfusionUpdated(profileId: $profileId) {
      ...BloodTransfusions
      ${AUDIT_FIELDS}
    }
  }
  ${BLOOD_TRANSFUSION_FIELDS}
`;

export const DELETE_BLOOD_TRANSFUSION_SUBS = gql`
  subscription DeleteBloodTransfusionSubs($profileId: String!) {
    BloodTransfusionRemoved(profileId: $profileId) {
      id
      admissionId
    }
  }
`;

const TRANSFER_PATIENT_FIELDS = gql`
  fragment TransferPatients on TransferPatientModel {
    id
    transferDateTime
    transferredBy
    transferReason
    concealTransferReason
    roomOption
    transferSource
    roomInventoryId
    transferHospital {
      id
      name
      clinifyId
    }
  }
`;

export const FETCH_TRANSFER_PATIENTS = gql`
  query TransferPatients($parentRecordId: String!) {
    admissionTransferPatients(admissionId: $parentRecordId) {
      ...TransferPatients
      ${AUDIT_FIELDS}
    }
  }
  ${TRANSFER_PATIENT_FIELDS}
`;

export const SAVE_TRANSFER_PATIENT = gql`
  mutation SaveTransferPatient($input: TransferPatientInput!, $id: String!, $clinifyId: String!, $pin: String) {
    saveTransferPatient(admissionId: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...TransferPatients
      ${AUDIT_FIELDS}
    }
  }
  ${TRANSFER_PATIENT_FIELDS}
`;

export const UPDATE_TRANSFER_PATIENT = gql`
  mutation UpdateTransferPatient($input: TransferPatientInput!, $id: String!, $clinifyId: String!, $pin: String) {
    updateTransferPatient(id: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...TransferPatients
      ${AUDIT_FIELDS}
    }
  }
  ${TRANSFER_PATIENT_FIELDS}
`;

export const CONCEAL_REASON_FOR_TRANSFER_PATIENT = gql`
  mutation ConcealReasonForTransfer($id: String!, $concealStatus: Boolean!) {
    concealReasonForTransfer(transferPatientId: $id, concealStatus: $concealStatus) {
      id
      concealTransferReason
      transferReason
    }
  }
`;

export const DELETE_TRANSFER_PATIENT = gql`
  mutation DeleteTransferPatient($id: String!, $clinifyId: String!) {
    deleteTransferPatient(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const TRANSFER_PATIENT_ADDED_SUBS = gql`
  subscription TransferPatientAddedSubs($profileId: String!) {
    TransferPatientAdded(profileId: $profileId) {
      ...TransferPatients
      admissionId
      ${AUDIT_FIELDS}
    }
  }
  ${TRANSFER_PATIENT_FIELDS}
`;

export const TRANSFER_PATIENT_UPDATED_SUBS = gql`
  subscription TransferPatientUpdatedSubs($profileId: String!) {
    TransferPatientUpdated(profileId: $profileId) {
      ...TransferPatients
      ${AUDIT_FIELDS}
    }
  }
  ${TRANSFER_PATIENT_FIELDS}
`;

export const DELETE_TRANSFER_PATIENT_SUBS = gql`
  subscription DeleteTransferPatientSubs($profileId: String!) {
    TransferPatientRemoved(profileId: $profileId) {
      id
      admissionId
    }
  }
`;

const DISCHARGE_PATIENT_FIELDS = gql`
  fragment DischargePatients on DischargePatientModel {
    id
    dischargeDate
    dischargedStatus
    deathDateTime
    deathCause
    deathLocation
    deathCertificateIssued
    dischargeSummary
    dischargedBy
    dischargedBySignature
    dischargedBySignatureType
    dischargedBySignatureDateTime
    dischargeDiagnosis {
      diagnosisICD10
      diagnosisICD11
      diagnosisSNOMED
    }
    causeOfDeath
    concealDischargeSummary
    followupDate
    dischargeLocation
    dischargeAddress
  }
`;

const INPUT_DETAILS_FIELDS = gql`
  fragment InputDetails on InputDetailsModel {
    id
    administrationDateTime
    administratorName
    inputFluidType
    routeOfAdministration
    inputQuantity
    inputQuantityUnit
    duration
    ${AUDIT_FIELDS}
  }
`;

const OUTPUT_DETAILS_FIELDS = gql`
  fragment OutputDetails on OutputDetailsModel {
    id
    administrationDateTime
    administratorName
    outputFluidType
    outputQuantity
    outputQuantityUnit
    observations
    concealObservations
    ${AUDIT_FIELDS}
  }
`;

const ADMISSION_LINES_FIELDS = gql`
  fragment AdmissionLines on AdmissionLinesModel {
    id
    placementDateTime
    placedBy
    placedPreArrival
    ivChangeDue
    size
    orientation
    site
    siteAssessment
    localAnesthetic
    duration
    lineStatus
    dressingDateTime
    dressingType
    dressingAppearance
    dressingIntervention
    lastDressingChange
    dressingChangeDue
    urinaryCatheterPlacedInEd
    insertedByAnother
    insertedBy
    insertionAttempts
    patientTolerance
    preExistingSite
    cathetherType
    tubeSize
    catheterBallonSize
    urineReturned
    removalDateTime
    removedBy
    removalReason
    ${AUDIT_FIELDS}
  }
`;

export const FETCH_DISCHARGE_PATIENTS = gql`
  query DischargePatients($parentRecordId: String!) {
    admissionDischargePatients(admissionId: $parentRecordId) {
      ...DischargePatients
      ${AUDIT_FIELDS}
    }
  }
  ${DISCHARGE_PATIENT_FIELDS}
`;

export const SAVE_DISCHARGE_PATIENT = gql`
  mutation SaveDischargePatient($input: DischargePatientInput!, $id: String!, $clinifyId: String!, $pin: String) {
    saveDischargePatient(admissionId: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...DischargePatients
      admission {
        id
        duration
      }
      ${AUDIT_FIELDS}
    }
  }
  ${DISCHARGE_PATIENT_FIELDS}
`;

export const UPDATE_DISCHARGE_PATIENT = gql`
  mutation UpdateDischargePatient($input: DischargePatientInput!, $id: String!, $clinifyId: String!, $pin: String) {
    updateDischargePatient(id: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...DischargePatients
      admission {
        id
        duration
      }
      ${AUDIT_FIELDS}
    }
  }
  ${DISCHARGE_PATIENT_FIELDS}
`;

export const CONCEAL_DISCHARGE_SUMMARY = gql`
  mutation ConcealDischargeSummary($id: String!, $concealStatus: Boolean!) {
    concealDischargeSummary(dischargePatientId: $id, concealStatus: $concealStatus) {
      id
      concealDischargeSummary
      dischargeSummary
    }
  }
`;

export const DELETE_DISCHARGE_PATIENT = gql`
  mutation DeleteDischargePatient($id: String!, $clinifyId: String!) {
    deleteDischargePatient(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const DISCHARGE_PATIENT_ADDED_SUBS = gql`
  subscription DischargePatientAddedSubs($profileId: String!) {
    DischargePatientAdded(profileId: $profileId) {
      ...DischargePatients
      admissionId
      admission {
        id
        duration
      }
      ${AUDIT_FIELDS}
    }
  }
  ${DISCHARGE_PATIENT_FIELDS}
`;

export const DISCHARGE_PATIENT_UPDATED_SUBS = gql`
  subscription DischargePatientUpdatedSubs($profileId: String!) {
    DischargePatientUpdated(profileId: $profileId) {
      ...DischargePatients
      ${AUDIT_FIELDS}
    }
  }
  ${DISCHARGE_PATIENT_FIELDS}
`;

export const DELETE_DISCHARGE_PATIENT_SUBS = gql`
  subscription DeleteDischargePatientSubs($profileId: String!) {
    DischargePatientRemoved(profileId: $profileId) {
      id
      admissionId
    }
  }
`;

const NOTES_FIELDS = gql`
  fragment AdmissionNotes on AdmissionNoteModel {
    id
    creatorProfileType
    note
    conceal
  }
`;

export const SAVE_ADMISSION_NOTES = gql`
  mutation SaveAdmissionNotes($input: AdmissionNoteInput!, $id: String!, $clinifyId: String!, $pin: String) {
    saveAdmissionNote(admissionId: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...AdmissionNotes
      ${AUDIT_FIELDS}
    }
  }
  ${NOTES_FIELDS}
`;

export const UPDATE_ADMISSION_NOTES = gql`
  mutation UpdateAdmissionNotes($input: AdmissionNoteInput!, $id: String!, $clinifyId: String!, $pin: String) {
    updateAdmissionNote(id: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...AdmissionNotes
      ${AUDIT_FIELDS}
    }
  }
  ${NOTES_FIELDS}
`;

export const CONCEAL_ADMISSION_NOTE = gql`
  mutation ConcealAdmissionNote($id: String!, $concealStatus: Boolean!) {
    concealAdmissionNote(id: $id, concealStatus: $concealStatus) {
      ...AdmissionNotes,
      ${AUDIT_FIELDS}
    }
  }
  ${NOTES_FIELDS}
`;

export const DELETE_ADMISSION_NOTES = gql`
  mutation DeleteAdmissionNotes($id: String!, $clinifyId: String!) {
    deleteAdmissionNote(id: $id, clinifyId: $clinifyId) {
      id
      admissionId
    }
  }
`;

export const ADMISSION_NOTE_ADDED_SUBS = gql`
  subscription AdmissionNoteAddedSubs($profileId: String!) {
    AdmissionNoteAdded(profileId: $profileId) {
      ...AdmissionNotes
      admissionId
      ${AUDIT_FIELDS}
    }
  }
  ${NOTES_FIELDS}
`;

export const ADMISSION_NOTE_UPDATED_SUBS = gql`
  subscription AdmissionNoteUpdatedSubs($profileId: String!) {
    AdmissionNoteUpdated(profileId: $profileId) {
      ...AdmissionNotes
      ${AUDIT_FIELDS}
    }
  }
  ${NOTES_FIELDS}
`;

export const ADMISSION_NOTE_DELETED_SUBS = gql`
  subscription AdmissionNoteRemovedSubs($profileId: String!) {
    AdmissionNoteRemoved(profileId: $profileId) {
      id
      admissionId
    }
  }
`;

export const ADMISSION_FIELDS = gql`
  fragment Admission on AdmissionModel {
    id
    admissionDate
    admissionDiagnosis {
      diagnosisICD10
      diagnosisICD11
      diagnosisSNOMED
    }
    duration
    priority
    category
    severeness
    admittedBy
    doctorInCharge
    ward
    provider
    providerServiceName
    hospitalUnit
    roomType
    fileNumber
    roomNumber
    bedNumber
    finding
    rank
    specialty
    department
    nurseName
    hmoProviderId
    serviceDetails {
      priceId
      type
      name
      quantity
      pricePerUnit
      patientType
      paymentType
      reference
    }
    isPackage
    bedAvailable
    roomOption
    patientConsent
    presentMedicalHistory
    valuablesOrBelongings
    patientEnvironmentOrientation
    currentMedications
    medicinesDeposition
    instructedToSendHome
    nonBroughtToHospital
    otherPlacement
    medicationOrDrug
    bloodTransfusion
    food
    latex
    adultAge
    adultSedativeMedication
    adultAmbulatorySupport
    mentalStatus
    childAge
    dehydration
    dizziness
    respirationDistress
    childAmbulatorySupport
    childSedativeMedication
    painScore
    painDescriptors
    specifyPainDescriptors
    painLocation
    specifyPainLocation
    acuity
    modifying
    tobaccoUse
    tobaccoUseDuration
    alcoholUse
    alcoholUseDuration
    psychologicalStatus
    specifyPsychologicalStatus
    sleep
    specifySleepDifficulty
    sleepRoutine
    specifySleepRoutine
    whatMakesYouSleep
    bathing
    dressing
    eating
    mobility
    stairClimbing
    toiletUse
    impairedHearing
    impairedVision
    canPerformAdl
    canRead
    canWrite
    hearingAid
    glasses
    contacts
    dentures
    partial
    difficultyInChewing
    difficultyInSwallowing
    specialDiet
    specifySpecialDiet
    reAdmission
    lastAdmissionDateTime
    specialCourtesy
    specialArrangement
    pallorSunkenEyesDehydrationAnorexia
    vomittingDiarrheaEdema
    newlyDiagnosedDiabeticOrHypertensive
    hairOrSkinChange
    nursingNeeds
    nursingDiagnosis
    objectives
    nursingOrders
    evaluation
    dischargeDate
    transferDate
    roomInventoryId
    clinicName
    clinicAddress
    appointmentId
    billStatus
    documentUrl
    createdDate
    updatedDate
    hospitalId
    patientConsentSignature
    patientConsentSignatureType
    patientConsentSignatureDateTime
    profileId
    vteAndBleedingRiskAssessment {
      furtherAssessment {
        bleedingRiskAssessed
        vteRiskAssessed
        bleedingRiskAssessedLastModifiedDate
        vteRiskAssessedLastModifiedDate
        bleedingRiskAssessedLastModifierName
        vteRiskAssessedLastModifierName
        ref
      }
      onAdmission {
        bleedingRiskAssessed
        vteRiskAssessed
        bleedingRiskAssessedLastModifiedDate
        vteRiskAssessedLastModifiedDate
        bleedingRiskAssessedLastModifierName
        vteRiskAssessedLastModifierName
      }
      within24Hours {
        bleedingRiskAssessed
        vteRiskAssessed
        bleedingRiskAssessedLastModifiedDate
        vteRiskAssessedLastModifiedDate
        bleedingRiskAssessedLastModifierName
        vteRiskAssessedLastModifierName
      }
    }
    profile {
      id
      clinifyId
      coverageDetails {
        id
        name
        coverageType
        companyName
        familyName
        memberNumber
        provider {
          id
          name
        }
      }
    }
    preauthorizationDetails {
      ...PreauthorizationDetails
    }
  }
  ${PREAUTHORIZATION_DETAILS_FRAGMENT}
`;

export const ADMISSION_LINKING_FIELDS = gql`
  fragment AdmissionLinking on AdmissionModel {
    allergies {
      id
    }
    medications {
      id
    }
    surgeries {
      id
    }
    consultations {
      id
    }
    vitals {
      id
    }
    radiology {
      id
    }
    labTests {
      id
    }
    investigations {
      id
    }
    nursingServices {
      id
    }
  }
`;

export const FETCH_ADMISSIONS = gql`
  query FetchAdmissions($filterOptions: AdmissionFilterInput, $id: String!) {
    profile(clinifyId: $id) {
      id
      clinifyId
      admissions(filterOptions: $filterOptions) {
        totalCount
        list {
          status
          ...Admission
          bill {
            id
            createdDate
          }
          billStatus
          ...AdmissionLinking
          bloodTransfusions {
            ...BloodTransfusions
          }
          transferPatients {
            ...TransferPatients
          }
          dischargePatients {
            ...DischargePatients
          }
        }
      }
    }
  }
  ${BLOOD_TRANSFUSION_FIELDS}
  ${TRANSFER_PATIENT_FIELDS}
  ${DISCHARGE_PATIENT_FIELDS}
  ${ADMISSION_FIELDS}
  ${ADMISSION_LINKING_FIELDS}
`;

export const FETCH_ADMISSION = gql`
  query FetchAdmission($id: String!, $clinifyId: String!) {
    admission(id: $id, clinifyId: $clinifyId) {
      ...Admission
      ...AdmissionLinking
      admissionNotes {
        ...AdmissionNotes
        ${AUDIT_FIELDS}
      }
     ${AUDIT_FIELDS_WITH_ID}
    }
    
  }
  ${NOTES_FIELDS}
  ${ADMISSION_FIELDS}
  ${ADMISSION_LINKING_FIELDS}
`;

export const ADD_ADMISSION = gql`
  mutation AddAdmission($input: NewAdmissionInput!, $id: String, $pin: String) {
    addAdmission(admission: $input, id: $id, pin: $pin) {
      ...Admission
      ...AdmissionLinking
      bloodTransfusions {
        ...BloodTransfusions
      }
      transferPatients {
        ...TransferPatients
      }
      dischargePatients {
        ...DischargePatients
      }
      admissionNotes {
        ...AdmissionNotes
      }
      billStatus
      status
      bill {
        id
        createdDate
      }
      ${AUDIT_FIELDS}
    }
  }
  ${BLOOD_TRANSFUSION_FIELDS}
  ${TRANSFER_PATIENT_FIELDS}
  ${DISCHARGE_PATIENT_FIELDS}
  ${NOTES_FIELDS}
  ${ADMISSION_FIELDS}
  ${ADMISSION_LINKING_FIELDS}
`;

export const UPDATE_ADMISSION = gql`
  mutation UpdateAdmission($id: String!, $input: AdmissionInput!, $pin: String) {
    updateAdmission(id: $id, admission: $input, pin: $pin) {
      ...Admission
      ...AdmissionLinking  
      hmoClaim {
        id
      }
      ${AUDIT_FIELDS_WITH_ID}
    }
  
  }
  ${ADMISSION_FIELDS}
  ${ADMISSION_LINKING_FIELDS}
`;

export const UPDATE_ADMISSION_BILL = gql`
  mutation UpdateAdmissionBill($id: String!, $input: AdmissionInput!) {
    updateAdmissionBill(id: $id, admission: $input) {
      ...Admission
      ...AdmissionLinking  
      hmoClaim {
        id
      }
      ${AUDIT_FIELDS_WITH_ID}
    }
  
  }
  ${ADMISSION_FIELDS}
  ${ADMISSION_LINKING_FIELDS}
`;

export const UPDATE_ADMISSION_WARD = gql`
  mutation UpdateAdmissionWard($id: String!, $input: AdmissionInput!, $pin: String) {
    updateAdmissionWard(id: $id, admission: $input, pin: $pin) {
      ...Admission
      ...AdmissionLinking  
      hmoClaim {
        id
      }
      ${AUDIT_FIELDS_WITH_ID}
    }
  
  }
  ${ADMISSION_FIELDS}
  ${ADMISSION_LINKING_FIELDS}
`;

export const LINK_RECORDS_TO_ADMISSION = gql`
    mutation LinkRecordsToAdmission(
        $parentId: String!
        $recordIds: [String!]!
        $recordType: AdmissionLinkedRecordType!
    ) {
        linkRecordsToAdmission(
            id: $parentId
            recordIds: $recordIds
            recordType: $recordType
        ) {
            ...Admission
            ...AdmissionLinking
            bloodTransfusions {
                ...BloodTransfusions
            }
            transferPatients {
                ...TransferPatients
            }
            dischargePatients {
                ...DischargePatients
            }
            admissionNotes {
                ...AdmissionNotes
            }
            ${AUDIT_FIELDS}
        }
    }
    ${BLOOD_TRANSFUSION_FIELDS}
    ${TRANSFER_PATIENT_FIELDS}
    ${DISCHARGE_PATIENT_FIELDS}
    ${NOTES_FIELDS}
    ${ADMISSION_FIELDS}
    ${ADMISSION_LINKING_FIELDS}
`;

export const DELETE_ADMISSIONS = gql`
  mutation DeleteAdmissions($ids: [String!]!, $clinifyId: String!) {
    deleteAdmissions(ids: $ids, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const ARCHIVE_ADMISSIONS = gql`
  mutation ArchiveAdmission($ids: [String!]!, $archive: Boolean, $clinifyId: String!) {
    archiveAdmissions(ids: $ids, archive: $archive, clinifyId: $clinifyId) {
      ...Admission
      ...AdmissionLinking
      bloodTransfusions {
        ...BloodTransfusions
      }
      transferPatients {
        ...TransferPatients
      }
      dischargePatients {
        ...DischargePatients
      }
      admissionNotes {
        ...AdmissionNotes
      }
      ${AUDIT_FIELDS}
    }
  }
  ${BLOOD_TRANSFUSION_FIELDS}
  ${TRANSFER_PATIENT_FIELDS}
  ${DISCHARGE_PATIENT_FIELDS}
  ${NOTES_FIELDS}
  ${ADMISSION_FIELDS}
  ${ADMISSION_LINKING_FIELDS}
`;

// InPatient List
export const GET_HOSPITAL_IN_PATIENTS = gql`
  query GetInpatientList($filterOptions: AdmissionFilterInput, $hospitalId: String) {
    hospital(hospitalId: $hospitalId) {
      id
      admissions(filterOptions: $filterOptions) {
        totalCount
        list {
            status
            priority
            profile {
              id
              fullName
              clinifyId
            }
            dischargePatients {
            ...DischargePatients
            }
            ...Admission
            bill {
              id
              createdDate
            }
            billStatus
          ${AUDIT_FIELDS}
        }
      }
    }
  }
  ${ADMISSION_FIELDS}
  ${DISCHARGE_PATIENT_FIELDS}
`;

// Transfer Patient
export const GET_TRANSFER_PATIENT = gql`
  query GetTransferPatient($filterOptions: AdmissionFilterInput, $hospitalId: String) {
    hospital(hospitalId: $hospitalId) {
      id
      admissions(filterOptions: $filterOptions) {
        totalCount
        list {
            status
            profile {
              id
              fullName
              clinifyId
            }
            dischargePatients {
            ...DischargePatients
            }
            transferPatients {
            ...TransferPatients
            }
            ...Admission
          ${AUDIT_FIELDS}
        }
      }
    }
  }
  ${ADMISSION_FIELDS}
  ${DISCHARGE_PATIENT_FIELDS}
  ${TRANSFER_PATIENT_FIELDS}
`;

export const ADMISSION_ADDED_SUBS = gql`
  subscription AdmissionAddedSubs($profileId: String!, $hospitalId: String!) {
    AdmissionAdded(profileId: $profileId, hospitalId: $hospitalId) {
      ...Admission
      ...AdmissionLinking
      bloodTransfusions {
        ...BloodTransfusions
      }
      transferPatients {
        ...TransferPatients
      }
      dischargePatients {
        ...DischargePatients
      }
      admissionNotes {
        ...AdmissionNotes
      }
      billStatus
      status
      bill {
        id
        createdDate
      }
      ${AUDIT_FIELDS}
    }
  }
  ${BLOOD_TRANSFUSION_FIELDS}
  ${TRANSFER_PATIENT_FIELDS}
  ${DISCHARGE_PATIENT_FIELDS}
  ${NOTES_FIELDS}
  ${ADMISSION_FIELDS}
  ${ADMISSION_LINKING_FIELDS}
`;

export const PATIENT_TRANSFERRED_SUBS = gql`
  subscription PatientTransferredSubs($hospitalId: String!) {
      PatientTransferred(hospitalId: $hospitalId) {
          ...Admission
          ${AUDIT_FIELDS}
      }
  }
  ${ADMISSION_FIELDS}
`;

export const ADMISSION_UPDATED_SUBS = gql`
  subscription AdmissionUpdatedSubs($profileId: String!, $hospitalId: String!) {
    AdmissionUpdated(profileId: $profileId, hospitalId: $hospitalId) {
        ...Admission
        ...AdmissionLinking
      ${AUDIT_FIELDS}
    }
  }
  ${ADMISSION_FIELDS}
  ${ADMISSION_LINKING_FIELDS}
`;

export const DELETE_ADMISSION_SUBS = gql`
  subscription DeleteAdmissionSubs($profileId: String!, $hospitalId: String!) {
    AdmissionsRemoved(profileId: $profileId, hospitalId: $hospitalId) {
      id
    }
  }
`;

export const ARCHIVE_ADMISSION_SUBS = gql`
  subscription ArchiveAdmissionSubs($profileId: String!, $hospitalId: String!) {
    AdmissionsArchived(profileId: $profileId, hospitalId: $hospitalId) {
      ...Admission
      ...AdmissionLinking
    }
  }
  ${ADMISSION_FIELDS}
  ${ADMISSION_LINKING_FIELDS}
`;

export const UNARCHIVE_ADMISSION_SUBS = gql`
  subscription UnarchiveAdmissionSubs($profileId: String!, $hospitalId: String!) {
    AdmissionsUnarchived(profileId: $profileId, hospitalId: $hospitalId) {
      ...Admission
      ...AdmissionLinking
    }
  }
  ${ADMISSION_FIELDS}
  ${ADMISSION_LINKING_FIELDS}
`;

export const INPATIENT_LIST_EVENT_SUBSCRIPTION = gql`
  subscription InPatientEvent($hospitalId: String!) {
    InPatientEvent(hospitalId: $hospitalId)
  }
`;

export const FETCH_INPUT_OUTPUT = gql`
  query InputOutput($parentRecordId: String!) {
    admissionInputOutput(admissionId: $parentRecordId) {
      inputDetails {
        ...InputDetails
      }
      outputDetails {
        ...OutputDetails
      }
    }
  }
  ${INPUT_DETAILS_FIELDS}
  ${OUTPUT_DETAILS_FIELDS}
`;

export const SAVE_INPUT_DETAIL = gql`
  mutation SaveInputDetail($input: InputDetailsInput!, $id: String!, $clinifyId: String!, $pin: String) {
    saveInputDetail(admissionId: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...InputDetails
      ${AUDIT_FIELDS}
    }
  }
  ${INPUT_DETAILS_FIELDS}
`;

export const SAVE_OUTPUT_DETAIL = gql`
  mutation SaveOutputDetail($input: OutputDetailsInput!, $id: String!, $clinifyId: String!, $pin: String) {
    saveOutputDetail(admissionId: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...OutputDetails
      ${AUDIT_FIELDS}
    }
  }
  ${OUTPUT_DETAILS_FIELDS}
`;

export const UPDATE_INPUT_DETAIL = gql`
  mutation UpdateInputDetail($input: InputDetailsInput!, $id: String!, $clinifyId: String!, $pin: String) {
    updateInputDetail(id: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...InputDetails
      ${AUDIT_FIELDS}
    }
  }
  ${INPUT_DETAILS_FIELDS}
`;

export const UPDATE_OUTPUT_DETAIL = gql`
  mutation UpdateOutputDetail($input: OutputDetailsInput!, $id: String!, $clinifyId: String!, $pin: String) {
    updateOutputDetail(id: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...OutputDetails
      ${AUDIT_FIELDS}
    }
  }
  ${OUTPUT_DETAILS_FIELDS}
`;

export const DELETE_INPUT_DETAIL = gql`
  mutation DeleteInputDetail($id: String!, $clinifyId: String!) {
    deleteInputDetail(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const DELETE_OUTPUT_DETAIL = gql`
  mutation DeleteOutputDetail($id: String!, $clinifyId: String!) {
    deleteOutputDetail(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const CONCEAL_OBSERVATIONS = gql`
  mutation ConcealObservations($id: String!, $concealStatus: Boolean!) {
    concealObservations(outputDetailsId: $id, concealStatus: $concealStatus) {
      id
      observations
      concealObservations
    }
  }
`;

export const INPUT_DETAIL_ADDED_SUBS = gql`
  subscription InputDetailAddedSubs($profileId: String!) {
    InputDetailAdded(profileId: $profileId) {
      ...InputDetails
      admissionId
    }
  }
  ${INPUT_DETAILS_FIELDS}
`;

export const OUTPUT_DETAIL_ADDED_SUBS = gql`
  subscription OutputDetailAddedSubs($profileId: String!) {
    OutputDetailAdded(profileId: $profileId) {
      ...OutputDetails
      admissionId
    }
  }
  ${OUTPUT_DETAILS_FIELDS}
`;

export const INPUT_DETAIL_UPDATED_SUBS = gql`
  subscription InputDetailUpdatedSubs($profileId: String!) {
    InputDetailUpdated(profileId: $profileId) {
      ...InputDetails
    }
  }
  ${INPUT_DETAILS_FIELDS}
`;

export const OUTPUT_DETAIL_UPDATED_SUBS = gql`
  subscription OutputDetailUpdatedSubs($profileId: String!) {
    OutputDetailUpdated(profileId: $profileId) {
      ...OutputDetails
    }
  }
  ${OUTPUT_DETAILS_FIELDS}
`;

export const INPUT_DETAIL_DELETED_SUBS = gql`
  subscription DeleteInputDetailSubs($profileId: String!) {
    InputDetailRemoved(profileId: $profileId) {
      id
      admissionId
    }
  }
`;

export const OUTPUT_DETAIL_DELETED_SUBS = gql`
  subscription DeleteOutputDetailSubs($profileId: String!) {
    OutputDetailRemoved(profileId: $profileId) {
      id
      admissionId
    }
  }
`;

export const FETCH_ADMISSION_LINES = gql`
  query AdmissionLines($parentRecordId: String!) {
    admissionLines(admissionId: $parentRecordId) {
      ...AdmissionLines
      ${AUDIT_FIELDS}
    }
  }
  ${ADMISSION_LINES_FIELDS}
`;

export const SAVE_ADMISSION_LINE = gql`
  mutation SaveAdmissionLines($input: AdmissionLinesInput!, $id: String!, $clinifyId: String!, $pin: String) {
    saveAdmissionLines(admissionId: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...AdmissionLines
      ${AUDIT_FIELDS}
    }
  }
  ${ADMISSION_LINES_FIELDS}
`;

export const UPDATE_ADMISSION_LINE = gql`
  mutation UpdateAdmissionLines($input: AdmissionLinesInput!, $id: String!, $clinifyId: String!, $pin: String) {
    updateAdmissionLines(id: $id, input: $input, clinifyId: $clinifyId, pin: $pin) {
      ...AdmissionLines
      ${AUDIT_FIELDS}
    }
  }
  ${ADMISSION_LINES_FIELDS}
`;

export const DELETE_ADMISSION_LINE = gql`
  mutation DeleteAdmissionLines($id: String!, $clinifyId: String!) {
    deleteAdmissionLines(id: $id, clinifyId: $clinifyId) {
      id
    }
  }
`;

export const ADMISSION_LINE_ADDED_SUBS = gql`
  subscription AdmissionLineAddedSubs($profileId: String!) {
    AdmissionLineAdded(profileId: $profileId) {
      ...AdmissionLines
      admissionId
      ${AUDIT_FIELDS}
    }
  }
  ${ADMISSION_LINES_FIELDS}
`;

export const ADMISSION_LINE_UPDATED_SUBS = gql`
  subscription AdmissionLineUpdatedSubs($profileId: String!) {
    AdmissionLineUpdated(profileId: $profileId) {
      ...AdmissionLines
      ${AUDIT_FIELDS}
    }
  }
  ${ADMISSION_LINES_FIELDS}
`;

export const DELETE_ADMISSION_LINE_SUBS = gql`
  subscription DeleteAdmissionLineSubs($profileId: String!) {
    AdmissionLineRemoved(profileId: $profileId) {
      id
      admissionId
    }
  }
`;

export const GET_ADMISSION_CLAIM = gql`
  query AdmissionClaim($id: String!, $clinifyId: String!) {
    admission(id: $id, clinifyId: $clinifyId) {
      id
      hmoClaim {
        id
      }
    }
  }
`;

export const ADD_ADMISSION_SIGNATURE = gql`
  mutation AddAdmissionConsentSignature($id: String!, $clinifyId: String!, $input: SignatureInput!, $pin: String) {
    addAdmissionConsentSignature(id: $id, clinifyId: $clinifyId, input: $input, pin: $pin) {
      ...Admission
      ${AUDIT_FIELDS_WITH_ID}
    }
  }
  ${ADMISSION_FIELDS}
`;

export const UPDATE_ADMISSION_SIGNATURE = gql`
  mutation UpdateAdmissionConsentSignature($id: String!, $clinifyId: String!, $input: SignatureInput!, $pin: String) {
    updateAdmissionConsentSignature(id: $id, clinifyId: $clinifyId, input: $input, pin: $pin) {
      ...Admission
      ${AUDIT_FIELDS_WITH_ID}
    }
  }
  ${ADMISSION_FIELDS}
`;

export const DELETE_ADMISSION_SIGNATURE = gql`
  mutation DeleteAdmissionConsentSignature($id: String!, $clinifyId: String!) {
    deleteAdmissionConsentSignature(id: $id, clinifyId: $clinifyId) {
      ...Admission
      ${AUDIT_FIELDS_WITH_ID}
    }
  }
  ${ADMISSION_FIELDS}
`;

export const ADD_BLOOD_TRANSFUSION_SIGNATURE = gql`
  mutation AddBloodTransfusionConsentSignature($id: String!, $clinifyId: String!, $input: SignatureInput!, $pin: String) {
    addBloodTransfusionConsentSignature(id: $id, clinifyId: $clinifyId, input: $input, pin: $pin) {
      ...BloodTransfusions
      ${AUDIT_FIELDS_WITH_ID}
    }
  }
  ${BLOOD_TRANSFUSION_FIELDS}
`;

export const UPDATE_BLOOD_TRANSFUSION_SIGNATURE = gql`
  mutation UpdateBloodTransfusionConsentSignature($id: String!, $clinifyId: String!, $input: SignatureInput!, $pin: String) {
    updateBloodTransfusionConsentSignature(id: $id, clinifyId: $clinifyId, input: $input, pin: $pin) {
      ...BloodTransfusions
      ${AUDIT_FIELDS_WITH_ID}
    }
  }
  ${BLOOD_TRANSFUSION_FIELDS}
`;

export const DELETE_BLOOD_TRANSFUSION_SIGNATURE = gql`
  mutation DeleteBloodTransfusionConsentSignature($id: String!, $clinifyId: String!) {
    deleteBloodTransfusionConsentSignature(id: $id, clinifyId: $clinifyId) {
      ...BloodTransfusions
      ${AUDIT_FIELDS_WITH_ID}
    }
  }
  ${BLOOD_TRANSFUSION_FIELDS}
`;

export const UPDATE_VTE_AND_BLEEDING_RISK_ASSESSMENT = gql`
  mutation UpdateVTEAndBleedingRiskAssessment($id: String!, $input: VTEAndBleedingRiskInput!) {
    updateVTEAndBleedingRiskAssessment(id: $id, input: $input) {
      id
      vteAndBleedingRiskAssessment {
        furtherAssessment {
          bleedingRiskAssessed
          vteRiskAssessed
          bleedingRiskAssessedLastModifiedDate
          vteRiskAssessedLastModifiedDate
          bleedingRiskAssessedLastModifierName
          vteRiskAssessedLastModifierName
          ref
        }
        onAdmission {
          bleedingRiskAssessed
          vteRiskAssessed
          bleedingRiskAssessedLastModifiedDate
          vteRiskAssessedLastModifiedDate
          bleedingRiskAssessedLastModifierName
          vteRiskAssessedLastModifierName
        }
        within24Hours {
          bleedingRiskAssessed
          vteRiskAssessed
          bleedingRiskAssessedLastModifiedDate
          vteRiskAssessedLastModifiedDate
          bleedingRiskAssessedLastModifierName
          vteRiskAssessedLastModifierName
        }
      }
    }
  }
`;
