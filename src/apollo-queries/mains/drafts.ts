import { gql } from '@apollo/client';

export const GET_DRAFTS = gql`
  query GetDrafts($type: String) {
    drafts(type: $type) {
      id
      type
      value
      createdDate
    }
  }
`;

export const GET_DRAFT = gql`
  query GetDraft($id: String!) {
    draft(id: $id) {
      id
      type
      value
      createdDate
    }
  }
`;

// createDraft
export const CREATE_DRAFT = gql`
  mutation CreateDraft($input: CreateDraftInput!) {
    createDraft(input: $input) {
      id
      type
      value
      createdDate
    }
  }
`;

// updateDraft
export const UPDATE_DRAFT = gql`
  mutation UpdateDraft($id: String!, $input: UpdateDraftInput!) {
    updateDraft(id: $id, input: $input) {
      id
      type
      value
      createdDate
    }
  }
`;

export const DELETE_DRAFT = gql`
  mutation DeleteDraft($id: String!) {
    deleteDraft(id: $id)
  }
`;

export const DRAFT_REMOVED_SUBS = gql`
  subscription DraftRemoved {
    DraftRemoved {
      id
    }
  }
`;

// update
export const DRAFT_UPDATED_SUBS = gql`
  subscription DraftUpdated {
    DraftUpdated {
      id
      type
      value
      createdDate
    }
  }
`;

export const DRAFT_CREATED_SUBS = gql`
  subscription DraftCreated {
    DraftCreated {
      id
      type
      value
      createdDate
    }
  }
`;
