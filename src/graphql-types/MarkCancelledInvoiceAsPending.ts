/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { InvoiceStatus } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: MarkCancelledInvoiceAsPending
// ====================================================

export interface MarkCancelledInvoiceAsPending_markCancelledInvoiceAsPending {
  __typename: "InvoiceModel";
  id: string;
  invoiceStatus: InvoiceStatus;
}

export interface MarkCancelledInvoiceAsPending {
  markCancelledInvoiceAsPending: MarkCancelledInvoiceAsPending_markCancelledInvoiceAsPending;
}

export interface MarkCancelledInvoiceAsPendingVariables {
  id: string;
}
