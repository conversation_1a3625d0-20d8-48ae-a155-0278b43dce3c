/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { RevokeOrganizationPermissionInput, Subject, Action } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: RevokeOrganizationPermissionForFacility
// ====================================================

export interface RevokeOrganizationPermissionForFacility_revokeOrganizationPermissionForFacility_rules {
  __typename: "PermissionModelInput";
  subject: Subject;
  action: Action;
}

export interface RevokeOrganizationPermissionForFacility_revokeOrganizationPermissionForFacility {
  __typename: "PermissionModel";
  id: string;
  rules: RevokeOrganizationPermissionForFacility_revokeOrganizationPermissionForFacility_rules[] | null;
}

export interface RevokeOrganizationPermissionForFacility {
  revokeOrganizationPermissionForFacility: RevokeOrganizationPermissionForFacility_revokeOrganizationPermissionForFacility;
}

export interface RevokeOrganizationPermissionForFacilityVariables {
  hospitalId: string;
  input: RevokeOrganizationPermissionInput;
}
