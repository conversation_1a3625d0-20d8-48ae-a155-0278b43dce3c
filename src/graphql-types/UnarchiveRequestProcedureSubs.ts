/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: UnarchiveRequestProcedureSubs
// ====================================================

export interface UnarchiveRequestProcedureSubs_RequestProcedureUnarchived_procedureType {
  __typename: "ProcedureTypeInputType";
  ref: string | null;
  type: string;
  priority: string | null;
  provider: string | null;
  itemId: string | null;
  preauthorizationDetailsId: string | null;
}

export interface UnarchiveRequestProcedureSubs_RequestProcedureUnarchived_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface UnarchiveRequestProcedureSubs_RequestProcedureUnarchived_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface UnarchiveRequestProcedureSubs_RequestProcedureUnarchived_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: UnarchiveRequestProcedureSubs_RequestProcedureUnarchived_profile_coverageDetails_provider | null;
}

export interface UnarchiveRequestProcedureSubs_RequestProcedureUnarchived_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: UnarchiveRequestProcedureSubs_RequestProcedureUnarchived_profile_coverageDetails[] | null;
}

export interface UnarchiveRequestProcedureSubs_RequestProcedureUnarchived_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface UnarchiveRequestProcedureSubs_RequestProcedureUnarchived_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: UnarchiveRequestProcedureSubs_RequestProcedureUnarchived_preauthorizationDetails_provider | null;
}

export interface UnarchiveRequestProcedureSubs_RequestProcedureUnarchived {
  __typename: "RequestProcedureModel";
  id: string;
  surgeryDate: any | null;
  duration: string | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
  procedureType: UnarchiveRequestProcedureSubs_RequestProcedureUnarchived_procedureType[];
  rank: string | null;
  reason: string | null;
  requestedBy: string | null;
  specialty: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  operatedBy: string | null;
  patientConsent: string | null;
  serviceDetails: UnarchiveRequestProcedureSubs_RequestProcedureUnarchived_serviceDetails[] | null;
  isPackage: boolean;
  documentUrl: string[] | null;
  surgeryStartDate: any | null;
  surgeryEndDate: any | null;
  appointmentId: string | null;
  createdDate: any;
  updatedDate: any;
  department: string | null;
  billStatus: string | null;
  hospitalId: string | null;
  profileId: string | null;
  profile: UnarchiveRequestProcedureSubs_RequestProcedureUnarchived_profile | null;
  preauthorizationDetails: UnarchiveRequestProcedureSubs_RequestProcedureUnarchived_preauthorizationDetails[] | null;
}

export interface UnarchiveRequestProcedureSubs {
  RequestProcedureUnarchived: UnarchiveRequestProcedureSubs_RequestProcedureUnarchived[];
}

export interface UnarchiveRequestProcedureSubsVariables {
  profileId: string;
}
