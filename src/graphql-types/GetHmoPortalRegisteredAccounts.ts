/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetHmoPortalRegisteredAccounts
// ====================================================

export interface GetHmoPortalRegisteredAccounts_getProviderStaffs_staffs {
  __typename: "HmoProviderStaff";
  email: string;
  name: string;
}

export interface GetHmoPortalRegisteredAccounts_getProviderStaffs {
  __typename: "HmoProviderStaffsResponse";
  providerName: string;
  staffs: GetHmoPortalRegisteredAccounts_getProviderStaffs_staffs[];
}

export interface GetHmoPortalRegisteredAccounts {
  getProviderStaffs: GetHmoPortalRegisteredAccounts_getProviderStaffs[];
}
