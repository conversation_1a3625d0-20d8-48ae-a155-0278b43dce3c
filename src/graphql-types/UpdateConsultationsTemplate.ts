/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ConsultationsTemplateInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateConsultationsTemplate
// ====================================================

export interface UpdateConsultationsTemplate_updateConsultationsTemplate {
  __typename: "ConsultationsTemplateModel";
  id: string;
  name: string;
  complaints: string | null;
  historyComplaints: string | null;
  healthEducation: string | null;
  reviewSystems: string | null;
  physicalExamination: string | null;
  audiometry: string | null;
  treatmentPlan: string | null;
}

export interface UpdateConsultationsTemplate {
  updateConsultationsTemplate: UpdateConsultationsTemplate_updateConsultationsTemplate;
}

export interface UpdateConsultationsTemplateVariables {
  input: ConsultationsTemplateInput;
}
