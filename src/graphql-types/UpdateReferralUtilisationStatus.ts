/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UpdateReferralUtilisationStatusInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateReferralUtilisationStatus
// ====================================================

export interface UpdateReferralUtilisationStatus_updateReferralUtilisationStatus {
  __typename: "PreAuthReferralUtilisationsModel";
  id: string;
  status: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
}

export interface UpdateReferralUtilisationStatus {
  updateReferralUtilisationStatus: UpdateReferralUtilisationStatus_updateReferralUtilisationStatus;
}

export interface UpdateReferralUtilisationStatusVariables {
  input: UpdateReferralUtilisationStatusInput;
}
