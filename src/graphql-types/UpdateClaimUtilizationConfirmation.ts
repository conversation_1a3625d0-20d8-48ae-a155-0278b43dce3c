/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: UpdateClaimUtilizationConfirmation
// ====================================================

export interface UpdateClaimUtilizationConfirmation_confirmUtilization {
  __typename: "PreAuthUtilisationsModel";
  id: string;
  confirmation: boolean | null;
  hmoClaimId: string;
}

export interface UpdateClaimUtilizationConfirmation {
  confirmUtilization: UpdateClaimUtilizationConfirmation_confirmUtilization[];
}

export interface UpdateClaimUtilizationConfirmationVariables {
  utilizationIds: string[];
  confirmation: boolean;
  claimId: string;
}
