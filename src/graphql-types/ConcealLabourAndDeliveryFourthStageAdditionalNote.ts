/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealLabourAndDeliveryFourthStageAdditionalNote
// ====================================================

export interface ConcealLabourAndDeliveryFourthStageAdditionalNote_concealLabourAndDeliveryFourthStageAdditionalNote {
  __typename: "LabourDeliveryModel";
  id: string;
  concealFourthStageAdditionalNote: boolean | null;
  fourthStageAdditionalNote: string | null;
}

export interface ConcealLabourAndDeliveryFourthStageAdditionalNote {
  concealLabourAndDeliveryFourthStageAdditionalNote: ConcealLabourAndDeliveryFourthStageAdditionalNote_concealLabourAndDeliveryFourthStageAdditionalNote;
}

export interface ConcealLabourAndDeliveryFourthStageAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
}
