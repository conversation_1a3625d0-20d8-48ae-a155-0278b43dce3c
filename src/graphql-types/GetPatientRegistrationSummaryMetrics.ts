/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ProfilesFilterInputs } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPatientRegistrationSummaryMetrics
// ====================================================

export interface GetPatientRegistrationSummaryMetrics_getPatientRegistrationSummary {
  __typename: "PatientRegistrationSummaryResponse";
  totalCommissionPayable: number;
  totalEnrollees: number;
  totalAgents: number | null;
  totalOfficers: number | null;
  totalEmployers: number | null;
  totalProviders: number;
  totalEnrolled: number;
}

export interface GetPatientRegistrationSummaryMetrics {
  getPatientRegistrationSummary: GetPatientRegistrationSummaryMetrics_getPatientRegistrationSummary;
}

export interface GetPatientRegistrationSummaryMetricsVariables {
  filterOptions: ProfilesFilterInputs;
}
