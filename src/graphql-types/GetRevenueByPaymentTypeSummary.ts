/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FinanceAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetRevenueByPaymentTypeSummary
// ====================================================

export interface GetRevenueByPaymentTypeSummary_getPaymentTypeDataSummary {
  __typename: "FinanceDataSummaryResponse";
  totalAmountDue: number | null;
  totalAmount: number | null;
  category: string;
  name: number | null;
  totalAmountOutstanding: number | null;
  totalAmountPaid: number | null;
  totalDiscountAmount: number | null;
  totalRevenue: number | null;
}

export interface GetRevenueByPaymentTypeSummary {
  getPaymentTypeDataSummary: GetRevenueByPaymentTypeSummary_getPaymentTypeDataSummary[];
}

export interface GetRevenueByPaymentTypeSummaryVariables {
  filter: FinanceAnalyticsFilter;
}
