/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealLabourAndDeliveryFourthStageCordCare
// ====================================================

export interface ConcealLabourAndDeliveryFourthStageCordCare_concealLabourAndDeliveryFourthStageCordCare {
  __typename: "LabourDeliveryModel";
  id: string;
  concealFourthStageCordCare: boolean | null;
  fourthStageCordCare: string | null;
}

export interface ConcealLabourAndDeliveryFourthStageCordCare {
  concealLabourAndDeliveryFourthStageCordCare: ConcealLabourAndDeliveryFourthStageCordCare_concealLabourAndDeliveryFourthStageCordCare;
}

export interface ConcealLabourAndDeliveryFourthStageCordCareVariables {
  id: string;
  concealStatus: boolean;
}
