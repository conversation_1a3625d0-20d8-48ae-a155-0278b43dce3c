/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetClinifyUserWalletBalance
// ====================================================

export interface GetClinifyUserWalletBalance_getClinifyUserWalletBalance {
  __typename: "getWalletBalanceResponse";
  totalBalance: number;
  availableBalance: number;
}

export interface GetClinifyUserWalletBalance {
  getClinifyUserWalletBalance: GetClinifyUserWalletBalance_getClinifyUserWalletBalance;
}

export interface GetClinifyUserWalletBalanceVariables {
  patientClinifyId: string;
}
