/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: DeletePreExistingConditionSubs
// ====================================================

export interface DeletePreExistingConditionSubs_PreExistingConditionRemoved {
  __typename: "PreExistingConditionModel";
  id: string;
}

export interface DeletePreExistingConditionSubs {
  PreExistingConditionRemoved: DeletePreExistingConditionSubs_PreExistingConditionRemoved;
}

export interface DeletePreExistingConditionSubsVariables {
  profileId: string;
}
