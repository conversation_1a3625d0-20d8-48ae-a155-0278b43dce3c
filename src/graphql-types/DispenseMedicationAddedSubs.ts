/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MedicationOptionType } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: DispenseMedicationAddedSubs
// ====================================================

export interface DispenseMedicationAddedSubs_DispenseMedicationAdded_dispenseServiceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
}

export interface DispenseMedicationAddedSubs_DispenseMedicationAdded_dispenseConsumables {
  __typename: "DispenseConsumables";
  name: string | null;
  drugInventoryId: string | null;
  quantityConsumed: string | null;
  quantityRemaining: string | null;
  inventoryClass: string | null;
}

export interface DispenseMedicationAddedSubs_DispenseMedicationAdded_hmoClaim_utilizations {
  __typename: "PreAuthUtilisationsModel";
  id: string;
  type: string | null;
  /**
   * Value is in Naira
   */
  price: string | null;
}

export interface DispenseMedicationAddedSubs_DispenseMedicationAdded_hmoClaim {
  __typename: "HmoClaimModel";
  id: string;
  claimDate: any | null;
  enrolleeNumber: string | null;
  utilizations: DispenseMedicationAddedSubs_DispenseMedicationAdded_hmoClaim_utilizations[] | null;
}

export interface DispenseMedicationAddedSubs_DispenseMedicationAdded_createdBy {
  __typename: "ProfileModel";
  id: string;
  hospitalId: string | null;
}

export interface DispenseMedicationAddedSubs_DispenseMedicationAdded_medication_billing {
  __typename: "BillDetailsModel";
  id: string;
  createdDate: any | null;
  billId: string | null;
}

export interface DispenseMedicationAddedSubs_DispenseMedicationAdded_medication {
  __typename: "MedicationModel";
  id: string;
  billing: DispenseMedicationAddedSubs_DispenseMedicationAdded_medication_billing[] | null;
  dispenseIds: string[] | null;
  billStatus: string | null;
}

export interface DispenseMedicationAddedSubs_DispenseMedicationAdded {
  __typename: "DispenseDetailsModel";
  id: string;
  dispenseDate: any | null;
  dispensedBy: string | null;
  medicationName: string[] | null;
  medicationDetailId: string | null;
  dispenseNote: string | null;
  concealDispenseNote: boolean;
  hmoProviderId: string | null;
  hospitalId: string;
  dispenseServiceDetails: DispenseMedicationAddedSubs_DispenseMedicationAdded_dispenseServiceDetails[] | null;
  facilityName: string | null;
  facilityAddress: string | null;
  medicationId: string | null;
  billingId: string | null;
  quantityRemaining: string | null;
  quantityDispensed: string | null;
  dispenseConsumables: DispenseMedicationAddedSubs_DispenseMedicationAdded_dispenseConsumables[] | null;
  option: MedicationOptionType | null;
  hmoClaimId: string | null;
  inventoryClass: string | null;
  hmoClaim: DispenseMedicationAddedSubs_DispenseMedicationAdded_hmoClaim | null;
  createdBy: DispenseMedicationAddedSubs_DispenseMedicationAdded_createdBy;
  medication: DispenseMedicationAddedSubs_DispenseMedicationAdded_medication | null;
}

export interface DispenseMedicationAddedSubs {
  DispenseMedicationAdded: DispenseMedicationAddedSubs_DispenseMedicationAdded;
}

export interface DispenseMedicationAddedSubsVariables {
  profileId: string;
  hospitalId: string;
}
