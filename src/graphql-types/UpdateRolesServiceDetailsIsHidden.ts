/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: UpdateRolesServiceDetailsIsHidden
// ====================================================

export interface UpdateRolesServiceDetailsIsHidden_updateRolesServiceDetailsIsHidden {
  __typename: "FacilityPreferenceModel";
  id: string;
  showServiceDetails: boolean | null;
  rolesServiceDetailsIsHidden: string[] | null;
  inventoryClass: string | null;
  hospitalId: string;
}

export interface UpdateRolesServiceDetailsIsHidden {
  updateRolesServiceDetailsIsHidden: UpdateRolesServiceDetailsIsHidden_updateRolesServiceDetailsIsHidden;
}

export interface UpdateRolesServiceDetailsIsHiddenVariables {
  roles: string[];
}
