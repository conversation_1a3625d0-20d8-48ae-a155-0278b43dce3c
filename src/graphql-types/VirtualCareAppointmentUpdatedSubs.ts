/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Gender } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: VirtualCareAppointmentUpdatedSubs
// ====================================================

export interface VirtualCareAppointmentUpdatedSubs_VirtualCareAppointmentUpdated_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface VirtualCareAppointmentUpdatedSubs_VirtualCareAppointmentUpdated_specialists_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  speciality: string | null;
}

export interface VirtualCareAppointmentUpdatedSubs_VirtualCareAppointmentUpdated_specialists {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
  personalInformation: VirtualCareAppointmentUpdatedSubs_VirtualCareAppointmentUpdated_specialists_personalInformation | null;
}

export interface VirtualCareAppointmentUpdatedSubs_VirtualCareAppointmentUpdated_patientProfile_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
  dateOfBirth: any | null;
  displayPictureUrl: string | null;
  bloodGroup: string | null;
}

export interface VirtualCareAppointmentUpdatedSubs_VirtualCareAppointmentUpdated_patientProfile_user {
  __typename: "UserModel";
  id: string;
  nonCorporateEmail: string | null;
  phoneNumber: string | null;
  country: string | null;
}

export interface VirtualCareAppointmentUpdatedSubs_VirtualCareAppointmentUpdated_patientProfile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  gender: Gender | null;
  clinifyId: string;
  personalInformation: VirtualCareAppointmentUpdatedSubs_VirtualCareAppointmentUpdated_patientProfile_personalInformation | null;
  user: VirtualCareAppointmentUpdatedSubs_VirtualCareAppointmentUpdated_patientProfile_user;
}

export interface VirtualCareAppointmentUpdatedSubs_VirtualCareAppointmentUpdated {
  __typename: "VirtualCareAppointmentModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  liveSessionUrl: string | null;
  category: string | null;
  appointmentDate: any;
  startDateTime: any | null;
  endDateTime: any | null;
  priority: string | null;
  duration: string | null;
  patientType: string | null;
  paymentType: string | null;
  hospital: VirtualCareAppointmentUpdatedSubs_VirtualCareAppointmentUpdated_hospital | null;
  specialists: VirtualCareAppointmentUpdatedSubs_VirtualCareAppointmentUpdated_specialists[] | null;
  patientProfile: VirtualCareAppointmentUpdatedSubs_VirtualCareAppointmentUpdated_patientProfile | null;
}

export interface VirtualCareAppointmentUpdatedSubs {
  VirtualCareAppointmentUpdated: VirtualCareAppointmentUpdatedSubs_VirtualCareAppointmentUpdated;
}

export interface VirtualCareAppointmentUpdatedSubsVariables {
  hospitalId?: string | null;
  profileId: string;
}
