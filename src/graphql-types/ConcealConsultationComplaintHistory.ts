/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealConsultationComplaintHistory
// ====================================================

export interface ConcealConsultationComplaintHistory_concealConsultationComplaintHistory {
  __typename: "ConsultationModel";
  id: string;
  concealComplaintHistory: boolean | null;
  complaintHistory: string | null;
}

export interface ConcealConsultationComplaintHistory {
  concealConsultationComplaintHistory: ConcealConsultationComplaintHistory_concealConsultationComplaintHistory;
}

export interface ConcealConsultationComplaintHistoryVariables {
  id: string;
  concealStatus: boolean;
}
