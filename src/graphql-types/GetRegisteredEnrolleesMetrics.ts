/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { RegisteredEnrolleesMetricsInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetRegisteredEnrolleesMetrics
// ====================================================

export interface GetRegisteredEnrolleesMetrics_registeredEnrolleesMetrics {
  __typename: "RegisteredEnrolleesMetricsResponse";
  totalEnrollees: number | null;
  totalFacilitiesEnrolledIn: number | null;
  totalCommissionPayable: number | null;
}

export interface GetRegisteredEnrolleesMetrics {
  registeredEnrolleesMetrics: GetRegisteredEnrolleesMetrics_registeredEnrolleesMetrics | null;
}

export interface GetRegisteredEnrolleesMetricsVariables {
  input: RegisteredEnrolleesMetricsInput;
}
