/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { EnrollmentAgentInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateEnrollmentAgentAssignments
// ====================================================

export interface UpdateEnrollmentAgentAssignments_updateEnrollmentAgent_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface UpdateEnrollmentAgentAssignments_updateEnrollmentAgent_agency {
  __typename: "EnrollmentAgencyModel";
  id: string;
  name: string | null;
}

export interface UpdateEnrollmentAgentAssignments_updateEnrollmentAgent {
  __typename: "EnrollmentAgentModel";
  id: string;
  profile: UpdateEnrollmentAgentAssignments_updateEnrollmentAgent_profile | null;
  administrationAgency: string | null;
  agency: UpdateEnrollmentAgentAssignments_updateEnrollmentAgent_agency | null;
  accountNumber: string | null;
  accountName: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface UpdateEnrollmentAgentAssignments {
  updateEnrollmentAgent: UpdateEnrollmentAgentAssignments_updateEnrollmentAgent;
}

export interface UpdateEnrollmentAgentAssignmentsVariables {
  id: string;
  input: EnrollmentAgentInput;
}
