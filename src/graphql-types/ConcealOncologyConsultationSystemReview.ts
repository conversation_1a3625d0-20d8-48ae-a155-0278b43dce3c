/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealOncologyConsultationSystemReview
// ====================================================

export interface ConcealOncologyConsultationSystemReview_concealOncologyConsultationSystemReview {
  __typename: "OncologyConsultationHistoryModel";
  id: string;
  concealSystemReview: boolean | null;
  systemReview: string | null;
}

export interface ConcealOncologyConsultationSystemReview {
  concealOncologyConsultationSystemReview: ConcealOncologyConsultationSystemReview_concealOncologyConsultationSystemReview;
}

export interface ConcealOncologyConsultationSystemReviewVariables {
  id: string;
  concealStatus: boolean;
}
