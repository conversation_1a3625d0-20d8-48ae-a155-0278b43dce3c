/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UserType } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: HandoverAdditionalNoteUpdatedSubs
// ====================================================

export interface HandoverAdditionalNoteUpdatedSubs_HandoverAdditionalNoteUpdated {
  __typename: "HandoverAdditionalNoteModel";
  id: string;
  additionalNote: string;
  handoverNoteId: string;
  creatorId: string;
  createdDate: any;
  updatedDate: any | null;
  lastModifierName: string | null;
}

export interface HandoverAdditionalNoteUpdatedSubs {
  HandoverAdditionalNoteUpdated: HandoverAdditionalNoteUpdatedSubs_HandoverAdditionalNoteUpdated;
}

export interface HandoverAdditionalNoteUpdatedSubsVariables {
  profileId: string;
  hospitalId?: string | null;
  userType: UserType;
}
