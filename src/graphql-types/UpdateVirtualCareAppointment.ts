/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { EditVirtualCareAppointmentInput, Gender } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateVirtualCareAppointment
// ====================================================

export interface UpdateVirtualCareAppointment_updateVirtualCareAppointment_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface UpdateVirtualCareAppointment_updateVirtualCareAppointment_specialists_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  speciality: string | null;
}

export interface UpdateVirtualCareAppointment_updateVirtualCareAppointment_specialists {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
  personalInformation: UpdateVirtualCareAppointment_updateVirtualCareAppointment_specialists_personalInformation | null;
}

export interface UpdateVirtualCareAppointment_updateVirtualCareAppointment_patientProfile_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
  dateOfBirth: any | null;
  displayPictureUrl: string | null;
  bloodGroup: string | null;
}

export interface UpdateVirtualCareAppointment_updateVirtualCareAppointment_patientProfile_user {
  __typename: "UserModel";
  id: string;
  nonCorporateEmail: string | null;
  phoneNumber: string | null;
  country: string | null;
}

export interface UpdateVirtualCareAppointment_updateVirtualCareAppointment_patientProfile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  gender: Gender | null;
  clinifyId: string;
  personalInformation: UpdateVirtualCareAppointment_updateVirtualCareAppointment_patientProfile_personalInformation | null;
  user: UpdateVirtualCareAppointment_updateVirtualCareAppointment_patientProfile_user;
}

export interface UpdateVirtualCareAppointment_updateVirtualCareAppointment {
  __typename: "VirtualCareAppointmentModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  liveSessionUrl: string | null;
  category: string | null;
  appointmentDate: any;
  startDateTime: any | null;
  endDateTime: any | null;
  priority: string | null;
  duration: string | null;
  patientType: string | null;
  paymentType: string | null;
  hospital: UpdateVirtualCareAppointment_updateVirtualCareAppointment_hospital | null;
  specialists: UpdateVirtualCareAppointment_updateVirtualCareAppointment_specialists[] | null;
  patientProfile: UpdateVirtualCareAppointment_updateVirtualCareAppointment_patientProfile | null;
}

export interface UpdateVirtualCareAppointment {
  updateVirtualCareAppointment: UpdateVirtualCareAppointment_updateVirtualCareAppointment;
}

export interface UpdateVirtualCareAppointmentVariables {
  id: string;
  input: EditVirtualCareAppointmentInput;
}
