/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealConsultationPhysicalExam
// ====================================================

export interface ConcealConsultationPhysicalExam_concealConsultationPhysicalExam {
  __typename: "ConsultationModel";
  id: string;
  concealPhysicalExam: boolean | null;
  physicalExam: string | null;
}

export interface ConcealConsultationPhysicalExam {
  concealConsultationPhysicalExam: ConcealConsultationPhysicalExam_concealConsultationPhysicalExam;
}

export interface ConcealConsultationPhysicalExamVariables {
  id: string;
  concealStatus: boolean;
}
