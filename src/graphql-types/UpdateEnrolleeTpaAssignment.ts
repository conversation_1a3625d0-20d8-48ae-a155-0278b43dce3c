/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { EnrollmentTpaNonTpaInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateEnrolleeTpaAssignment
// ====================================================

export interface UpdateEnrolleeTpaAssignment_updateEnrollmentTpaNonTpa_primaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrolleeTpaAssignment_updateEnrollmentTpaNonTpa_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrolleeTpaAssignment_updateEnrollmentTpaNonTpa_contactPersonPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrolleeTpaAssignment_updateEnrollmentTpaNonTpa_contactPersonAltPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrolleeTpaAssignment_updateEnrollmentTpaNonTpa {
  __typename: "EnrollmentTpaNonTpaModel";
  id: string;
  name: string | null;
  address: string | null;
  country: string | null;
  state: string | null;
  localGovernmentArea: string | null;
  primaryPhoneNumber: UpdateEnrolleeTpaAssignment_updateEnrollmentTpaNonTpa_primaryPhoneNumber | null;
  primaryEmailAddress: string | null;
  secondaryPhoneNumber: UpdateEnrolleeTpaAssignment_updateEnrollmentTpaNonTpa_secondaryPhoneNumber | null;
  secondaryEmailAddress: string | null;
  contactPersonTitle: string | null;
  contactPersonFirstName: string | null;
  contactPersonMiddleName: string | null;
  contactPersonLastName: string | null;
  contactPersonPhoneNumber: UpdateEnrolleeTpaAssignment_updateEnrollmentTpaNonTpa_contactPersonPhoneNumber | null;
  contactPersonEmailAddress: string | null;
  contactPersonAltTitle: string | null;
  contactPersonAltFirstName: string | null;
  contactPersonAltMiddleName: string | null;
  contactPersonAltLastName: string | null;
  contactPersonAltPhoneNumber: UpdateEnrolleeTpaAssignment_updateEnrollmentTpaNonTpa_contactPersonAltPhoneNumber | null;
  contactPersonAltEmailAddress: string | null;
  tpaNumber: string | null;
  tpaCode: string | null;
  accountName: string | null;
  accountNumber: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface UpdateEnrolleeTpaAssignment {
  updateEnrollmentTpaNonTpa: UpdateEnrolleeTpaAssignment_updateEnrollmentTpaNonTpa;
}

export interface UpdateEnrolleeTpaAssignmentVariables {
  id: string;
  input: EnrollmentTpaNonTpaInput;
}
