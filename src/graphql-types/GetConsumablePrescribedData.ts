/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ServicesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetConsumablePrescribedData
// ====================================================

export interface GetConsumablePrescribedData_getConsumablePrescribedData_servicesSummary {
  __typename: "ServicesSummary";
  totalConsumablesPrescribed: number | null;
  name: number | null;
}

export interface GetConsumablePrescribedData_getConsumablePrescribedData_list_consumables_male {
  __typename: "DetailNameQuantity";
  name: string;
  quantity: number | null;
}

export interface GetConsumablePrescribedData_getConsumablePrescribedData_list_consumables_female {
  __typename: "DetailNameQuantity";
  name: string;
  quantity: number | null;
}

export interface GetConsumablePrescribedData_getConsumablePrescribedData_list_consumables {
  __typename: "ListsByGenderWithQuantity";
  male: GetConsumablePrescribedData_getConsumablePrescribedData_list_consumables_male[] | null;
  female: GetConsumablePrescribedData_getConsumablePrescribedData_list_consumables_female[] | null;
}

export interface GetConsumablePrescribedData_getConsumablePrescribedData_list_byConsumablePrescriptionDateAndPrescriberName {
  __typename: "ListByMedicationPrescriptionDateAndPrescriberName";
  prescriptionDate: string | null;
  prescribedBy: string | null;
  medicationNames: string[] | null;
  quantity: number | null;
}

export interface GetConsumablePrescribedData_getConsumablePrescribedData_list_byMost {
  __typename: "ListsByOrderByName";
  name: string | null;
  total: number | null;
}

export interface GetConsumablePrescribedData_getConsumablePrescribedData_list_byLeast {
  __typename: "ListsByOrderByName";
  name: string | null;
  total: number | null;
}

export interface GetConsumablePrescribedData_getConsumablePrescribedData_list_byPatientName {
  __typename: "ListByPatientName";
  patientName: string | null;
  quantity: string | null;
  name: string;
  prescribedDate: string | null;
  prescribedBy: string | null;
  clinifyId: string | null;
}

export interface GetConsumablePrescribedData_getConsumablePrescribedData_list {
  __typename: "ServiceSummaryList";
  consumables: GetConsumablePrescribedData_getConsumablePrescribedData_list_consumables | null;
  byConsumablePrescriptionDateAndPrescriberName: GetConsumablePrescribedData_getConsumablePrescribedData_list_byConsumablePrescriptionDateAndPrescriberName[] | null;
  byMost: GetConsumablePrescribedData_getConsumablePrescribedData_list_byMost[] | null;
  byLeast: GetConsumablePrescribedData_getConsumablePrescribedData_list_byLeast[] | null;
  byPatientName: GetConsumablePrescribedData_getConsumablePrescribedData_list_byPatientName[] | null;
}

export interface GetConsumablePrescribedData_getConsumablePrescribedData {
  __typename: "ServiceSummaryWithList";
  servicesSummary: GetConsumablePrescribedData_getConsumablePrescribedData_servicesSummary[];
  list: GetConsumablePrescribedData_getConsumablePrescribedData_list;
}

export interface GetConsumablePrescribedData {
  getConsumablePrescribedData: GetConsumablePrescribedData_getConsumablePrescribedData;
}

export interface GetConsumablePrescribedDataVariables {
  filter?: ServicesAnalyticsFilter | null;
}
