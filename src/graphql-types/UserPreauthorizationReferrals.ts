/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PreauthorizationReferralFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: UserPreauthorizationReferrals
// ====================================================

export interface UserPreauthorizationReferrals_profile_preauthorizationReferrals_list_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface UserPreauthorizationReferrals_profile_preauthorizationReferrals_list_diagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface UserPreauthorizationReferrals_profile_preauthorizationReferrals_list_utilizations_statusHistory {
  __typename: "ReferalStatusHistoryType";
  status: string;
  creatorName: string;
  createdDate: any;
}

export interface UserPreauthorizationReferrals_profile_preauthorizationReferrals_list_utilizations {
  __typename: "PreAuthReferralUtilisationsModel";
  id: string;
  category: string | null;
  type: string | null;
  quantity: string | null;
  /**
   * Value is in Naira
   */
  price: string | null;
  status: string | null;
  paCode: string | null;
  utilizationCode: string | null;
  utilizationId: string | null;
  rhesusFactor: string | null;
  specialty: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusHistory: UserPreauthorizationReferrals_profile_preauthorizationReferrals_list_utilizations_statusHistory[] | null;
}

export interface UserPreauthorizationReferrals_profile_preauthorizationReferrals_list_referredProvider {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
}

export interface UserPreauthorizationReferrals_profile_preauthorizationReferrals_list_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface UserPreauthorizationReferrals_profile_preauthorizationReferrals_list {
  __typename: "PreauthorisationReferralModel";
  id: string;
  requestDateTime: any;
  requestedBy: string | null;
  referredBy: string | null;
  serviceType: string;
  serviceTypeCode: string;
  provider: UserPreauthorizationReferrals_profile_preauthorizationReferrals_list_provider;
  serviceName: string | null;
  priority: string | null;
  diagnosis: UserPreauthorizationReferrals_profile_preauthorizationReferrals_list_diagnosis[] | null;
  additionalNote: string | null;
  documentUrl: string[] | null;
  code: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  rank: string | null;
  department: string | null;
  specialty: string | null;
  utilizations: UserPreauthorizationReferrals_profile_preauthorizationReferrals_list_utilizations[] | null;
  totalQuantity: string;
  grandTotal: string;
  profileId: string | null;
  referralProviderRemark: string | null;
  referredProvider: UserPreauthorizationReferrals_profile_preauthorizationReferrals_list_referredProvider | null;
  hospital: UserPreauthorizationReferrals_profile_preauthorizationReferrals_list_hospital | null;
  enrolleeNumber: string | null;
  isExternalPlanType: boolean | null;
  externalPlanTypeId: string | null;
}

export interface UserPreauthorizationReferrals_profile_preauthorizationReferrals {
  __typename: "PreauthorisationReferralResponse";
  totalCount: number;
  list: UserPreauthorizationReferrals_profile_preauthorizationReferrals_list[];
}

export interface UserPreauthorizationReferrals_profile {
  __typename: "ProfileModel";
  id: string;
  preauthorizationReferrals: UserPreauthorizationReferrals_profile_preauthorizationReferrals | null;
}

export interface UserPreauthorizationReferrals {
  profile: UserPreauthorizationReferrals_profile;
}

export interface UserPreauthorizationReferralsVariables {
  id: string;
  filterOptions?: PreauthorizationReferralFilterInput | null;
}
