/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealOncologyConsultationComplaintHistory
// ====================================================

export interface ConcealOncologyConsultationComplaintHistory_concealOncologyConsultationComplaintHistory {
  __typename: "OncologyConsultationHistoryModel";
  id: string;
  concealComplaintHistory: boolean | null;
  complaintHistory: string | null;
}

export interface ConcealOncologyConsultationComplaintHistory {
  concealOncologyConsultationComplaintHistory: ConcealOncologyConsultationComplaintHistory_concealOncologyConsultationComplaintHistory;
}

export interface ConcealOncologyConsultationComplaintHistoryVariables {
  id: string;
  concealStatus: boolean;
}
