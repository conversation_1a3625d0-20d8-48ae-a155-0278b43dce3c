/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetActivePreauthorizationReferral
// ====================================================

export interface GetActivePreauthorizationReferral_activePreauthorizationReferral_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface GetActivePreauthorizationReferral_activePreauthorizationReferral_diagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface GetActivePreauthorizationReferral_activePreauthorizationReferral_utilizations_statusHistory {
  __typename: "ReferalStatusHistoryType";
  status: string;
  creatorName: string;
  createdDate: any;
}

export interface GetActivePreauthorizationReferral_activePreauthorizationReferral_utilizations {
  __typename: "PreAuthReferralUtilisationsModel";
  id: string;
  category: string | null;
  type: string | null;
  quantity: string | null;
  /**
   * Value is in Naira
   */
  price: string | null;
  status: string | null;
  paCode: string | null;
  utilizationCode: string | null;
  utilizationId: string | null;
  rhesusFactor: string | null;
  specialty: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusHistory: GetActivePreauthorizationReferral_activePreauthorizationReferral_utilizations_statusHistory[] | null;
}

export interface GetActivePreauthorizationReferral_activePreauthorizationReferral_referredProvider {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
}

export interface GetActivePreauthorizationReferral_activePreauthorizationReferral_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface GetActivePreauthorizationReferral_activePreauthorizationReferral {
  __typename: "PreauthorisationReferralModel";
  id: string;
  requestDateTime: any;
  requestedBy: string | null;
  referredBy: string | null;
  serviceType: string;
  serviceTypeCode: string;
  provider: GetActivePreauthorizationReferral_activePreauthorizationReferral_provider;
  serviceName: string | null;
  priority: string | null;
  diagnosis: GetActivePreauthorizationReferral_activePreauthorizationReferral_diagnosis[] | null;
  additionalNote: string | null;
  documentUrl: string[] | null;
  code: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  rank: string | null;
  department: string | null;
  specialty: string | null;
  utilizations: GetActivePreauthorizationReferral_activePreauthorizationReferral_utilizations[] | null;
  totalQuantity: string;
  grandTotal: string;
  profileId: string | null;
  referralProviderRemark: string | null;
  referredProvider: GetActivePreauthorizationReferral_activePreauthorizationReferral_referredProvider | null;
  hospital: GetActivePreauthorizationReferral_activePreauthorizationReferral_hospital | null;
  enrolleeNumber: string | null;
  isExternalPlanType: boolean | null;
  externalPlanTypeId: string | null;
}

export interface GetActivePreauthorizationReferral {
  activePreauthorizationReferral: GetActivePreauthorizationReferral_activePreauthorizationReferral;
}

export interface GetActivePreauthorizationReferralVariables {
  profileId: string;
}
