/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Gender } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: ArchiveVirtualCareAppointments
// ====================================================

export interface ArchiveVirtualCareAppointments_archiveVirtualCareAppointments_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface ArchiveVirtualCareAppointments_archiveVirtualCareAppointments_specialists_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  speciality: string | null;
}

export interface ArchiveVirtualCareAppointments_archiveVirtualCareAppointments_specialists {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
  personalInformation: ArchiveVirtualCareAppointments_archiveVirtualCareAppointments_specialists_personalInformation | null;
}

export interface ArchiveVirtualCareAppointments_archiveVirtualCareAppointments_patientProfile_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
  dateOfBirth: any | null;
  displayPictureUrl: string | null;
  bloodGroup: string | null;
}

export interface ArchiveVirtualCareAppointments_archiveVirtualCareAppointments_patientProfile_user {
  __typename: "UserModel";
  id: string;
  nonCorporateEmail: string | null;
  phoneNumber: string | null;
  country: string | null;
}

export interface ArchiveVirtualCareAppointments_archiveVirtualCareAppointments_patientProfile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  gender: Gender | null;
  clinifyId: string;
  personalInformation: ArchiveVirtualCareAppointments_archiveVirtualCareAppointments_patientProfile_personalInformation | null;
  user: ArchiveVirtualCareAppointments_archiveVirtualCareAppointments_patientProfile_user;
}

export interface ArchiveVirtualCareAppointments_archiveVirtualCareAppointments {
  __typename: "VirtualCareAppointmentModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  liveSessionUrl: string | null;
  category: string | null;
  appointmentDate: any;
  startDateTime: any | null;
  endDateTime: any | null;
  priority: string | null;
  duration: string | null;
  patientType: string | null;
  paymentType: string | null;
  hospital: ArchiveVirtualCareAppointments_archiveVirtualCareAppointments_hospital | null;
  specialists: ArchiveVirtualCareAppointments_archiveVirtualCareAppointments_specialists[] | null;
  patientProfile: ArchiveVirtualCareAppointments_archiveVirtualCareAppointments_patientProfile | null;
}

export interface ArchiveVirtualCareAppointments {
  archiveVirtualCareAppointments: ArchiveVirtualCareAppointments_archiveVirtualCareAppointments[];
}

export interface ArchiveVirtualCareAppointmentsVariables {
  ids: string[];
  archive: boolean;
}
