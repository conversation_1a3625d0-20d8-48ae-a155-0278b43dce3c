/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NewDischargeSummaryTemplateInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: SaveDischargeSummaryTemplate
// ====================================================

export interface SaveDischargeSummaryTemplate_saveDischargeSummaryTemplate {
  __typename: "DischargeSummaryTemplateModel";
  id: string;
  name: string;
  summary: string;
}

export interface SaveDischargeSummaryTemplate {
  saveDischargeSummaryTemplate: SaveDischargeSummaryTemplate_saveDischargeSummaryTemplate;
}

export interface SaveDischargeSummaryTemplateVariables {
  input: NewDischargeSummaryTemplateInput;
}
