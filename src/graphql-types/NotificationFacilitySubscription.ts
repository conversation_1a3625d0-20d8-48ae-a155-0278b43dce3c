/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: NotificationFacilitySubscription
// ====================================================

export interface NotificationFacilitySubscription_NotificationFacilitySubscription_data_metaData {
  __typename: "Notifications";
  clinifyId: string | null;
  specialty: string | null;
  provider: string | null;
  department: string | null;
}

export interface NotificationFacilitySubscription_NotificationFacilitySubscription_data {
  __typename: "NotificationsModel";
  id: string;
  title: string;
  tag: string;
  description: string;
  profileId: string | null;
  metaData: NotificationFacilitySubscription_NotificationFacilitySubscription_data_metaData | null;
  createdDate: any;
}

export interface NotificationFacilitySubscription_NotificationFacilitySubscription {
  __typename: "NotificationFacilitySubscriptionResponse";
  triggeredBy: string;
  data: NotificationFacilitySubscription_NotificationFacilitySubscription_data;
}

export interface NotificationFacilitySubscription {
  NotificationFacilitySubscription: NotificationFacilitySubscription_NotificationFacilitySubscription;
}
