/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: FetchPreferredPayoutBankAccount
// ====================================================

export interface FetchPreferredPayoutBankAccount_getPreferredPayoutAccount {
  __typename: "BankAccountDetailsResponse";
  accountName: string | null;
  accountNumber: string;
  bankName: string;
}

export interface FetchPreferredPayoutBankAccount {
  getPreferredPayoutAccount: FetchPreferredPayoutBankAccount_getPreferredPayoutAccount | null;
}

export interface FetchPreferredPayoutBankAccountVariables {
  hospitalId: string;
}
