/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { SignatureInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddBloodTransfusionConsentSignature
// ====================================================

export interface AddBloodTransfusionConsentSignature_addBloodTransfusionConsentSignature {
  __typename: "BloodTransfusionModel";
  id: string;
  transfusionDateTime: any | null;
  transfusionOrderGiven: string;
  transfusionDoctor: string | null;
  transfusionNurse: string | null;
  patientBloodGroup: string | null;
  patientGenoType: string | null;
  crossMatchingTime: string | null;
  bloodLabel: string | null;
  bloodProduct: string | null;
  expiryDate: string | null;
  donorBloodType: string | null;
  bloodPint: string | null;
  lengthOfTransfusion: string | null;
  transfusionStartDateTime: any | null;
  transfusionEndDateTime: any | null;
  adverseReaction: string | null;
  reaction: string | null;
  transfusionNote: string | null;
  patientConsent: string | null;
  consentReason: string | null;
  bloodSource: string | null;
  bloodDonorStatus: string | null;
  concealTransfusionNote: boolean | null;
  postTransfusionFBC: string | null;
  concealPostTransfusionFBC: boolean | null;
  diuretic: string | null;
  diureticType: string | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
}

export interface AddBloodTransfusionConsentSignature {
  addBloodTransfusionConsentSignature: AddBloodTransfusionConsentSignature_addBloodTransfusionConsentSignature;
}

export interface AddBloodTransfusionConsentSignatureVariables {
  id: string;
  clinifyId: string;
  input: SignatureInput;
  pin?: string | null;
}
