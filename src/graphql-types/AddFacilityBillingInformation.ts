/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FacilityBillingInformationInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddFacilityBillingInformation
// ====================================================

export interface AddFacilityBillingInformation_addFacilityBillingInformation {
  __typename: "FacilityBillingInformationModel";
  id: string;
  accountName: string;
  accountNumber: string;
  bankName: string;
  bvn: string | null;
  branchName: string | null;
  isPreferredPayoutAccount: boolean | null;
}

export interface AddFacilityBillingInformation {
  addFacilityBillingInformation: AddFacilityBillingInformation_addFacilityBillingInformation;
}

export interface AddFacilityBillingInformationVariables {
  hospitalId: string;
  input: FacilityBillingInformationInput;
}
