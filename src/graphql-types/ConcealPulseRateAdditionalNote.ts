/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealPulseRateAdditionalNote
// ====================================================

export interface ConcealPulseRateAdditionalNote_concealPulseRateAdditionalNote {
  __typename: "PulseRateModel";
  id: string;
  concealAdditionalNote: boolean | null;
  additionalNote: string | null;
}

export interface ConcealPulseRateAdditionalNote {
  concealPulseRateAdditionalNote: ConcealPulseRateAdditionalNote_concealPulseRateAdditionalNote;
}

export interface ConcealPulseRateAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
  clinifyId: string;
}
