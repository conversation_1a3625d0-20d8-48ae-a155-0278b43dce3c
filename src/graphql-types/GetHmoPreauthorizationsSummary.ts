/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HmosAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHmoPreauthorizationsSummary
// ====================================================

export interface GetHmoPreauthorizationsSummary_getHmoPreauthorizationsSummary_groupedData {
  __typename: "GroupedHmoPreauthorizationSummary";
  hospitalName: string | null;
  hmoProvider: string | null;
  totalPreauthorizations: number | null;
  totalPendingPreauthorizations: number | null;
  totalApprovedPreauthorizations: number | null;
  totalRejectedPreauthorizations: number | null;
  totalPreauthorizationAmount: string | null;
  totalRejectedPreauthorizationAmount: string | null;
  totalPendingPreauthorizationAmount: string | null;
  totalApprovedPreauthorizationAmount: string | null;
  totalEnrollees: number | null;
  totalEnrolleeVisitations: number | null;
  totalActiveEnrollees: number | null;
  totalInactiveEnrollees: number | null;
  totalReferrals: number | null;
  totalReferralsAmount: number | null;
  totalPendingReferrals: number | null;
  totalPendingReferralsAmount: number | null;
  totalApprovedReferrals: number | null;
  totalApprovedReferralsAmount: number | null;
  totalRejectedReferrals: number | null;
  totalRejectedReferralsAmount: number | null;
  totalFlaggedPreauthorizations: number | null;
  totalFlaggedPreauthorizationsAmount: string | null;
  totalClaimConfirmationApprovedPreauthorizations: number | null;
  totalClaimConfirmationApprovedPreauthorizationsAmount: number | null;
  totalClaimOfficerApprovedPreauthorizations: number | null;
  totalClaimOfficerApprovedPreauthorizationsAmount: number | null;
  totalClaimOfficerHODApprovedPreauthorizations: number | null;
  totalClaimOfficerHODApprovedPreauthorizationsAmount: number | null;
  totalClaimReviewerApprovedPreauthorizations: number | null;
  totalClaimReviewerApprovedPreauthorizationsAmount: number | null;
  totalClaimReviewerHODApprovedPreauthorizations: number | null;
  totalClaimReviewerHODApprovedPreauthorizationsAmount: number | null;
  totalClaimAuditApprovedPreauthorizations: number | null;
  totalClaimAuditApprovedPreauthorizationsAmount: number | null;
  totalClaimAuditHODApprovedPreauthorizations: number | null;
  totalClaimAuditHODApprovedPreauthorizationsAmount: number | null;
  totalClaimAdminApprovedPreauthorizations: number | null;
  totalClaimAdminApprovedPreauthorizationsAmount: number | null;
  totalClaimFinanceApprovedPreauthorizations: number | null;
  totalClaimFinanceApprovedPreauthorizationsAmount: number | null;
  totalClaimAccountApprovedPreauthorizations: number | null;
  totalClaimAccountApprovedPreauthorizationsAmount: number | null;
  totalClaimAgentApprovedPreauthorizations: number | null;
  totalClaimAgentApprovedPreauthorizationsAmount: number | null;
  totalClaimAgentHODApprovedPreauthorizations: number | null;
  totalClaimAgentHODApprovedPreauthorizationsAmount: number | null;
  totalClaimConfirmationRejectedPreauthorizations: number | null;
  totalClaimConfirmationRejectedPreauthorizationsAmount: number | null;
  totalClaimOfficerRejectedPreauthorizations: number | null;
  totalClaimOfficerRejectedPreauthorizationsAmount: number | null;
  totalClaimOfficerHODRejectedPreauthorizations: number | null;
  totalClaimOfficerHODRejectedPreauthorizationsAmount: number | null;
  totalClaimReviewerRejectedPreauthorizations: number | null;
  totalClaimReviewerRejectedPreauthorizationsAmount: number | null;
  totalClaimReviewerHODRejectedPreauthorizations: number | null;
  totalClaimReviewerHODRejectedPreauthorizationsAmount: number | null;
  totalClaimAuditRejectedPreauthorizations: number | null;
  totalClaimAuditRejectedPreauthorizationsAmount: number | null;
  totalClaimAuditHODRejectedPreauthorizations: number | null;
  totalClaimAuditHODRejectedPreauthorizationsAmount: number | null;
  totalClaimAdminRejectedPreauthorizations: number | null;
  totalClaimAdminRejectedPreauthorizationsAmount: number | null;
  totalClaimFinanceRejectedPreauthorizations: number | null;
  totalClaimFinanceRejectedPreauthorizationsAmount: number | null;
  totalClaimAccountRejectedPreauthorizations: number | null;
  totalClaimAccountRejectedPreauthorizationsAmount: number | null;
  totalClaimAgentRejectedPreauthorizations: number | null;
  totalClaimAgentRejectedPreauthorizationsAmount: number | null;
  totalClaimAgentHODRejectedPreauthorizations: number | null;
  totalClaimAgentHODRejectedPreauthorizationsAmount: number | null;
  totalClaimConfirmationFlaggedPreauthorizations: number | null;
  totalClaimConfirmationFlaggedPreauthorizationsAmount: number | null;
  totalClaimOfficerFlaggedPreauthorizations: number | null;
  totalClaimOfficerFlaggedPreauthorizationsAmount: number | null;
  totalClaimOfficerHODFlaggedPreauthorizations: number | null;
  totalClaimOfficerHODFlaggedPreauthorizationsAmount: number | null;
  totalClaimReviewerFlaggedPreauthorizations: number | null;
  totalClaimReviewerFlaggedPreauthorizationsAmount: number | null;
  totalClaimReviewerHODFlaggedPreauthorizations: number | null;
  totalClaimReviewerHODFlaggedPreauthorizationsAmount: number | null;
  totalClaimAuditFlaggedPreauthorizations: number | null;
  totalClaimAuditFlaggedPreauthorizationsAmount: number | null;
  totalClaimAuditHODFlaggedPreauthorizations: number | null;
  totalClaimAuditHODFlaggedPreauthorizationsAmount: number | null;
  totalClaimAdminFlaggedPreauthorizations: number | null;
  totalClaimAdminFlaggedPreauthorizationsAmount: number | null;
  totalClaimFinanceFlaggedPreauthorizations: number | null;
  totalClaimFinanceFlaggedPreauthorizationsAmount: number | null;
  totalClaimAccountFlaggedPreauthorizations: number | null;
  totalClaimAccountFlaggedPreauthorizationsAmount: number | null;
  totalClaimAgentFlaggedPreauthorizations: number | null;
  totalClaimAgentFlaggedPreauthorizationsAmount: number | null;
  totalClaimAgentHODFlaggedPreauthorizations: number | null;
  totalClaimAgentHODFlaggedPreauthorizationsAmount: number | null;
}

export interface GetHmoPreauthorizationsSummary_getHmoPreauthorizationsSummary_automaticAndManualData_byRoles {
  __typename: "SummaryCount";
  name: string | null;
  count: number | null;
  manualCount: number | null;
}

export interface GetHmoPreauthorizationsSummary_getHmoPreauthorizationsSummary_automaticAndManualData_byStaffs {
  __typename: "SummaryCount";
  name: string | null;
  count: number | null;
  type: string | null;
  manualCount: number | null;
}

export interface GetHmoPreauthorizationsSummary_getHmoPreauthorizationsSummary_automaticAndManualData {
  __typename: "AutomaticAndManualHmoClaimsData";
  totalAutomated: number | null;
  totalManual: number | null;
  byRoles: GetHmoPreauthorizationsSummary_getHmoPreauthorizationsSummary_automaticAndManualData_byRoles[] | null;
  byStaffs: GetHmoPreauthorizationsSummary_getHmoPreauthorizationsSummary_automaticAndManualData_byStaffs[] | null;
}

export interface GetHmoPreauthorizationsSummary_getHmoPreauthorizationsSummary {
  __typename: "HmoPreauthorizationSummary";
  groupedData: GetHmoPreauthorizationsSummary_getHmoPreauthorizationsSummary_groupedData[] | null;
  automaticAndManualData: GetHmoPreauthorizationsSummary_getHmoPreauthorizationsSummary_automaticAndManualData | null;
  totalClaimOfficerStaffCount: number | null;
  totalClaimOfficerHODStaffCount: number | null;
  totalClaimReviewerStaffCount: number | null;
  totalClaimReviewerHODStaffCount: number | null;
  totalClaimAuditStaffCount: number | null;
  totalClaimAuditHODStaffCount: number | null;
  totalClaimAdminStaffCount: number | null;
  totalClaimFinanceStaffCount: number | null;
  totalClaimAccountStaffCount: number | null;
  totalClaimAgentStaffCount: number | null;
  totalClaimAgentHODStaffCount: number | null;
  totalClaimConfirmationStaffCount: number | null;
}

export interface GetHmoPreauthorizationsSummary {
  getHmoPreauthorizationsSummary: GetHmoPreauthorizationsSummary_getHmoPreauthorizationsSummary;
}

export interface GetHmoPreauthorizationsSummaryVariables {
  filter?: HmosAnalyticsFilter | null;
}
