/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: PreChemoEducationUpdatedSubs
// ====================================================

export interface PreChemoEducationUpdatedSubs_PreChemoEducationUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface PreChemoEducationUpdatedSubs_PreChemoEducationUpdated {
  __typename: "PreChemoEducationModel";
  id: string;
  preChemoChecklist: string[] | null;
  treatmentPlan: string | null;
  parentGuardianName: string | null;
  drugInformationProvided: string | null;
  diseaseOrDiagnosis: string | null;
  dataLibrary: string | null;
  handOutsProvided: string | null;
  steroidEducation: string | null;
  ivAccessEvaluated: string | null;
  fertilityIssues: string | null;
  spiritualCareService: string | null;
  spiritualGroup: string | null;
  financialIssues: string | null;
  comfortItems: string | null;
  herbalInteraction: string | null;
  appointmentFlow: string | null;
  clinicalProcessDiscussed: string | null;
  alcoholBasedHandRub: string | null;
  anaemia: string | null;
  diarrhea: string | null;
  hairLossSyndrome: string | null;
  mouthSores: string | null;
  neuropathy: string | null;
  thrombocytopenia: string | null;
  handAndFoot: string | null;
  nauseaAndVomitting: string | null;
  neutropenia: string | null;
  nadir: string | null;
  fatigue: string | null;
  doesPatientWork: string | null;
  patientWithLargeGroups: string | null;
  cancerDiagnosis: string | null;
  pastPresentProcedure: string | null;
  radiationTherapy: string | null;
  hormonalAgent: string | null;
  allergies: string | null;
  pastMedicalHistory: string | null;
  medicationReview: string | null;
  symptomsReview: string | null;
  distress: string | null;
  performanceStatus: string | null;
  heightAndWeight: string | null;
  labValues: string | null;
  tumourInfo: string | null;
  baselineVitalSigns: string | null;
  otherDeformities: string | null;
  gestationalAge: string | null;
  treatmentPlanOrProtocol: string | null;
  participatingInClinicalTrials: string | null;
  painScore: string | null;
  informedConsentForChemo: string | null;
  consentGiven: string | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean;
  documentUrl: string[] | null;
  profileId: string | null;
  profile: PreChemoEducationUpdatedSubs_PreChemoEducationUpdated_profile | null;
}

export interface PreChemoEducationUpdatedSubs {
  PreChemoEducationUpdated: PreChemoEducationUpdatedSubs_PreChemoEducationUpdated;
}

export interface PreChemoEducationUpdatedSubsVariables {
  profileId: string;
}
