/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: DeleteAnaesthesiaChecklists
// ====================================================

export interface DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_drugsGiven {
  __typename: "DrugGivenType";
  readingDate: any | null;
  time: string | null;
  drugName: string | null;
  dosage: string | null;
  dosageUnit: string | null;
  routeOfAdministration: string | null;
}

export interface DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_pulseAndBp {
  __typename: "PulseAndBpType";
  readingDate: any | null;
  time: string | null;
  pulse: string | null;
  bpSystolic: string | null;
  bpDiastolic: string | null;
}

export interface DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_eventTimeGraph {
  __typename: "EventTimeType";
  readingDate: any | null;
  eventName: string | null;
  time: any | null;
}

export interface DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_oxygenLevel {
  __typename: "OxygenLevelDataType";
  readingDate: any | null;
  time: string | null;
  ecgRythm: string | null;
  saturated: string | null;
  airFGF: string | null;
  endTidal: string | null;
  oxygenFi: string | null;
  agentFe: string | null;
  airwayPressure: string | null;
  airwayPressureUnit: string | null;
}

export interface DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_additionalOxygenLevel_value {
  __typename: "TimeVolumeType";
  time: string | null;
  volume: string | null;
}

export interface DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_additionalOxygenLevel {
  __typename: "AdditionalTimeVolumeType";
  name: string | null;
  value: DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_additionalOxygenLevel_value[] | null;
}

export interface DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_fluids {
  __typename: "FliudDataType";
  readingDate: any | null;
  time: string | null;
  ivFluids: string | null;
  bloodLoss: string | null;
  urineOutput: string | null;
}

export interface DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_size {
  __typename: "TicknessValueType";
  tick: boolean | null;
  value: string | null;
}

export interface DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_cmToLips {
  __typename: "TicknessValueType";
  tick: boolean | null;
  value: string | null;
}

export interface DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_nmj {
  __typename: "TicknessSiteType";
  tick: boolean | null;
  site: string | null;
}

export interface DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists {
  __typename: "AnaesthesiaModel";
  id: string;
  drugsGiven: DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_drugsGiven[] | null;
  pulseAndBp: DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_pulseAndBp[] | null;
  eventTimeGraph: DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_eventTimeGraph[] | null;
  oxygenLevel: DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_oxygenLevel[] | null;
  additionalOxygenLevel: DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_additionalOxygenLevel[] | null;
  fluids: DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_fluids[] | null;
  inOt: boolean | null;
  rapidSequence: boolean | null;
  inhalational: boolean | null;
  equipmentCheck: boolean | null;
  preOxygenation: boolean | null;
  vascularAccessSite: string | null;
  preInduction: string | null;
  postInduction: string | null;
  nasalSpecs: boolean | null;
  faceMask: boolean | null;
  iGel: boolean | null;
  lma: boolean | null;
  endatrachealTube: boolean | null;
  size: DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_size | null;
  cmToLips: DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_cmToLips | null;
  cuff: boolean | null;
  amoured: boolean | null;
  oralNasal: boolean | null;
  grade: string | null;
  difficult: boolean | null;
  bougie: boolean | null;
  mcCoy: boolean | null;
  bougieILMA: boolean | null;
  fibreOptic: boolean | null;
  mcIntosh: boolean | null;
  capnograph: boolean | null;
  auscultation: boolean | null;
  nmj: DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_nmj | null;
  tempProbe: boolean | null;
  respiration: string | null;
  mode: string | null;
  tv: string | null;
  frequency: string | null;
  circuit: string | null;
  other: string | null;
  eyesCovered: boolean | null;
  pressurePointsPadded: boolean | null;
  urinaryCatheter: boolean | null;
  nasogastricTube: boolean | null;
  warmingBlanket: boolean | null;
  position: string | null;
  arms: string | null;
  intravenousFluidsGiven: string | null;
  localAnaestheticTechnique: string | null;
  clinicalAdverseEvent: string | null;
  createdDate: any;
  updatedDate: any;
  creatorName: string | null;
  lastModifierName: string | null;
  profile: DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists_profile | null;
}

export interface DeleteAnaesthesiaChecklists {
  deleteAnaesthesiaChecklists: DeleteAnaesthesiaChecklists_deleteAnaesthesiaChecklists[];
}

export interface DeleteAnaesthesiaChecklistsVariables {
  ids: string[];
  clinifyId: string;
}
