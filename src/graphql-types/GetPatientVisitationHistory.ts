/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PatientVisitationHistoryFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPatientVisitationHistory
// ====================================================

export interface GetPatientVisitationHistory_getPatientVisitationHistory_admission {
  __typename: "PVH_AdmissionResponse";
  date: any;
  dischargeDate: any | null;
  hospitalName: string;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_antenatal {
  __typename: "PVH_AntenatalResponse";
  hospitalName: string;
  date: any;
  estimatedDateOfDelivery: any | null;
  lastMenstrualPeriod: string | null;
  seenBy: string | null;
  gestationAge: string | null;
  maternalWeight: string | null;
  maternalWeightUnit: string | null;
  bloodPressure: string | null;
  fetalHeartRate: string | null;
  symphysioHeight: string | null;
  presentation: string | null;
  lie: string | null;
  fetalMovement: string | null;
  oedema: string | null;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_consultation_finalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_consultation_provisionalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_consultation {
  __typename: "PVH_ConsultationResponse";
  date: any;
  doctorName: string;
  finalDiagnosis: GetPatientVisitationHistory_getPatientVisitationHistory_consultation_finalDiagnosis[] | null;
  provisionalDiagnosis: GetPatientVisitationHistory_getPatientVisitationHistory_consultation_provisionalDiagnosis[] | null;
  complaint: string | null;
  complaintHistory: string | null;
  hospitalName: string | null;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_immunization {
  __typename: "PVH_ImmunizationResponse";
  hospitalName: string;
  administeredDate: any;
  name: string;
  administratorName: string | null;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_laboratory {
  __typename: "PVH_InvestigationResponse";
  name: string[];
  hospitalName: string;
  date: any;
  performedBy: string | null;
  requestedBy: string;
  type: string;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_radiology {
  __typename: "PVH_InvestigationResponse";
  name: string[];
  hospitalName: string;
  date: any;
  performedBy: string | null;
  requestedBy: string;
  type: string;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_labourAndDelivery {
  __typename: "PVH_LaborAndDeliveryResponse";
  date: any;
  hospitalName: string;
  seenBy: string;
  estimatedDateOfDelivery: any;
  gestationalAge: string;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_medicalReport {
  __typename: "PVH_MedicalReportResponse";
  hospitalName: string;
  date: any;
  type: string[];
  doctorName: string;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_medication {
  __typename: "PVH_MedicationResponse";
  hospitalName: string;
  name: string[] | null;
  consumableName: string[] | null;
  prescribedBy: string;
  prescriptionDate: any;
  administrationMethod: string | null;
  dosage: string | null;
  dosageUnit: string | null;
  frequency: string | null;
  duration: string | null;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_nursingServices {
  __typename: "PVH_NursingServicesResponse";
  hospitalName: string;
  assistantNurseName: string | null;
  nurseName: string | null;
  procedureDate: any | null;
  procedureName: string | null;
  procedureType: string | null;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_postnatal {
  __typename: "PVH_PostnatalResponse";
  hospitalName: string;
  seenBy: string;
  deliveryDate: any | null;
  visitationDate: any | null;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_procedure {
  __typename: "PVH_ProcedureResponse";
  hospitalName: string;
  type: string[];
  date: any;
  requestedBy: string;
  operatedBy: string | null;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns_anthropometry {
  __typename: "PVH_Anthropometry";
  height: string | null;
  heightUnit: string | null;
  readingDateTime: any | null;
  weight: string | null;
  weightUnit: string | null;
  bmi: string | null;
  bsa: string | null;
  isHeightCritical: boolean | null;
  isWeightCritical: boolean | null;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns_bloodGlucose {
  __typename: "PVH_BloodGlucose";
  readingDateTime: any | null;
  reading: string | null;
  readingUnit: string | null;
  isReadingCritical: boolean | null;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns_bloodPressure {
  __typename: "PVH_BloodPressure";
  readingDateTime: any | null;
  diastolic: string | null;
  systolic: string | null;
  heartRate: string | null;
  isDiastolicCritical: boolean | null;
  isSystolicCritical: boolean | null;
  isHeartRateCritical: boolean | null;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns_pain {
  __typename: "PVH_Pain";
  type: string | null;
  dateTimePainStarted: any | null;
  score: string | null;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns_pulseRate {
  __typename: "PVH_PulseRate";
  readingDateTime: any | null;
  reading: string | null;
  isReadingCritical: boolean | null;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns_respiratoryRate {
  __typename: "PVH_RespiratoryRate";
  reading: string | null;
  readingDateTime: any | null;
  isReadingCritical: boolean | null;
  isOxygenSaturationCritical: boolean | null;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns_temperature {
  __typename: "PVH_Temperature";
  readingDateTime: any | null;
  reading: string | null;
  readingUnit: string | null;
  isReadingCritical: boolean | null;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns_urineDipstick {
  __typename: "PVH_UrineDipstick";
  readingDateTime: any | null;
  blood: string | null;
  glucose: string | null;
  ketones: string | null;
  nitrites: string | null;
  protein: string | null;
  urobilinogen: string | null;
  leucocyte: string | null;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns_visualAcuity {
  __typename: "PVH_VisualAcuity";
  readingDateTime: any | null;
  withGlassesLeft: string | null;
  withGlassesRight: string | null;
  withoutGlassesLeft: string | null;
  withoutGlassesRight: string | null;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns {
  __typename: "PVH_VitalSignsResponse";
  date: any;
  hospitalName: string;
  anthropometry: GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns_anthropometry | null;
  bloodGlucose: GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns_bloodGlucose | null;
  bloodPressure: GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns_bloodPressure | null;
  pain: GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns_pain | null;
  pulseRate: GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns_pulseRate | null;
  respiratoryRate: GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns_respiratoryRate | null;
  takenBy: string;
  temperature: GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns_temperature | null;
  urineDipstick: GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns_urineDipstick | null;
  visualAcuity: GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns_visualAcuity | null;
}

export interface GetPatientVisitationHistory_getPatientVisitationHistory {
  __typename: "PatientVisitationHistoryResponse";
  nextVisitDate: any | null;
  previousVisitDate: any | null;
  admission: GetPatientVisitationHistory_getPatientVisitationHistory_admission[] | null;
  antenatal: GetPatientVisitationHistory_getPatientVisitationHistory_antenatal[] | null;
  consultation: GetPatientVisitationHistory_getPatientVisitationHistory_consultation[] | null;
  immunization: GetPatientVisitationHistory_getPatientVisitationHistory_immunization[] | null;
  laboratory: GetPatientVisitationHistory_getPatientVisitationHistory_laboratory[] | null;
  radiology: GetPatientVisitationHistory_getPatientVisitationHistory_radiology[] | null;
  labourAndDelivery: GetPatientVisitationHistory_getPatientVisitationHistory_labourAndDelivery[] | null;
  medicalReport: GetPatientVisitationHistory_getPatientVisitationHistory_medicalReport[] | null;
  medication: GetPatientVisitationHistory_getPatientVisitationHistory_medication[] | null;
  nursingServices: GetPatientVisitationHistory_getPatientVisitationHistory_nursingServices[] | null;
  postnatal: GetPatientVisitationHistory_getPatientVisitationHistory_postnatal[] | null;
  procedure: GetPatientVisitationHistory_getPatientVisitationHistory_procedure[] | null;
  vitalSigns: GetPatientVisitationHistory_getPatientVisitationHistory_vitalSigns[] | null;
}

export interface GetPatientVisitationHistory {
  getPatientVisitationHistory: GetPatientVisitationHistory_getPatientVisitationHistory;
}

export interface GetPatientVisitationHistoryVariables {
  filter: PatientVisitationHistoryFilter;
}
