/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FamilyPlanningAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetNewFamilyPlanningAcceptors
// ====================================================

export interface GetNewFamilyPlanningAcceptors_getNewFamilyPlanningAcceptors {
  __typename: "FamilyPlanningSummary";
  name: number | null;
  totalNewFamilyPlanningAcceptors: number | null;
  category: string | null;
}

export interface GetNewFamilyPlanningAcceptors {
  getNewFamilyPlanningAcceptors: GetNewFamilyPlanningAcceptors_getNewFamilyPlanningAcceptors[];
}

export interface GetNewFamilyPlanningAcceptorsVariables {
  filter?: FamilyPlanningAnalyticsFilter | null;
}
