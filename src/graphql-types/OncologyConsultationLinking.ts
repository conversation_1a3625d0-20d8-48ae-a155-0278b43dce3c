/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL fragment: OncologyConsultationLinking
// ====================================================

export interface OncologyConsultationLinking_allergies {
  __typename: "AllergyModel";
  id: string;
}

export interface OncologyConsultationLinking_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface OncologyConsultationLinking_surgeries {
  __typename: "SurgeryModel";
  id: string;
}

export interface OncologyConsultationLinking_admissions {
  __typename: "AdmissionModel";
  id: string;
}

export interface OncologyConsultationLinking_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface OncologyConsultationLinking_labTests {
  __typename: "InvestigationModel";
  id: string;
}

export interface OncologyConsultationLinking_radiology {
  __typename: "InvestigationModel";
  id: string;
}

export interface OncologyConsultationLinking_investigations {
  __typename: "InvestigationModel";
  id: string;
}

export interface OncologyConsultationLinking_nursingServices {
  __typename: "NursingServiceModel";
  id: string;
}

export interface OncologyConsultationLinking {
  __typename: "OncologyConsultationHistoryModel";
  allergies: OncologyConsultationLinking_allergies[];
  medications: OncologyConsultationLinking_medications[];
  surgeries: OncologyConsultationLinking_surgeries[];
  admissions: OncologyConsultationLinking_admissions[];
  vitals: OncologyConsultationLinking_vitals[];
  labTests: OncologyConsultationLinking_labTests[];
  radiology: OncologyConsultationLinking_radiology[];
  investigations: OncologyConsultationLinking_investigations[];
  nursingServices: OncologyConsultationLinking_nursingServices[];
}
