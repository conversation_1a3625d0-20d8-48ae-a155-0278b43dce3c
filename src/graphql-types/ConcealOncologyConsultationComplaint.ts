/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealOncologyConsultationComplaint
// ====================================================

export interface ConcealOncologyConsultationComplaint_concealOncologyConsultationComplaint {
  __typename: "OncologyConsultationHistoryModel";
  id: string;
  concealComplaint: boolean | null;
  complaint: string | null;
}

export interface ConcealOncologyConsultationComplaint {
  concealOncologyConsultationComplaint: ConcealOncologyConsultationComplaint_concealOncologyConsultationComplaint;
}

export interface ConcealOncologyConsultationComplaintVariables {
  id: string;
  concealStatus: boolean;
}
