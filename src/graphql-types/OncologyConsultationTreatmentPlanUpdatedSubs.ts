/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: OncologyConsultationTreatmentPlanUpdatedSubs
// ====================================================

export interface OncologyConsultationTreatmentPlanUpdatedSubs_OncologyConsultationTreatmentPlanUpdated {
  __typename: "OncologyTreatmentPlanModel";
  id: string;
  treatmentPlan: string | null;
  conceal: boolean | null;
  patientAdmitted: string | null;
  concealObservationNote: boolean | null;
  observationNote: string | null;
  admissionConsent: string | null;
  adverseEffectsFollowingTreatment: string | null;
  stateEffects: string | null;
  adverseEffectsInvestigated: string | null;
  outcomeOfInvestigation: string | null;
  treatmentGiven: string | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
  treatmentStatus: string | null;
}

export interface OncologyConsultationTreatmentPlanUpdatedSubs {
  OncologyConsultationTreatmentPlanUpdated: OncologyConsultationTreatmentPlanUpdatedSubs_OncologyConsultationTreatmentPlanUpdated;
}

export interface OncologyConsultationTreatmentPlanUpdatedSubsVariables {
  profileId: string;
}
