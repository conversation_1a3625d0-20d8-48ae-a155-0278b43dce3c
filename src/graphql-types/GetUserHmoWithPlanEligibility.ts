/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HmoProfileFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetUserHmoWithPlanEligibility
// ====================================================

export interface GetUserHmoWithPlanEligibility_profile_hmos_list_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface GetUserHmoWithPlanEligibility_profile_hmos_list {
  __typename: "HmoProfileModel";
  id: string;
  provider: GetUserHmoWithPlanEligibility_profile_hmos_list_provider;
  planEligibility: string | null;
}

export interface GetUserHmoWithPlanEligibility_profile_hmos {
  __typename: "HmoProfileResponse";
  totalCount: number;
  list: GetUserHmoWithPlanEligibility_profile_hmos_list[];
}

export interface GetUserHmoWithPlanEligibility_profile {
  __typename: "ProfileModel";
  id: string;
  hmos: GetUserHmoWithPlanEligibility_profile_hmos;
}

export interface GetUserHmoWithPlanEligibility {
  profile: GetUserHmoWithPlanEligibility_profile;
}

export interface GetUserHmoWithPlanEligibilityVariables {
  filterInput?: HmoProfileFilterInput | null;
  id: string;
}
