/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FinanceAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetServiceRevenueDataSummary
// ====================================================

export interface GetServiceRevenueDataSummary_getServiceRevenueDataSummary {
  __typename: "FinanceDataSummaryResponse";
  name: number | null;
  category: string;
  totalDiscountAmount: number | null;
  totalAmount: number | null;
  totalAmountDue: number | null;
  totalAmountOutstanding: number | null;
  totalAmountPaid: number | null;
  totalRevenue: number | null;
}

export interface GetServiceRevenueDataSummary {
  getServiceRevenueDataSummary: GetServiceRevenueDataSummary_getServiceRevenueDataSummary[];
}

export interface GetServiceRevenueDataSummaryVariables {
  filter: FinanceAnalyticsFilter;
}
