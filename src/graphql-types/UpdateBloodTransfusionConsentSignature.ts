/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { SignatureInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateBloodTransfusionConsentSignature
// ====================================================

export interface UpdateBloodTransfusionConsentSignature_updateBloodTransfusionConsentSignature {
  __typename: "BloodTransfusionModel";
  id: string;
  transfusionDateTime: any | null;
  transfusionOrderGiven: string;
  transfusionDoctor: string | null;
  transfusionNurse: string | null;
  patientBloodGroup: string | null;
  patientGenoType: string | null;
  crossMatchingTime: string | null;
  bloodLabel: string | null;
  bloodProduct: string | null;
  expiryDate: string | null;
  donorBloodType: string | null;
  bloodPint: string | null;
  lengthOfTransfusion: string | null;
  transfusionStartDateTime: any | null;
  transfusionEndDateTime: any | null;
  adverseReaction: string | null;
  reaction: string | null;
  transfusionNote: string | null;
  patientConsent: string | null;
  consentReason: string | null;
  bloodSource: string | null;
  bloodDonorStatus: string | null;
  concealTransfusionNote: boolean | null;
  postTransfusionFBC: string | null;
  concealPostTransfusionFBC: boolean | null;
  diuretic: string | null;
  diureticType: string | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
}

export interface UpdateBloodTransfusionConsentSignature {
  updateBloodTransfusionConsentSignature: UpdateBloodTransfusionConsentSignature_updateBloodTransfusionConsentSignature;
}

export interface UpdateBloodTransfusionConsentSignatureVariables {
  id: string;
  clinifyId: string;
  input: SignatureInput;
  pin?: string | null;
}
