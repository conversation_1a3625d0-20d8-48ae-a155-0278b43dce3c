/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealAllergyAdditionalNote
// ====================================================

export interface ConcealAllergyAdditionalNote_concealAllergyAdditionalNote {
  __typename: "AllergyModel";
  id: string;
  concealAdditionalNote: boolean | null;
  additionalNote: string | null;
}

export interface ConcealAllergyAdditionalNote {
  concealAllergyAdditionalNote: ConcealAllergyAdditionalNote_concealAllergyAdditionalNote;
}

export interface ConcealAllergyAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
}
