/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { GrantOrganizationPermissionInput, Subject, Action } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: GrantOrganizationPermission
// ====================================================

export interface GrantOrganizationPermission_grantOrganizationPermission_rules {
  __typename: "PermissionModelInput";
  subject: Subject;
  action: Action;
}

export interface GrantOrganizationPermission_grantOrganizationPermission {
  __typename: "PermissionModel";
  id: string;
  rules: GrantOrganizationPermission_grantOrganizationPermission_rules[] | null;
}

export interface GrantOrganizationPermission {
  grantOrganizationPermission: GrantOrganizationPermission_grantOrganizationPermission;
}

export interface GrantOrganizationPermissionVariables {
  input: GrantOrganizationPermissionInput;
}
