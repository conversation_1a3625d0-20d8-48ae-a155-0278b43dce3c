/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ArchiveMedicationBundleItem
// ====================================================

export interface ArchiveMedicationBundleItem_archiveMedicationBundles {
  __typename: "MedicationBundleModel";
  id: string;
  bundleName: string;
  clinifyId: string;
  createdDate: any;
  updatedDate: any;
  creatorId: string | null;
  creatorName: string | null;
  documentUrl: string[] | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
  additionalNote: string | null;
}

export interface ArchiveMedicationBundleItem {
  archiveMedicationBundles: ArchiveMedicationBundleItem_archiveMedicationBundles[];
}

export interface ArchiveMedicationBundleItemVariables {
  ids: string[];
  archive?: boolean | null;
}
