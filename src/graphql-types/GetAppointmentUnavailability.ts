/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetAppointmentUnavailability
// ====================================================

export interface GetAppointmentUnavailability_getAppointmentUnavailability_list {
  __typename: "unavailableDates";
  startDate: any;
  endDate: any | null;
  isAllDay: boolean;
  reason: string | null;
}

export interface GetAppointmentUnavailability_getAppointmentUnavailability {
  __typename: "ProfileUnavailableDates";
  id: string;
  list: GetAppointmentUnavailability_getAppointmentUnavailability_list[] | null;
}

export interface GetAppointmentUnavailability {
  getAppointmentUnavailability: GetAppointmentUnavailability_getAppointmentUnavailability;
}

export interface GetAppointmentUnavailabilityVariables {
  profileId: string;
}
