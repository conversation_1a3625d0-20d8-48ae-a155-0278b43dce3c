/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: UpdatePatientDataAccessSettings
// ====================================================

export interface UpdatePatientDataAccessSettings_updatePatientDataAccessSetting {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface UpdatePatientDataAccessSettings {
  updatePatientDataAccessSetting: UpdatePatientDataAccessSettings_updatePatientDataAccessSetting[];
}

export interface UpdatePatientDataAccessSettingsVariables {
  hospitalId: string;
  profileIds: string[];
  visibilityStatus: boolean;
  origin: string;
  sendConfirmationEmail?: boolean | null;
}
