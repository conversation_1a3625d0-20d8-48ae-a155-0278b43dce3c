/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetTariffsAndInventoryPreference
// ====================================================

export interface GetTariffsAndInventoryPreference_hospital_preference {
  __typename: "FacilityPreferencePublic";
  id: string | null;
  useHQFacilityTariffs: boolean | null;
  useHQFacilityInventory: boolean | null;
}

export interface GetTariffsAndInventoryPreference_hospital {
  __typename: "HospitalModel";
  id: string;
  preference: GetTariffsAndInventoryPreference_hospital_preference;
}

export interface GetTariffsAndInventoryPreference {
  hospital: GetTariffsAndInventoryPreference_hospital;
}
