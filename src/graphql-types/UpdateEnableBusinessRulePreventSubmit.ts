/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: UpdateEnableBusinessRulePreventSubmit
// ====================================================

export interface UpdateEnableBusinessRulePreventSubmit_updateEnableBusinessRulePreventSubmit {
  __typename: "FacilityPreferenceModel";
  id: string;
  enableBusinessRulePreventSubmit: boolean | null;
}

export interface UpdateEnableBusinessRulePreventSubmit {
  updateEnableBusinessRulePreventSubmit: UpdateEnableBusinessRulePreventSubmit_updateEnableBusinessRulePreventSubmit;
}

export interface UpdateEnableBusinessRulePreventSubmitVariables {
  mode: boolean;
}
