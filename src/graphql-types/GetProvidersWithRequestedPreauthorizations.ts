/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PreauthorizationFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetProvidersWithRequestedPreauthorizations
// ====================================================

export interface GetProvidersWithRequestedPreauthorizations_getProvidersWithRequestedPreauthorizations_hospitalSupportPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  countryName: string | null;
  value: string | null;
}

export interface GetProvidersWithRequestedPreauthorizations_getProvidersWithRequestedPreauthorizations {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
  hospitalSupportPhoneNumber: GetProvidersWithRequestedPreauthorizations_getProvidersWithRequestedPreauthorizations_hospitalSupportPhoneNumber | null;
  supportMail: string | null;
}

export interface GetProvidersWithRequestedPreauthorizations {
  getProvidersWithRequestedPreauthorizations: GetProvidersWithRequestedPreauthorizations_getProvidersWithRequestedPreauthorizations[];
}

export interface GetProvidersWithRequestedPreauthorizationsVariables {
  filterOptions: PreauthorizationFilterInput;
}
