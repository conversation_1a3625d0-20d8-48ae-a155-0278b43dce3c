/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PayoutCommissionPayer } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdatePayoutCommissionPayer
// ====================================================

export interface UpdatePayoutCommissionPayer_updatePayoutCommissionPayer {
  __typename: "FacilityPreferenceModel";
  id: string;
  payoutCommissionPayer: PayoutCommissionPayer | null;
}

export interface UpdatePayoutCommissionPayer {
  updatePayoutCommissionPayer: UpdatePayoutCommissionPayer_updatePayoutCommissionPayer;
}

export interface UpdatePayoutCommissionPayerVariables {
  payoutCommissionPayer: PayoutCommissionPayer;
}
