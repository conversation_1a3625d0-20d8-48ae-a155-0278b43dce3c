/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealRespiratoryRateAdditionalNote
// ====================================================

export interface ConcealRespiratoryRateAdditionalNote_concealRespiratoryRateAdditionalNote {
  __typename: "RespiratoryRateModel";
  id: string;
  concealAdditionalNote: boolean | null;
  additionalNote: string | null;
}

export interface ConcealRespiratoryRateAdditionalNote {
  concealRespiratoryRateAdditionalNote: ConcealRespiratoryRateAdditionalNote_concealRespiratoryRateAdditionalNote;
}

export interface ConcealRespiratoryRateAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
  clinifyId: string;
}
