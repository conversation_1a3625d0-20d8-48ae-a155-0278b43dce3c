/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealOncologyConsultationAudiometry
// ====================================================

export interface ConcealOncologyConsultationAudiometry_concealOncologyConsultationAudiometry {
  __typename: "OncologyConsultationHistoryModel";
  id: string;
  concealAudiometry: boolean | null;
  audiometry: string | null;
}

export interface ConcealOncologyConsultationAudiometry {
  concealOncologyConsultationAudiometry: ConcealOncologyConsultationAudiometry_concealOncologyConsultationAudiometry;
}

export interface ConcealOncologyConsultationAudiometryVariables {
  id: string;
  concealStatus: boolean;
}
