/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { CasesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetGenderBasedViolenceCases
// ====================================================

export interface GetGenderBasedViolenceCases_getGenderBasedViolenceCases_ageRanges {
  __typename: "CategoryCount";
  category: string | null;
  count: number | null;
}

export interface GetGenderBasedViolenceCases_getGenderBasedViolenceCases {
  __typename: "CasesSummary";
  name: number | null;
  totalFemale: number | null;
  totalMale: number | null;
  ageRanges: GetGenderBasedViolenceCases_getGenderBasedViolenceCases_ageRanges[] | null;
}

export interface GetGenderBasedViolenceCases {
  getGenderBasedViolenceCases: GetGenderBasedViolenceCases_getGenderBasedViolenceCases[];
}

export interface GetGenderBasedViolenceCasesVariables {
  filter?: CasesAnalyticsFilter | null;
}
