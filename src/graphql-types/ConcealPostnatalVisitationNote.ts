/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealPostnatalVisitationNote
// ====================================================

export interface ConcealPostnatalVisitationNote_concealPostnatalVisitationNote {
  __typename: "PostnatalModel";
  id: string;
  concealVisitationNote: boolean | null;
  visitationNote: string | null;
}

export interface ConcealPostnatalVisitationNote {
  concealPostnatalVisitationNote: ConcealPostnatalVisitationNote_concealPostnatalVisitationNote;
}

export interface ConcealPostnatalVisitationNoteVariables {
  id: string;
  concealStatus: boolean;
}
