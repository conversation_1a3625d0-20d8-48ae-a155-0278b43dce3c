/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealCancerScreeningAdditionalNote
// ====================================================

export interface ConcealCancerScreeningAdditionalNote_concealCancerScreeningAdditionalNote {
  __typename: "CancerScreeningModel";
  id: string;
  additionalNote: string | null;
  concealAdditionalNote: boolean;
}

export interface ConcealCancerScreeningAdditionalNote {
  concealCancerScreeningAdditionalNote: ConcealCancerScreeningAdditionalNote_concealCancerScreeningAdditionalNote;
}

export interface ConcealCancerScreeningAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
}
