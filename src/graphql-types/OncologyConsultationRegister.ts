/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL fragment: OncologyConsultationRegister
// ====================================================

export interface OncologyConsultationRegister_treatmentChart_periods_cycles {
  __typename: "OncologyCycle";
  cycleNo: number;
  value: any | null;
}

export interface OncologyConsultationRegister_treatmentChart_periods {
  __typename: "OncologyPeriod";
  day: number;
  cycles: OncologyConsultationRegister_treatmentChart_periods_cycles[];
}

export interface OncologyConsultationRegister_treatmentChart {
  __typename: "OncologyChart";
  noOfCycles: number;
  periods: OncologyConsultationRegister_treatmentChart_periods[];
}

export interface OncologyConsultationRegister_therapyChart_periods_cycles {
  __typename: "OncologyCycle";
  cycleNo: number;
  value: any | null;
}

export interface OncologyConsultationRegister_therapyChart_periods {
  __typename: "OncologyPeriod";
  day: number;
  cycles: OncologyConsultationRegister_therapyChart_periods_cycles[];
}

export interface OncologyConsultationRegister_therapyChart {
  __typename: "OncologyChart";
  noOfCycles: number;
  periods: OncologyConsultationRegister_therapyChart_periods[];
}

export interface OncologyConsultationRegister {
  __typename: "OncologyConsultationRegisterModel";
  id: string;
  creatorName: string | null;
  lastModifierName: string | null;
  treatmentChart: OncologyConsultationRegister_treatmentChart | null;
  therapyChart: OncologyConsultationRegister_therapyChart | null;
  createdDate: any;
  updatedDate: any;
}
