/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Gender } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: VirtualCareAppointmentUnarchivedSubs
// ====================================================

export interface VirtualCareAppointmentUnarchivedSubs_VirtualCareAppointmentArchived_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface VirtualCareAppointmentUnarchivedSubs_VirtualCareAppointmentArchived_specialists_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  speciality: string | null;
}

export interface VirtualCareAppointmentUnarchivedSubs_VirtualCareAppointmentArchived_specialists {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
  personalInformation: VirtualCareAppointmentUnarchivedSubs_VirtualCareAppointmentArchived_specialists_personalInformation | null;
}

export interface VirtualCareAppointmentUnarchivedSubs_VirtualCareAppointmentArchived_patientProfile_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
  dateOfBirth: any | null;
  displayPictureUrl: string | null;
  bloodGroup: string | null;
}

export interface VirtualCareAppointmentUnarchivedSubs_VirtualCareAppointmentArchived_patientProfile_user {
  __typename: "UserModel";
  id: string;
  nonCorporateEmail: string | null;
  phoneNumber: string | null;
  country: string | null;
}

export interface VirtualCareAppointmentUnarchivedSubs_VirtualCareAppointmentArchived_patientProfile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  gender: Gender | null;
  clinifyId: string;
  personalInformation: VirtualCareAppointmentUnarchivedSubs_VirtualCareAppointmentArchived_patientProfile_personalInformation | null;
  user: VirtualCareAppointmentUnarchivedSubs_VirtualCareAppointmentArchived_patientProfile_user;
}

export interface VirtualCareAppointmentUnarchivedSubs_VirtualCareAppointmentArchived {
  __typename: "VirtualCareAppointmentModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  liveSessionUrl: string | null;
  category: string | null;
  appointmentDate: any;
  startDateTime: any | null;
  endDateTime: any | null;
  priority: string | null;
  duration: string | null;
  patientType: string | null;
  paymentType: string | null;
  hospital: VirtualCareAppointmentUnarchivedSubs_VirtualCareAppointmentArchived_hospital | null;
  specialists: VirtualCareAppointmentUnarchivedSubs_VirtualCareAppointmentArchived_specialists[] | null;
  patientProfile: VirtualCareAppointmentUnarchivedSubs_VirtualCareAppointmentArchived_patientProfile | null;
}

export interface VirtualCareAppointmentUnarchivedSubs {
  VirtualCareAppointmentArchived: VirtualCareAppointmentUnarchivedSubs_VirtualCareAppointmentArchived;
}

export interface VirtualCareAppointmentUnarchivedSubsVariables {
  hospitalId?: string | null;
  profileId: string;
}
