/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealLabourAndDeliveryFirstStageAdditionalNote
// ====================================================

export interface ConcealLabourAndDeliveryFirstStageAdditionalNote_concealLabourAndDeliveryFirstStageAdditionalNote {
  __typename: "LabourDeliveryModel";
  id: string;
  concealFirstStageAdditionalNote: boolean | null;
  firstStageAdditionalNote: string | null;
}

export interface ConcealLabourAndDeliveryFirstStageAdditionalNote {
  concealLabourAndDeliveryFirstStageAdditionalNote: ConcealLabourAndDeliveryFirstStageAdditionalNote_concealLabourAndDeliveryFirstStageAdditionalNote;
}

export interface ConcealLabourAndDeliveryFirstStageAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
}
