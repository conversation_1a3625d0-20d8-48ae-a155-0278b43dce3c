/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { GrantPatientAccessPermissionInput, PatientAccessType } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: GrantPatientAccessPermission
// ====================================================

export interface GrantPatientAccessPermission_grantPatientAccessPermission_dataAccess {
  __typename: "GrantFacilityPatientAccess";
  /**
   * The facility ID to grant/revoke access
   */
  facilityId: string;
  /**
   * Staff IDs to grant/revoke access
   */
  staffIds: string[];
}

export interface GrantPatientAccessPermission_grantPatientAccessPermission {
  __typename: "GrantPatientAccessResult";
  /**
   * The patient data access type
   */
  patientAccessType: PatientAccessType;
  /**
   * The patient ID
   */
  patientId: string;
  /**
   * Data access object
   */
  dataAccess: GrantPatientAccessPermission_grantPatientAccessPermission_dataAccess[] | null;
}

export interface GrantPatientAccessPermission {
  grantPatientAccessPermission: GrantPatientAccessPermission_grantPatientAccessPermission;
}

export interface GrantPatientAccessPermissionVariables {
  input: GrantPatientAccessPermissionInput;
}
