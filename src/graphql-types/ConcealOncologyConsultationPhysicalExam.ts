/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealOncologyConsultationPhysicalExam
// ====================================================

export interface ConcealOncologyConsultationPhysicalExam_concealOncologyConsultationPhysicalExam {
  __typename: "OncologyConsultationHistoryModel";
  id: string;
  concealPhysicalExam: boolean | null;
  physicalExam: string | null;
}

export interface ConcealOncologyConsultationPhysicalExam {
  concealOncologyConsultationPhysicalExam: ConcealOncologyConsultationPhysicalExam_concealOncologyConsultationPhysicalExam;
}

export interface ConcealOncologyConsultationPhysicalExamVariables {
  id: string;
  concealStatus: boolean;
}
