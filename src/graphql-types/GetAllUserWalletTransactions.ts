/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { WalletTransactionFilter, TransactionType, TransactionStatus, Currency } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetAllUserWalletTransactions
// ====================================================

export interface GetAllUserWalletTransactions_getUserWalletTransactions_list_senderWallet_profile {
  __typename: "ProfileModel";
  clinifyId: string;
  fullName: string;
}

export interface GetAllUserWalletTransactions_getUserWalletTransactions_list_senderWallet {
  __typename: "WalletModel";
  profile: GetAllUserWalletTransactions_getUserWalletTransactions_list_senderWallet_profile | null;
}

export interface GetAllUserWalletTransactions_getUserWalletTransactions_list_receiverWallet_profile {
  __typename: "ProfileModel";
  clinifyId: string;
  fullName: string;
}

export interface GetAllUserWalletTransactions_getUserWalletTransactions_list_receiverWallet {
  __typename: "WalletModel";
  profile: GetAllUserWalletTransactions_getUserWalletTransactions_list_receiverWallet_profile | null;
}

export interface GetAllUserWalletTransactions_getUserWalletTransactions_list {
  __typename: "WalletTransactionModel";
  id: string;
  createdDate: any;
  amount: number;
  transactionType: TransactionType;
  transactionStatus: TransactionStatus;
  transactionDetails: string;
  currency: Currency;
  amountSent: number;
  description: string;
  senderWallet: GetAllUserWalletTransactions_getUserWalletTransactions_list_senderWallet | null;
  receiverWallet: GetAllUserWalletTransactions_getUserWalletTransactions_list_receiverWallet | null;
}

export interface GetAllUserWalletTransactions_getUserWalletTransactions {
  __typename: "WalletTransactionResponse";
  totalCount: number;
  list: GetAllUserWalletTransactions_getUserWalletTransactions_list[];
}

export interface GetAllUserWalletTransactions {
  getUserWalletTransactions: GetAllUserWalletTransactions_getUserWalletTransactions;
}

export interface GetAllUserWalletTransactionsVariables {
  filterOptions?: WalletTransactionFilter | null;
}
