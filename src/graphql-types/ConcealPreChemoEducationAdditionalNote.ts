/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealPreChemoEducationAdditionalNote
// ====================================================

export interface ConcealPreChemoEducationAdditionalNote_concealPreChemoEducationAdditionalNote {
  __typename: "PreChemoEducationModel";
  id: string;
  additionalNote: string | null;
  concealAdditionalNote: boolean;
}

export interface ConcealPreChemoEducationAdditionalNote {
  concealPreChemoEducationAdditionalNote: ConcealPreChemoEducationAdditionalNote_concealPreChemoEducationAdditionalNote;
}

export interface ConcealPreChemoEducationAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
}
