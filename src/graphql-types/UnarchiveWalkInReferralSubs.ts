/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: UnarchiveWalkInReferralSubs
// ====================================================

export interface UnarchiveWalkInReferralSubs_WalkInReferralUnarchived_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface UnarchiveWalkInReferralSubs_WalkInReferralUnarchived {
  __typename: "WalkInReferralModel";
  id: string;
  referralDateTime: any | null;
  referredBy: string | null;
  referralReason: string | null;
  concealReferralReason: boolean | null;
  referralFacilityName: string | null;
  referralFacilityAddress: string | null;
  documentUrl: string[] | null;
  patientInformation: UnarchiveWalkInReferralSubs_WalkInReferralUnarchived_patientInformation | null;
}

export interface UnarchiveWalkInReferralSubs {
  WalkInReferralUnarchived: UnarchiveWalkInReferralSubs_WalkInReferralUnarchived[];
}

export interface UnarchiveWalkInReferralSubsVariables {
  hospitalId: string;
}
