/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: UpdateEnrolleeCapitationAmountPerPlan
// ====================================================

export interface UpdateEnrolleeCapitationAmountPerPlan_updateEnrolleeCapitationAmountPerPlan {
  __typename: "FacilityPreferenceModel";
  id: string;
  enrolleeCapitationAmountPerPlan: boolean | null;
}

export interface UpdateEnrolleeCapitationAmountPerPlan {
  updateEnrolleeCapitationAmountPerPlan: UpdateEnrolleeCapitationAmountPerPlan_updateEnrolleeCapitationAmountPerPlan;
}

export interface UpdateEnrolleeCapitationAmountPerPlanVariables {
  enabled: boolean;
}
