/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { AdmissionAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetLengthOfStayByDepartment
// ====================================================

export interface GetLengthOfStayByDepartment_getLengthOfStayByDepartment {
  __typename: "CategoryDataResponse";
  category: string | null;
  count: number | null;
  name: number | null;
  averageLengthOfStay: number | null;
}

export interface GetLengthOfStayByDepartment {
  getLengthOfStayByDepartment: GetLengthOfStayByDepartment_getLengthOfStayByDepartment[];
}

export interface GetLengthOfStayByDepartmentVariables {
  filter?: AdmissionAnalyticsFilter | null;
}
