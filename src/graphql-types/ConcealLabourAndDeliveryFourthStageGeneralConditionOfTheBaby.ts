/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealLabourAndDeliveryFourthStageGeneralConditionOfTheBaby
// ====================================================

export interface ConcealLabourAndDeliveryFourthStageGeneralConditionOfTheBaby_concealLabourAndDeliveryFourthStageGeneralConditionOfTheBaby {
  __typename: "LabourDeliveryModel";
  id: string;
  concealFourthStageGeneralConditionOfTheBaby: boolean | null;
  fourthStageGeneralConditionOfTheBaby: string | null;
}

export interface ConcealLabourAndDeliveryFourthStageGeneralConditionOfTheBaby {
  concealLabourAndDeliveryFourthStageGeneralConditionOfTheBaby: ConcealLabourAndDeliveryFourthStageGeneralConditionOfTheBaby_concealLabourAndDeliveryFourthStageGeneralConditionOfTheBaby;
}

export interface ConcealLabourAndDeliveryFourthStageGeneralConditionOfTheBabyVariables {
  id: string;
  concealStatus: boolean;
}
