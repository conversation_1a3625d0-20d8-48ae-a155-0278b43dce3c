/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: UtilizationReferralUpdatedSubs
// ====================================================

export interface UtilizationReferralUpdatedSubs_UtilizationReferralUpdated_statusHistory {
  __typename: "ReferalStatusHistoryType";
  status: string;
  creatorName: string;
  createdDate: any;
}

export interface UtilizationReferralUpdatedSubs_UtilizationReferralUpdated {
  __typename: "PreAuthReferralUtilisationsModel";
  id: string;
  category: string | null;
  type: string | null;
  quantity: string | null;
  /**
   * Value is in Naira
   */
  price: string | null;
  status: string | null;
  paCode: string | null;
  utilizationCode: string | null;
  utilizationId: string | null;
  rhesusFactor: string | null;
  specialty: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusHistory: UtilizationReferralUpdatedSubs_UtilizationReferralUpdated_statusHistory[] | null;
}

export interface UtilizationReferralUpdatedSubs {
  UtilizationReferralUpdated: UtilizationReferralUpdatedSubs_UtilizationReferralUpdated;
}

export interface UtilizationReferralUpdatedSubsVariables {
  profileId: string;
  hospitalId: string;
  hmoProviderId?: string | null;
}
