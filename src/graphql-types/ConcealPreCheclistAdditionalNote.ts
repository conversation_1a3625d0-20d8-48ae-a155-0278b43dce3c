/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealPreCheclistAdditionalNote
// ====================================================

export interface ConcealPreCheclistAdditionalNote_concealPreCheclistAdditionalNote {
  __typename: "PreOperationChecklistModel";
  id: string;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
}

export interface ConcealPreCheclistAdditionalNote {
  concealPreCheclistAdditionalNote: ConcealPreCheclistAdditionalNote_concealPreCheclistAdditionalNote;
}

export interface ConcealPreCheclistAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
}
