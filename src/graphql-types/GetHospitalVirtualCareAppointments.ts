/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { VirtualCareAppointmentFilterInput, Gender } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHospitalVirtualCareAppointments
// ====================================================

export interface GetHospitalVirtualCareAppointments_hospital_virtualCareAppointments_list_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface GetHospitalVirtualCareAppointments_hospital_virtualCareAppointments_list_specialists_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  speciality: string | null;
}

export interface GetHospitalVirtualCareAppointments_hospital_virtualCareAppointments_list_specialists {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
  personalInformation: GetHospitalVirtualCareAppointments_hospital_virtualCareAppointments_list_specialists_personalInformation | null;
}

export interface GetHospitalVirtualCareAppointments_hospital_virtualCareAppointments_list_patientProfile_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
  dateOfBirth: any | null;
  displayPictureUrl: string | null;
  bloodGroup: string | null;
}

export interface GetHospitalVirtualCareAppointments_hospital_virtualCareAppointments_list_patientProfile_user {
  __typename: "UserModel";
  id: string;
  nonCorporateEmail: string | null;
  phoneNumber: string | null;
  country: string | null;
}

export interface GetHospitalVirtualCareAppointments_hospital_virtualCareAppointments_list_patientProfile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  gender: Gender | null;
  clinifyId: string;
  personalInformation: GetHospitalVirtualCareAppointments_hospital_virtualCareAppointments_list_patientProfile_personalInformation | null;
  user: GetHospitalVirtualCareAppointments_hospital_virtualCareAppointments_list_patientProfile_user;
}

export interface GetHospitalVirtualCareAppointments_hospital_virtualCareAppointments_list {
  __typename: "VirtualCareAppointmentModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  liveSessionUrl: string | null;
  category: string | null;
  appointmentDate: any;
  startDateTime: any | null;
  endDateTime: any | null;
  priority: string | null;
  duration: string | null;
  patientType: string | null;
  paymentType: string | null;
  hospital: GetHospitalVirtualCareAppointments_hospital_virtualCareAppointments_list_hospital | null;
  specialists: GetHospitalVirtualCareAppointments_hospital_virtualCareAppointments_list_specialists[] | null;
  patientProfile: GetHospitalVirtualCareAppointments_hospital_virtualCareAppointments_list_patientProfile | null;
}

export interface GetHospitalVirtualCareAppointments_hospital_virtualCareAppointments {
  __typename: "VirtualCareAppointmentListResponse";
  totalCount: number;
  list: GetHospitalVirtualCareAppointments_hospital_virtualCareAppointments_list[];
}

export interface GetHospitalVirtualCareAppointments_hospital {
  __typename: "HospitalModel";
  id: string;
  virtualCareAppointments: GetHospitalVirtualCareAppointments_hospital_virtualCareAppointments | null;
}

export interface GetHospitalVirtualCareAppointments {
  hospital: GetHospitalVirtualCareAppointments_hospital;
}

export interface GetHospitalVirtualCareAppointmentsVariables {
  filterOptions: VirtualCareAppointmentFilterInput;
  hospitalId?: string | null;
}
