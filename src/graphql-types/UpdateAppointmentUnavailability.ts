/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ProfileUnavailableDatesInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateAppointmentUnavailability
// ====================================================

export interface UpdateAppointmentUnavailability_updateAppointmentUnavailability_list {
  __typename: "unavailableDates";
  startDate: any;
  endDate: any | null;
  isAllDay: boolean;
  reason: string | null;
}

export interface UpdateAppointmentUnavailability_updateAppointmentUnavailability {
  __typename: "ProfileUnavailableDates";
  id: string;
  list: UpdateAppointmentUnavailability_updateAppointmentUnavailability_list[] | null;
}

export interface UpdateAppointmentUnavailability {
  updateAppointmentUnavailability: UpdateAppointmentUnavailability_updateAppointmentUnavailability;
}

export interface UpdateAppointmentUnavailabilityVariables {
  input?: ProfileUnavailableDatesInput[] | null;
  profileId?: string | null;
}
