/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealNursingServicesProgressNotes
// ====================================================

export interface ConcealNursingServicesProgressNotes_concealNursingServicesProgressNote {
  __typename: "NursingServiceProgressNoteModel";
  id: string;
  note: string;
  conceal: boolean | null;
  nursingServiceId: string | null;
}

export interface ConcealNursingServicesProgressNotes {
  concealNursingServicesProgressNote: ConcealNursingServicesProgressNotes_concealNursingServicesProgressNote;
}

export interface ConcealNursingServicesProgressNotesVariables {
  id: string;
  concealStatus: boolean;
}
