/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealOncologyConsultationChemoNote
// ====================================================

export interface ConcealOncologyConsultationChemoNote_concealOncologyConsultationChemoNote {
  __typename: "OncologyConsultationHistoryModel";
  id: string;
  concealChemoNote: boolean | null;
  chemoNote: string | null;
}

export interface ConcealOncologyConsultationChemoNote {
  concealOncologyConsultationChemoNote: ConcealOncologyConsultationChemoNote_concealOncologyConsultationChemoNote;
}

export interface ConcealOncologyConsultationChemoNoteVariables {
  id: string;
  concealStatus: boolean;
}
