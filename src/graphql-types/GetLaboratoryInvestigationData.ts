/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ServicesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetLaboratoryInvestigationData
// ====================================================

export interface GetLaboratoryInvestigationData_getLaboratoryInvestigationData_servicesSummary {
  __typename: "ServicesSummary";
  totalLaboratory: number | null;
  name: number | null;
}

export interface GetLaboratoryInvestigationData_getLaboratoryInvestigationData_list_testInfos_male {
  __typename: "ListByPatientName";
  name: string;
  count: number | null;
  quantity: string | null;
  totalAmount: number | null;
}

export interface GetLaboratoryInvestigationData_getLaboratoryInvestigationData_list_testInfos_female {
  __typename: "ListByPatientName";
  name: string;
  count: number | null;
  quantity: string | null;
  totalAmount: number | null;
}

export interface GetLaboratoryInvestigationData_getLaboratoryInvestigationData_list_testInfos {
  __typename: "InvestigationTestInfoInputByGender";
  male: GetLaboratoryInvestigationData_getLaboratoryInvestigationData_list_testInfos_male[] | null;
  female: GetLaboratoryInvestigationData_getLaboratoryInvestigationData_list_testInfos_female[] | null;
}

export interface GetLaboratoryInvestigationData_getLaboratoryInvestigationData_list_byLabTestDate {
  __typename: "ListByLabTestDate";
  testDate: string | null;
  testVerifiedBy: string | null;
  investigationNames: string[] | null;
}

export interface GetLaboratoryInvestigationData_getLaboratoryInvestigationData_list_byPatientName {
  __typename: "ListByPatientName";
  patientName: string | null;
  testDate: string | null;
  paymentType: string | null;
  performedBy: string | null;
  totalAmount: number | null;
  clinifyId: string | null;
  quantity: string | null;
  name: string;
}

export interface GetLaboratoryInvestigationData_getLaboratoryInvestigationData_list {
  __typename: "ServiceSummaryList";
  testInfos: GetLaboratoryInvestigationData_getLaboratoryInvestigationData_list_testInfos | null;
  byLabTestDate: GetLaboratoryInvestigationData_getLaboratoryInvestigationData_list_byLabTestDate[] | null;
  byPatientName: GetLaboratoryInvestigationData_getLaboratoryInvestigationData_list_byPatientName[] | null;
}

export interface GetLaboratoryInvestigationData_getLaboratoryInvestigationData {
  __typename: "ServiceSummaryWithList";
  servicesSummary: GetLaboratoryInvestigationData_getLaboratoryInvestigationData_servicesSummary[];
  list: GetLaboratoryInvestigationData_getLaboratoryInvestigationData_list;
}

export interface GetLaboratoryInvestigationData {
  getLaboratoryInvestigationData: GetLaboratoryInvestigationData_getLaboratoryInvestigationData;
}

export interface GetLaboratoryInvestigationDataVariables {
  filter?: ServicesAnalyticsFilter | null;
}
