/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { GrantOrganizationPermissionInput, Subject, Action } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: GrantOrganizationPermissionForFacility
// ====================================================

export interface GrantOrganizationPermissionForFacility_grantOrganizationPermissionForFacility_rules {
  __typename: "PermissionModelInput";
  subject: Subject;
  action: Action;
}

export interface GrantOrganizationPermissionForFacility_grantOrganizationPermissionForFacility {
  __typename: "PermissionModel";
  id: string;
  rules: GrantOrganizationPermissionForFacility_grantOrganizationPermissionForFacility_rules[] | null;
}

export interface GrantOrganizationPermissionForFacility {
  grantOrganizationPermissionForFacility: GrantOrganizationPermissionForFacility_grantOrganizationPermissionForFacility;
}

export interface GrantOrganizationPermissionForFacilityVariables {
  input: GrantOrganizationPermissionInput;
  hospitalId: string;
}
