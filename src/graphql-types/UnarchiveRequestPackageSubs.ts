/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: UnarchiveRequestPackageSubs
// ====================================================

export interface UnarchiveRequestPackageSubs_RequestPackageUnarchived_serviceDetails {
  __typename: "ServiceDetailsFields";
  serviceType: string | null;
  serviceName: string | null;
}

export interface UnarchiveRequestPackageSubs_RequestPackageUnarchived_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface UnarchiveRequestPackageSubs_RequestPackageUnarchived {
  __typename: "RequestPackageModel";
  id: string;
  requestDate: any;
  packageName: string;
  priority: string | null;
  category: string | null;
  orderedBy: string | null;
  specialty: string | null;
  rank: string | null;
  department: string | null;
  price: string;
  paymentType: string | null;
  patientType: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  additionalNote: string | null;
  serviceDetails: UnarchiveRequestPackageSubs_RequestPackageUnarchived_serviceDetails[] | null;
  createdDate: any;
  updatedDate: any;
  profileId: string | null;
  profile: UnarchiveRequestPackageSubs_RequestPackageUnarchived_profile | null;
}

export interface UnarchiveRequestPackageSubs {
  RequestPackageUnarchived: UnarchiveRequestPackageSubs_RequestPackageUnarchived[];
}

export interface UnarchiveRequestPackageSubsVariables {
  profileId: string;
}
