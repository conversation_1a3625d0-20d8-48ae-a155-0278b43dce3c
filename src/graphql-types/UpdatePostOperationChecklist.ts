/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PostOperationChecklistInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdatePostOperationChecklist
// ====================================================

export interface UpdatePostOperationChecklist_updatePostOperationChecklist_dietOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface UpdatePostOperationChecklist_updatePostOperationChecklist_ambutationOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface UpdatePostOperationChecklist_updatePostOperationChecklist_urethralCatheterizationOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface UpdatePostOperationChecklist_updatePostOperationChecklist_fluidTherapyOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface UpdatePostOperationChecklist_updatePostOperationChecklist_antiBioticsOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface UpdatePostOperationChecklist_updatePostOperationChecklist_analgesicOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
  title: string | null;
  names: string[] | null;
  isMultiple: boolean | null;
}

export interface UpdatePostOperationChecklist_updatePostOperationChecklist_clexane40mgFor3DaysOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface UpdatePostOperationChecklist_updatePostOperationChecklist_vitalSigns {
  __typename: "VitalSignType";
  timeIn: string | null;
  systolic: string | null;
  diastolic: string | null;
  pulseRate: string | null;
  oxygenSaturation: string | null;
  respiratoryRate: string | null;
  temperature: string | null;
  temperatureUnit: string | null;
  nausea: string | null;
  painScore: string | null;
  state: string | null;
}

export interface UpdatePostOperationChecklist_updatePostOperationChecklist_medications {
  __typename: "MedicationDetailType";
  medicationName: string | null;
  prescriptionTime: any | null;
  dosage: string | null;
  dosageUnit: string | null;
}

export interface UpdatePostOperationChecklist_updatePostOperationChecklist_urineOutput {
  __typename: "UrineOutputDetailType";
  timeIn: string | null;
  output: string | null;
}

export interface UpdatePostOperationChecklist_updatePostOperationChecklist_linesDrainsTubes {
  __typename: "LinesDrainsTubesDetailType";
  intravenousInfusions: string | null;
  others: string | null;
  dressingChecked: string | null;
  dressingCheckedTime: any | null;
  drainsChecked: string | null;
  drainsCheckedTime: any | null;
  catheterChecked: string | null;
  catheterCheckedTime: any | null;
  quantityDrained: string | null;
}

export interface UpdatePostOperationChecklist_updatePostOperationChecklist_newOrders {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface UpdatePostOperationChecklist_updatePostOperationChecklist_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface UpdatePostOperationChecklist_updatePostOperationChecklist {
  __typename: "PostOperationModel";
  id: string;
  serviceStartDateTime: any | null;
  chartType: string | null;
  surgeryStartDateTime: any | null;
  surgeryEndDateTime: any | null;
  dietOrderSheet: UpdatePostOperationChecklist_updatePostOperationChecklist_dietOrderSheet[] | null;
  ambutationOrderSheet: UpdatePostOperationChecklist_updatePostOperationChecklist_ambutationOrderSheet[] | null;
  urethralCatheterizationOrderSheet: UpdatePostOperationChecklist_updatePostOperationChecklist_urethralCatheterizationOrderSheet[] | null;
  fluidTherapyOrderSheet: UpdatePostOperationChecklist_updatePostOperationChecklist_fluidTherapyOrderSheet[] | null;
  antiBioticsOrderSheet: UpdatePostOperationChecklist_updatePostOperationChecklist_antiBioticsOrderSheet[] | null;
  analgesicOrderSheet: UpdatePostOperationChecklist_updatePostOperationChecklist_analgesicOrderSheet[] | null;
  clexane40mgFor3DaysOrderSheet: UpdatePostOperationChecklist_updatePostOperationChecklist_clexane40mgFor3DaysOrderSheet[] | null;
  vitalSigns: UpdatePostOperationChecklist_updatePostOperationChecklist_vitalSigns[] | null;
  medications: UpdatePostOperationChecklist_updatePostOperationChecklist_medications[] | null;
  discontinueMedication: boolean;
  urineOutput: UpdatePostOperationChecklist_updatePostOperationChecklist_urineOutput[] | null;
  linesDrainsTubes: UpdatePostOperationChecklist_updatePostOperationChecklist_linesDrainsTubes[] | null;
  newOrders: UpdatePostOperationChecklist_updatePostOperationChecklist_newOrders[] | null;
  SurgeonName: string | null;
  surgeonNameSignature: string | null;
  surgeonNameSignatureType: string | null;
  surgeonNameSignatureDateTime: any | null;
  SurgeonSpecialty: string | null;
  SurgeonAssistantName: string | null;
  surgeonAssistantNameSignature: string | null;
  surgeonAssistantNameSignatureType: string | null;
  surgeonAssistantNameSignatureDateTime: any | null;
  operatingRoomNurse: string | null;
  operatingRoomNurseSignature: string | null;
  operatingRoomNurseSignatureType: string | null;
  operatingRoomNurseSignatureDateTime: any | null;
  anesthetistName: string | null;
  anesthetistNameSignature: string | null;
  anesthetistNameSignatureType: string | null;
  anesthetistNameSignatureDateTime: any | null;
  recoveryNurse: string | null;
  recoveryNurseSignature: string | null;
  recoveryNurseSignatureType: string | null;
  recoveryNurseSignatureDateTime: any | null;
  visitingSpecialistName: string | null;
  visitingSpecialistSignature: string | null;
  visitingSpecialistSignatureType: string | null;
  visitingSpecialistSignatureDateTime: any | null;
  visitingFacilityName: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean;
  profileId: string | null;
  profile: UpdatePostOperationChecklist_updatePostOperationChecklist_profile | null;
}

export interface UpdatePostOperationChecklist {
  updatePostOperationChecklist: UpdatePostOperationChecklist_updatePostOperationChecklist;
}

export interface UpdatePostOperationChecklistVariables {
  id: string;
  input: PostOperationChecklistInput;
  pin?: string | null;
}
