/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NursingServiceProgressNoteInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: SaveNursingServicesProgressNotes
// ====================================================

export interface SaveNursingServicesProgressNotes_saveNursingServicesProgressNote {
  __typename: "NursingServiceProgressNoteModel";
  id: string;
  note: string;
  conceal: boolean | null;
  nursingServiceId: string | null;
}

export interface SaveNursingServicesProgressNotes {
  saveNursingServicesProgressNote: SaveNursingServicesProgressNotes_saveNursingServicesProgressNote;
}

export interface SaveNursingServicesProgressNotesVariables {
  input: NursingServiceProgressNoteInput;
  id: string;
  clinifyId: string;
  pin?: string | null;
}
