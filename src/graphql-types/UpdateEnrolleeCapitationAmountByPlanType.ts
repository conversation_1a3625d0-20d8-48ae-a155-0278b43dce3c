/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: UpdateEnrolleeCapitationAmountByPlanType
// ====================================================

export interface UpdateEnrolleeCapitationAmountByPlanType_updateEnrolleeCapitationAmountByPlanType_enrolleeCapitionAmountByPlanType {
  __typename: "CapitionAmountByPlanType";
  planTypeId: string | null;
  planTypeName: string | null;
  amount: number | null;
}

export interface UpdateEnrolleeCapitationAmountByPlanType_updateEnrolleeCapitationAmountByPlanType {
  __typename: "FacilityPreferenceModel";
  id: string;
  enrolleeCapitionAmountByPlanType: UpdateEnrolleeCapitationAmountByPlanType_updateEnrolleeCapitationAmountByPlanType_enrolleeCapitionAmountByPlanType[] | null;
}

export interface UpdateEnrolleeCapitationAmountByPlanType {
  updateEnrolleeCapitationAmountByPlanType: UpdateEnrolleeCapitationAmountByPlanType_updateEnrolleeCapitationAmountByPlanType;
}

export interface UpdateEnrolleeCapitationAmountByPlanTypeVariables {
  planTypeId: string;
  planTypeName: string;
  capitationAmount: number;
}
