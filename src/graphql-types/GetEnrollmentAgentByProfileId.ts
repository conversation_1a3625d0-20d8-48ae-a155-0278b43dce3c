/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetEnrollmentAgentByProfileId
// ====================================================

export interface GetEnrollmentAgentByProfileId_enrollmentAgentByProfileId_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface GetEnrollmentAgentByProfileId_enrollmentAgentByProfileId_agency {
  __typename: "EnrollmentAgencyModel";
  id: string;
  name: string | null;
  agencyType: string | null;
  isTpa: boolean | null;
}

export interface GetEnrollmentAgentByProfileId_enrollmentAgentByProfileId_tpaNonTpa {
  __typename: "EnrollmentTpaNonTpaModel";
  id: string;
  name: string | null;
  isTpa: boolean | null;
}

export interface GetEnrollmentAgentByProfileId_enrollmentAgentByProfileId {
  __typename: "EnrollmentAgentModel";
  id: string;
  profile: GetEnrollmentAgentByProfileId_enrollmentAgentByProfileId_profile | null;
  agency: GetEnrollmentAgentByProfileId_enrollmentAgentByProfileId_agency | null;
  tpaNonTpa: GetEnrollmentAgentByProfileId_enrollmentAgentByProfileId_tpaNonTpa | null;
  accountNumber: string | null;
  accountName: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface GetEnrollmentAgentByProfileId {
  enrollmentAgentByProfileId: GetEnrollmentAgentByProfileId_enrollmentAgentByProfileId;
}

export interface GetEnrollmentAgentByProfileIdVariables {
  profileId: string;
}
