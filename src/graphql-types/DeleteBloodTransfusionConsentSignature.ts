/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: DeleteBloodTransfusionConsentSignature
// ====================================================

export interface DeleteBloodTransfusionConsentSignature_deleteBloodTransfusionConsentSignature {
  __typename: "BloodTransfusionModel";
  id: string;
  transfusionDateTime: any | null;
  transfusionOrderGiven: string;
  transfusionDoctor: string | null;
  transfusionNurse: string | null;
  patientBloodGroup: string | null;
  patientGenoType: string | null;
  crossMatchingTime: string | null;
  bloodLabel: string | null;
  bloodProduct: string | null;
  expiryDate: string | null;
  donorBloodType: string | null;
  bloodPint: string | null;
  lengthOfTransfusion: string | null;
  transfusionStartDateTime: any | null;
  transfusionEndDateTime: any | null;
  adverseReaction: string | null;
  reaction: string | null;
  transfusionNote: string | null;
  patientConsent: string | null;
  consentReason: string | null;
  bloodSource: string | null;
  bloodDonorStatus: string | null;
  concealTransfusionNote: boolean | null;
  postTransfusionFBC: string | null;
  concealPostTransfusionFBC: boolean | null;
  diuretic: string | null;
  diureticType: string | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
}

export interface DeleteBloodTransfusionConsentSignature {
  deleteBloodTransfusionConsentSignature: DeleteBloodTransfusionConsentSignature_deleteBloodTransfusionConsentSignature;
}

export interface DeleteBloodTransfusionConsentSignatureVariables {
  id: string;
  clinifyId: string;
}
