/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: PreauthorisationReferralUpdatedSubs
// ====================================================

export interface PreauthorisationReferralUpdatedSubs_PreauthorisationReferralUpdated_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface PreauthorisationReferralUpdatedSubs_PreauthorisationReferralUpdated_diagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface PreauthorisationReferralUpdatedSubs_PreauthorisationReferralUpdated_utilizations_statusHistory {
  __typename: "ReferalStatusHistoryType";
  status: string;
  creatorName: string;
  createdDate: any;
}

export interface PreauthorisationReferralUpdatedSubs_PreauthorisationReferralUpdated_utilizations {
  __typename: "PreAuthReferralUtilisationsModel";
  id: string;
  category: string | null;
  type: string | null;
  quantity: string | null;
  /**
   * Value is in Naira
   */
  price: string | null;
  status: string | null;
  paCode: string | null;
  utilizationCode: string | null;
  utilizationId: string | null;
  rhesusFactor: string | null;
  specialty: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusHistory: PreauthorisationReferralUpdatedSubs_PreauthorisationReferralUpdated_utilizations_statusHistory[] | null;
}

export interface PreauthorisationReferralUpdatedSubs_PreauthorisationReferralUpdated_referredProvider {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
}

export interface PreauthorisationReferralUpdatedSubs_PreauthorisationReferralUpdated_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface PreauthorisationReferralUpdatedSubs_PreauthorisationReferralUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface PreauthorisationReferralUpdatedSubs_PreauthorisationReferralUpdated {
  __typename: "PreauthorisationReferralModel";
  id: string;
  requestDateTime: any;
  requestedBy: string | null;
  referredBy: string | null;
  serviceType: string;
  serviceTypeCode: string;
  provider: PreauthorisationReferralUpdatedSubs_PreauthorisationReferralUpdated_provider;
  serviceName: string | null;
  priority: string | null;
  diagnosis: PreauthorisationReferralUpdatedSubs_PreauthorisationReferralUpdated_diagnosis[] | null;
  additionalNote: string | null;
  documentUrl: string[] | null;
  code: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  rank: string | null;
  department: string | null;
  specialty: string | null;
  utilizations: PreauthorisationReferralUpdatedSubs_PreauthorisationReferralUpdated_utilizations[] | null;
  totalQuantity: string;
  grandTotal: string;
  profileId: string | null;
  referralProviderRemark: string | null;
  referredProvider: PreauthorisationReferralUpdatedSubs_PreauthorisationReferralUpdated_referredProvider | null;
  hospital: PreauthorisationReferralUpdatedSubs_PreauthorisationReferralUpdated_hospital | null;
  enrolleeNumber: string | null;
  isExternalPlanType: boolean | null;
  externalPlanTypeId: string | null;
  profile: PreauthorisationReferralUpdatedSubs_PreauthorisationReferralUpdated_profile | null;
}

export interface PreauthorisationReferralUpdatedSubs {
  PreauthorisationReferralUpdated: PreauthorisationReferralUpdatedSubs_PreauthorisationReferralUpdated;
}

export interface PreauthorisationReferralUpdatedSubsVariables {
  profileId: string;
  hospitalId: string;
  hmoProviderId?: string | null;
}
