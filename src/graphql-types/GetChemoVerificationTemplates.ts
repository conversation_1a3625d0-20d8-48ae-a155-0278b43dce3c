/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetChemoVerificationTemplates
// ====================================================

export interface GetChemoVerificationTemplates_fetchChemoDiagnosisTemplates {
  __typename: "ChemoDiagnosisTemplateModel";
  id: string;
  combinationName: string;
  type: string;
}

export interface GetChemoVerificationTemplates {
  fetchChemoDiagnosisTemplates: GetChemoVerificationTemplates_fetchChemoDiagnosisTemplates[];
}

export interface GetChemoVerificationTemplatesVariables {
  facilityPreferenceId?: string | null;
  section?: string | null;
  chemoDiagnosis?: string | null;
}
