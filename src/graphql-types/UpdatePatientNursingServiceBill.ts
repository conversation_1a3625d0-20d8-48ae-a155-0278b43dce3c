/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NursingServicesInput, BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdatePatientNursingServiceBill
// ====================================================

export interface UpdatePatientNursingServiceBill_updateNursingServiceBill_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface UpdatePatientNursingServiceBill_updateNursingServiceBill_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface UpdatePatientNursingServiceBill_updateNursingServiceBill_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: UpdatePatientNursingServiceBill_updateNursingServiceBill_profile_coverageDetails_provider | null;
}

export interface UpdatePatientNursingServiceBill_updateNursingServiceBill_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: UpdatePatientNursingServiceBill_updateNursingServiceBill_profile_coverageDetails[] | null;
}

export interface UpdatePatientNursingServiceBill_updateNursingServiceBill_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface UpdatePatientNursingServiceBill_updateNursingServiceBill_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: UpdatePatientNursingServiceBill_updateNursingServiceBill_preauthorizationDetails_provider | null;
}

export interface UpdatePatientNursingServiceBill_updateNursingServiceBill_hmoClaim {
  __typename: "HmoClaimModel";
  id: string;
}

export interface UpdatePatientNursingServiceBill_updateNursingServiceBill_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface UpdatePatientNursingServiceBill_updateNursingServiceBill_admissions {
  __typename: "AdmissionModel";
  id: string;
}

export interface UpdatePatientNursingServiceBill_updateNursingServiceBill_consultations {
  __typename: "ConsultationModel";
  id: string;
}

export interface UpdatePatientNursingServiceBill_updateNursingServiceBill_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface UpdatePatientNursingServiceBill_updateNursingServiceBill_surgeries {
  __typename: "SurgeryModel";
  id: string;
}

export interface UpdatePatientNursingServiceBill_updateNursingServiceBill_investigations {
  __typename: "InvestigationModel";
  id: string;
}

export interface UpdatePatientNursingServiceBill_updateNursingServiceBill_labTests {
  __typename: "InvestigationModel";
  id: string;
}

export interface UpdatePatientNursingServiceBill_updateNursingServiceBill_radiology {
  __typename: "InvestigationModel";
  id: string;
}

export interface UpdatePatientNursingServiceBill_updateNursingServiceBill_immunizations {
  __typename: "AdmissionModel";
  id: string;
}

export interface UpdatePatientNursingServiceBill_updateNursingServiceBill {
  __typename: "NursingServiceModel";
  id: string;
  hmoProviderId: string | null;
  serviceDetails: UpdatePatientNursingServiceBill_updateNursingServiceBill_serviceDetails[] | null;
  isPackage: boolean;
  specialty: string | null;
  department: string | null;
  nurseName: string | null;
  assistantNurseName: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  billStatus: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  hospitalId: string | null;
  profileId: string | null;
  profile: UpdatePatientNursingServiceBill_updateNursingServiceBill_profile | null;
  preauthorizationDetails: UpdatePatientNursingServiceBill_updateNursingServiceBill_preauthorizationDetails | null;
  hmoClaim: UpdatePatientNursingServiceBill_updateNursingServiceBill_hmoClaim | null;
  vitals: UpdatePatientNursingServiceBill_updateNursingServiceBill_vitals[];
  admissions: UpdatePatientNursingServiceBill_updateNursingServiceBill_admissions[];
  consultations: UpdatePatientNursingServiceBill_updateNursingServiceBill_consultations[];
  medications: UpdatePatientNursingServiceBill_updateNursingServiceBill_medications[];
  surgeries: UpdatePatientNursingServiceBill_updateNursingServiceBill_surgeries[];
  investigations: UpdatePatientNursingServiceBill_updateNursingServiceBill_investigations[];
  labTests: UpdatePatientNursingServiceBill_updateNursingServiceBill_labTests[];
  radiology: UpdatePatientNursingServiceBill_updateNursingServiceBill_radiology[];
  immunizations: UpdatePatientNursingServiceBill_updateNursingServiceBill_immunizations[];
}

export interface UpdatePatientNursingServiceBill {
  updateNursingServiceBill: UpdatePatientNursingServiceBill_updateNursingServiceBill;
}

export interface UpdatePatientNursingServiceBillVariables {
  input: NursingServicesInput;
  id: string;
}
