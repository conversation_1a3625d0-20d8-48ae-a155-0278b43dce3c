/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FinanceAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetRevenueBySpecialtySummary
// ====================================================

export interface GetRevenueBySpecialtySummary_getRevenueBySpecialtySummary {
  __typename: "FinanceDataSummaryResponse";
  totalRevenue: number | null;
  totalDiscountAmount: number | null;
  totalAmountPaid: number | null;
  totalAmount: number | null;
  totalAmountOutstanding: number | null;
  name: number | null;
  category: string;
  totalAmountDue: number | null;
}

export interface GetRevenueBySpecialtySummary {
  getRevenueBySpecialtySummary: GetRevenueBySpecialtySummary_getRevenueBySpecialtySummary[];
}

export interface GetRevenueBySpecialtySummaryVariables {
  filter: FinanceAnalyticsFilter;
}
