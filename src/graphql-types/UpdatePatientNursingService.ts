/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NursingServicesInput, BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdatePatientNursingService
// ====================================================

export interface UpdatePatientNursingService_updateNursingService_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface UpdatePatientNursingService_updateNursingService_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface UpdatePatientNursingService_updateNursingService_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: UpdatePatientNursingService_updateNursingService_profile_coverageDetails_provider | null;
}

export interface UpdatePatientNursingService_updateNursingService_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: UpdatePatientNursingService_updateNursingService_profile_coverageDetails[] | null;
}

export interface UpdatePatientNursingService_updateNursingService_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface UpdatePatientNursingService_updateNursingService_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: UpdatePatientNursingService_updateNursingService_preauthorizationDetails_provider | null;
}

export interface UpdatePatientNursingService_updateNursingService_hmoClaim {
  __typename: "HmoClaimModel";
  id: string;
}

export interface UpdatePatientNursingService_updateNursingService_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface UpdatePatientNursingService_updateNursingService_admissions {
  __typename: "AdmissionModel";
  id: string;
}

export interface UpdatePatientNursingService_updateNursingService_consultations {
  __typename: "ConsultationModel";
  id: string;
}

export interface UpdatePatientNursingService_updateNursingService_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface UpdatePatientNursingService_updateNursingService_surgeries {
  __typename: "SurgeryModel";
  id: string;
}

export interface UpdatePatientNursingService_updateNursingService_investigations {
  __typename: "InvestigationModel";
  id: string;
}

export interface UpdatePatientNursingService_updateNursingService_labTests {
  __typename: "InvestigationModel";
  id: string;
}

export interface UpdatePatientNursingService_updateNursingService_radiology {
  __typename: "InvestigationModel";
  id: string;
}

export interface UpdatePatientNursingService_updateNursingService_immunizations {
  __typename: "AdmissionModel";
  id: string;
}

export interface UpdatePatientNursingService_updateNursingService {
  __typename: "NursingServiceModel";
  id: string;
  hmoProviderId: string | null;
  serviceDetails: UpdatePatientNursingService_updateNursingService_serviceDetails[] | null;
  isPackage: boolean;
  specialty: string | null;
  department: string | null;
  nurseName: string | null;
  assistantNurseName: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  billStatus: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  hospitalId: string | null;
  profileId: string | null;
  profile: UpdatePatientNursingService_updateNursingService_profile | null;
  preauthorizationDetails: UpdatePatientNursingService_updateNursingService_preauthorizationDetails | null;
  hmoClaim: UpdatePatientNursingService_updateNursingService_hmoClaim | null;
  vitals: UpdatePatientNursingService_updateNursingService_vitals[];
  admissions: UpdatePatientNursingService_updateNursingService_admissions[];
  consultations: UpdatePatientNursingService_updateNursingService_consultations[];
  medications: UpdatePatientNursingService_updateNursingService_medications[];
  surgeries: UpdatePatientNursingService_updateNursingService_surgeries[];
  investigations: UpdatePatientNursingService_updateNursingService_investigations[];
  labTests: UpdatePatientNursingService_updateNursingService_labTests[];
  radiology: UpdatePatientNursingService_updateNursingService_radiology[];
  immunizations: UpdatePatientNursingService_updateNursingService_immunizations[];
}

export interface UpdatePatientNursingService {
  updateNursingService: UpdatePatientNursingService_updateNursingService;
}

export interface UpdatePatientNursingServiceVariables {
  input: NursingServicesInput;
  id: string;
  pin?: string | null;
}
