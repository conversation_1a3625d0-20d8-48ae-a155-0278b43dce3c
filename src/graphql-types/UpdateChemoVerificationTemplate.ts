/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ChemoDiagnosisTemplateInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateChemoVerificationTemplate
// ====================================================

export interface UpdateChemoVerificationTemplate_updateChemoDiagnosisTemplate_cycles_investigationDetails {
  __typename: "ChemoInvestigationDetails";
  investigationName: string | null;
  investigationType: string | null;
}

export interface UpdateChemoVerificationTemplate_updateChemoDiagnosisTemplate_cycles_drugs {
  __typename: "ChemoDiagnosisDrugTemplate";
  day: string;
  dosage: string | null;
  dosagePercentage: string | null;
  infusionUsed: string | null;
  route: string | null;
  drugName: string;
  drugId: string | null;
  frequency: string | null;
  ref: string;
  note: string | null;
  inventoryClass: string | null;
}

export interface UpdateChemoVerificationTemplate_updateChemoDiagnosisTemplate_cycles {
  __typename: "ChemoDiagnosisCycleModel";
  id: string;
  cycleNumber: number;
  investigationDetails: UpdateChemoVerificationTemplate_updateChemoDiagnosisTemplate_cycles_investigationDetails[] | null;
  drugs: UpdateChemoVerificationTemplate_updateChemoDiagnosisTemplate_cycles_drugs[] | null;
}

export interface UpdateChemoVerificationTemplate_updateChemoDiagnosisTemplate {
  __typename: "ChemoDiagnosisTemplateModel";
  id: string;
  combinationName: string;
  creatorName: string | null;
  createdDate: any;
  type: string;
  section: string;
  cycles: UpdateChemoVerificationTemplate_updateChemoDiagnosisTemplate_cycles[];
  updatedDate: any;
  lastModifierName: string | null;
}

export interface UpdateChemoVerificationTemplate {
  updateChemoDiagnosisTemplate: UpdateChemoVerificationTemplate_updateChemoDiagnosisTemplate;
}

export interface UpdateChemoVerificationTemplateVariables {
  input: ChemoDiagnosisTemplateInput;
  id: string;
}
