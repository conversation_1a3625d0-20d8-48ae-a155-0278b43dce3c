/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FamilyPlanningAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetFemalesUsingModernContraceptions
// ====================================================

export interface GetFemalesUsingModernContraceptions_getFemalesUsingModernContraceptions_ageRanges {
  __typename: "CategoryCount";
  count: number | null;
  category: string | null;
}

export interface GetFemalesUsingModernContraceptions_getFemalesUsingModernContraceptions {
  __typename: "FamilyPlanningSummary";
  name: number | null;
  totalFemalesUsingModernContraceptions: number | null;
  ageRanges: GetFemalesUsingModernContraceptions_getFemalesUsingModernContraceptions_ageRanges[] | null;
}

export interface GetFemalesUsingModernContraceptions {
  getFemalesUsingModernContraceptions: GetFemalesUsingModernContraceptions_getFemalesUsingModernContraceptions;
}

export interface GetFemalesUsingModernContraceptionsVariables {
  filter?: FamilyPlanningAnalyticsFilter | null;
}
