/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UpdateUtilizationsStatusInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: ProcessBulkPreauthUtilizationsStatus
// ====================================================

export interface ProcessBulkPreauthUtilizationsStatus_updateBulkUtilizationsStatus_utilisationStatus {
  __typename: "ClaimsApprovalInputType";
  status: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusDescription: string | null;
  serviceAmount: number | null;
  vettingGroup: string;
  creatorId: string;
  creatorName: string;
  createdDate: any;
  updatedDate: any | null;
  comment: string | null;
}

export interface ProcessBulkPreauthUtilizationsStatus_updateBulkUtilizationsStatus {
  __typename: "PreAuthUtilisationsModel";
  id: string;
  status: string | null;
  rejectionReason: string[] | null;
  statusDescription: string | null;
  autoApprovalSource: string | null;
  utilisationStatus: ProcessBulkPreauthUtilizationsStatus_updateBulkUtilizationsStatus_utilisationStatus[] | null;
}

export interface ProcessBulkPreauthUtilizationsStatus {
  updateBulkUtilizationsStatus: ProcessBulkPreauthUtilizationsStatus_updateBulkUtilizationsStatus[];
}

export interface ProcessBulkPreauthUtilizationsStatusVariables {
  input: UpdateUtilizationsStatusInput;
}
