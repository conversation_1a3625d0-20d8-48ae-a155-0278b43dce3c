/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { SignatureInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddTreatmentPlanConsentSignature
// ====================================================

export interface AddTreatmentPlanConsentSignature_addTreatmentPlanConsentSignature {
  __typename: "TreatmentPlanModel";
  id: string;
  treatmentPlan: string | null;
  conceal: boolean | null;
  patientAdmitted: string | null;
  concealObservationNote: boolean | null;
  observationNote: string | null;
  admissionConsent: string | null;
  adverseEffectsFollowingTreatment: string | null;
  stateEffects: string | null;
  adverseEffectsInvestigated: string | null;
  outcomeOfInvestigation: string | null;
  treatmentGiven: string | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
  treatmentStatus: string | null;
}

export interface AddTreatmentPlanConsentSignature {
  addTreatmentPlanConsentSignature: AddTreatmentPlanConsentSignature_addTreatmentPlanConsentSignature;
}

export interface AddTreatmentPlanConsentSignatureVariables {
  id: string;
  clinifyId: string;
  input: SignatureInput;
  pin?: string | null;
}
