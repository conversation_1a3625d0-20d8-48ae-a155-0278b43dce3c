/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { SurgeryFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPatientAnaesthesiaChecklists
// ====================================================

export interface GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_drugsGiven {
  __typename: "DrugGivenType";
  readingDate: any | null;
  time: string | null;
  drugName: string | null;
  dosage: string | null;
  dosageUnit: string | null;
  routeOfAdministration: string | null;
}

export interface GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_pulseAndBp {
  __typename: "PulseAndBpType";
  readingDate: any | null;
  time: string | null;
  pulse: string | null;
  bpSystolic: string | null;
  bpDiastolic: string | null;
}

export interface GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_eventTimeGraph {
  __typename: "EventTimeType";
  readingDate: any | null;
  eventName: string | null;
  time: any | null;
}

export interface GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_oxygenLevel {
  __typename: "OxygenLevelDataType";
  readingDate: any | null;
  time: string | null;
  ecgRythm: string | null;
  saturated: string | null;
  airFGF: string | null;
  endTidal: string | null;
  oxygenFi: string | null;
  agentFe: string | null;
  airwayPressure: string | null;
  airwayPressureUnit: string | null;
}

export interface GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_additionalOxygenLevel_value {
  __typename: "TimeVolumeType";
  time: string | null;
  volume: string | null;
}

export interface GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_additionalOxygenLevel {
  __typename: "AdditionalTimeVolumeType";
  name: string | null;
  value: GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_additionalOxygenLevel_value[] | null;
}

export interface GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_fluids {
  __typename: "FliudDataType";
  readingDate: any | null;
  time: string | null;
  ivFluids: string | null;
  bloodLoss: string | null;
  urineOutput: string | null;
}

export interface GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_size {
  __typename: "TicknessValueType";
  tick: boolean | null;
  value: string | null;
}

export interface GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_cmToLips {
  __typename: "TicknessValueType";
  tick: boolean | null;
  value: string | null;
}

export interface GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_nmj {
  __typename: "TicknessSiteType";
  tick: boolean | null;
  site: string | null;
}

export interface GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list {
  __typename: "AnaesthesiaModel";
  id: string;
  drugsGiven: GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_drugsGiven[] | null;
  pulseAndBp: GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_pulseAndBp[] | null;
  eventTimeGraph: GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_eventTimeGraph[] | null;
  oxygenLevel: GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_oxygenLevel[] | null;
  additionalOxygenLevel: GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_additionalOxygenLevel[] | null;
  fluids: GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_fluids[] | null;
  inOt: boolean | null;
  rapidSequence: boolean | null;
  inhalational: boolean | null;
  equipmentCheck: boolean | null;
  preOxygenation: boolean | null;
  vascularAccessSite: string | null;
  preInduction: string | null;
  postInduction: string | null;
  nasalSpecs: boolean | null;
  faceMask: boolean | null;
  iGel: boolean | null;
  lma: boolean | null;
  endatrachealTube: boolean | null;
  size: GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_size | null;
  cmToLips: GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_cmToLips | null;
  cuff: boolean | null;
  amoured: boolean | null;
  oralNasal: boolean | null;
  grade: string | null;
  difficult: boolean | null;
  bougie: boolean | null;
  mcCoy: boolean | null;
  bougieILMA: boolean | null;
  fibreOptic: boolean | null;
  mcIntosh: boolean | null;
  capnograph: boolean | null;
  auscultation: boolean | null;
  nmj: GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_nmj | null;
  tempProbe: boolean | null;
  respiration: string | null;
  mode: string | null;
  tv: string | null;
  frequency: string | null;
  circuit: string | null;
  other: string | null;
  eyesCovered: boolean | null;
  pressurePointsPadded: boolean | null;
  urinaryCatheter: boolean | null;
  nasogastricTube: boolean | null;
  warmingBlanket: boolean | null;
  position: string | null;
  arms: string | null;
  intravenousFluidsGiven: string | null;
  localAnaestheticTechnique: string | null;
  clinicalAdverseEvent: string | null;
  createdDate: any;
  updatedDate: any;
  creatorName: string | null;
  lastModifierName: string | null;
  profile: GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list_profile | null;
}

export interface GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist {
  __typename: "AnaesthesiaResponse";
  totalCount: number;
  list: GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist_list[];
}

export interface GetPatientAnaesthesiaChecklists_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  /**
   * Get patient intra operative checklists
   */
  anaesthesiaChecklist: GetPatientAnaesthesiaChecklists_profile_anaesthesiaChecklist;
}

export interface GetPatientAnaesthesiaChecklists {
  profile: GetPatientAnaesthesiaChecklists_profile;
}

export interface GetPatientAnaesthesiaChecklistsVariables {
  filterOptions?: SurgeryFilterInput | null;
  id: string;
}
