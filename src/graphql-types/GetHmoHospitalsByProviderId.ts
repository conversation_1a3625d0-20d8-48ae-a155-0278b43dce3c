/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HmoFilterOptions, HospitalPlan, PlanStatus } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHmoHospitalsByProviderId
// ====================================================

export interface GetHmoHospitalsByProviderId_getHmoHospitalByProviderId_list {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
  lga: string | null;
  plan: HospitalPlan;
  planStatus: PlanStatus;
}

export interface GetHmoHospitalsByProviderId_getHmoHospitalByProviderId {
  __typename: "HmoHospitalResponse";
  totalCount: number;
  list: GetHmoHospitalsByProviderId_getHmoHospitalByProviderId_list[];
}

export interface GetHmoHospitalsByProviderId {
  getHmoHospitalByProviderId: GetHmoHospitalsByProviderId_getHmoHospitalByProviderId;
}

export interface GetHmoHospitalsByProviderIdVariables {
  providerId: string;
  filterOptions?: HmoFilterOptions | null;
}
