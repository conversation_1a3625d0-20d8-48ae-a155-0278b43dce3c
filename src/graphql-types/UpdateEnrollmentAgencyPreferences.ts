/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: UpdateEnrollmentAgencyPreferences
// ====================================================

export interface UpdateEnrollmentAgencyPreferences_updateEnrollmentAgencyPreferences_enrollmentAgencyPreferences {
  __typename: "EnrollmentAgencyPreferencesDto";
  administrationAgency: string | null;
  agencies: string[] | null;
}

export interface UpdateEnrollmentAgencyPreferences_updateEnrollmentAgencyPreferences {
  __typename: "FacilityPreferenceModel";
  id: string;
  enrollmentAgencyPreferences: UpdateEnrollmentAgencyPreferences_updateEnrollmentAgencyPreferences_enrollmentAgencyPreferences[] | null;
}

export interface UpdateEnrollmentAgencyPreferences {
  updateEnrollmentAgencyPreferences: UpdateEnrollmentAgencyPreferences_updateEnrollmentAgencyPreferences;
}

export interface UpdateEnrollmentAgencyPreferencesVariables {
  administrationAgency: string;
  enrollmentAgency: string[];
}
