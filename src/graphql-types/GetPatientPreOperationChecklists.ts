/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { SurgeryFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPatientPreOperationChecklists
// ====================================================

export interface GetPatientPreOperationChecklists_profile_preOperationChecklist_list_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface GetPatientPreOperationChecklists_profile_preOperationChecklist_list {
  __typename: "PreOperationChecklistModel";
  id: string;
  operationDate: any | null;
  preSurgeryReadiness: boolean | null;
  completeConsent: string | null;
  bloodProductConent: string | null;
  alternateForm: string | null;
  historyAvailable: string | null;
  allergiesMarked: string | null;
  bpmhComplete: string | null;
  vitalsComplete: string | null;
  heightWeightDoc: string | null;
  anaestheticHistory: string | null;
  completeConsult: string | null;
  aroDocument: string | null;
  interpreterNeeded: string | null;
  fallsRisk: string | null;
  bariatricRequired: string | null;
  vteProphylaxis: string | null;
  presurgicalScrub: string | null;
  patientPreparation: boolean | null;
  idBand: string | null;
  facilityDetails: string | null;
  allergyBand: string | null;
  tsinBand: string | null;
  preprocedureCollected: string | null;
  implants: string | null;
  belongingsDocumented: string | null;
  cbap: string | null;
  verifiedSurgicalSite: string | null;
  nutritionStatusDoc: string | null;
  preoperationMedications: string | null;
  orStaffReviewed: string | null;
  surgicalSafetyChecklist: boolean | null;
  patientConfirmation: string | null;
  isSiteMarked: string | null;
  anaesthesiaMedicationCheck: string | null;
  pulseOximeterFunctioning: string | null;
  knownAllergy: string | null;
  difficultAirway: string | null;
  riskOfBloodLoss: string | null;
  teamMembersIntroduced: string | null;
  pantientNameAndProcedure: string | null;
  antibioticBeenGiven: string | null;
  criticalSteps: string | null;
  casePeriod: string | null;
  anticipatedBloodLoss: string | null;
  patientSpecificConcern: string | null;
  specifyPatientConcern: string | null;
  sterilityConfirmed: string | null;
  equipmentIssues: string | null;
  specifyEquipmentIssues: string | null;
  essentialImagingDisplayed: string | null;
  confirmedProcedureName: string | null;
  confirmSpecimensLabeled: string | null;
  confirmInstrumentCount: string | null;
  equipmentProblemsToAddress: string | null;
  recoveryKeyConcerns: string | null;
  urinaryOutputAmount: string | null;
  urinaryOutputAmountUnit: string | null;
  catheterDateTime: string | null;
  denturePlateRemoved: string | null;
  contactLensRemoved: string | null;
  prosthesisRemoved: string | null;
  jewelryRemoved: string | null;
  shavedSurgicalSite: string | null;
  lastMealDateTime: string | null;
  bowelEmptied: string | null;
  preoperationMedicationDateTime: string | null;
  medicationProfileAvailable: string | null;
  identifiedPatient: string | null;
  patientCaseFileAvailable: string | null;
  assessedDvtRisk: string | null;
  equipmentImplantAvailable: string | null;
  verifiedAvailabilityOrderedBloodProduct: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  profile: GetPatientPreOperationChecklists_profile_preOperationChecklist_list_profile | null;
}

export interface GetPatientPreOperationChecklists_profile_preOperationChecklist {
  __typename: "PreOperationChecklistResponse";
  totalCount: number;
  list: GetPatientPreOperationChecklists_profile_preOperationChecklist_list[];
}

export interface GetPatientPreOperationChecklists_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  /**
   * Get patient pre operation checklists
   */
  preOperationChecklist: GetPatientPreOperationChecklists_profile_preOperationChecklist;
}

export interface GetPatientPreOperationChecklists {
  profile: GetPatientPreOperationChecklists_profile;
}

export interface GetPatientPreOperationChecklistsVariables {
  filterOptions?: SurgeryFilterInput | null;
  id: string;
}
