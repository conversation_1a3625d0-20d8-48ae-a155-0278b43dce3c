/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealLabourAndDeliverySecondStageAdditionalNote
// ====================================================

export interface ConcealLabourAndDeliverySecondStageAdditionalNote_concealLabourAndDeliverySecondStageAdditionalNote {
  __typename: "LabourDeliveryModel";
  id: string;
  concealSecondStageAdditionalNote: boolean | null;
  secondStageAdditionalNote: string | null;
}

export interface ConcealLabourAndDeliverySecondStageAdditionalNote {
  concealLabourAndDeliverySecondStageAdditionalNote: ConcealLabourAndDeliverySecondStageAdditionalNote_concealLabourAndDeliverySecondStageAdditionalNote;
}

export interface ConcealLabourAndDeliverySecondStageAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
}
