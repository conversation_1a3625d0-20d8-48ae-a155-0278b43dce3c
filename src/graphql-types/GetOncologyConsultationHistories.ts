/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FilterInput, BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetOncologyConsultationHistories
// ====================================================

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_tumorDetails {
  __typename: "TumorDetailsInputType";
  size: string | null;
  laterality: string | null;
}

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_complaintSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_systemReviewSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_physicalExamSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_oncologyRegister_treatmentChart_periods_cycles {
  __typename: "OncologyCycle";
  cycleNo: number;
  value: any | null;
}

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_oncologyRegister_treatmentChart_periods {
  __typename: "OncologyPeriod";
  day: number;
  cycles: GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_oncologyRegister_treatmentChart_periods_cycles[];
}

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_oncologyRegister_treatmentChart {
  __typename: "OncologyChart";
  noOfCycles: number;
  periods: GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_oncologyRegister_treatmentChart_periods[];
}

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_oncologyRegister_therapyChart_periods_cycles {
  __typename: "OncologyCycle";
  cycleNo: number;
  value: any | null;
}

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_oncologyRegister_therapyChart_periods {
  __typename: "OncologyPeriod";
  day: number;
  cycles: GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_oncologyRegister_therapyChart_periods_cycles[];
}

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_oncologyRegister_therapyChart {
  __typename: "OncologyChart";
  noOfCycles: number;
  periods: GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_oncologyRegister_therapyChart_periods[];
}

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_oncologyRegister {
  __typename: "OncologyConsultationRegisterModel";
  id: string;
  creatorName: string | null;
  lastModifierName: string | null;
  treatmentChart: GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_oncologyRegister_treatmentChart | null;
  therapyChart: GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_oncologyRegister_therapyChart | null;
  createdDate: any;
  updatedDate: any;
}

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_profile_coverageDetails_provider | null;
}

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_profile_coverageDetails[] | null;
}

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_preauthorizationDetails_provider | null;
}

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_bill {
  __typename: "BillModel";
  id: string;
  createdDate: any;
}

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list {
  __typename: "OncologyConsultationHistoryModel";
  id: string;
  consultationDateTime: any | null;
  duration: string | null;
  priority: string | null;
  category: string | null;
  doctorName: string | null;
  specialty: string | null;
  rank: string | null;
  department: string | null;
  consultationStartDate: any | null;
  consultationEndDate: any | null;
  initialDiagnosisICD10: string | null;
  initialDiagnosisICD11: string | null;
  initialDiagnosisSNOMED: string | null;
  finalDiagnosisICD10: string | null;
  finalDiagnosisICD11: string | null;
  finalDiagnosisSNOMED: string | null;
  diagnosisDateTime: any | null;
  diagnosedBy: string | null;
  additionalNote: string | null;
  stageDiagnosisICD10: string | null;
  stageDiagnosisICD11: string | null;
  stageDiagnosisSNOMED: string | null;
  stageDiagnosisDateTime: any | null;
  classification: string | null;
  stage: string | null;
  stageDate: any | null;
  histopathologicType: string | null;
  stageTiming: string | null;
  primaryTumor: string | null;
  residualTumor: string | null;
  tumorDetails: GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_tumorDetails[] | null;
  lymphovascularInvasion: string | null;
  regionalLymphNodes: string | null;
  numberOfNodes: string | null;
  distantMetastasis: string | null;
  grade: string | null;
  stageStatus: string | null;
  cancerType: string | null;
  progression: string | null;
  relapse: string | null;
  remission: string | null;
  stageTreatmentType: string | null;
  stageAdditionalNote: string | null;
  nuclearGradeOrPleomorphism: string | null;
  mitoticCountScore: string | null;
  tubuleFormation: string | null;
  stageGroupingScore: string | null;
  scarffBloomRichardsonScore: string | null;
  nottinghamModificationSbrGrade: string | null;
  stageLymphovascularInvasion: string | null;
  stageHistopathologicType: string | null;
  diagnosticInformation: string | null;
  typeOfSpecimen: string | null;
  stagingRole: string | null;
  stagingDate: any | null;
  treatmentType: string | null;
  treatmentSite: string | null;
  intentOfTreatment: string | null;
  lineOfTreatment: string | null;
  concurrentTreatment: string | null;
  treatmentPlanProvider: string | null;
  treatmentDepartment: string | null;
  treatmentStatus: string | null;
  treatmentPriority: string | null;
  treatmentInterval: string | null;
  treatmentStartDate: any | null;
  treatmentEndDate: any | null;
  treatmentCycleDays: string | null;
  treatmentCycleNumber: string | null;
  treatmentPatientType: string | null;
  treatmentAdverseReaction: string | null;
  treatmentSpecificReaction: string | null;
  treatmentOutcome: string | null;
  treatmentResponse: string | null;
  treatmentFollowupDate: any | null;
  treatmentAdditionalNote: string | null;
  therapyType: string | null;
  therapySite: string | null;
  intentOfTherapy: string | null;
  lineOfTherapy: string | null;
  concurrentTherapy: string | null;
  therapyPlanProvider: string | null;
  therapyDepartment: string | null;
  therapyStatus: string | null;
  therapyPriority: string | null;
  therapyInterval: string | null;
  therapyStartDate: any | null;
  therapyEndDate: any | null;
  therapyCycleDays: string | null;
  therapyCycleNumber: string | null;
  therapyPatientType: string | null;
  therapyAdverseReaction: string | null;
  therapySpecificReaction: string | null;
  therapyOutcome: string | null;
  therapyResponse: string | null;
  therapyFollowupDate: any | null;
  therapyAdditionalNote: string | null;
  nottinghamGradeAbove: string | null;
  estrogenReceptorExpression: string | null;
  erPercentagePositive: string | null;
  erAllredScore: string | null;
  progesteroneReceptor: string | null;
  prPercentagePositive: string | null;
  prAllredScore: string | null;
  overallHer2Status: string | null;
  ihcScore: string | null;
  fishResult: string | null;
  fishCopyNumber: string | null;
  her2OrCep17Ratio: string | null;
  circulatingTumorCells: string | null;
  complaintGender: string | null;
  complaintSmartText: string | null;
  complaintSmartSelection: GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_complaintSmartSelection | null;
  systemReviewSmartText: string | null;
  systemReviewSmartSelection: GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_systemReviewSmartSelection | null;
  physicalExamSmartText: string | null;
  physicalExamSmartSelection: GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_physicalExamSmartSelection | null;
  complaint: string | null;
  concealComplaint: boolean | null;
  complaintHistory: string | null;
  concealComplaintHistory: boolean | null;
  systemReview: string | null;
  concealSystemReview: boolean | null;
  physicalExam: string | null;
  concealPhysicalExam: boolean | null;
  audiometry: string | null;
  concealAudiometry: boolean | null;
  healthEducation: string | null;
  concealHealthEducation: boolean | null;
  oncologyRegister: GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_oncologyRegister | null;
  profileId: string | null;
  profile: GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_profile | null;
  hmoProviderId: string | null;
  serviceDetails: GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_serviceDetails[] | null;
  hospitalId: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  billStatus: string | null;
  isPackage: boolean;
  appointmentId: string | null;
  documentUrl: string[] | null;
  laboratoryTestVerified: string | null;
  radiologyExaminationVerified: string | null;
  chemoNote: string | null;
  concealChemoNote: boolean | null;
  preauthorizationDetails: GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_preauthorizationDetails | null;
  bill: GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list_bill | null;
}

export interface GetOncologyConsultationHistories_profile_oncologyConsultationHistories {
  __typename: "OncologyConsultationHistoryResponse";
  totalCount: number;
  list: GetOncologyConsultationHistories_profile_oncologyConsultationHistories_list[];
}

export interface GetOncologyConsultationHistories_profile {
  __typename: "ProfileModel";
  id: string;
  oncologyConsultationHistories: GetOncologyConsultationHistories_profile_oncologyConsultationHistories;
}

export interface GetOncologyConsultationHistories {
  profile: GetOncologyConsultationHistories_profile;
}

export interface GetOncologyConsultationHistoriesVariables {
  filterOptions: FilterInput;
  id: string;
}
