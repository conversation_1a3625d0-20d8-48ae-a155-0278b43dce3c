/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { VirtualCareAppointmentFilterInput, Gender } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPatientVirtualCareAppointments
// ====================================================

export interface GetPatientVirtualCareAppointments_profile_virtualCareAppointments_list_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface GetPatientVirtualCareAppointments_profile_virtualCareAppointments_list_specialists_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  speciality: string | null;
}

export interface GetPatientVirtualCareAppointments_profile_virtualCareAppointments_list_specialists {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
  personalInformation: GetPatientVirtualCareAppointments_profile_virtualCareAppointments_list_specialists_personalInformation | null;
}

export interface GetPatientVirtualCareAppointments_profile_virtualCareAppointments_list_patientProfile_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
  dateOfBirth: any | null;
  displayPictureUrl: string | null;
  bloodGroup: string | null;
}

export interface GetPatientVirtualCareAppointments_profile_virtualCareAppointments_list_patientProfile_user {
  __typename: "UserModel";
  id: string;
  nonCorporateEmail: string | null;
  phoneNumber: string | null;
  country: string | null;
}

export interface GetPatientVirtualCareAppointments_profile_virtualCareAppointments_list_patientProfile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  gender: Gender | null;
  clinifyId: string;
  personalInformation: GetPatientVirtualCareAppointments_profile_virtualCareAppointments_list_patientProfile_personalInformation | null;
  user: GetPatientVirtualCareAppointments_profile_virtualCareAppointments_list_patientProfile_user;
}

export interface GetPatientVirtualCareAppointments_profile_virtualCareAppointments_list {
  __typename: "VirtualCareAppointmentModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  liveSessionUrl: string | null;
  category: string | null;
  appointmentDate: any;
  startDateTime: any | null;
  endDateTime: any | null;
  priority: string | null;
  duration: string | null;
  patientType: string | null;
  paymentType: string | null;
  hospital: GetPatientVirtualCareAppointments_profile_virtualCareAppointments_list_hospital | null;
  specialists: GetPatientVirtualCareAppointments_profile_virtualCareAppointments_list_specialists[] | null;
  patientProfile: GetPatientVirtualCareAppointments_profile_virtualCareAppointments_list_patientProfile | null;
}

export interface GetPatientVirtualCareAppointments_profile_virtualCareAppointments {
  __typename: "VirtualCareAppointmentListResponse";
  totalCount: number;
  list: GetPatientVirtualCareAppointments_profile_virtualCareAppointments_list[];
}

export interface GetPatientVirtualCareAppointments_profile {
  __typename: "ProfileModel";
  id: string;
  virtualCareAppointments: GetPatientVirtualCareAppointments_profile_virtualCareAppointments | null;
}

export interface GetPatientVirtualCareAppointments {
  profile: GetPatientVirtualCareAppointments_profile;
}

export interface GetPatientVirtualCareAppointmentsVariables {
  filterOptions: VirtualCareAppointmentFilterInput;
  id: string;
}
