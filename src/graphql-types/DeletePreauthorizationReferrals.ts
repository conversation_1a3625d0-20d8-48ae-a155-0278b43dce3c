/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: DeletePreauthorizationReferrals
// ====================================================

export interface DeletePreauthorizationReferrals_deletePreauthorizationReferralss {
  __typename: "PreauthorisationReferralModel";
  id: string;
}

export interface DeletePreauthorizationReferrals {
  deletePreauthorizationReferralss: DeletePreauthorizationReferrals_deletePreauthorizationReferralss[];
}

export interface DeletePreauthorizationReferralsVariables {
  ids: string[];
}
