/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BranchInformationInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateFacilityBranchInformation
// ====================================================

export interface UpdateFacilityBranchInformation_updateBranchInformation_phoneNumber {
  __typename: "PhoneNumberFields";
  value: string | null;
  countryCode: string | null;
  countryName: string | null;
}

export interface UpdateFacilityBranchInformation_updateBranchInformation_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateFacilityBranchInformation_updateBranchInformation_orgAdmin_personalInformation {
  __typename: "PersonalInformation";
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
}

export interface UpdateFacilityBranchInformation_updateBranchInformation_orgAdmin {
  __typename: "ProfileModel";
  id: string;
  title: string | null;
  personalInformation: UpdateFacilityBranchInformation_updateBranchInformation_orgAdmin_personalInformation | null;
}

export interface UpdateFacilityBranchInformation_updateBranchInformation_preference {
  __typename: "FacilityPreferencePublic";
  id: string | null;
  useHQFacilityInventory: boolean | null;
  useHQFacilityTariffs: boolean | null;
}

export interface UpdateFacilityBranchInformation_updateBranchInformation {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  supportMail: string | null;
  website: string | null;
  address: string | null;
  phoneNumber: UpdateFacilityBranchInformation_updateBranchInformation_phoneNumber | null;
  secondaryPhoneNumber: UpdateFacilityBranchInformation_updateBranchInformation_secondaryPhoneNumber | null;
  /**
   * Organization administrator
   */
  orgAdmin: UpdateFacilityBranchInformation_updateBranchInformation_orgAdmin;
  preference: UpdateFacilityBranchInformation_updateBranchInformation_preference;
}

export interface UpdateFacilityBranchInformation {
  updateBranchInformation: UpdateFacilityBranchInformation_updateBranchInformation;
}

export interface UpdateFacilityBranchInformationVariables {
  hospitalId: string;
  input: BranchInformationInput;
}
