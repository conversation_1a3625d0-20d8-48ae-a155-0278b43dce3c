/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ServicesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetAdverseEffectsFollowingImmunizationData
// ====================================================

export interface GetAdverseEffectsFollowingImmunizationData_getAdverseEffectsFollowingImmunizationsData {
  __typename: "ServicesSummary";
  totalSeriousAdverseEffects: number | null;
  totalNonSeriousAdverseEffects: number | null;
  name: number | null;
}

export interface GetAdverseEffectsFollowingImmunizationData {
  getAdverseEffectsFollowingImmunizationsData: GetAdverseEffectsFollowingImmunizationData_getAdverseEffectsFollowingImmunizationsData;
}

export interface GetAdverseEffectsFollowingImmunizationDataVariables {
  filter?: ServicesAnalyticsFilter | null;
}
