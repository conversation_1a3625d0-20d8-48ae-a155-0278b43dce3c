/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HmoClaimFilterInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: SendPaymentAdviceToProviders
// ====================================================

export interface SendPaymentAdviceToProviders {
  sendHmoClaimsPaymentAdviceToProvider: boolean;
}

export interface SendPaymentAdviceToProvidersVariables {
  filterOptions: HmoClaimFilterInput;
  hmoClaimIds?: string[] | null;
  origin: string;
}
