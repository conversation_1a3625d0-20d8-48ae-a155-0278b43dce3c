/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: BulkRegistrationStatusUpdateSubs
// ====================================================

export interface BulkRegistrationStatusUpdateSubs_BulkRegistrationStatusUpdate_failedEnrollees_enrolleeData {
  __typename: "EnrolleeData";
  firstName: string;
  lastName: string;
  email: string | null;
  phoneNumber: string | null;
}

export interface BulkRegistrationStatusUpdateSubs_BulkRegistrationStatusUpdate_failedEnrollees {
  __typename: "FailedEnrollee";
  code: string;
  index: number;
  reason: string;
  enrolleeData: BulkRegistrationStatusUpdateSubs_BulkRegistrationStatusUpdate_failedEnrollees_enrolleeData;
}

export interface BulkRegistrationStatusUpdateSubs_BulkRegistrationStatusUpdate_successfulEnrollees_enrolleeData {
  __typename: "EnrolleeData";
  firstName: string;
  lastName: string;
  email: string | null;
  phoneNumber: string | null;
}

export interface BulkRegistrationStatusUpdateSubs_BulkRegistrationStatusUpdate_successfulEnrollees {
  __typename: "SuccessfulEnrollee";
  enrolleeId: string;
  index: number;
  enrolleeData: BulkRegistrationStatusUpdateSubs_BulkRegistrationStatusUpdate_successfulEnrollees_enrolleeData;
}

export interface BulkRegistrationStatusUpdateSubs_BulkRegistrationStatusUpdate {
  __typename: "BulkRegistrationResult";
  failed: number;
  successful: number;
  total: number;
  failedEnrollees: BulkRegistrationStatusUpdateSubs_BulkRegistrationStatusUpdate_failedEnrollees[];
  successfulEnrollees: BulkRegistrationStatusUpdateSubs_BulkRegistrationStatusUpdate_successfulEnrollees[];
}

export interface BulkRegistrationStatusUpdateSubs {
  BulkRegistrationStatusUpdate: BulkRegistrationStatusUpdateSubs_BulkRegistrationStatusUpdate;
}

export interface BulkRegistrationStatusUpdateSubsVariables {
  profileId: string;
}
