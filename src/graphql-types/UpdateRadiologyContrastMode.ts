/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: UpdateRadiologyContrastMode
// ====================================================

export interface UpdateRadiologyContrastMode_updateRadiologyContrastMode {
  __typename: "FacilityPreferenceModel";
  id: string;
  radiologyContrastConfirmation: boolean | null;
  hospitalId: string;
}

export interface UpdateRadiologyContrastMode {
  updateRadiologyContrastMode: UpdateRadiologyContrastMode_updateRadiologyContrastMode;
}

export interface UpdateRadiologyContrastModeVariables {
  mode: boolean;
}
