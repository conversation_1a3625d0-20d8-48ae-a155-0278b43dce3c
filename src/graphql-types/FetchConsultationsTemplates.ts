/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: FetchConsultationsTemplates
// ====================================================

export interface FetchConsultationsTemplates_findByHospitalIdConsultationsTemplates {
  __typename: "ConsultationsTemplateModel";
  id: string;
  name: string;
  complaints: string | null;
  historyComplaints: string | null;
  healthEducation: string | null;
  reviewSystems: string | null;
  physicalExamination: string | null;
  audiometry: string | null;
  treatmentPlan: string | null;
}

export interface FetchConsultationsTemplates {
  findByHospitalIdConsultationsTemplates: FetchConsultationsTemplates_findByHospitalIdConsultationsTemplates[];
}

export interface FetchConsultationsTemplatesVariables {
  facilityPreferenceId: string;
}
