/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UserType } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: HandoverAdditionalNoteAddedSubs
// ====================================================

export interface HandoverAdditionalNoteAddedSubs_HandoverAdditionalNoteAdded {
  __typename: "HandoverAdditionalNoteModel";
  id: string;
  additionalNote: string;
  handoverNoteId: string;
  creatorId: string;
  createdDate: any;
  updatedDate: any | null;
}

export interface HandoverAdditionalNoteAddedSubs {
  HandoverAdditionalNoteAdded: HandoverAdditionalNoteAddedSubs_HandoverAdditionalNoteAdded;
}

export interface HandoverAdditionalNoteAddedSubsVariables {
  profileId: string;
  hospitalId?: string | null;
  userType: UserType;
}
