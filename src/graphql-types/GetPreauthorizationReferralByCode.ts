/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetPreauthorizationReferralByCode
// ====================================================

export interface GetPreauthorizationReferralByCode_getPreauthorizationReferralByCode_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface GetPreauthorizationReferralByCode_getPreauthorizationReferralByCode_diagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface GetPreauthorizationReferralByCode_getPreauthorizationReferralByCode_utilizations_statusHistory {
  __typename: "ReferalStatusHistoryType";
  status: string;
  creatorName: string;
  createdDate: any;
}

export interface GetPreauthorizationReferralByCode_getPreauthorizationReferralByCode_utilizations {
  __typename: "PreAuthReferralUtilisationsModel";
  id: string;
  category: string | null;
  type: string | null;
  quantity: string | null;
  /**
   * Value is in Naira
   */
  price: string | null;
  status: string | null;
  paCode: string | null;
  utilizationCode: string | null;
  utilizationId: string | null;
  rhesusFactor: string | null;
  specialty: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusHistory: GetPreauthorizationReferralByCode_getPreauthorizationReferralByCode_utilizations_statusHistory[] | null;
}

export interface GetPreauthorizationReferralByCode_getPreauthorizationReferralByCode {
  __typename: "PreauthorisationReferralModel";
  id: string;
  referredBy: string | null;
  facilityName: string | null;
  referredProviderName: string;
  enrolleeNumber: string | null;
  isExternalPlanType: boolean | null;
  externalPlanTypeId: string | null;
  provider: GetPreauthorizationReferralByCode_getPreauthorizationReferralByCode_provider;
  requestDateTime: any;
  serviceType: string;
  serviceTypeCode: string;
  serviceName: string | null;
  priority: string | null;
  specialty: string | null;
  totalQuantity: string;
  grandTotal: string;
  diagnosis: GetPreauthorizationReferralByCode_getPreauthorizationReferralByCode_diagnosis[] | null;
  utilizations: GetPreauthorizationReferralByCode_getPreauthorizationReferralByCode_utilizations[] | null;
}

export interface GetPreauthorizationReferralByCode {
  getPreauthorizationReferralByCode: GetPreauthorizationReferralByCode_getPreauthorizationReferralByCode;
}

export interface GetPreauthorizationReferralByCodeVariables {
  referralCode: string;
}
