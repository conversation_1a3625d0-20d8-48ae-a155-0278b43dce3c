/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FieldOfficerResponseInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateFieldOfficerAgentAssignments
// ====================================================

export interface UpdateFieldOfficerAgentAssignments_updateFieldOfficer_fieldOfficers {
  __typename: "FieldOfficerResponseDto";
  profileId: string | null;
  administrationAgency: string | null;
  enrollmentAgency: string | null;
  accountNumber: string | null;
  accountName: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface UpdateFieldOfficerAgentAssignments_updateFieldOfficer {
  __typename: "FacilityPreferenceModel";
  id: string;
  fieldOfficers: UpdateFieldOfficerAgentAssignments_updateFieldOfficer_fieldOfficers[] | null;
}

export interface UpdateFieldOfficerAgentAssignments {
  updateFieldOfficer: UpdateFieldOfficerAgentAssignments_updateFieldOfficer;
}

export interface UpdateFieldOfficerAgentAssignmentsVariables {
  fieldOfficer: FieldOfficerResponseInput;
}
