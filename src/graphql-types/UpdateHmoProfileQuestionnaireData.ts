/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { QuestionnaireInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateHmoProfileQuestionnaireData
// ====================================================

export interface UpdateHmoProfileQuestionnaireData_updateQuestionnaireData_questionnaireData {
  __typename: "QuestionnaireData";
  employmentStatus: string | null;
  gender: string | null;
  highestFormalEducationalLevel: string | null;
  maritalStatus: string | null;
  numberOfHouseholdMembers: string | null;
  numberOfMattresses: string | null;
  numberOfMobilePhones: string | null;
  numberOfRooms: string | null;
  numberOfTVs: string | null;
  numberOfVehicles: string | null;
  occupationalGroup: string | null;
  occupation: string | null;
  primaryCookingImplement: string | null;
  relationshipToHouseholdHead: string | null;
  typeOfRoof: string | null;
  typeOfToilet: string | null;
  /**
   * Score in percentage
   */
  questionnaireScore: number | null;
}

export interface UpdateHmoProfileQuestionnaireData_updateQuestionnaireData {
  __typename: "HmoProfileModel";
  id: string;
  questionnaireData: UpdateHmoProfileQuestionnaireData_updateQuestionnaireData_questionnaireData | null;
}

export interface UpdateHmoProfileQuestionnaireData {
  updateQuestionnaireData: UpdateHmoProfileQuestionnaireData_updateQuestionnaireData;
}

export interface UpdateHmoProfileQuestionnaireDataVariables {
  coverageInformationId: string;
  input: QuestionnaireInput;
}
