/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HmoClaimFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetProvidersWithSubmittedClaims
// ====================================================

export interface GetProvidersWithSubmittedClaims_getProvidersWithSubmittedClaims_hospitalSupportPhoneNumber {
  __typename: "PhoneNumberFields";
  countryName: string | null;
  countryCode: string | null;
  value: string | null;
}

export interface GetProvidersWithSubmittedClaims_getProvidersWithSubmittedClaims {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
  hospitalSupportPhoneNumber: GetProvidersWithSubmittedClaims_getProvidersWithSubmittedClaims_hospitalSupportPhoneNumber | null;
  supportMail: string | null;
}

export interface GetProvidersWithSubmittedClaims {
  getProvidersWithSubmittedClaims: GetProvidersWithSubmittedClaims_getProvidersWithSubmittedClaims[];
}

export interface GetProvidersWithSubmittedClaimsVariables {
  filterOptions: HmoClaimFilterInput;
}
