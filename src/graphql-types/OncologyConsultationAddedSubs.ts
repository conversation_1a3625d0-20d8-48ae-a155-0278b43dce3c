/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: OncologyConsultationAddedSubs
// ====================================================

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_tumorDetails {
  __typename: "TumorDetailsInputType";
  size: string | null;
  laterality: string | null;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_complaintSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_systemReviewSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_physicalExamSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_oncologyRegister_treatmentChart_periods_cycles {
  __typename: "OncologyCycle";
  cycleNo: number;
  value: any | null;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_oncologyRegister_treatmentChart_periods {
  __typename: "OncologyPeriod";
  day: number;
  cycles: OncologyConsultationAddedSubs_OncologyConsultationAdded_oncologyRegister_treatmentChart_periods_cycles[];
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_oncologyRegister_treatmentChart {
  __typename: "OncologyChart";
  noOfCycles: number;
  periods: OncologyConsultationAddedSubs_OncologyConsultationAdded_oncologyRegister_treatmentChart_periods[];
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_oncologyRegister_therapyChart_periods_cycles {
  __typename: "OncologyCycle";
  cycleNo: number;
  value: any | null;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_oncologyRegister_therapyChart_periods {
  __typename: "OncologyPeriod";
  day: number;
  cycles: OncologyConsultationAddedSubs_OncologyConsultationAdded_oncologyRegister_therapyChart_periods_cycles[];
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_oncologyRegister_therapyChart {
  __typename: "OncologyChart";
  noOfCycles: number;
  periods: OncologyConsultationAddedSubs_OncologyConsultationAdded_oncologyRegister_therapyChart_periods[];
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_oncologyRegister {
  __typename: "OncologyConsultationRegisterModel";
  id: string;
  creatorName: string | null;
  lastModifierName: string | null;
  treatmentChart: OncologyConsultationAddedSubs_OncologyConsultationAdded_oncologyRegister_treatmentChart | null;
  therapyChart: OncologyConsultationAddedSubs_OncologyConsultationAdded_oncologyRegister_therapyChart | null;
  createdDate: any;
  updatedDate: any;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: OncologyConsultationAddedSubs_OncologyConsultationAdded_profile_coverageDetails_provider | null;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: OncologyConsultationAddedSubs_OncologyConsultationAdded_profile_coverageDetails[] | null;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: OncologyConsultationAddedSubs_OncologyConsultationAdded_preauthorizationDetails_provider | null;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_allergies {
  __typename: "AllergyModel";
  id: string;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_surgeries {
  __typename: "SurgeryModel";
  id: string;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_admissions {
  __typename: "AdmissionModel";
  id: string;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_labTests {
  __typename: "InvestigationModel";
  id: string;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_radiology {
  __typename: "InvestigationModel";
  id: string;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_investigations {
  __typename: "InvestigationModel";
  id: string;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_nursingServices {
  __typename: "NursingServiceModel";
  id: string;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded_bill {
  __typename: "BillModel";
  id: string;
  createdDate: any;
}

export interface OncologyConsultationAddedSubs_OncologyConsultationAdded {
  __typename: "OncologyConsultationHistoryModel";
  id: string;
  consultationDateTime: any | null;
  duration: string | null;
  priority: string | null;
  category: string | null;
  doctorName: string | null;
  specialty: string | null;
  rank: string | null;
  department: string | null;
  consultationStartDate: any | null;
  consultationEndDate: any | null;
  initialDiagnosisICD10: string | null;
  initialDiagnosisICD11: string | null;
  initialDiagnosisSNOMED: string | null;
  finalDiagnosisICD10: string | null;
  finalDiagnosisICD11: string | null;
  finalDiagnosisSNOMED: string | null;
  diagnosisDateTime: any | null;
  diagnosedBy: string | null;
  additionalNote: string | null;
  stageDiagnosisICD10: string | null;
  stageDiagnosisICD11: string | null;
  stageDiagnosisSNOMED: string | null;
  stageDiagnosisDateTime: any | null;
  classification: string | null;
  stage: string | null;
  stageDate: any | null;
  histopathologicType: string | null;
  stageTiming: string | null;
  primaryTumor: string | null;
  residualTumor: string | null;
  tumorDetails: OncologyConsultationAddedSubs_OncologyConsultationAdded_tumorDetails[] | null;
  lymphovascularInvasion: string | null;
  regionalLymphNodes: string | null;
  numberOfNodes: string | null;
  distantMetastasis: string | null;
  grade: string | null;
  stageStatus: string | null;
  cancerType: string | null;
  progression: string | null;
  relapse: string | null;
  remission: string | null;
  stageTreatmentType: string | null;
  stageAdditionalNote: string | null;
  nuclearGradeOrPleomorphism: string | null;
  mitoticCountScore: string | null;
  tubuleFormation: string | null;
  stageGroupingScore: string | null;
  scarffBloomRichardsonScore: string | null;
  nottinghamModificationSbrGrade: string | null;
  stageLymphovascularInvasion: string | null;
  stageHistopathologicType: string | null;
  diagnosticInformation: string | null;
  typeOfSpecimen: string | null;
  stagingRole: string | null;
  stagingDate: any | null;
  treatmentType: string | null;
  treatmentSite: string | null;
  intentOfTreatment: string | null;
  lineOfTreatment: string | null;
  concurrentTreatment: string | null;
  treatmentPlanProvider: string | null;
  treatmentDepartment: string | null;
  treatmentStatus: string | null;
  treatmentPriority: string | null;
  treatmentInterval: string | null;
  treatmentStartDate: any | null;
  treatmentEndDate: any | null;
  treatmentCycleDays: string | null;
  treatmentCycleNumber: string | null;
  treatmentPatientType: string | null;
  treatmentAdverseReaction: string | null;
  treatmentSpecificReaction: string | null;
  treatmentOutcome: string | null;
  treatmentResponse: string | null;
  treatmentFollowupDate: any | null;
  treatmentAdditionalNote: string | null;
  therapyType: string | null;
  therapySite: string | null;
  intentOfTherapy: string | null;
  lineOfTherapy: string | null;
  concurrentTherapy: string | null;
  therapyPlanProvider: string | null;
  therapyDepartment: string | null;
  therapyStatus: string | null;
  therapyPriority: string | null;
  therapyInterval: string | null;
  therapyStartDate: any | null;
  therapyEndDate: any | null;
  therapyCycleDays: string | null;
  therapyCycleNumber: string | null;
  therapyPatientType: string | null;
  therapyAdverseReaction: string | null;
  therapySpecificReaction: string | null;
  therapyOutcome: string | null;
  therapyResponse: string | null;
  therapyFollowupDate: any | null;
  therapyAdditionalNote: string | null;
  nottinghamGradeAbove: string | null;
  estrogenReceptorExpression: string | null;
  erPercentagePositive: string | null;
  erAllredScore: string | null;
  progesteroneReceptor: string | null;
  prPercentagePositive: string | null;
  prAllredScore: string | null;
  overallHer2Status: string | null;
  ihcScore: string | null;
  fishResult: string | null;
  fishCopyNumber: string | null;
  her2OrCep17Ratio: string | null;
  circulatingTumorCells: string | null;
  complaintGender: string | null;
  complaintSmartText: string | null;
  complaintSmartSelection: OncologyConsultationAddedSubs_OncologyConsultationAdded_complaintSmartSelection | null;
  systemReviewSmartText: string | null;
  systemReviewSmartSelection: OncologyConsultationAddedSubs_OncologyConsultationAdded_systemReviewSmartSelection | null;
  physicalExamSmartText: string | null;
  physicalExamSmartSelection: OncologyConsultationAddedSubs_OncologyConsultationAdded_physicalExamSmartSelection | null;
  complaint: string | null;
  concealComplaint: boolean | null;
  complaintHistory: string | null;
  concealComplaintHistory: boolean | null;
  systemReview: string | null;
  concealSystemReview: boolean | null;
  physicalExam: string | null;
  concealPhysicalExam: boolean | null;
  audiometry: string | null;
  concealAudiometry: boolean | null;
  healthEducation: string | null;
  concealHealthEducation: boolean | null;
  oncologyRegister: OncologyConsultationAddedSubs_OncologyConsultationAdded_oncologyRegister | null;
  profileId: string | null;
  profile: OncologyConsultationAddedSubs_OncologyConsultationAdded_profile | null;
  hmoProviderId: string | null;
  serviceDetails: OncologyConsultationAddedSubs_OncologyConsultationAdded_serviceDetails[] | null;
  hospitalId: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  billStatus: string | null;
  isPackage: boolean;
  appointmentId: string | null;
  documentUrl: string[] | null;
  laboratoryTestVerified: string | null;
  radiologyExaminationVerified: string | null;
  chemoNote: string | null;
  concealChemoNote: boolean | null;
  preauthorizationDetails: OncologyConsultationAddedSubs_OncologyConsultationAdded_preauthorizationDetails | null;
  allergies: OncologyConsultationAddedSubs_OncologyConsultationAdded_allergies[];
  medications: OncologyConsultationAddedSubs_OncologyConsultationAdded_medications[];
  surgeries: OncologyConsultationAddedSubs_OncologyConsultationAdded_surgeries[];
  admissions: OncologyConsultationAddedSubs_OncologyConsultationAdded_admissions[];
  vitals: OncologyConsultationAddedSubs_OncologyConsultationAdded_vitals[];
  labTests: OncologyConsultationAddedSubs_OncologyConsultationAdded_labTests[];
  radiology: OncologyConsultationAddedSubs_OncologyConsultationAdded_radiology[];
  investigations: OncologyConsultationAddedSubs_OncologyConsultationAdded_investigations[];
  nursingServices: OncologyConsultationAddedSubs_OncologyConsultationAdded_nursingServices[];
  bill: OncologyConsultationAddedSubs_OncologyConsultationAdded_bill | null;
}

export interface OncologyConsultationAddedSubs {
  OncologyConsultationAdded: OncologyConsultationAddedSubs_OncologyConsultationAdded;
}

export interface OncologyConsultationAddedSubsVariables {
  profileId: string;
}
