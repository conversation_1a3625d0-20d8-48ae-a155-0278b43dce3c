/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: OncologyConsultationChartUpdatedSubs
// ====================================================

export interface OncologyConsultationChartUpdatedSubs_OncologyConsultationChartUpdated_treatmentChart_periods_cycles {
  __typename: "OncologyCycle";
  cycleNo: number;
  value: any | null;
}

export interface OncologyConsultationChartUpdatedSubs_OncologyConsultationChartUpdated_treatmentChart_periods {
  __typename: "OncologyPeriod";
  day: number;
  cycles: OncologyConsultationChartUpdatedSubs_OncologyConsultationChartUpdated_treatmentChart_periods_cycles[];
}

export interface OncologyConsultationChartUpdatedSubs_OncologyConsultationChartUpdated_treatmentChart {
  __typename: "OncologyChart";
  noOfCycles: number;
  periods: OncologyConsultationChartUpdatedSubs_OncologyConsultationChartUpdated_treatmentChart_periods[];
}

export interface OncologyConsultationChartUpdatedSubs_OncologyConsultationChartUpdated_therapyChart_periods_cycles {
  __typename: "OncologyCycle";
  cycleNo: number;
  value: any | null;
}

export interface OncologyConsultationChartUpdatedSubs_OncologyConsultationChartUpdated_therapyChart_periods {
  __typename: "OncologyPeriod";
  day: number;
  cycles: OncologyConsultationChartUpdatedSubs_OncologyConsultationChartUpdated_therapyChart_periods_cycles[];
}

export interface OncologyConsultationChartUpdatedSubs_OncologyConsultationChartUpdated_therapyChart {
  __typename: "OncologyChart";
  noOfCycles: number;
  periods: OncologyConsultationChartUpdatedSubs_OncologyConsultationChartUpdated_therapyChart_periods[];
}

export interface OncologyConsultationChartUpdatedSubs_OncologyConsultationChartUpdated {
  __typename: "OncologyConsultationRegisterModel";
  id: string;
  creatorName: string | null;
  lastModifierName: string | null;
  treatmentChart: OncologyConsultationChartUpdatedSubs_OncologyConsultationChartUpdated_treatmentChart | null;
  therapyChart: OncologyConsultationChartUpdatedSubs_OncologyConsultationChartUpdated_therapyChart | null;
  createdDate: any;
  updatedDate: any;
}

export interface OncologyConsultationChartUpdatedSubs {
  OncologyConsultationChartUpdated: OncologyConsultationChartUpdatedSubs_OncologyConsultationChartUpdated;
}

export interface OncologyConsultationChartUpdatedSubsVariables {
  profileId: string;
}
