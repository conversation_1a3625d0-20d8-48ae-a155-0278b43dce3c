/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: FetchByFacilityPreferenceIdConsultationsTemplates
// ====================================================

export interface FetchByFacilityPreferenceIdConsultationsTemplates_findByFacilityPreferenceIdConsultationsTemplates {
  __typename: "ConsultationsTemplateModel";
  id: string;
  name: string;
  complaints: string | null;
  historyComplaints: string | null;
  healthEducation: string | null;
  reviewSystems: string | null;
  physicalExamination: string | null;
  audiometry: string | null;
  treatmentPlan: string | null;
}

export interface FetchByFacilityPreferenceIdConsultationsTemplates {
  findByFacilityPreferenceIdConsultationsTemplates: FetchByFacilityPreferenceIdConsultationsTemplates_findByFacilityPreferenceIdConsultationsTemplates[];
}

export interface FetchByFacilityPreferenceIdConsultationsTemplatesVariables {
  facilityPreferenceId: string;
}
