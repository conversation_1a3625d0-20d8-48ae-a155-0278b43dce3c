/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealImmunizationAdditionalNote
// ====================================================

export interface ConcealImmunizationAdditionalNote_concealImmunizationAdditionalNote {
  __typename: "ImmunizationModel";
  id: string;
  concealAdditionalNote: boolean | null;
  additionalNote: string | null;
}

export interface ConcealImmunizationAdditionalNote {
  concealImmunizationAdditionalNote: ConcealImmunizationAdditionalNote_concealImmunizationAdditionalNote;
}

export interface ConcealImmunizationAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
}
