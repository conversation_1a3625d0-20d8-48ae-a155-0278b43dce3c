/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UpdateUtilizationsStatusInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: BulkUpdateClaimsUtilizationStatus
// ====================================================

export interface BulkUpdateClaimsUtilizationStatus_updateClaimUtilizationsStatus {
  __typename: "HmoClaimModel";
  id: string;
  status: string | null;
  lastModifierName: string | null;
  updatedDate: any;
}

export interface BulkUpdateClaimsUtilizationStatus {
  updateClaimUtilizationsStatus: BulkUpdateClaimsUtilizationStatus_updateClaimUtilizationsStatus[];
}

export interface BulkUpdateClaimsUtilizationStatusVariables {
  input: UpdateUtilizationsStatusInput;
}
