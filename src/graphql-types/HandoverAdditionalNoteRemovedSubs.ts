/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UserType } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: HandoverAdditionalNoteRemovedSubs
// ====================================================

export interface HandoverAdditionalNoteRemovedSubs_HandoverAdditionalNoteRemoved {
  __typename: "HandoverAdditionalNoteModel";
  id: string;
  handoverNoteId: string;
}

export interface HandoverAdditionalNoteRemovedSubs {
  HandoverAdditionalNoteRemoved: HandoverAdditionalNoteRemovedSubs_HandoverAdditionalNoteRemoved;
}

export interface HandoverAdditionalNoteRemovedSubsVariables {
  profileId: string;
  hospitalId?: string | null;
  userType: UserType;
}
