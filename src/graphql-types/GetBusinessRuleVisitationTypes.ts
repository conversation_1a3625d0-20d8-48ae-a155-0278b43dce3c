/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BusinessRuleFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetBusinessRuleVisitationTypes
// ====================================================

export interface GetBusinessRuleVisitationTypes {
  getBusinessRuleVisitationTypes: string[];
}

export interface GetBusinessRuleVisitationTypesVariables {
  filterOptions: BusinessRuleFilterInput;
}
