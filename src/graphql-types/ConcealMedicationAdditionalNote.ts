/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealMedicationAdditionalNote
// ====================================================

export interface ConcealMedicationAdditionalNote_concealMedicationAdditionalNote {
  __typename: "MedicationModel";
  id: string;
  concealAdditionalNote: boolean | null;
  additionalNote: string | null;
}

export interface ConcealMedicationAdditionalNote {
  concealMedicationAdditionalNote: ConcealMedicationAdditionalNote_concealMedicationAdditionalNote;
}

export interface ConcealMedicationAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
}
