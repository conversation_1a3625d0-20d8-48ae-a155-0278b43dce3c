/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UserType } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: HandoverNoteItemDeletedSubs
// ====================================================

export interface HandoverNoteItemDeletedSubs_HandoverNoteItemRemoved {
  __typename: "HandoverNoteItemModel";
  id: string;
  handoverNoteId: string;
}

export interface HandoverNoteItemDeletedSubs {
  HandoverNoteItemRemoved: HandoverNoteItemDeletedSubs_HandoverNoteItemRemoved;
}

export interface HandoverNoteItemDeletedSubsVariables {
  profileId: string;
  hospitalId?: string | null;
  userType: UserType;
}
