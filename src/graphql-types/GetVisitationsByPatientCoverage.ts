/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { VisitationAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetVisitationsByPatientCoverage
// ====================================================

export interface GetVisitationsByPatientCoverage_getVisitationsByPatientCoverage_byCoverageType_hmo {
  __typename: "V_ByCoverageType";
  coverageName: string | null;
  memberNumber: string | null;
  patientFullName: string | null;
  visitDate: string | null;
}

export interface GetVisitationsByPatientCoverage_getVisitationsByPatientCoverage_byCoverageType_private {
  __typename: "V_ByCoverageType";
  coverageName: string | null;
  memberNumber: string | null;
  patientFullName: string | null;
  visitDate: string | null;
}

export interface GetVisitationsByPatientCoverage_getVisitationsByPatientCoverage_byCoverageType_company {
  __typename: "V_ByCoverageType";
  coverageName: string | null;
  memberNumber: string | null;
  patientFullName: string | null;
  visitDate: string | null;
}

export interface GetVisitationsByPatientCoverage_getVisitationsByPatientCoverage_byCoverageType_family {
  __typename: "V_ByCoverageType";
  coverageName: string | null;
  memberNumber: string | null;
  patientFullName: string | null;
  visitDate: string | null;
}

export interface GetVisitationsByPatientCoverage_getVisitationsByPatientCoverage_byCoverageType {
  __typename: "VisitationByCoverageTypeSummary";
  hmo: GetVisitationsByPatientCoverage_getVisitationsByPatientCoverage_byCoverageType_hmo[] | null;
  private: GetVisitationsByPatientCoverage_getVisitationsByPatientCoverage_byCoverageType_private[] | null;
  company: GetVisitationsByPatientCoverage_getVisitationsByPatientCoverage_byCoverageType_company[] | null;
  family: GetVisitationsByPatientCoverage_getVisitationsByPatientCoverage_byCoverageType_family[] | null;
}

export interface GetVisitationsByPatientCoverage_getVisitationsByPatientCoverage {
  __typename: "VisitationSummary";
  name: number | null;
  byCoverageType: GetVisitationsByPatientCoverage_getVisitationsByPatientCoverage_byCoverageType | null;
}

export interface GetVisitationsByPatientCoverage {
  getVisitationsByPatientCoverage: GetVisitationsByPatientCoverage_getVisitationsByPatientCoverage;
}

export interface GetVisitationsByPatientCoverageVariables {
  filter?: VisitationAnalyticsFilter | null;
}
