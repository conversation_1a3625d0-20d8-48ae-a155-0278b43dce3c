/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealUrineDipstickAdditionalNote
// ====================================================

export interface ConcealUrineDipstickAdditionalNote_concealUrineDipstickAdditionalNote {
  __typename: "UrineDipstickModel";
  id: string;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
}

export interface ConcealUrineDipstickAdditionalNote {
  concealUrineDipstickAdditionalNote: ConcealUrineDipstickAdditionalNote_concealUrineDipstickAdditionalNote;
}

export interface ConcealUrineDipstickAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
  clinifyId: string;
}
