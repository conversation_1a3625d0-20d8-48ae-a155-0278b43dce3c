/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealAntenatalTreatmentPlan
// ====================================================

export interface ConcealAntenatalTreatmentPlan_concealAntenatalTreatmentPlan {
  __typename: "AntenatalDetailsModel";
  id: string;
  treatmentPlan: string | null;
  concealTreatmentPlan: boolean | null;
}

export interface ConcealAntenatalTreatmentPlan {
  concealAntenatalTreatmentPlan: ConcealAntenatalTreatmentPlan_concealAntenatalTreatmentPlan;
}

export interface ConcealAntenatalTreatmentPlanVariables {
  id: string;
  concealStatus: boolean;
}
