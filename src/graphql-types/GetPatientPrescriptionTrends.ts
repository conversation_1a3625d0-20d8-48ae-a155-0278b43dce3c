/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MedsHistoryFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPatientPrescriptionTrends
// ====================================================

export interface GetPatientPrescriptionTrends_getPatientMedsHistory_list {
  __typename: "PVH_MedicationResponse";
  duration: string | null;
  dosage: string | null;
  dosageUnit: string | null;
  prescribedBy: string;
  administrationMethod: string | null;
  name: string[] | null;
  frequency: string | null;
  consumableName: string[] | null;
  prescriptionDate: any;
}

export interface GetPatientPrescriptionTrends_getPatientMedsHistory {
  __typename: "PatientMedHistoryResponse";
  totalCount: number;
  list: GetPatientPrescriptionTrends_getPatientMedsHistory_list[];
}

export interface GetPatientPrescriptionTrends {
  getPatientMedsHistory: GetPatientPrescriptionTrends_getPatientMedsHistory;
}

export interface GetPatientPrescriptionTrendsVariables {
  filter: MedsHistoryFilter;
}
