/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UpdateDispenseRegisterInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateMedicationDispenseRegister
// ====================================================

export interface UpdateMedicationDispenseRegister_updateMedicationDispenseRegister_details_periods_audits {
  __typename: "dospenseRegisterAudit";
  fullName: string | null;
  dateTime: any | null;
  desc: string | null;
  checkId: number | null;
  profileId: string | null;
}

export interface UpdateMedicationDispenseRegister_updateMedicationDispenseRegister_details_periods {
  __typename: "PeriodsDetails";
  no: number | null;
  values: string | null;
  count: number | null;
  audits: UpdateMedicationDispenseRegister_updateMedicationDispenseRegister_details_periods_audits[] | null;
}

export interface UpdateMedicationDispenseRegister_updateMedicationDispenseRegister_details {
  __typename: "dispenseRegisterDetails";
  medicationName: string;
  reference: string | null;
  periodName: string;
  periods: UpdateMedicationDispenseRegister_updateMedicationDispenseRegister_details_periods[];
}

export interface UpdateMedicationDispenseRegister_updateMedicationDispenseRegister {
  __typename: "DispenseRegistersModel";
  id: string;
  details: UpdateMedicationDispenseRegister_updateMedicationDispenseRegister_details | null;
  creator: string | null;
  updater: string | null;
  updatedDate: any;
  createdDate: any;
}

export interface UpdateMedicationDispenseRegister {
  updateMedicationDispenseRegister: UpdateMedicationDispenseRegister_updateMedicationDispenseRegister;
}

export interface UpdateMedicationDispenseRegisterVariables {
  input: UpdateDispenseRegisterInput;
  id: string;
  pin?: string | null;
}
