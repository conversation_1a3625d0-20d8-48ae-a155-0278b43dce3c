/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { RevokeOrganizationPermissionInput, Subject, Action } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: RevokeOrganizationPermission
// ====================================================

export interface RevokeOrganizationPermission_revokeOrganizationPermission_rules {
  __typename: "PermissionModelInput";
  subject: Subject;
  action: Action;
}

export interface RevokeOrganizationPermission_revokeOrganizationPermission {
  __typename: "PermissionModel";
  id: string;
  rules: RevokeOrganizationPermission_revokeOrganizationPermission_rules[] | null;
}

export interface RevokeOrganizationPermission {
  revokeOrganizationPermission: RevokeOrganizationPermission_revokeOrganizationPermission;
}

export interface RevokeOrganizationPermissionVariables {
  input: RevokeOrganizationPermissionInput;
}
