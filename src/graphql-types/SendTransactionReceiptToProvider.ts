/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { EmailTransactionReceiptInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: SendTransactionReceiptToProvider
// ====================================================

export interface SendTransactionReceiptToProvider_sendTransactionReceiptToProvider {
  __typename: "TransactionReceiptSentResponse";
  sent: boolean;
  email: string;
  id: string;
}

export interface SendTransactionReceiptToProvider {
  sendTransactionReceiptToProvider: SendTransactionReceiptToProvider_sendTransactionReceiptToProvider[];
}

export interface SendTransactionReceiptToProviderVariables {
  input: EmailTransactionReceiptInput[];
  origin: string;
}
