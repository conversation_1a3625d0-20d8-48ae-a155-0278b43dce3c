/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: UpdateEmployeesCoveredInInvoice
// ====================================================

export interface UpdateEmployeesCoveredInInvoice_updateEmployeesDetails_employeesDetails_dependents {
  __typename: "DependentInfo";
  fullName: string;
  enrolleeId: string | null;
  relationship: string | null;
}

export interface UpdateEmployeesCoveredInInvoice_updateEmployeesDetails_employeesDetails {
  __typename: "EmployeesInfo";
  enrolleeId: string | null;
  fullName: string;
  planCategory: string | null;
  planType: string | null;
  planAmount: number | null;
  paymentFrequency: string | null;
  planStartDate: any | null;
  planDueDate: any | null;
  planStatus: string | null;
  isCovered: boolean | null;
  dependents: UpdateEmployeesCoveredInInvoice_updateEmployeesDetails_employeesDetails_dependents[] | null;
}

export interface UpdateEmployeesCoveredInInvoice_updateEmployeesDetails {
  __typename: "InvoiceModel";
  id: string;
  employeesDetails: UpdateEmployeesCoveredInInvoice_updateEmployeesDetails_employeesDetails[] | null;
}

export interface UpdateEmployeesCoveredInInvoice {
  updateEmployeesDetails: UpdateEmployeesCoveredInInvoice_updateEmployeesDetails;
}

export interface UpdateEmployeesCoveredInInvoiceVariables {
  id: string;
  enrolleeIds: string[];
  isCovered: boolean;
}
