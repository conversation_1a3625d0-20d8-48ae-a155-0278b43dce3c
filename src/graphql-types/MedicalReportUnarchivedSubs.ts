/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MedicalReportStatus } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: MedicalReportUnarchivedSubs
// ====================================================

export interface MedicalReportUnarchivedSubs_MedicalReportUnarchived_updatedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface MedicalReportUnarchivedSubs_MedicalReportUnarchived_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface MedicalReportUnarchivedSubs_MedicalReportUnarchived_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface MedicalReportUnarchivedSubs_MedicalReportUnarchived_reportType {
  __typename: "ReportTypeInputType";
  ref: string | null;
  name: string;
  itemId: string | null;
}

export interface MedicalReportUnarchivedSubs_MedicalReportUnarchived_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface MedicalReportUnarchivedSubs_MedicalReportUnarchived_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface MedicalReportUnarchivedSubs_MedicalReportUnarchived {
  __typename: "MedicalReportModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  updatedBy: MedicalReportUnarchivedSubs_MedicalReportUnarchived_updatedBy | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
  createdBy: MedicalReportUnarchivedSubs_MedicalReportUnarchived_createdBy;
  creatorId: string | null;
  creatorName: string | null;
  profile: MedicalReportUnarchivedSubs_MedicalReportUnarchived_profile | null;
  reportDate: any | null;
  reportType: MedicalReportUnarchivedSubs_MedicalReportUnarchived_reportType[] | null;
  patientClinifyId: string | null;
  patientFullname: string | null;
  patientPhone: string | null;
  patientEmail: string | null;
  report: string[] | null;
  rank: string | null;
  department: string | null;
  doctorName: string | null;
  specialty: string | null;
  serviceDetails: MedicalReportUnarchivedSubs_MedicalReportUnarchived_serviceDetails[] | null;
  facilityName: string | null;
  facilityAddress: string | null;
  additionalNote: string | null;
  documentUrl: string[] | null;
  hospital: MedicalReportUnarchivedSubs_MedicalReportUnarchived_hospital | null;
  hospitalId: string | null;
  status: MedicalReportStatus | null;
}

export interface MedicalReportUnarchivedSubs {
  MedicalReportUnarchived: MedicalReportUnarchivedSubs_MedicalReportUnarchived;
}

export interface MedicalReportUnarchivedSubsVariables {
  hospitalId: string;
}
