/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Action, Subject } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: organizationPermissionRevokedSubs
// ====================================================

export interface organizationPermissionRevokedSubs_OrganizationPermissionRevoked_rules_conditions {
  __typename: "ConditionModel";
  organizationId: string | null;
}

export interface organizationPermissionRevokedSubs_OrganizationPermissionRevoked_rules {
  __typename: "PermissionModelInput";
  action: Action;
  subject: Subject;
  conditions: organizationPermissionRevokedSubs_OrganizationPermissionRevoked_rules_conditions | null;
}

export interface organizationPermissionRevokedSubs_OrganizationPermissionRevoked {
  __typename: "PermissionModel";
  id: string;
  rules: organizationPermissionRevokedSubs_OrganizationPermissionRevoked_rules[] | null;
}

export interface organizationPermissionRevokedSubs {
  OrganizationPermissionRevoked: organizationPermissionRevokedSubs_OrganizationPermissionRevoked;
}

export interface organizationPermissionRevokedSubsVariables {
  recipientId: string;
}
