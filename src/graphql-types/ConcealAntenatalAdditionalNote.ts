/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealAntenatalAdditionalNote
// ====================================================

export interface ConcealAntenatalAdditionalNote_concealAntenatalAdditionalNote {
  __typename: "AntenatalModel";
  id: string;
  concealAdditionalNote: boolean | null;
  additionalNote: string | null;
}

export interface ConcealAntenatalAdditionalNote {
  concealAntenatalAdditionalNote: ConcealAntenatalAdditionalNote_concealAntenatalAdditionalNote;
}

export interface ConcealAntenatalAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
}
