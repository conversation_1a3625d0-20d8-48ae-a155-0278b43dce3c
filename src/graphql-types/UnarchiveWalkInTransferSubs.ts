/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: UnarchiveWalkInTransferSubs
// ====================================================

export interface UnarchiveWalkInTransferSubs_WalkInTransferUnarchived_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface UnarchiveWalkInTransferSubs_WalkInTransferUnarchived {
  __typename: "WalkInTransferModel";
  id: string;
  transferDateTime: any | null;
  transferredBy: string | null;
  transferReason: string | null;
  concealTransferReason: boolean | null;
  transferFacilityName: string | null;
  transferFacilityAddress: string | null;
  documentUrl: string[] | null;
  patientInformation: UnarchiveWalkInTransferSubs_WalkInTransferUnarchived_patientInformation | null;
}

export interface UnarchiveWalkInTransferSubs {
  WalkInTransferUnarchived: UnarchiveWalkInTransferSubs_WalkInTransferUnarchived[];
}

export interface UnarchiveWalkInTransferSubsVariables {
  hospitalId: string;
}
