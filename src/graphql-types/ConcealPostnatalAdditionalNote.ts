/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealPostnatalAdditionalNote
// ====================================================

export interface ConcealPostnatalAdditionalNote_concealPostnatalAdditionalNote {
  __typename: "PostnatalModel";
  id: string;
  concealAdditionalNote: boolean | null;
  additionalNote: string | null;
}

export interface ConcealPostnatalAdditionalNote {
  concealPostnatalAdditionalNote: ConcealPostnatalAdditionalNote_concealPostnatalAdditionalNote;
}

export interface ConcealPostnatalAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
}
