/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { VirtualAccountTransactionType, TransactionStatus, BillStatus, InvoiceStatus } from "./globalTypes";

// ====================================================
// GraphQL fragment: BankAccountTransactionFields
// ====================================================

export interface BankAccountTransactionFields_sender {
  __typename: "BankAccountDetails";
  name: string;
  bankName: string | null;
  bankCode: string | null;
  accountNumber: string;
}

export interface BankAccountTransactionFields_receiver {
  __typename: "BankAccountDetails";
  name: string;
  accountNumber: string;
  bankName: string | null;
  bankCode: string | null;
}

export interface BankAccountTransactionFields_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface BankAccountTransactionFields_bill {
  __typename: "BillModel";
  id: string;
  billingDateTime: any | null;
  billStatus: BillStatus;
}

export interface BankAccountTransactionFields_invoice_recipient {
  __typename: "InvoiceRecipient";
  name: string;
  clinifyId: string | null;
  email: string | null;
  address: string | null;
  phone: string | null;
}

export interface BankAccountTransactionFields_invoice_invoicePayments {
  __typename: "InvoicePaymentModel";
  id: string;
  paymentStatus: string;
  amountPaid: number;
}

export interface BankAccountTransactionFields_invoice {
  __typename: "InvoiceModel";
  id: string;
  issueDate: any;
  invoiceStatus: InvoiceStatus;
  invoiceReference: string;
  recipient: BankAccountTransactionFields_invoice_recipient;
  invoicePayments: BankAccountTransactionFields_invoice_invoicePayments[] | null;
}

export interface BankAccountTransactionFields {
  __typename: "BankAccountTransactionModel";
  id: string;
  transactionDateTime: any;
  description: string | null;
  reference: string;
  amount: number;
  transactionType: VirtualAccountTransactionType;
  transactionStatus: TransactionStatus;
  sender: BankAccountTransactionFields_sender;
  receiver: BankAccountTransactionFields_receiver;
  hospital: BankAccountTransactionFields_hospital | null;
  bill: BankAccountTransactionFields_bill | null;
  invoice: BankAccountTransactionFields_invoice | null;
}
