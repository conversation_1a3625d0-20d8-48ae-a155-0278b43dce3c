/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HmosAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHmoPreauthorizationsData
// ====================================================

export interface GetHmoPreauthorizationsData_getHmoPreauthorizationsData_summary {
  __typename: "HmoPreauthorizationSummary";
  name: number | null;
  hmoProvider: string | null;
  hospitalName: string | null;
  totalEnrollees: number;
  totalEnrolleeVisitations: number;
  totalActiveEnrollees: number;
  totalInactiveEnrollees: number;
  totalPreauthorizations: number;
  totalPendingPreauthorizations: number;
  totalApprovedPreauthorizations: number;
  totalRejectedPreauthorizations: number;
  totalPreauthorizationAmount: string | null;
  totalApprovedPreauthorizationAmount: string | null;
  totalRejectedPreauthorizationAmount: string | null;
  totalPendingPreauthorizationAmount: string | null;
  totalReferrals: number | null;
  totalReferralsAmount: number | null;
  totalPendingReferrals: number | null;
  totalPendingReferralsAmount: number | null;
  totalApprovedReferrals: number | null;
  totalApprovedReferralsAmount: number | null;
  totalRejectedReferrals: number | null;
  totalRejectedReferralsAmount: number | null;
  totalFlaggedPreauthorizations: number | null;
  totalFlaggedPreauthorizationsAmount: string | null;
  totalClaimConfirmationApprovedPreauthorizations: number | null;
  totalClaimConfirmationApprovedPreauthorizationsAmount: number | null;
  totalClaimOfficerApprovedPreauthorizations: number | null;
  totalClaimOfficerApprovedPreauthorizationsAmount: number | null;
  totalClaimOfficerHODApprovedPreauthorizations: number | null;
  totalClaimOfficerHODApprovedPreauthorizationsAmount: number | null;
  totalClaimReviewerApprovedPreauthorizations: number | null;
  totalClaimReviewerApprovedPreauthorizationsAmount: number | null;
  totalClaimReviewerHODApprovedPreauthorizations: number | null;
  totalClaimReviewerHODApprovedPreauthorizationsAmount: number | null;
  totalClaimAuditApprovedPreauthorizations: number | null;
  totalClaimAuditApprovedPreauthorizationsAmount: number | null;
  totalClaimAuditHODApprovedPreauthorizations: number | null;
  totalClaimAuditHODApprovedPreauthorizationsAmount: number | null;
  totalClaimAdminApprovedPreauthorizations: number | null;
  totalClaimAdminApprovedPreauthorizationsAmount: number | null;
  totalClaimFinanceApprovedPreauthorizations: number | null;
  totalClaimFinanceApprovedPreauthorizationsAmount: number | null;
  totalClaimAccountApprovedPreauthorizations: number | null;
  totalClaimAccountApprovedPreauthorizationsAmount: number | null;
  totalClaimAgentApprovedPreauthorizations: number | null;
  totalClaimAgentApprovedPreauthorizationsAmount: number | null;
  totalClaimAgentHODApprovedPreauthorizations: number | null;
  totalClaimAgentHODApprovedPreauthorizationsAmount: number | null;
  totalClaimConfirmationRejectedPreauthorizations: number | null;
  totalClaimConfirmationRejectedPreauthorizationsAmount: number | null;
  totalClaimOfficerRejectedPreauthorizations: number | null;
  totalClaimOfficerRejectedPreauthorizationsAmount: number | null;
  totalClaimOfficerHODRejectedPreauthorizations: number | null;
  totalClaimOfficerHODRejectedPreauthorizationsAmount: number | null;
  totalClaimReviewerRejectedPreauthorizations: number | null;
  totalClaimReviewerRejectedPreauthorizationsAmount: number | null;
  totalClaimReviewerHODRejectedPreauthorizations: number | null;
  totalClaimReviewerHODRejectedPreauthorizationsAmount: number | null;
  totalClaimAuditRejectedPreauthorizations: number | null;
  totalClaimAuditRejectedPreauthorizationsAmount: number | null;
  totalClaimAuditHODRejectedPreauthorizations: number | null;
  totalClaimAuditHODRejectedPreauthorizationsAmount: number | null;
  totalClaimAdminRejectedPreauthorizations: number | null;
  totalClaimAdminRejectedPreauthorizationsAmount: number | null;
  totalClaimFinanceRejectedPreauthorizations: number | null;
  totalClaimFinanceRejectedPreauthorizationsAmount: number | null;
  totalClaimAccountRejectedPreauthorizations: number | null;
  totalClaimAccountRejectedPreauthorizationsAmount: number | null;
  totalClaimAgentRejectedPreauthorizations: number | null;
  totalClaimAgentRejectedPreauthorizationsAmount: number | null;
  totalClaimAgentHODRejectedPreauthorizations: number | null;
  totalClaimAgentHODRejectedPreauthorizationsAmount: number | null;
  totalClaimConfirmationFlaggedPreauthorizations: number | null;
  totalClaimConfirmationFlaggedPreauthorizationsAmount: number | null;
  totalClaimOfficerFlaggedPreauthorizations: number | null;
  totalClaimOfficerFlaggedPreauthorizationsAmount: number | null;
  totalClaimOfficerHODFlaggedPreauthorizations: number | null;
  totalClaimOfficerHODFlaggedPreauthorizationsAmount: number | null;
  totalClaimReviewerFlaggedPreauthorizations: number | null;
  totalClaimReviewerFlaggedPreauthorizationsAmount: number | null;
  totalClaimReviewerHODFlaggedPreauthorizations: number | null;
  totalClaimReviewerHODFlaggedPreauthorizationsAmount: number | null;
  totalClaimAuditFlaggedPreauthorizations: number | null;
  totalClaimAuditFlaggedPreauthorizationsAmount: number | null;
  totalClaimAuditHODFlaggedPreauthorizations: number | null;
  totalClaimAuditHODFlaggedPreauthorizationsAmount: number | null;
  totalClaimAdminFlaggedPreauthorizations: number | null;
  totalClaimAdminFlaggedPreauthorizationsAmount: number | null;
  totalClaimFinanceFlaggedPreauthorizations: number | null;
  totalClaimFinanceFlaggedPreauthorizationsAmount: number | null;
  totalClaimAccountFlaggedPreauthorizations: number | null;
  totalClaimAccountFlaggedPreauthorizationsAmount: number | null;
  totalClaimAgentFlaggedPreauthorizations: number | null;
  totalClaimAgentFlaggedPreauthorizationsAmount: number | null;
  totalClaimAgentHODFlaggedPreauthorizations: number | null;
  totalClaimAgentHODFlaggedPreauthorizationsAmount: number | null;
}

export interface GetHmoPreauthorizationsData_getHmoPreauthorizationsData_detailedData {
  __typename: "HmoPreauthorizationDetailedData";
  hospitalName: string | null;
  hmoProvider: string | null;
  treatmentDateTime: string | null;
  treatmentStartDate: string | null;
  treatmentEndDate: string | null;
  visitationType: string | null;
  enrolleeNumber: string | null;
  enrolleeName: string | null;
  serviceTypeName: string | null;
  preauthorizationStatus: string | null;
  preauthorizationCode: string | null;
  totalQuantity: string | null;
  amountRequested: string | null;
  requestedBy: string | null;
  amountApproved: string | null;
  approvedBy: string | null;
  amountRejected: string | null;
  rejectedBy: string | null;
  clinicalDiagnosis: string | null;
  causeOfDeath: string | null;
  timeOfDeath: string | null;
  birthCount: string | null;
  deliveryDateTime: string | null;
  age: string | null;
  gender: string | null;
  memberPlan: string | null;
  memberPlanGroup: string | null;
  memberPlanSubGroup: string | null;
  lga: string | null;
  ward: string | null;
  responseDateTime: any | null;
  createdDate: any | null;
}

export interface GetHmoPreauthorizationsData_getHmoPreauthorizationsData_referralData {
  __typename: "HmoReferralDetailedData";
  treatmentDateTime: string | null;
  enrolleeNumber: string | null;
  enrolleeName: string | null;
  referralCode: string | null;
  referralStatus: string | null;
  referredBy: string | null;
  referringFacility: string | null;
  referredToFacility: string | null;
  visitType: string | null;
  priority: string | null;
  clinicalDiagnosis: string | null;
  approvedBy: string | null;
  rejectedBy: string | null;
  age: string | null;
  gender: string | null;
  memberPlan: string | null;
  memberPlanGroup: string | null;
  memberPlanSubGroup: string | null;
  providerReferralRemarks: string | null;
  lga: string | null;
  ward: string | null;
}

export interface GetHmoPreauthorizationsData_getHmoPreauthorizationsData_automaticAndManualData_byRoles {
  __typename: "SummaryCount";
  name: string | null;
  count: number | null;
  manualCount: number | null;
}

export interface GetHmoPreauthorizationsData_getHmoPreauthorizationsData_automaticAndManualData_byStaffs {
  __typename: "SummaryCount";
  name: string | null;
  count: number | null;
  type: string | null;
  manualCount: number | null;
}

export interface GetHmoPreauthorizationsData_getHmoPreauthorizationsData_automaticAndManualData {
  __typename: "AutomaticAndManualHmoClaimsData";
  totalAutomated: number | null;
  totalManual: number | null;
  byRoles: GetHmoPreauthorizationsData_getHmoPreauthorizationsData_automaticAndManualData_byRoles[] | null;
  byStaffs: GetHmoPreauthorizationsData_getHmoPreauthorizationsData_automaticAndManualData_byStaffs[] | null;
}

export interface GetHmoPreauthorizationsData_getHmoPreauthorizationsData_deathAgeRanges {
  __typename: "CategoryCount";
  count: number | null;
  category: string | null;
}

export interface GetHmoPreauthorizationsData_getHmoPreauthorizationsData {
  __typename: "HmoPreauthorizationDataResponse";
  summary: GetHmoPreauthorizationsData_getHmoPreauthorizationsData_summary[] | null;
  detailedData: GetHmoPreauthorizationsData_getHmoPreauthorizationsData_detailedData[] | null;
  referralData: GetHmoPreauthorizationsData_getHmoPreauthorizationsData_referralData[] | null;
  automaticAndManualData: GetHmoPreauthorizationsData_getHmoPreauthorizationsData_automaticAndManualData | null;
  totalClaimOfficerStaffCount: number | null;
  totalClaimOfficerHODStaffCount: number | null;
  totalClaimReviewerStaffCount: number | null;
  totalClaimReviewerHODStaffCount: number | null;
  totalClaimAuditStaffCount: number | null;
  totalClaimAuditHODStaffCount: number | null;
  totalClaimAdminStaffCount: number | null;
  totalClaimFinanceStaffCount: number | null;
  totalClaimAccountStaffCount: number | null;
  totalClaimAgentStaffCount: number | null;
  totalClaimAgentHODStaffCount: number | null;
  totalClaimConfirmationStaffCount: number | null;
  totalMaleDeath: number | null;
  totalFemaleDeath: number | null;
  deathAgeRanges: GetHmoPreauthorizationsData_getHmoPreauthorizationsData_deathAgeRanges[] | null;
}

export interface GetHmoPreauthorizationsData {
  getHmoPreauthorizationsData: GetHmoPreauthorizationsData_getHmoPreauthorizationsData;
}

export interface GetHmoPreauthorizationsDataVariables {
  filter?: HmosAnalyticsFilter | null;
}
