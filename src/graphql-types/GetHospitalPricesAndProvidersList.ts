/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PricesFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHospitalPricesAndProvidersList
// ====================================================

export interface GetHospitalPricesAndProvidersList_hospital_providers {
  __typename: "HospitalProviders";
  id: string | null;
  name: string | null;
  code: string | null;
  createdOn: any | null;
  creatorName: string | null;
}

export interface GetHospitalPricesAndProvidersList_hospital_services {
  __typename: "HospitalService";
  id: string | null;
  name: string | null;
  createdOn: any | null;
  updatedOn: any | null;
  description: string | null;
  creatorName: string | null;
}

export interface GetHospitalPricesAndProvidersList_hospital_prices_list {
  __typename: "PricesModel";
  id: string;
  name: string | null;
  code: string | null;
  category: string | null;
  price: string | null;
  aliasCode: string | null;
  serviceType: string | null;
  providerCode: string | null;
  aliasName: string | null;
}

export interface GetHospitalPricesAndProvidersList_hospital_prices {
  __typename: "PriceResponse";
  totalCount: number;
  list: GetHospitalPricesAndProvidersList_hospital_prices_list[];
}

export interface GetHospitalPricesAndProvidersList_hospital {
  __typename: "HospitalModel";
  id: string;
  providers: GetHospitalPricesAndProvidersList_hospital_providers[] | null;
  services: GetHospitalPricesAndProvidersList_hospital_services[] | null;
  /**
   * Get Hospital Prices
   */
  prices: GetHospitalPricesAndProvidersList_hospital_prices;
}

export interface GetHospitalPricesAndProvidersList {
  hospital: GetHospitalPricesAndProvidersList_hospital;
}

export interface GetHospitalPricesAndProvidersListVariables {
  filterOptions?: PricesFilterInput | null;
}
