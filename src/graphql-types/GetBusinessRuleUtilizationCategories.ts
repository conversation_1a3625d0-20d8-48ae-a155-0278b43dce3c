/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BusinessRuleFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetBusinessRuleUtilizationCategories
// ====================================================

export interface GetBusinessRuleUtilizationCategories {
  getBusinessRuleUtilizationCategories: string[];
}

export interface GetBusinessRuleUtilizationCategoriesVariables {
  filterOptions: BusinessRuleFilterInput;
}
