/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealTemperatureAdditionalNote
// ====================================================

export interface ConcealTemperatureAdditionalNote_concealTemperatureAdditionalNote {
  __typename: "TemperatureModel";
  id: string;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
}

export interface ConcealTemperatureAdditionalNote {
  concealTemperatureAdditionalNote: ConcealTemperatureAdditionalNote_concealTemperatureAdditionalNote;
}

export interface ConcealTemperatureAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
  clinifyId: string;
}
