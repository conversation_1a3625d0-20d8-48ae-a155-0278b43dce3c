/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealLabourAndDeliveryThirdStageAdditionalNote
// ====================================================

export interface ConcealLabourAndDeliveryThirdStageAdditionalNote_concealLabourAndDeliveryThirdStageAdditionalNote {
  __typename: "LabourDeliveryModel";
  id: string;
  concealThirdStageAdditionalNote: boolean | null;
  thirdStageAdditionalNote: string | null;
}

export interface ConcealLabourAndDeliveryThirdStageAdditionalNote {
  concealLabourAndDeliveryThirdStageAdditionalNote: ConcealLabourAndDeliveryThirdStageAdditionalNote_concealLabourAndDeliveryThirdStageAdditionalNote;
}

export interface ConcealLabourAndDeliveryThirdStageAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
}
