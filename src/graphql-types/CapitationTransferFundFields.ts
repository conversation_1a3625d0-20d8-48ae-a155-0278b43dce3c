/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FundTransactionStatus, PayoutCommissionPayer } from "./globalTypes";

// ====================================================
// GraphQL fragment: CapitationTransferFundFields
// ====================================================

export interface CapitationTransferFundFields_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
  clinifyId: string;
  title: string | null;
}

export interface CapitationTransferFundFields_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface CapitationTransferFundFields_auditApproval {
  __typename: "ClaimsAccountApprovalType";
  createdDate: any;
  creatorName: string;
  approvalGroup: string;
  creatorId: string;
}

export interface CapitationTransferFundFields_hmoPlanType {
  __typename: "HmoPlanTypeModel";
  id: string;
  name: string | null;
}

export interface CapitationTransferFundFields_detailsByPlanType {
  __typename: "CapitationDetailByPlanType";
  enrolleeCount: number;
  planType: string;
  planTypeName: string;
  totalCapitationAmount: number;
  payoutDecreasePercentage: number | null;
}

export interface CapitationTransferFundFields {
  __typename: "TransferFundModel";
  id: string;
  createdBy: CapitationTransferFundFields_createdBy | null;
  amount: number;
  createdDate: any;
  updatedDate: any;
  destinationAccountName: string | null;
  destinationAccountNumber: string | null;
  destinationBankCode: string | null;
  destinationBankName: string | null;
  narration: string | null;
  originatorName: string | null;
  sourceAccountNumber: string | null;
  transferReference: string | null;
  transferStatus: FundTransactionStatus;
  additionalNote: string | null;
  serviceChargeAmount: number | null;
  hospitalId: string | null;
  hmoPlanTypeId: string | null;
  hospital: CapitationTransferFundFields_hospital | null;
  enrolleeCount: number | null;
  isEnrolleePayout: boolean | null;
  totalCapitationAmount: number | null;
  auditApproval: CapitationTransferFundFields_auditApproval[] | null;
  hmoPlanType: CapitationTransferFundFields_hmoPlanType | null;
  detailsByPlanType: CapitationTransferFundFields_detailsByPlanType[] | null;
  payoutCommissionPayer: PayoutCommissionPayer | null;
  payoutDecreasePercentage: number | null;
  enrolleeCommissionAmount: number | null;
}
