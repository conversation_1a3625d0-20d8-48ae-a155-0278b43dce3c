/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcelBloodPressureAdditionalNote
// ====================================================

export interface ConcelBloodPressureAdditionalNote_concealBloodPressureAdditionalNote {
  __typename: "BloodPressureModel";
  id: string;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
}

export interface ConcelBloodPressureAdditionalNote {
  concealBloodPressureAdditionalNote: ConcelBloodPressureAdditionalNote_concealBloodPressureAdditionalNote;
}

export interface ConcelBloodPressureAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
  clinifyId: string;
}
