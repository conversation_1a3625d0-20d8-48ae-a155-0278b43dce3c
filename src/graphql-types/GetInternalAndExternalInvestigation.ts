/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ServicesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetInternalAndExternalInvestigation
// ====================================================

export interface GetInternalAndExternalInvestigation_getInternalAndExternalInvestigation_servicesSummary {
  __typename: "ServicesSummary";
  name: number | null;
  totalInternalInvestigations: number | null;
  totalExternalInvestigations: number | null;
  totalInternalInvestigationsQuantity: number | null;
  totalExternalInvestigationsQuantity: number | null;
  totalExternalInvestigationsAmount: number | null;
  totalInternalInvestigationsAmount: number | null;
}

export interface GetInternalAndExternalInvestigation_getInternalAndExternalInvestigation_list_byInternalAndExternalInvestigation {
  __typename: "ListByPatientName";
  patientName: string | null;
  examinationDate: string | null;
  name: string;
  radiographerName: string | null;
  totalAmount: number | null;
  clinifyId: string | null;
  quantity: string | null;
  paymentType: string | null;
  testDate: string | null;
  performedBy: string | null;
  isExternal: boolean | null;
}

export interface GetInternalAndExternalInvestigation_getInternalAndExternalInvestigation_list {
  __typename: "ServiceSummaryList";
  byInternalAndExternalInvestigation: GetInternalAndExternalInvestigation_getInternalAndExternalInvestigation_list_byInternalAndExternalInvestigation[] | null;
}

export interface GetInternalAndExternalInvestigation_getInternalAndExternalInvestigation {
  __typename: "ServiceSummaryWithList";
  servicesSummary: GetInternalAndExternalInvestigation_getInternalAndExternalInvestigation_servicesSummary[];
  list: GetInternalAndExternalInvestigation_getInternalAndExternalInvestigation_list;
}

export interface GetInternalAndExternalInvestigation {
  getInternalAndExternalInvestigation: GetInternalAndExternalInvestigation_getInternalAndExternalInvestigation;
}

export interface GetInternalAndExternalInvestigationVariables {
  filter?: ServicesAnalyticsFilter | null;
}
