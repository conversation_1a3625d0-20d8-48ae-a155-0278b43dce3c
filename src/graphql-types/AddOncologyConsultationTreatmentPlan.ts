/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { OncologyTreatmentPlanInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddOncologyConsultationTreatmentPlan
// ====================================================

export interface AddOncologyConsultationTreatmentPlan_addOncologyConsultationTreatmentPlan {
  __typename: "OncologyTreatmentPlanModel";
  id: string;
  treatmentPlan: string | null;
  conceal: boolean | null;
  patientAdmitted: string | null;
  concealObservationNote: boolean | null;
  observationNote: string | null;
  admissionConsent: string | null;
  adverseEffectsFollowingTreatment: string | null;
  stateEffects: string | null;
  adverseEffectsInvestigated: string | null;
  outcomeOfInvestigation: string | null;
  treatmentGiven: string | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
  treatmentStatus: string | null;
}

export interface AddOncologyConsultationTreatmentPlan {
  addOncologyConsultationTreatmentPlan: AddOncologyConsultationTreatmentPlan_addOncologyConsultationTreatmentPlan;
}

export interface AddOncologyConsultationTreatmentPlanVariables {
  input: OncologyTreatmentPlanInput;
  id: string;
  clinifyId: string;
  pin?: string | null;
}
