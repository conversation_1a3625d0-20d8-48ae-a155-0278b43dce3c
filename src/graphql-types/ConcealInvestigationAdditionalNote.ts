/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealInvestigationAdditionalNote
// ====================================================

export interface ConcealInvestigationAdditionalNote_concealInvestigationAdditionalNote {
  __typename: "AdditionalNoteModel";
  id: string;
  additionalNote: string | null;
  conceal: boolean | null;
}

export interface ConcealInvestigationAdditionalNote {
  concealInvestigationAdditionalNote: ConcealInvestigationAdditionalNote_concealInvestigationAdditionalNote;
}

export interface ConcealInvestigationAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
}
