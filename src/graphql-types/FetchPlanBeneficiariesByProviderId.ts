/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HmoPlanBeneficiariesFilterOptions } from "./globalTypes";

// ====================================================
// GraphQL query operation: FetchPlanBeneficiariesByProviderId
// ====================================================

export interface FetchPlanBeneficiariesByProviderId_fetchPlanBeneficiariesByProviderId_list_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface FetchPlanBeneficiariesByProviderId_fetchPlanBeneficiariesByProviderId_list {
  __typename: "HmoProfileModel";
  id: string;
  memberNumber: string | null;
  profile: FetchPlanBeneficiariesByProviderId_fetchPlanBeneficiariesByProviderId_list_profile | null;
}

export interface FetchPlanBeneficiariesByProviderId_fetchPlanBeneficiariesByProviderId {
  __typename: "HmoPlanBeneficiariesResponse";
  list: FetchPlanBeneficiariesByProviderId_fetchPlanBeneficiariesByProviderId_list[];
  totalCount: number;
}

export interface FetchPlanBeneficiariesByProviderId {
  fetchPlanBeneficiariesByProviderId: FetchPlanBeneficiariesByProviderId_fetchPlanBeneficiariesByProviderId;
}

export interface FetchPlanBeneficiariesByProviderIdVariables {
  providerId: string;
  filterOptions?: HmoPlanBeneficiariesFilterOptions | null;
}
