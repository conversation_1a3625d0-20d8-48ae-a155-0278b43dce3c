/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetHmoCapitatedPaymentSummary
// ====================================================

export interface GetHmoCapitatedPaymentSummary_getHmoCapitatedPaymentSummary {
  __typename: "HmoCapitatedPaymentSummary";
  totalCapitationAmount: number | null;
  totalEnrollees: number | null;
  enrolleeCapitationAmount: number | null;
  planTypeName: string | null;
}

export interface GetHmoCapitatedPaymentSummary {
  getHmoCapitatedPaymentSummary: GetHmoCapitatedPaymentSummary_getHmoCapitatedPaymentSummary[];
}

export interface GetHmoCapitatedPaymentSummaryVariables {
  hmoPlanTypeId?: string | null;
  startDate: any;
  endDate: any;
}
