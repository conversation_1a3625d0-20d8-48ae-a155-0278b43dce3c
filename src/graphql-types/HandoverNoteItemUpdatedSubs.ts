/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UserType } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: HandoverNoteItemUpdatedSubs
// ====================================================

export interface HandoverNoteItemUpdatedSubs_HandoverNoteItemUpdated_patientInformation {
  __typename: "PatientInformation";
  fullName: string;
  clinifyId: string | null;
  phone: string | null;
  email: string | null;
}

export interface HandoverNoteItemUpdatedSubs_HandoverNoteItemUpdated {
  __typename: "HandoverNoteItemModel";
  id: string;
  createdDate: any;
  updatedDate: any | null;
  admissionWard: string | null;
  patientProfileId: string;
  patientInformation: HandoverNoteItemUpdatedSubs_HandoverNoteItemUpdated_patientInformation;
  note: string | null;
  priority: string | null;
  handoverNoteId: string;
  creatorId: string;
  lastModifierName: string | null;
}

export interface HandoverNoteItemUpdatedSubs {
  HandoverNoteItemUpdated: HandoverNoteItemUpdatedSubs_HandoverNoteItemUpdated;
}

export interface HandoverNoteItemUpdatedSubsVariables {
  profileId: string;
  hospitalId?: string | null;
  userType: UserType;
}
