/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { VTEAndBleedingRiskInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateVTEAndBleedingRiskAssessment
// ====================================================

export interface UpdateVTEAndBleedingRiskAssessment_updateVTEAndBleedingRiskAssessment_vteAndBleedingRiskAssessment_furtherAssessment {
  __typename: "Assessment";
  bleedingRiskAssessed: boolean | null;
  vteRiskAssessed: boolean | null;
  bleedingRiskAssessedLastModifiedDate: any | null;
  vteRiskAssessedLastModifiedDate: any | null;
  bleedingRiskAssessedLastModifierName: string | null;
  vteRiskAssessedLastModifierName: string | null;
  ref: string | null;
}

export interface UpdateVTEAndBleedingRiskAssessment_updateVTEAndBleedingRiskAssessment_vteAndBleedingRiskAssessment_onAdmission {
  __typename: "Assessment";
  bleedingRiskAssessed: boolean | null;
  vteRiskAssessed: boolean | null;
  bleedingRiskAssessedLastModifiedDate: any | null;
  vteRiskAssessedLastModifiedDate: any | null;
  bleedingRiskAssessedLastModifierName: string | null;
  vteRiskAssessedLastModifierName: string | null;
}

export interface UpdateVTEAndBleedingRiskAssessment_updateVTEAndBleedingRiskAssessment_vteAndBleedingRiskAssessment_within24Hours {
  __typename: "Assessment";
  bleedingRiskAssessed: boolean | null;
  vteRiskAssessed: boolean | null;
  bleedingRiskAssessedLastModifiedDate: any | null;
  vteRiskAssessedLastModifiedDate: any | null;
  bleedingRiskAssessedLastModifierName: string | null;
  vteRiskAssessedLastModifierName: string | null;
}

export interface UpdateVTEAndBleedingRiskAssessment_updateVTEAndBleedingRiskAssessment_vteAndBleedingRiskAssessment {
  __typename: "VTEAndBleedingRisk";
  furtherAssessment: UpdateVTEAndBleedingRiskAssessment_updateVTEAndBleedingRiskAssessment_vteAndBleedingRiskAssessment_furtherAssessment[] | null;
  onAdmission: UpdateVTEAndBleedingRiskAssessment_updateVTEAndBleedingRiskAssessment_vteAndBleedingRiskAssessment_onAdmission | null;
  within24Hours: UpdateVTEAndBleedingRiskAssessment_updateVTEAndBleedingRiskAssessment_vteAndBleedingRiskAssessment_within24Hours | null;
}

export interface UpdateVTEAndBleedingRiskAssessment_updateVTEAndBleedingRiskAssessment {
  __typename: "AdmissionModel";
  id: string;
  vteAndBleedingRiskAssessment: UpdateVTEAndBleedingRiskAssessment_updateVTEAndBleedingRiskAssessment_vteAndBleedingRiskAssessment | null;
}

export interface UpdateVTEAndBleedingRiskAssessment {
  updateVTEAndBleedingRiskAssessment: UpdateVTEAndBleedingRiskAssessment_updateVTEAndBleedingRiskAssessment;
}

export interface UpdateVTEAndBleedingRiskAssessmentVariables {
  id: string;
  input: VTEAndBleedingRiskInput;
}
