/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PreauthorisationReferralInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddPreauthorizationReferral
// ====================================================

export interface AddPreauthorizationReferral_addPreauthorizationReferral_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface AddPreauthorizationReferral_addPreauthorizationReferral_diagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface AddPreauthorizationReferral_addPreauthorizationReferral_utilizations_statusHistory {
  __typename: "ReferalStatusHistoryType";
  status: string;
  creatorName: string;
  createdDate: any;
}

export interface AddPreauthorizationReferral_addPreauthorizationReferral_utilizations {
  __typename: "PreAuthReferralUtilisationsModel";
  id: string;
  category: string | null;
  type: string | null;
  quantity: string | null;
  /**
   * Value is in Naira
   */
  price: string | null;
  status: string | null;
  paCode: string | null;
  utilizationCode: string | null;
  utilizationId: string | null;
  rhesusFactor: string | null;
  specialty: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusHistory: AddPreauthorizationReferral_addPreauthorizationReferral_utilizations_statusHistory[] | null;
}

export interface AddPreauthorizationReferral_addPreauthorizationReferral_referredProvider {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
}

export interface AddPreauthorizationReferral_addPreauthorizationReferral_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface AddPreauthorizationReferral_addPreauthorizationReferral_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  fullName: string;
}

export interface AddPreauthorizationReferral_addPreauthorizationReferral {
  __typename: "PreauthorisationReferralModel";
  id: string;
  requestDateTime: any;
  requestedBy: string | null;
  referredBy: string | null;
  serviceType: string;
  serviceTypeCode: string;
  provider: AddPreauthorizationReferral_addPreauthorizationReferral_provider;
  serviceName: string | null;
  priority: string | null;
  diagnosis: AddPreauthorizationReferral_addPreauthorizationReferral_diagnosis[] | null;
  additionalNote: string | null;
  documentUrl: string[] | null;
  code: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  rank: string | null;
  department: string | null;
  specialty: string | null;
  utilizations: AddPreauthorizationReferral_addPreauthorizationReferral_utilizations[] | null;
  totalQuantity: string;
  grandTotal: string;
  profileId: string | null;
  referralProviderRemark: string | null;
  referredProvider: AddPreauthorizationReferral_addPreauthorizationReferral_referredProvider | null;
  hospital: AddPreauthorizationReferral_addPreauthorizationReferral_hospital | null;
  enrolleeNumber: string | null;
  isExternalPlanType: boolean | null;
  externalPlanTypeId: string | null;
  profile: AddPreauthorizationReferral_addPreauthorizationReferral_profile | null;
}

export interface AddPreauthorizationReferral {
  addPreauthorizationReferral: AddPreauthorizationReferral_addPreauthorizationReferral;
}

export interface AddPreauthorizationReferralVariables {
  input: PreauthorisationReferralInput;
}
