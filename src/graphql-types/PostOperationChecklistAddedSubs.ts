/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: PostOperationChecklistAddedSubs
// ====================================================

export interface PostOperationChecklistAddedSubs_PostOperationChecklistAdded_dietOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface PostOperationChecklistAddedSubs_PostOperationChecklistAdded_ambutationOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface PostOperationChecklistAddedSubs_PostOperationChecklistAdded_urethralCatheterizationOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface PostOperationChecklistAddedSubs_PostOperationChecklistAdded_fluidTherapyOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface PostOperationChecklistAddedSubs_PostOperationChecklistAdded_antiBioticsOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface PostOperationChecklistAddedSubs_PostOperationChecklistAdded_analgesicOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
  title: string | null;
  names: string[] | null;
  isMultiple: boolean | null;
}

export interface PostOperationChecklistAddedSubs_PostOperationChecklistAdded_clexane40mgFor3DaysOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface PostOperationChecklistAddedSubs_PostOperationChecklistAdded_vitalSigns {
  __typename: "VitalSignType";
  timeIn: string | null;
  systolic: string | null;
  diastolic: string | null;
  pulseRate: string | null;
  oxygenSaturation: string | null;
  respiratoryRate: string | null;
  temperature: string | null;
  temperatureUnit: string | null;
  nausea: string | null;
  painScore: string | null;
  state: string | null;
}

export interface PostOperationChecklistAddedSubs_PostOperationChecklistAdded_medications {
  __typename: "MedicationDetailType";
  medicationName: string | null;
  prescriptionTime: any | null;
  dosage: string | null;
  dosageUnit: string | null;
}

export interface PostOperationChecklistAddedSubs_PostOperationChecklistAdded_urineOutput {
  __typename: "UrineOutputDetailType";
  timeIn: string | null;
  output: string | null;
}

export interface PostOperationChecklistAddedSubs_PostOperationChecklistAdded_linesDrainsTubes {
  __typename: "LinesDrainsTubesDetailType";
  intravenousInfusions: string | null;
  others: string | null;
  dressingChecked: string | null;
  dressingCheckedTime: any | null;
  drainsChecked: string | null;
  drainsCheckedTime: any | null;
  catheterChecked: string | null;
  catheterCheckedTime: any | null;
  quantityDrained: string | null;
}

export interface PostOperationChecklistAddedSubs_PostOperationChecklistAdded_newOrders {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface PostOperationChecklistAddedSubs_PostOperationChecklistAdded_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface PostOperationChecklistAddedSubs_PostOperationChecklistAdded {
  __typename: "PostOperationModel";
  id: string;
  serviceStartDateTime: any | null;
  chartType: string | null;
  surgeryStartDateTime: any | null;
  surgeryEndDateTime: any | null;
  dietOrderSheet: PostOperationChecklistAddedSubs_PostOperationChecklistAdded_dietOrderSheet[] | null;
  ambutationOrderSheet: PostOperationChecklistAddedSubs_PostOperationChecklistAdded_ambutationOrderSheet[] | null;
  urethralCatheterizationOrderSheet: PostOperationChecklistAddedSubs_PostOperationChecklistAdded_urethralCatheterizationOrderSheet[] | null;
  fluidTherapyOrderSheet: PostOperationChecklistAddedSubs_PostOperationChecklistAdded_fluidTherapyOrderSheet[] | null;
  antiBioticsOrderSheet: PostOperationChecklistAddedSubs_PostOperationChecklistAdded_antiBioticsOrderSheet[] | null;
  analgesicOrderSheet: PostOperationChecklistAddedSubs_PostOperationChecklistAdded_analgesicOrderSheet[] | null;
  clexane40mgFor3DaysOrderSheet: PostOperationChecklistAddedSubs_PostOperationChecklistAdded_clexane40mgFor3DaysOrderSheet[] | null;
  vitalSigns: PostOperationChecklistAddedSubs_PostOperationChecklistAdded_vitalSigns[] | null;
  medications: PostOperationChecklistAddedSubs_PostOperationChecklistAdded_medications[] | null;
  discontinueMedication: boolean;
  urineOutput: PostOperationChecklistAddedSubs_PostOperationChecklistAdded_urineOutput[] | null;
  linesDrainsTubes: PostOperationChecklistAddedSubs_PostOperationChecklistAdded_linesDrainsTubes[] | null;
  newOrders: PostOperationChecklistAddedSubs_PostOperationChecklistAdded_newOrders[] | null;
  SurgeonName: string | null;
  surgeonNameSignature: string | null;
  surgeonNameSignatureType: string | null;
  surgeonNameSignatureDateTime: any | null;
  SurgeonSpecialty: string | null;
  SurgeonAssistantName: string | null;
  surgeonAssistantNameSignature: string | null;
  surgeonAssistantNameSignatureType: string | null;
  surgeonAssistantNameSignatureDateTime: any | null;
  operatingRoomNurse: string | null;
  operatingRoomNurseSignature: string | null;
  operatingRoomNurseSignatureType: string | null;
  operatingRoomNurseSignatureDateTime: any | null;
  anesthetistName: string | null;
  anesthetistNameSignature: string | null;
  anesthetistNameSignatureType: string | null;
  anesthetistNameSignatureDateTime: any | null;
  recoveryNurse: string | null;
  recoveryNurseSignature: string | null;
  recoveryNurseSignatureType: string | null;
  recoveryNurseSignatureDateTime: any | null;
  visitingSpecialistName: string | null;
  visitingSpecialistSignature: string | null;
  visitingSpecialistSignatureType: string | null;
  visitingSpecialistSignatureDateTime: any | null;
  visitingFacilityName: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean;
  profileId: string | null;
  profile: PostOperationChecklistAddedSubs_PostOperationChecklistAdded_profile | null;
}

export interface PostOperationChecklistAddedSubs {
  PostOperationChecklistAdded: PostOperationChecklistAddedSubs_PostOperationChecklistAdded;
}

export interface PostOperationChecklistAddedSubsVariables {
  profileId: string;
}
