/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FacilityStaffsAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetFacilityStaffsGenderData
// ====================================================

export interface GetFacilityStaffsGenderData_getGenderByDepartment {
  __typename: "DepartmentsData";
  totalMaleStaffs: number | null;
  totalFemaleStaffs: number | null;
  name: number | null;
  category: string | null;
}

export interface GetFacilityStaffsGenderData {
  getGenderByDepartment: GetFacilityStaffsGenderData_getGenderByDepartment[];
}

export interface GetFacilityStaffsGenderDataVariables {
  filter?: FacilityStaffsAnalyticsFilter | null;
}
