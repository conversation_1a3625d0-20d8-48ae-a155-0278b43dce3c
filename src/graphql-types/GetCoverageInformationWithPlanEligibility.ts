/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetCoverageInformationWithPlanEligibility
// ====================================================

export interface GetCoverageInformationWithPlanEligibility_profile_coverageInformations_list_hmoProfile_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface GetCoverageInformationWithPlanEligibility_profile_coverageInformations_list_hmoProfile_lastCheckIn {
  __typename: "LastCheckInData";
  verificationCode: string;
}

export interface GetCoverageInformationWithPlanEligibility_profile_coverageInformations_list_hmoProfile {
  __typename: "HmoProfileModel";
  id: string;
  planEligibility: string | null;
  memberNumber: string | null;
  memberStatus: string | null;
  memberPlan: string | null;
  memberPlanId: string | null;
  planCategory: string | null;
  primaryProviderId: string | null;
  provider: GetCoverageInformationWithPlanEligibility_profile_coverageInformations_list_hmoProfile_provider;
  lastCheckIn: GetCoverageInformationWithPlanEligibility_profile_coverageInformations_list_hmoProfile_lastCheckIn | null;
}

export interface GetCoverageInformationWithPlanEligibility_profile_coverageInformations_list {
  __typename: "CoverageInformationModel";
  id: string;
  name: string | null;
  companyName: string | null;
  familyName: string | null;
  coverageType: string | null;
  hmoProfile: GetCoverageInformationWithPlanEligibility_profile_coverageInformations_list_hmoProfile | null;
}

export interface GetCoverageInformationWithPlanEligibility_profile_coverageInformations {
  __typename: "CoverageInformationResponse";
  list: GetCoverageInformationWithPlanEligibility_profile_coverageInformations_list[];
}

export interface GetCoverageInformationWithPlanEligibility_profile {
  __typename: "ProfileModel";
  id: string;
  coverageInformations: GetCoverageInformationWithPlanEligibility_profile_coverageInformations;
}

export interface GetCoverageInformationWithPlanEligibility {
  profile: GetCoverageInformationWithPlanEligibility_profile;
}

export interface GetCoverageInformationWithPlanEligibilityVariables {
  id: string;
}
