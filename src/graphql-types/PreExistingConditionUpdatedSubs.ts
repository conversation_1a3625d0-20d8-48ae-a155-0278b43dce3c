/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: PreExistingConditionUpdatedSubs
// ====================================================

export interface PreExistingConditionUpdatedSubs_PreExistingConditionUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface PreExistingConditionUpdatedSubs_PreExistingConditionUpdated {
  __typename: "PreExistingConditionModel";
  id: string;
  ageOfOnset: string | null;
  diagnosedDate: any | null;
  duration: string | null;
  additionalNote: string | null;
  conditionICD10: string | null;
  conditionICD11: string | null;
  conditionSNOMED: string | null;
  profile: PreExistingConditionUpdatedSubs_PreExistingConditionUpdated_profile | null;
}

export interface PreExistingConditionUpdatedSubs {
  PreExistingConditionUpdated: PreExistingConditionUpdatedSubs_PreExistingConditionUpdated;
}

export interface PreExistingConditionUpdatedSubsVariables {
  profileId: string;
}
