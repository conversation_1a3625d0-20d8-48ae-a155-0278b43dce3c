/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { TestReferenceRangeInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateTestReferenceRangeFields
// ====================================================

export interface UpdateTestReferenceRangeFields_updateTestReferenceRange {
  __typename: "TestReferenceRangeModel";
  id: string;
  testName: string;
  maleReferenceRange: string | null;
  femaleReferenceRange: string | null;
  referenceRange: string;
  updatedDate: any;
  lastModifierName: string | null;
}

export interface UpdateTestReferenceRangeFields {
  updateTestReferenceRange: UpdateTestReferenceRangeFields_updateTestReferenceRange;
}

export interface UpdateTestReferenceRangeFieldsVariables {
  input: TestReferenceRangeInput;
}
