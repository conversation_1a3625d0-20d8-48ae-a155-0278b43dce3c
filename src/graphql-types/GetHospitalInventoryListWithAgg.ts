/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { InventoryFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHospitalInventoryListWithAgg
// ====================================================

export interface GetHospitalInventoryListWithAgg_hospital_inventories_list {
  __typename: "InventoryModel";
  id: string;
  sn: number | null;
  supplier: string | null;
  invoiceNumber: string | null;
  name: string | null;
  type: string | null;
  size: string | null;
  group: string | null;
  flag: string | null;
  description: string | null;
  category: string | null;
  code: string | null;
  strength: string | null;
  ward: string | null;
  addedBy: string | null;
  addedDateTime: string | null;
  batchNumber: string | null;
  barcode: string | null;
  expiryDate: string | null;
  expiryStatus: string | null;
  damagedCount: string | null;
  bedNumber: string | null;
  markup: string | null;
  unitCost: string | null;
  averageCostPrice: string | null;
  colour: string | null;
  model: string | null;
  vin: string | null;
  plateNumber: string | null;
  year: string | null;
  purchasedBy: string | null;
  status: string | null;
  unitSellingPrice: string | null;
  bedAvailable: string | null;
  totalCost: string | null;
  totalSale: string | null;
  quantityRemaining: string | null;
  quantityPurchased: string | null;
  quantityAvailable: string | null;
  quantityDispensed: string | null;
  quantityOrdered: string | null;
  quantitySold: string | null;
  manufacturer: string | null;
  recievedDateTime: string | null;
  reorderLevel: string | null;
  receivedBy: string | null;
  comments: string | null;
  images: string | null;
  section: string | null;
  creatorId: string | null;
  class: string | null;
}

export interface GetHospitalInventoryListWithAgg_hospital_inventories {
  __typename: "InventoryResponse";
  totalCount: number;
  list: GetHospitalInventoryListWithAgg_hospital_inventories_list[];
}

export interface GetHospitalInventoryListWithAgg_hospital {
  __typename: "HospitalModel";
  id: string;
  inventories: GetHospitalInventoryListWithAgg_hospital_inventories;
}

export interface GetHospitalInventoryListWithAgg {
  hospital: GetHospitalInventoryListWithAgg_hospital;
}

export interface GetHospitalInventoryListWithAggVariables {
  filterOptions?: InventoryFilterInput | null;
}
