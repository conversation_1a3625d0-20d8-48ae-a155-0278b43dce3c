/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: DischargePatientUpdatedSubs
// ====================================================

export interface DischargePatientUpdatedSubs_DischargePatientUpdated_dischargeDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface DischargePatientUpdatedSubs_DischargePatientUpdated {
  __typename: "DischargePatientModel";
  id: string;
  dischargeDate: any | null;
  dischargedStatus: string | null;
  deathDateTime: any | null;
  deathCause: string | null;
  deathLocation: string | null;
  deathCertificateIssued: string | null;
  dischargeSummary: string | null;
  dischargedBy: string | null;
  dischargedBySignature: string | null;
  dischargedBySignatureType: string | null;
  dischargedBySignatureDateTime: any | null;
  dischargeDiagnosis: DischargePatientUpdatedSubs_DischargePatientUpdated_dischargeDiagnosis[] | null;
  causeOfDeath: string | null;
  concealDischargeSummary: boolean | null;
  followupDate: any | null;
  dischargeLocation: string | null;
  dischargeAddress: string | null;
}

export interface DischargePatientUpdatedSubs {
  DischargePatientUpdated: DischargePatientUpdatedSubs_DischargePatientUpdated;
}

export interface DischargePatientUpdatedSubsVariables {
  profileId: string;
}
