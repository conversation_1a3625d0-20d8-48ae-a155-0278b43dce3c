/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

//==============================================================
// START Enums and Input Objects
//==============================================================

/**
 * supported AccountHolder types
 */
export enum AccountHolder {
  Hospital = "Hospital",
  User = "User",
}

export enum Action {
  AccessPatientData = "AccessPatientData",
  Any = "Any",
  ApproveOrReject = "ApproveOrReject",
  Archive = "Archive",
  ArchiveOwn = "ArchiveOwn",
  ArchiveTeamAny = "ArchiveTeamAny",
  ArchiveTeamOwn = "ArchiveTeamOwn",
  Create = "Create",
  CreateOwn = "CreateOwn",
  CreateTeamAny = "CreateTeamAny",
  Delete = "Delete",
  DeleteOwn = "DeleteOwn",
  DeleteTeamAny = "DeleteTeamAny",
  DeleteTeamOwn = "DeleteTeamOwn",
  GrantOwn = "GrantOwn",
  GrantTeamAny = "GrantTeamAny",
  Manage = "Manage",
  Own = "Own",
  Read = "Read",
  ReadAny = "ReadAny",
  ReadOwn = "ReadOwn",
  ReadTeam = "ReadTeam",
  ReadTeamAny = "ReadTeamAny",
  RevokeOwn = "RevokeOwn",
  RevokeTeamAny = "RevokeTeamAny",
  Shared = "Shared",
  Skip = "Skip",
  Team = "Team",
  TeamAny = "TeamAny",
  TeamOwn = "TeamOwn",
  Update = "Update",
  UpdateOwn = "UpdateOwn",
  UpdateTeamAny = "UpdateTeamAny",
  UpdateTeamOwn = "UpdateTeamOwn",
}

/**
 * Record types that can be linked to Admission
 */
export enum AdmissionLinkedRecordType {
  Allergy = "Allergy",
  Consultation = "Consultation",
  Investigation = "Investigation",
  LabTest = "LabTest",
  Medication = "Medication",
  NursingService = "NursingService",
  Procedure = "Procedure",
  Radiology = "Radiology",
  VitalSign = "VitalSign",
}

/**
 * The types of acceptable note creator profile types
 */
export enum AdmissionNotesCreatorProfileTypes {
  Doctor = "Doctor",
  OrganizationDoctor = "OrganizationDoctor",
  OrganizationNurse = "OrganizationNurse",
}

/**
 * Record types that can be linked to Allergy
 */
export enum AllergyLinkedRecordType {
  Medication = "Medication",
}

/**
 * Record types that can be linked to Antenatal
 */
export enum AntenatalLinkedRecordType {
  Investigation = "Investigation",
  LabTest = "LabTest",
  Medication = "Medication",
  NursingService = "NursingService",
  Radiology = "Radiology",
  VitalSign = "VitalSign",
}

/**
 * Audit Action types
 */
export enum AuditAction {
  Create = "Create",
  Delete = "Delete",
  Modify = "Modify",
}

/**
 * Audit Entities Types
 */
export enum AuditEntities {
  AdditionalNote = "AdditionalNote",
  AdministrationNote = "AdministrationNote",
  Admission = "Admission",
  AdmissionLines = "AdmissionLines",
  AdmissionNote = "AdmissionNote",
  Allergy = "Allergy",
  Anaesthesia = "Anaesthesia",
  Antenatal = "Antenatal",
  AntenatalDetails = "AntenatalDetails",
  Anthropometry = "Anthropometry",
  Appointment = "Appointment",
  Bill = "Bill",
  BillDetails = "BillDetails",
  BloodGlucose = "BloodGlucose",
  BloodPressure = "BloodPressure",
  BloodTransfusion = "BloodTransfusion",
  ChemoDiagnosisTemplate = "ChemoDiagnosisTemplate",
  Consultation = "Consultation",
  Dependent = "Dependent",
  DevelopmentalHistory = "DevelopmentalHistory",
  Disability = "Disability",
  DischargePatient = "DischargePatient",
  DispenseDetails = "DispenseDetails",
  Employee = "Employee",
  EmployeeDependant = "EmployeeDependant",
  Employer = "Employer",
  FacilityBillingInformation = "FacilityBillingInformation",
  FamilyHistory = "FamilyHistory",
  GynecologicHistory = "GynecologicHistory",
  Habit = "Habit",
  HandoverAdditionalNote = "HandoverAdditionalNote",
  HandoverNote = "HandoverNote",
  HandoverNoteItem = "HandoverNoteItem",
  HmoClaim = "HmoClaim",
  HmoProfile = "HmoProfile",
  Immunization = "Immunization",
  InputOutput = "InputOutput",
  Investigation = "Investigation",
  Invoice = "Invoice",
  InvoiceItem = "InvoiceItem",
  InvoicePayment = "InvoicePayment",
  LabourDelivery = "LabourDelivery",
  Medication = "Medication",
  MedicationDetails = "MedicationDetails",
  NextOfKin = "NextOfKin",
  Notes = "Notes",
  NursingService = "NursingService",
  ObstetricHistory = "ObstetricHistory",
  OncologyHistory = "OncologyHistory",
  OperationNote = "OperationNote",
  Pain = "Pain",
  Partograph = "Partograph",
  PastEncounter = "PastEncounter",
  PastSurgery = "PastSurgery",
  PaymentDeposit = "PaymentDeposit",
  PhysicalActivity = "PhysicalActivity",
  PostOperation = "PostOperation",
  Postnatal = "Postnatal",
  PreAuthUtilisations = "PreAuthUtilisations",
  PreExistingCondition = "PreExistingCondition",
  PreOperationChecklist = "PreOperationChecklist",
  Preauthorisation = "Preauthorisation",
  PreauthorizationDetails = "PreauthorizationDetails",
  Profile = "Profile",
  PulseRate = "PulseRate",
  Radiology = "Radiology",
  RequestPayout = "RequestPayout",
  RespiratoryRate = "RespiratoryRate",
  SharedRecord = "SharedRecord",
  SubBillDetails = "SubBillDetails",
  Surgery = "Surgery",
  Temperature = "Temperature",
  TransferPatient = "TransferPatient",
  TreatmentPlan = "TreatmentPlan",
  UrineDipstick = "UrineDipstick",
  User = "User",
  VisualAcuity = "VisualAcuity",
  Vital = "Vital",
}

/**
 * The drug or cunsumable bank type
 */
export enum BankType {
  CLINIFY = "CLINIFY",
  FACILITY = "FACILITY",
}

export enum BenefitCategory {
  Capitated = "Capitated",
  FeeForService = "FeeForService",
}

export enum BenefitCoverage {
  Covered = "Covered",
  NotCovered = "NotCovered",
}

/**
 * supported bill status types
 */
export enum BillStatus {
  Cancelled = "Cancelled",
  CancelledWithLevy = "CancelledWithLevy",
  Paid = "Paid",
  PartialPayment = "PartialPayment",
  PendingPayment = "PendingPayment",
}

export enum BillableClaimModel {
  AdmissionModel = "AdmissionModel",
  AntenatalModel = "AntenatalModel",
  ConsultationModel = "ConsultationModel",
  DispenseDetailsModel = "DispenseDetailsModel",
  ImmunizationModel = "ImmunizationModel",
  InvestigationModel = "InvestigationModel",
  LabourDeliveryModel = "LabourDeliveryModel",
  NursingServiceModel = "NursingServiceModel",
  OncologyConsultationHistoryModel = "OncologyConsultationHistoryModel",
  PostnatalModel = "PostnatalModel",
  ProfileModel = "ProfileModel",
  RequestProcedureModel = "RequestProcedureModel",
  SurgeryModel = "SurgeryModel",
}

export enum BillableRecords {
  Admission = "Admission",
  Antenatal = "Antenatal",
  Consultation = "Consultation",
  Immunization = "Immunization",
  Laboratory = "Laboratory",
  LabourDelivery = "LabourDelivery",
  Medication = "Medication",
  NursingService = "NursingService",
  OncologyConsultationHistory = "OncologyConsultationHistory",
  Postnatal = "Postnatal",
  Radiology = "Radiology",
  Registration = "Registration",
  RequestProcedure = "RequestProcedure",
  Surgery = "Surgery",
}

/**
 * The type of business rule item
 */
export enum BusinessRuleItemType {
  ACTION = "ACTION",
  CONDITION = "CONDITION",
}

/**
 * The valid status types for bulk check in and check out
 */
export enum CheckInOrOutStatus {
  CheckedInAndUnassigned = "CheckedInAndUnassigned",
  CheckedOut = "CheckedOut",
}

/**
 * Default commission payer for invoice
 */
export enum CommissionPayer {
  Facility = "Facility",
  Patient = "Patient",
}

/**
 * Record types that can be linked to Consultation
 */
export enum ConsultationLinkedRecordType {
  Admission = "Admission",
  Allergy = "Allergy",
  Investigation = "Investigation",
  LabTest = "LabTest",
  Medication = "Medication",
  NursingService = "NursingService",
  Procedure = "Procedure",
  Radiology = "Radiology",
  VitalSign = "VitalSign",
}

export enum ContactType {
  CONNECTION = "CONNECTION",
  ORGANIZATION = "ORGANIZATION",
}

/**
 * supported Currency types
 */
export enum Currency {
  KOBO = "KOBO",
}

/**
 * Icon options acceptable by filter
 */
export enum DashboardIcon {
  Admission = "Admission",
  Allergy = "Allergy",
  Antenatal = "Antenatal",
  AntenatalPartograph = "AntenatalPartograph",
  Appointment = "Appointment",
  BackgroundInformation = "BackgroundInformation",
  Billing = "Billing",
  CancerScreening = "CancerScreening",
  Claim = "Claim",
  Consultation = "Consultation",
  ConsultationReferral = "ConsultationReferral",
  CoverageInformation = "CoverageInformation",
  HandoverNote = "HandoverNote",
  Immunization = "Immunization",
  Inventory = "Inventory",
  Investigation = "Investigation",
  Laboratory = "Laboratory",
  LabourDelivery = "LabourDelivery",
  Medication = "Medication",
  NursingService = "NursingService",
  OncologyConsultationHistory = "OncologyConsultationHistory",
  PatientProfile = "PatientProfile",
  PatientRegistration = "PatientRegistration",
  PatientTransfers = "PatientTransfers",
  PatientWaitingList = "PatientWaitingList",
  PersonalInformation = "PersonalInformation",
  PostOperation = "PostOperation",
  Postnatal = "Postnatal",
  PreAuthorization = "PreAuthorization",
  PreAuthorizationReferral = "PreAuthorizationReferral",
  PreChemoEducation = "PreChemoEducation",
  Price = "Price",
  Procedure = "Procedure",
  ProcedureChecklist = "ProcedureChecklist",
  Radiology = "Radiology",
  RequestProcedure = "RequestProcedure",
  Vital = "Vital",
}

export enum DefaultPatientAccessType {
  AllPatients = "AllPatients",
  RegisteredPatients = "RegisteredPatients",
}

/**
 * Diagnosis Group Type
 */
export enum DiagnosisGroup {
  CHEMO_DIAGNOSIS = "CHEMO_DIAGNOSIS",
  FINAL = "FINAL",
  PROVISIONAL = "PROVISIONAL",
}

/**
 * Diagnosis Classification Type
 */
export enum DiagnosisType {
  ICD10 = "ICD10",
  ICD11 = "ICD11",
  SNOMED = "SNOMED",
}

/**
 * Dispense stats options acceptable by filter
 */
export enum DispenseStatus {
  All = "All",
  Dispensed = "Dispensed",
  Prescribed = "Prescribed",
}

/**
 * Dispense type options acceptable by filter
 */
export enum DispenseType {
  All = "All",
  External = "External",
  Internal = "Internal",
}

/**
 * Feedback status options acceptable by filter
 */
export enum FeedbackStatusOptions {
  All = "All",
  Resolved = "Resolved",
  Unresolved = "Unresolved",
}

/**
 * Inventory Filter Color Options
 */
export enum FilterColorOption {
  All = "All",
  Blue = "Blue",
  Gray = "Gray",
  Green = "Green",
  Red = "Red",
  Yellow = "Yellow",
}

export enum FilterDateType {
  CreatedDate = "CreatedDate",
  RequestDate = "RequestDate",
}

/**
 * Manually generated bills (Bill Now), Auto generated bill (BIll Later) 
 */
export enum FinanceBillType {
  BillLater = "BillLater",
  BillNow = "BillNow",
}

/**
 * Apply a multiplier for values returned
 */
export enum FinanceValueMultiplier {
  Kobo = "Kobo",
  Naira = "Naira",
}

/**
 * The input mode to use for resetting passcode
 */
export enum ForgotPasscodeInputMode {
  EMAIL = "EMAIL",
  PHONE_NUMBER = "PHONE_NUMBER",
}

/**
 * The types of supported transfer status
 */
export enum FundTransactionStatus {
  Failed = "Failed",
  Pending = "Pending",
  Processing = "Processing",
  Success = "Success",
}

/**
 * supported transfer status types
 */
export enum FundTransferStatus {
  Cancelled = "Cancelled",
  CancelledWithLevy = "CancelledWithLevy",
  Completed = "Completed",
  Failed = "Failed",
  Overpayed = "Overpayed",
  PartialPayment = "PartialPayment",
  Pending = "Pending",
}

export enum Gender {
  Female = "Female",
  Male = "Male",
  Others = "Others",
}

export enum GenderOption {
  All = "All",
  Female = "Female",
  Male = "Male",
}

export enum HMOTariffBand {
  A = "A",
  B = "B",
  C = "C",
}

export enum HandoverNoteStatus {
  Draft = "Draft",
  High = "High",
  Low = "Low",
}

export enum HmoClaimDateFilterType {
  ClaimDate = "ClaimDate",
  CreatedDate = "CreatedDate",
  SubmissionDate = "SubmissionDate",
}

export enum HmoPlanStatus {
  Active = "Active",
  Expired = "Expired",
  Inactive = "Inactive",
  Pending = "Pending",
}

/**
 * The types acceptable for hospital plans
 */
export enum HospitalPlan {
  AmbulanceMinusInventory = "AmbulanceMinusInventory",
  AmbulancePlusInventory = "AmbulancePlusInventory",
  BloodBankMinusInventory = "BloodBankMinusInventory",
  BloodBankPlusInventory = "BloodBankPlusInventory",
  ClinifyMinusInventory = "ClinifyMinusInventory",
  ClinifyPlusInventory = "ClinifyPlusInventory",
  DentalMinusInventory = "DentalMinusInventory",
  DentalPlusInventory = "DentalPlusInventory",
  EyeMinusInventory = "EyeMinusInventory",
  EyePlusInventory = "EyePlusInventory",
  HMOMinusInventory = "HMOMinusInventory",
  HMOPlusInventory = "HMOPlusInventory",
  InvestigationMinusInventory = "InvestigationMinusInventory",
  InvestigationPlusInventory = "InvestigationPlusInventory",
  LaboratoryMinusInventory = "LaboratoryMinusInventory",
  LaboratoryPlusInventory = "LaboratoryPlusInventory",
  PharmacyMinusInventory = "PharmacyMinusInventory",
  PharmacyPlusInventory = "PharmacyPlusInventory",
  RadiologyMinusInventory = "RadiologyMinusInventory",
  RadiologyPlusInventory = "RadiologyPlusInventory",
  StorePlusInventory = "StorePlusInventory",
  TelemedicineMinusInventory = "TelemedicineMinusInventory",
}

/**
 * The types acceptable for hospital status
 */
export enum HospitalStatusTypes {
  Approved = "Approved",
  Pending = "Pending",
  Rejected = "Rejected",
}

/**
 * The types of hospital users that can be inserted
 */
export enum HospitalUserType {
  ClaimAccount = "ClaimAccount",
  ClaimAdmin = "ClaimAdmin",
  ClaimAgent = "ClaimAgent",
  ClaimAgentHOD = "ClaimAgentHOD",
  ClaimAudit = "ClaimAudit",
  ClaimAuditHOD = "ClaimAuditHOD",
  ClaimConfirmation = "ClaimConfirmation",
  ClaimFinance = "ClaimFinance",
  ClaimOfficer = "ClaimOfficer",
  ClaimOfficerHOD = "ClaimOfficerHOD",
  ClaimReviewer = "ClaimReviewer",
  ClaimReviewerHOD = "ClaimReviewerHOD",
  EnrollmentAdmin = "EnrollmentAdmin",
  EnrollmentAgency = "EnrollmentAgency",
  EnrollmentAgent = "EnrollmentAgent",
  EnrollmentTpaNonTpa = "EnrollmentTpaNonTpa",
  FieldManager = "FieldManager",
  FieldOfficer = "FieldOfficer",
  OrganizationBillingOfficer = "OrganizationBillingOfficer",
  OrganizationCashier = "OrganizationCashier",
  OrganizationDoctor = "OrganizationDoctor",
  OrganizationFrontDeskOfficer = "OrganizationFrontDeskOfficer",
  OrganizationLabTechnician = "OrganizationLabTechnician",
  OrganizationNurse = "OrganizationNurse",
  OrganizationRadiographer = "OrganizationRadiographer",
  OrganizationRadiologist = "OrganizationRadiologist",
  OrganizationRecordOfficer = "OrganizationRecordOfficer",
  OrganizationStaffAdmin = "OrganizationStaffAdmin",
  OrganizationStoreClerk = "OrganizationStoreClerk",
  Pharmacist = "Pharmacist",
}

/**
 * Inventory Order & Supply Status
 */
export enum InventoryOrderStatus {
  Accepted = "Accepted",
  Approved = "Approved",
  Cancelled = "Cancelled",
  Delivered = "Delivered",
  Denied = "Denied",
  NotDelivered = "NotDelivered",
  NotSupplied = "NotSupplied",
  Pending = "Pending",
  Rejected = "Rejected",
  Supplied = "Supplied",
}

/**
 * Inventory Status
 */
export enum InventoryStatus {
  Accepted = "Accepted",
  Cancelled = "Cancelled",
  Completed = "Completed",
  Fulfilled = "Fulfilled",
  Pending = "Pending",
  Rejected = "Rejected",
}

/**
 * Inventory Supply Status
 */
export enum InventorySupplyStatus {
  NotSupplied = "NotSupplied",
  Supplied = "Supplied",
}

export enum InvoiceStatus {
  Cancelled = "Cancelled",
  Draft = "Draft",
  Overdue = "Overdue",
  Pending = "Pending",
}

export enum LookupPatientType {
  ALL = "ALL",
  ORGANISATION = "ORGANISATION",
}

/**
 * Status of medical report
 */
export enum MedicalReportStatus {
  Completed = "Completed",
  Draft = "Draft",
}

/**
 * Medication option, (medication & consumable, consumable, medication)
 */
export enum MedicationOptionType {
  C = "C",
  M = "M",
}

export enum MemberOrDependent {
  D = "D",
  M = "M",
}

export enum MessageStatus {
  DELIVERED = "DELIVERED",
  FAILED = "FAILED",
  READ = "READ",
  SENT = "SENT",
}

export enum MessageType {
  FILE = "FILE",
  IMAGE = "IMAGE",
  SYSTEM = "SYSTEM",
  TEXT = "TEXT",
}

/**
 * Next Appointment Entities Types
 */
export enum NextAppointmentEntities {
  Admission = "Admission",
  Antenatal = "Antenatal",
  Consultation = "Consultation",
  Immunization = "Immunization",
  LabourDelivery = "LabourDelivery",
  OncologyConsultationHistory = "OncologyConsultationHistory",
  Postnatal = "Postnatal",
  Surgery = "Surgery",
}

/**
 * Tag options acceptable by filter
 */
export enum NotificationTag {
  Accepted = "Accepted",
  Added = "Added",
  Admitted = "Admitted",
  Approved = "Approved",
  Assigned = "Assigned",
  BloodTransfusion = "BloodTransfusion",
  BloodTransfusionDeleted = "BloodTransfusionDeleted",
  BloodTransfusionUpdated = "BloodTransfusionUpdated",
  Cancelled = "Cancelled",
  Checkedout = "Checkedout",
  Completed = "Completed",
  Confirmed = "Confirmed",
  Created = "Created",
  Deleted = "Deleted",
  Discharged = "Discharged",
  DischargedDeleted = "DischargedDeleted",
  DischargedUpdated = "DischargedUpdated",
  Dispensed = "Dispensed",
  DispensedDeleted = "DispensedDeleted",
  DispensedUpdated = "DispensedUpdated",
  Drafted = "Drafted",
  Emailed = "Emailed",
  Excluded = "Excluded",
  Included = "Included",
  Missed = "Missed",
  Moved = "Moved",
  Ordered = "Ordered",
  Paid = "Paid",
  PartiallyPaid = "PartiallyPaid",
  PreAuthorizationReferral = "PreAuthorizationReferral",
  PreAuthorizationReferralCreated = "PreAuthorizationReferralCreated",
  Prescribed = "Prescribed",
  PrescribedDeleted = "PrescribedDeleted",
  PrescribedUpdated = "PrescribedUpdated",
  Raised = "Raised",
  Recalled = "Recalled",
  ReferralAdded = "ReferralAdded",
  ReferralExternal = "ReferralExternal",
  Registered = "Registered",
  Rejected = "Rejected",
  Requested = "Requested",
  Rescheduled = "Rescheduled",
  Resent = "Resent",
  Scheduled = "Scheduled",
  Submitted = "Submitted",
  Transferred = "Transferred",
  Uncancelled = "Uncancelled",
  Updated = "Updated",
}

/**
 * Record types that can be linked to Nursing Services
 */
export enum NursingServiceLinkRecordType {
  Admission = "Admission",
  Consultation = "Consultation",
  Immunization = "Immunization",
  Investigation = "Investigation",
  LabTest = "LabTest",
  Medication = "Medication",
  Procedure = "Procedure",
  Radiology = "Radiology",
  VitalSign = "VitalSign",
}

/**
 * Record types that can be linked to Oncology Consultation
 */
export enum OncologyConsultationLinkedRecordType {
  Admission = "Admission",
  Allergy = "Allergy",
  Investigation = "Investigation",
  LabTest = "LabTest",
  Medication = "Medication",
  NursingService = "NursingService",
  Procedure = "Procedure",
  Radiology = "Radiology",
  VitalSign = "VitalSign",
}

/**
 * Organisation Appointment Status
 */
export enum OrganisationApointmentStatus {
  Accepted = "Accepted",
  Arrived = "Arrived",
  Cancelled = "Cancelled",
  Completed = "Completed",
  Confirmed = "Confirmed",
  Missed = "Missed",
  Rejected = "Rejected",
  Rescheduled = "Rescheduled",
  Scheduled = "Scheduled",
}

export enum OrganisationProfileType {
  Cashier = "Cashier",
  ClaimAccount = "ClaimAccount",
  ClaimAdmin = "ClaimAdmin",
  ClaimAgent = "ClaimAgent",
  ClaimAgentHOD = "ClaimAgentHOD",
  ClaimAudit = "ClaimAudit",
  ClaimAuditHOD = "ClaimAuditHOD",
  ClaimConfirmation = "ClaimConfirmation",
  ClaimFinance = "ClaimFinance",
  ClaimOfficer = "ClaimOfficer",
  ClaimOfficerHOD = "ClaimOfficerHOD",
  ClaimReviewer = "ClaimReviewer",
  ClaimReviewerHOD = "ClaimReviewerHOD",
  EnrollmentAdmin = "EnrollmentAdmin",
  EnrollmentAgency = "EnrollmentAgency",
  EnrollmentAgent = "EnrollmentAgent",
  EnrollmentTpaNonTpa = "EnrollmentTpaNonTpa",
  FieldManager = "FieldManager",
  FieldOfficer = "FieldOfficer",
  OrganizationAdmin = "OrganizationAdmin",
  OrganizationBillingOfficer = "OrganizationBillingOfficer",
  OrganizationCashier = "OrganizationCashier",
  OrganizationDoctor = "OrganizationDoctor",
  OrganizationFrontDeskOfficer = "OrganizationFrontDeskOfficer",
  OrganizationLabTechnician = "OrganizationLabTechnician",
  OrganizationNurse = "OrganizationNurse",
  OrganizationRadiographer = "OrganizationRadiographer",
  OrganizationRadiologist = "OrganizationRadiologist",
  OrganizationRecordOfficer = "OrganizationRecordOfficer",
  OrganizationStaffAdmin = "OrganizationStaffAdmin",
  OrganizationStoreClerk = "OrganizationStoreClerk",
  Pharmacist = "Pharmacist",
}

export enum OrganizationAction {
  AccessPatientData = "AccessPatientData",
  ArchiveTeamAny = "ArchiveTeamAny",
  ArchiveTeamOwn = "ArchiveTeamOwn",
  CreateTeamAny = "CreateTeamAny",
  DeleteTeamAny = "DeleteTeamAny",
  DeleteTeamOwn = "DeleteTeamOwn",
  GrantTeamAny = "GrantTeamAny",
  ReadAny = "ReadAny",
  ReadTeam = "ReadTeam",
  ReadTeamAny = "ReadTeamAny",
  RevokeTeamAny = "RevokeTeamAny",
  UpdateOwn = "UpdateOwn",
  UpdateTeamAny = "UpdateTeamAny",
  UpdateTeamOwn = "UpdateTeamOwn",
}

export enum PaCodeStatus {
  Approved = "Approved",
  Pending = "Pending",
  Rejected = "Rejected",
}

export enum PatientAccessType {
  All = "All",
  Custom = "Custom",
  RegisteredFacility = "RegisteredFacility",
}

export enum PatientProfileType {
  Adult = "Adult",
  Others = "Others",
}

/**
 * The type of patient
 */
export enum PatientType {
  InPatient = "InPatient",
  OutPatient = "OutPatient",
}

/**
 * Default commission payer for payout
 */
export enum PayoutCommissionPayer {
  Agency = "Agency",
  Provider = "Provider",
}

export enum PayoutStatus {
  Cancelled = "Cancelled",
  Failed = "Failed",
  Outstanding = "Outstanding",
  Paid = "Paid",
  Pending = "Pending",
  Processing = "Processing",
  Rejected = "Rejected",
  Reversed = "Reversed",
}

export enum PercentOrAmount {
  Amount = "Amount",
  Percent = "Percent",
}

/**
 * Subscription plan status of facility
 */
export enum PlanStatus {
  Paid = "Paid",
  Unpaid = "Unpaid",
}

export enum PreauthReferralDateFilterType {
  CreatedDate = "CreatedDate",
  RequestDate = "RequestDate",
}

export enum PreauthorizationStatus {
  Denied = "Denied",
  Error = "Error",
  Pending = "Pending",
  Submitted = "Submitted",
  Success = "Success",
}

export enum QuestionOption {
  No = "No",
  Yes = "Yes",
}

export enum RecordCreator {
  OTHERS = "OTHERS",
  SELF = "SELF",
}

/**
 * Refeerral options acceptable by filter
 */
export enum ReferralOptions {
  All = "All",
  External = "External",
  Internal = "Internal",
}

export enum ReminderIcons {
  Claims = "Claims",
  Preauthorizations = "Preauthorizations",
  Referrals = "Referrals",
}

export enum ReminderTag {
  Approved = "Approved",
  Rejected = "Rejected",
  Requested = "Requested",
  Submitted = "Submitted",
}

/**
 * supported service types
 */
export enum ServiceType {
  Admission = "Admission",
  Antenatal = "Antenatal",
  Appointment = "Appointment",
  Consultation = "Consultation",
  Consumable = "Consumable",
  Immunization = "Immunization",
  Investigation = "Investigation",
  Laboratory = "Laboratory",
  LabourDelivery = "LabourDelivery",
  MedicalReport = "MedicalReport",
  Medication = "Medication",
  NursingService = "NursingService",
  Others = "Others",
  Postnatal = "Postnatal",
  Procedure = "Procedure",
  Radiology = "Radiology",
  Registration = "Registration",
  RequestProcedure = "RequestProcedure",
}

/**
 * The types of severeness that can be inserted
 */
export enum SevereType {
  Mild = "Mild",
  Moderate = "Moderate",
  Severe = "Severe",
}

export enum SortOrder {
  ASC = "ASC",
  DESC = "DESC",
}

/**
 * The types of acceptable staff-activities service types
 */
export enum StaffActivitiesServiceType {
  Admission = "Admission",
  Consultation = "Consultation",
  Consumables = "Consumables",
  ConsumablesConsumed = "ConsumablesConsumed",
  ConsumablesPrescribed = "ConsumablesPrescribed",
  GPConsultation = "GPConsultation",
  Immunization = "Immunization",
  InvestigationLaboratory = "InvestigationLaboratory",
  InvestigationRadiology = "InvestigationRadiology",
  Investigations = "Investigations",
  MedicalReports = "MedicalReports",
  Medication = "Medication",
  MedicationDispensed = "MedicationDispensed",
  MedicationPrescribed = "MedicationPrescribed",
  NursingServices = "NursingServices",
  OncologyConsultation = "OncologyConsultation",
  PregnancyCareAntenatal = "PregnancyCareAntenatal",
  PregnancyCareLabourAndDelivery = "PregnancyCareLabourAndDelivery",
  PregnancyCarePostnatal = "PregnancyCarePostnatal",
  Procedure = "Procedure",
  ProcessedInvestigationLaboratory = "ProcessedInvestigationLaboratory",
  ProcessedInvestigationRadiology = "ProcessedInvestigationRadiology",
  RequestedInvestigationLaboratory = "RequestedInvestigationLaboratory",
  RequestedInvestigationRadiology = "RequestedInvestigationRadiology",
  VitalSignsAnthropometry = "VitalSignsAnthropometry",
  VitalSignsBloodGlucose = "VitalSignsBloodGlucose",
  VitalSignsBloodPressure = "VitalSignsBloodPressure",
  VitalSignsPainAssessment = "VitalSignsPainAssessment",
  VitalSignsPulseRate = "VitalSignsPulseRate",
  VitalSignsRespiratoryRate = "VitalSignsRespiratoryRate",
  VitalSignsTemperature = "VitalSignsTemperature",
  VitalSignsUrineDipstick = "VitalSignsUrineDipstick",
  VitalSignsVisualAcuity = "VitalSignsVisualAcuity",
}

export enum SubUserType {
  Doctor = "Doctor",
  Patient = "Patient",
}

export enum Subject {
  Admission = "Admission",
  AdmissionLinesSubRecord = "AdmissionLinesSubRecord",
  AdmissionNoteSubRecord = "AdmissionNoteSubRecord",
  All = "All",
  Allergy = "Allergy",
  AnaesthesiaCheckList = "AnaesthesiaCheckList",
  Antenatal = "Antenatal",
  AntenatalDetail = "AntenatalDetail",
  Appointment = "Appointment",
  BackgroundInformation = "BackgroundInformation",
  BillLater = "BillLater",
  BillNow = "BillNow",
  BloodTransfusionSubRecord = "BloodTransfusionSubRecord",
  CancerScreening = "CancerScreening",
  Consultation = "Consultation",
  ConsultationOncology = "ConsultationOncology",
  ConsultationProcedure = "ConsultationProcedure",
  ConsultationTreatmentPlan = "ConsultationTreatmentPlan",
  CoverageInformation = "CoverageInformation",
  Dependents = "Dependents",
  DevelopmentalHistory = "DevelopmentalHistory",
  Disability = "Disability",
  DischargePatientSubRecord = "DischargePatientSubRecord",
  DispenseMedication = "DispenseMedication",
  FamilyHistory = "FamilyHistory",
  GynaecologicHistory = "GynaecologicHistory",
  Hospital = "Hospital",
  Immunization = "Immunization",
  ImmunizationDetail = "ImmunizationDetail",
  InputOutputSubRecord = "InputOutputSubRecord",
  Inventory = "Inventory",
  Investigation = "Investigation",
  InvestigationAdditionalNote = "InvestigationAdditionalNote",
  InvestigationLabResult = "InvestigationLabResult",
  InvestigationRadiology = "InvestigationRadiology",
  LabTest = "LabTest",
  LabourAndDelivery = "LabourAndDelivery",
  Medication = "Medication",
  MedicationDetail = "MedicationDetail",
  MergeRecord = "MergeRecord",
  NextOfKin = "NextOfKin",
  Notes = "Notes",
  NursingService = "NursingService",
  NutritionalHistory = "NutritionalHistory",
  NutritionalHistoryGrowth = "NutritionalHistoryGrowth",
  ObstetricHistory = "ObstetricHistory",
  OncologyConsultationTreatmentPlan = "OncologyConsultationTreatmentPlan",
  OncologyHistory = "OncologyHistory",
  Partograph = "Partograph",
  PastEncounters = "PastEncounters",
  PastSurgicalHistory = "PastSurgicalHistory",
  PatientRegistration = "PatientRegistration",
  Permission = "Permission",
  PersonalInformation = "PersonalInformation",
  PhysicalHistory = "PhysicalHistory",
  PostOperationChecklist = "PostOperationChecklist",
  Postnatal = "Postnatal",
  PreChemoEducation = "PreChemoEducation",
  PreExistingCondition = "PreExistingCondition",
  PreOperationChecklist = "PreOperationChecklist",
  Profile = "Profile",
  Radiology = "Radiology",
  RecordBill = "RecordBill",
  SharedRecord = "SharedRecord",
  SocialHistory = "SocialHistory",
  Staff = "Staff",
  Surgery = "Surgery",
  SurgeryOperationNote = "SurgeryOperationNote",
  TransferPatientSubRecord = "TransferPatientSubRecord",
  User = "User",
  VitalAnthropometry = "VitalAnthropometry",
  VitalBloodGlucose = "VitalBloodGlucose",
  VitalBloodPressure = "VitalBloodPressure",
  VitalPain = "VitalPain",
  VitalPulseRate = "VitalPulseRate",
  VitalRespiratoryRate = "VitalRespiratoryRate",
  VitalTemperature = "VitalTemperature",
  VitalUrineDipstick = "VitalUrineDipstick",
  VitalVisualAcuity = "VitalVisualAcuity",
  Vitals = "Vitals",
}

/**
 * Record types that can be linked to Surgery
 */
export enum SurgeryLinkedRecordType {
  Investigation = "Investigation",
  LabTest = "LabTest",
  Medication = "Medication",
  NursingService = "NursingService",
  Radiology = "Radiology",
  VitalSign = "VitalSign",
}

export enum TimeSortOrder {
  ASC = "ASC",
  DESC = "DESC",
}

/**
 * The types of supported transaction status
 */
export enum TransactionStatus {
  FAILED = "FAILED",
  INIT = "INIT",
  NOT_YET_PROCESSED = "NOT_YET_PROCESSED",
  PENDING = "PENDING",
  REVERSED = "REVERSED",
  SUCCESS = "SUCCESS",
}

/**
 * The types of supported transactions
 */
export enum TransactionType {
  CANCELLATION_LEVY = "CANCELLATION_LEVY",
  HMO_REMITTANCE = "HMO_REMITTANCE",
  PAYMENT = "PAYMENT",
  REVERSAL = "REVERSAL",
  TOPUP = "TOPUP",
  TRANSFER = "TRANSFER",
  WITHDRAWAL = "WITHDRAWAL",
}

export enum UserListType {
  DOCTOR = "DOCTOR",
  PATIENT = "PATIENT",
}

export enum UserType {
  Admin = "Admin",
  ClaimAccount = "ClaimAccount",
  ClaimAdmin = "ClaimAdmin",
  ClaimAgent = "ClaimAgent",
  ClaimAgentHOD = "ClaimAgentHOD",
  ClaimAudit = "ClaimAudit",
  ClaimAuditHOD = "ClaimAuditHOD",
  ClaimConfirmation = "ClaimConfirmation",
  ClaimFinance = "ClaimFinance",
  ClaimOfficer = "ClaimOfficer",
  ClaimOfficerHOD = "ClaimOfficerHOD",
  ClaimReviewer = "ClaimReviewer",
  ClaimReviewerHOD = "ClaimReviewerHOD",
  Doctor = "Doctor",
  EnrollmentAdmin = "EnrollmentAdmin",
  EnrollmentAgency = "EnrollmentAgency",
  EnrollmentAgent = "EnrollmentAgent",
  EnrollmentTpaNonTpa = "EnrollmentTpaNonTpa",
  FieldManager = "FieldManager",
  FieldOfficer = "FieldOfficer",
  OrganizationAdmin = "OrganizationAdmin",
  OrganizationBillingOfficer = "OrganizationBillingOfficer",
  OrganizationCashier = "OrganizationCashier",
  OrganizationDoctor = "OrganizationDoctor",
  OrganizationFrontDeskOfficer = "OrganizationFrontDeskOfficer",
  OrganizationLabTechnician = "OrganizationLabTechnician",
  OrganizationNurse = "OrganizationNurse",
  OrganizationRadiographer = "OrganizationRadiographer",
  OrganizationRadiologist = "OrganizationRadiologist",
  OrganizationRecordOfficer = "OrganizationRecordOfficer",
  OrganizationStaffAdmin = "OrganizationStaffAdmin",
  OrganizationStoreClerk = "OrganizationStoreClerk",
  Patient = "Patient",
  Pharmacist = "Pharmacist",
  SuperAdmin = "SuperAdmin",
}

export enum VirtualAccountProvider {
  WEMA = "WEMA",
}

export enum VirtualAccountTransactionType {
  FundWallet = "FundWallet",
  Hmo = "Hmo",
  PayBill = "PayBill",
  PayInvoice = "PayInvoice",
  ProfileEnrollment = "ProfileEnrollment",
  Registration = "Registration",
}

export enum VirtualAccountType {
  Permanent = "Permanent",
  Temporary = "Temporary",
}

/**
 * The types of vitals that can be inserted
 */
export enum VitalType {
  Anthropometry = "Anthropometry",
  BloodGlucose = "BloodGlucose",
  BloodPressure = "BloodPressure",
  Cholesterol = "Cholesterol",
  PulseRate = "PulseRate",
  RespiratoryRate = "RespiratoryRate",
  Temperature = "Temperature",
  UrineDipstick = "UrineDipstick",
  VisualAcuity = "VisualAcuity",
}

/**
 * The types of acceptable status for waiting list
 */
export enum WaitingListStatus {
  CheckedInAndAssigned = "CheckedInAndAssigned",
  CheckedInAndUnassigned = "CheckedInAndUnassigned",
  CheckedOut = "CheckedOut",
  Completed = "Completed",
  Transferred = "Transferred",
}

/**
 * The admission status
 */
export enum admissionStatus {
  Admitted = "Admitted",
  Discharged = "Discharged",
}

export enum immunizationCategory {
  ADULT_AND_PREG = "ADULT_AND_PREG",
  BIRTH = "BIRTH",
  OTHERS = "OTHERS",
  SIX_MONTHS_AND = "SIX_MONTHS_AND",
  TWO_YEARS_AND = "TWO_YEARS_AND",
}

/**
 * The investigation status type
 */
export enum investigationStatus {
  Accepted = "Accepted",
  Approved = "Approved",
  Completed = "Completed",
  Rejected = "Rejected",
  Submitted = "Submitted",
}

/**
 * The type of investigation request
 */
export enum requestType {
  Laboratory = "Laboratory",
  Radiology = "Radiology",
}

/**
 * Specify if investigation has a specimen
 */
export enum specimenCollection {
  No = "No",
  Yes = "Yes",
}

export interface AddSupplyInput {
  items: SupplyItemInput[];
}

export interface AdditionalNoteInput {
  id?: string | null;
  additionalNote?: string | null;
  conceal?: boolean | null;
}

export interface AdditionalPaymentInput {
  splitPaymentType?: string | null;
  splitAmount?: number | null;
  splitBankName?: string | null;
  splitAccountNumber?: string | null;
}

export interface AdditionalTimeVolume {
  name?: string | null;
  value?: TimeVolume[] | null;
}

export interface AdministrationNoteInput {
  administrationNote?: string | null;
  conceal?: boolean | null;
}

export interface AdmissionAnalyticsFilter {
  startDate?: string | null;
  endDate?: string | null;
  duration?: string | null;
  overall?: boolean | null;
  hospitalId?: string | null;
}

export interface AdmissionFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  status?: admissionStatus | null;
  transferred?: boolean | null;
  providerInsight?: boolean | null;
  hospitalId?: string | null;
}

export interface AdmissionInput {
  id?: string | null;
  clinifyId?: string | null;
  admissionDate?: any | null;
  duration?: string | null;
  priority?: string | null;
  category?: string | null;
  severeness?: string | null;
  admittedBy?: string | null;
  fileNumber?: string | null;
  ward?: string | null;
  specialty?: string | null;
  rank?: string | null;
  department?: string | null;
  nurseName?: string | null;
  roomType?: string | null;
  roomNumber?: string | null;
  bedNumber?: string | null;
  presentMedicalHistory?: string | null;
  valuablesOrBelongings?: string | null;
  patientEnvironmentOrientation?: string | null;
  currentMedications?: string | null;
  medicinesDeposition?: string | null;
  instructedToSendHome?: string | null;
  nonBroughtToHospital?: string | null;
  otherPlacement?: string | null;
  medicationOrDrug?: string | null;
  bloodTransfusion?: string | null;
  food?: string | null;
  latex?: string | null;
  adultAge?: string | null;
  adultSedativeMedication?: string | null;
  adultAmbulatorySupport?: string | null;
  mentalStatus?: string | null;
  childAge?: string | null;
  dehydration?: string | null;
  dizziness?: string | null;
  respirationDistress?: string | null;
  childAmbulatorySupport?: string | null;
  childSedativeMedication?: string | null;
  painScore?: string | null;
  painDescriptors?: string[] | null;
  specifyPainDescriptors?: string | null;
  painLocation?: string[] | null;
  specifyPainLocation?: string | null;
  acuity?: string | null;
  modifying?: string | null;
  tobaccoUse?: string | null;
  tobaccoUseDuration?: string | null;
  alcoholUse?: string | null;
  alcoholUseDuration?: string | null;
  psychologicalStatus?: string | null;
  specifyPsychologicalStatus?: string | null;
  sleep?: string | null;
  specifySleepDifficulty?: string | null;
  sleepRoutine?: string | null;
  specifySleepRoutine?: string | null;
  whatMakesYouSleep?: string | null;
  bathing?: string | null;
  dressing?: string | null;
  eating?: string | null;
  mobility?: string | null;
  stairClimbing?: string | null;
  toiletUse?: string | null;
  impairedHearing?: string | null;
  impairedVision?: string | null;
  canPerformAdl?: string | null;
  canRead?: string | null;
  canWrite?: string | null;
  hearingAid?: string | null;
  glasses?: string | null;
  contacts?: string | null;
  dentures?: string | null;
  partial?: string | null;
  difficultyInChewing?: string | null;
  difficultyInSwallowing?: string | null;
  specialDiet?: string | null;
  specifySpecialDiet?: string | null;
  pallorSunkenEyesDehydrationAnorexia?: string | null;
  vomittingDiarrheaEdema?: string | null;
  newlyDiagnosedDiabeticOrHypertensive?: string | null;
  hairOrSkinChange?: string | null;
  nursingNeeds?: string | null;
  reAdmission?: string | null;
  lastAdmissionDateTime?: any | null;
  specialCourtesy?: string | null;
  specialArrangement?: string | null;
  nursingDiagnosis?: string[] | null;
  objectives?: string | null;
  nursingOrders?: string | null;
  evaluation?: string | null;
  provider?: string | null;
  providerServiceName?: string | null;
  roomInventoryId?: string | null;
  nextAppointmentDateTime?: any | null;
  nextAppointmentEndDateTime?: any | null;
  nextAppointmentDuration?: string | null;
  nextAppointmentSpecialty?: string | null;
  nextAppointmentRole?: string | null;
  nextAppointmentCategory?: string | null;
  nextAppointmentSpecialistId?: string | null;
  nextAppointmentSpecialistName?: string | null;
  nextAppointmentUrgency?: string | null;
  nextAppointmentDeliveryMethod?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  dischargeDate?: any | null;
  transferDate?: any | null;
  clinicName?: string | null;
  clinicAddress?: string | null;
  hospitalUnit?: string | null;
  admissionDiagnosis?: DiagnosisInput[] | null;
  doctorInCharge?: string | null;
  roomOption?: string | null;
  patientConsent?: string | null;
  patientConsentSignature?: string | null;
  patientConsentSignatureType?: string | null;
  patientConsentSignatureDateTime?: any | null;
  bedAvailable?: string | null;
  finding?: string | null;
  documentUrl?: string[] | null;
  additionalNote?: string | null;
  allergies?: string[] | null;
  consultations?: string[] | null;
  labTests?: string[] | null;
  medications?: string[] | null;
  radiology?: string[] | null;
  investigations?: string[] | null;
  surgeries?: string[] | null;
  vitals?: string[] | null;
  nursingServices?: string[] | null;
  hmoProviderId?: string | null;
}

export interface AdmissionLinesInput {
  id?: string | null;
  placementDateTime?: any | null;
  placedBy?: string | null;
  placedPreArrival?: string | null;
  ivChangeDue?: any | null;
  size?: string | null;
  orientation?: string | null;
  site?: string | null;
  siteAssessment?: string | null;
  localAnesthetic?: string | null;
  duration?: string | null;
  lineStatus?: string | null;
  dressingDateTime?: any | null;
  dressingType?: string | null;
  dressingAppearance?: string[] | null;
  dressingIntervention?: string[] | null;
  lastDressingChange?: string | null;
  dressingChangeDue?: string | null;
  urinaryCatheterPlacedInEd?: string | null;
  insertedByAnother?: string | null;
  insertedBy?: string | null;
  insertionAttempts?: string | null;
  patientTolerance?: string | null;
  preExistingSite?: string | null;
  cathetherType?: string | null;
  tubeSize?: string | null;
  catheterBallonSize?: string | null;
  urineReturned?: string | null;
  removalDateTime?: any | null;
  removedBy?: string | null;
  removalReason?: string | null;
}

export interface AdmissionNoteInput {
  id?: string | null;
  creatorProfileType: AdmissionNotesCreatorProfileTypes;
  conceal?: boolean | null;
  note?: string | null;
}

export interface AgreedTariffInput {
  providerId: string;
  id: string;
  enrolleeId: string;
  treatmentDate: any;
  position?: number | null;
  utilizationId?: string | null;
  visitTypeId?: string | null;
  facilityId?: string | null;
  externalPlanId?: string | null;
}

export interface AllPatientProfilesFilterInput {
  skip?: number | null;
  take?: number | null;
  keyword?: string | null;
  hospitalId: string;
  visibility: boolean;
}

export interface AllVitalsAnthropometryInput {
  id?: string | null;
  height?: string | null;
  isHeightCritical?: boolean | null;
  weight?: string | null;
  isWeightCritical?: boolean | null;
  heightUnit?: string | null;
  weightUnit?: string | null;
  bmi?: string | null;
  bsa?: string | null;
}

export interface AllVitalsBloodGlucoseInput {
  id?: string | null;
  bloodGlucose?: string | null;
  isReadingCritical?: boolean | null;
  bloodGlucoseUnit?: string | null;
}

export interface AllVitalsBloodPressureInput {
  id?: string | null;
  systolic?: string | null;
  isSystolicCritical?: boolean | null;
  diastolic?: string | null;
  heartRate?: string | null;
  fetalHeartRate?: string | null;
  isDiastolicCritical?: boolean | null;
  meanArterialPressure?: string | null;
  isHeartRateCritical?: boolean | null;
  isFetalHeartRateCritical?: boolean | null;
}

export interface AllVitalsInput {
  readingDateTime?: any | null;
  additionalNote?: string | null;
  anthropometry?: AllVitalsAnthropometryInput | null;
  bloodPressure?: AllVitalsBloodPressureInput | null;
  temperature?: AllVitalsTemperatureInput | null;
  pulseRate?: AllVitalsPulseRateInput | null;
  respiratoryRate?: AllVitalsRespiratoryRateInput | null;
  bloodGlucose?: AllVitalsBloodGlucoseInput | null;
  pain?: AllVitalsPainInput | null;
}

export interface AllVitalsPainInput {
  id?: string | null;
  score?: string | null;
  isScoreCritical?: boolean | null;
}

export interface AllVitalsPulseRateInput {
  id?: string | null;
  pulseRate?: string | null;
  isReadingCritical?: boolean | null;
}

export interface AllVitalsRespiratoryRateInput {
  id?: string | null;
  respiratoryRate?: string | null;
  isReadingCritical?: boolean | null;
  oxygenSaturation?: string | null;
  isOxygenSaturationCritical?: boolean | null;
}

export interface AllVitalsTemperatureInput {
  id?: string | null;
  temperature?: string | null;
  isReadingCritical?: boolean | null;
  temperatureUnit?: string | null;
}

export interface AllergyFieldsInput {
  type: string;
  trigger: string;
  reactions?: string[] | null;
  severeness?: SevereType | null;
  clinicalDiagnosis?: DiagnosisInput[] | null;
}

export interface AllergyFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
}

export interface AllergyInput {
  id?: string | null;
  clinifyId: string;
  occurenceDate?: any | null;
  duration?: string | null;
  details?: AllergyFieldsInput[] | null;
  doctorName?: string | null;
  specialty?: string | null;
  rank?: string | null;
  department?: string | null;
  hospitalName?: string | null;
  hospitalAddress?: string | null;
  medications?: string[] | null;
  documentUrl?: string[] | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
}

export interface AmnoiticFluid {
  readingDate?: any | null;
  value?: string | null;
  moulding?: string | null;
  time?: string | null;
}

export interface AnaesthesiaInput {
  id?: string | null;
  clinifyId?: string | null;
  drugsGiven?: DrugGiven[] | null;
  pulseAndBp?: PulseAndBp[] | null;
  eventTimeGraph?: EventTime[] | null;
  oxygenLevel?: OxygenLevelData[] | null;
  additionalOxygenLevel?: AdditionalTimeVolume[] | null;
  fluids?: FliudData[] | null;
  inOt?: boolean | null;
  rapidSequence?: boolean | null;
  inhalational?: boolean | null;
  equipmentCheck?: boolean | null;
  preOxygenation?: boolean | null;
  vascularAccessSite?: string | null;
  preInduction?: string | null;
  postInduction?: string | null;
  nasalSpecs?: boolean | null;
  faceMask?: boolean | null;
  iGel?: boolean | null;
  lma?: boolean | null;
  endatrachealTube?: boolean | null;
  size?: TicknessValue | null;
  cmToLips?: TicknessValue | null;
  cuff?: boolean | null;
  amoured?: boolean | null;
  oralNasal?: boolean | null;
  grade?: string | null;
  difficult?: boolean | null;
  bougie?: boolean | null;
  mcCoy?: boolean | null;
  bougieILMA?: boolean | null;
  fibreOptic?: boolean | null;
  mcIntosh?: boolean | null;
  capnograph?: boolean | null;
  auscultation?: boolean | null;
  nmj?: TicknessSite | null;
  tempProbe?: boolean | null;
  respiration?: string | null;
  mode?: string | null;
  tv?: string | null;
  frequency?: string | null;
  circuit?: string | null;
  other?: string | null;
  eyesCovered?: boolean | null;
  pressurePointsPadded?: boolean | null;
  urinaryCatheter?: boolean | null;
  nasogastricTube?: boolean | null;
  warmingBlanket?: boolean | null;
  position?: string | null;
  arms?: string | null;
  intravenousFluidsGiven?: string | null;
  localAnaestheticTechnique?: string | null;
  clinicalAdverseEvent?: string | null;
}

export interface AntenatalDetailsInput {
  id?: string | null;
  priority?: string | null;
  category?: string | null;
  clinicalDiagnosis?: DiagnosisInput[] | null;
  gravidity?: string | null;
  parity?: string | null;
  visitationDateTime?: any | null;
  counselledFGM?: string | null;
  counselledMaternalNutrition?: string | null;
  counselledFamilyPlanning?: string | null;
  counselledLabourBirthPreparedness?: string | null;
  counselledEarlyInitiationBreastFeeding?: string | null;
  counselledExclusiveBreastFeeding?: string | null;
  counselledPostpartumFamilyPlanning?: string | null;
  givenLlin?: string | null;
  outcomeOfVisit?: string | null;
  position?: string | null;
  presentation?: string | null;
  specifyBreechPresentation?: string | null;
  fetalMovement?: string | null;
  gestationAge?: string | null;
  fetalHeartRate?: string | null;
  bloodPressure?: string | null;
  weight?: string | null;
  weightUnit?: string | null;
  symphysioHeight?: string | null;
  heightUnit?: string | null;
  oedama?: string | null;
  specifyOedama?: string | null;
  lie?: string | null;
  grade?: string | null;
  seenBy?: string | null;
  specialty?: string | null;
  rank?: string | null;
  department?: string | null;
  visitationNote?: string | null;
  provider?: string | null;
  itemId?: string | null;
  concealVisitationNote?: boolean | null;
  treatmentPlan?: string | null;
  concealTreatmentPlan?: boolean | null;
  lastMenstrualPeriod?: any | null;
  estimatedDateOfDelivery?: any | null;
  currentPregnancyNumber?: string | null;
  ancHivIAndIiScreening?: string | null;
  hiv?: string | null;
  ancHepatitisBScreening?: string | null;
  hbvAntibody?: string | null;
  ancHepatitisCScreening?: string | null;
  hcvAntibody?: string | null;
  ancGenotype?: string | null;
  genotype?: string | null;
  ancVdrl?: string | null;
  vdrl?: string | null;
  ancPcvAndHb?: string | null;
  invPcv?: string | null;
  invPcvRange?: string | null;
  invHb?: string | null;
  invHbRange?: string | null;
  ancRandomBloodSugar?: string | null;
  randomBloodSugar?: string | null;
  randomBloodSugarUnit?: string | null;
  randomBloodSugarRange?: string | null;
  ancFastingBloodSugar?: string | null;
  fastingBloodSugar?: string | null;
  fastingBloodSugarUnit?: string | null;
  fastingBloodSugarRange?: string | null;
  ancBloodGrouping?: string | null;
  bloodGrouping?: string | null;
  bloodGroupingRange?: string | null;
  rhesusFactor?: string | null;
  rhesusFactorRange?: string | null;
  ancUrinalysis?: string | null;
  colour?: string | null;
  appearanceClarity?: string | null;
  ph?: string | null;
  specificGravity?: string | null;
  glucose?: string | null;
  ketones?: string | null;
  blood?: string | null;
  protein?: string | null;
  bilirubin?: string | null;
  uribilinogen?: string | null;
  nitrites?: string | null;
  leukocyteEsterase?: string | null;
  rbcs?: string | null;
  wbcs?: string | null;
  epithelialCells?: string | null;
  casts?: string | null;
  crystals?: string | null;
  bacteria?: string | null;
  yeast?: string | null;
  ipt1?: string | null;
  ipt1Ega?: string | null;
  ipt2?: string | null;
  ipt2Ega?: string | null;
  ipt3?: string | null;
  ipt3Ega?: string | null;
  ipt4?: string | null;
  ipt4Ega?: string | null;
  hematinics?: string | null;
  hematinicsEga?: string | null;
  albendazole?: string | null;
  albendazoleEga?: string | null;
  tetanusToxoid1?: string | null;
  tetanusToid1Ega?: string | null;
  tetanusToxoid2?: string | null;
  tetanusToid2Ega?: string | null;
  tetanusToxoid3?: string | null;
  tetanusToid3Ega?: string | null;
  tetanusToxoid4?: string | null;
  tetanusToid4Ega?: string | null;
  tetanusToxoid5?: string | null;
  tetanusToid5Ega?: string | null;
}

export interface AntenatalFilterOptions {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
}

export interface AntenatalInput {
  id?: string | null;
  concealAdditionalNote?: boolean | null;
  clinifyId?: string | null;
  additionalNote?: string | null;
  documentUrl?: string[] | null;
  hospitalName?: string | null;
  hospitalAddress?: string | null;
  provider?: string | null;
  nextAppointmentDateTime?: any | null;
  nextAppointmentEndDateTime?: any | null;
  nextAppointmentDuration?: string | null;
  nextAppointmentSpecialty?: string | null;
  nextAppointmentRole?: string | null;
  nextAppointmentCategory?: string | null;
  nextAppointmentSpecialistId?: string | null;
  nextAppointmentSpecialistName?: string | null;
  nextAppointmentUrgency?: string | null;
  nextAppointmentDeliveryMethod?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  details: AntenatalDetailsInput[];
  medications?: string[] | null;
  vitals?: string[] | null;
  investigations?: string[] | null;
  labTests?: string[] | null;
  radiology?: string[] | null;
  nursingServices?: string[] | null;
  hmoProviderId?: string | null;
  facilityAddress?: string | null;
  billInfo?: BillInfoUpdateInput[] | null;
}

export interface AnthropometryVitalFields {
  id?: string | null;
  readingDateTime?: any | null;
  height?: string | null;
  isHeightCritical?: boolean | null;
  heightUnit?: string | null;
  weight?: string | null;
  isWeightCritical?: boolean | null;
  weightUnit?: string | null;
  bmi?: string | null;
  bsa?: string | null;
  hipCircumference?: string | null;
  isHipCircumferenceCritical?: boolean | null;
  hipCircumferenceUnit?: string | null;
  waistCircumference?: string | null;
  isWaistCircumferenceCritical?: boolean | null;
  waistCircumferenceUnit?: string | null;
  skinfoldThickness?: string | null;
  isSkinfoldThicknessCritical?: boolean | null;
  skinfoldThicknessUnit?: string | null;
  leftUpperLimbCircumference?: string | null;
  rightUpperLimbCircumference?: string | null;
  upperLimbCircumferenceUnit?: string | null;
  leftLowerLimbCircumference?: string | null;
  rightLowerLimbCircumference?: string | null;
  lowerLimbCircumferenceUnit?: string | null;
  leftThighCircumference?: string | null;
  rightThighCircumference?: string | null;
  thighCircumferenceUnit?: string | null;
  abdominalGirth?: string | null;
  isAbdominalGirthCritical?: boolean | null;
  abdominalGirthUnit?: string | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
}

export interface AssessmentInput {
  vteRiskAssessed?: boolean | null;
  bleedingRiskAssessed?: boolean | null;
  vteRiskAssessedLastModifierName?: string | null;
  vteRiskAssessedLastModifiedDate?: any | null;
  bleedingRiskAssessedLastModifierName?: string | null;
  bleedingRiskAssessedLastModifiedDate?: any | null;
  ref?: string | null;
}

export interface AssignedTo {
  specialtyAssignedTo?: string | null;
  roleAssignedTo?: string | null;
  assignedTo?: string | null;
}

export interface AuditFilter {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  entity?: string | null;
  entityType?: AuditEntities | null;
  mutator?: string | null;
  action?: AuditAction | null;
}

export interface BackgroundInformationInput {
  id?: string | null;
  maritalStatus?: string | null;
  numberOfChildren?: number | null;
  education?: string | null;
  state?: string | null;
  religion?: string | null;
  occupation?: string | null;
  salaryRange?: string | null;
  bloodDonor?: string | null;
  nationality?: string | null;
  originLga?: string | null;
  ethnicity?: string | null;
  organDonor?: string | null;
  preferredLanguage?: string | null;
  modeOfCommunication?: string | null;
  tissueDonor?: string | null;
  boneMarrowDonor?: string | null;
}

export interface BankInformationInput {
  accountName?: string | null;
  accountNumber: string;
  bankCode?: string | null;
  bankName: string;
  branchName?: string | null;
  accountType?: string | null;
}

export interface BankTransactionFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  status?: string | null;
  transactionCategory?: string | null;
  transactionType?: string | null;
}

export interface BenefitCustomPriceInput {
  hospitalId: string;
  benefitId: string;
  price?: number | null;
  utilisationCode: string;
}

export interface BillDetailInput {
  id?: string | null;
  amount?: number | null;
  amountDue?: number | null;
  amountPaid?: number | null;
  amountOwing?: number | null;
  amountOutstanding?: number | null;
  discountAmount?: number | null;
  discountPercentage?: number | null;
  vatAmount?: number | null;
  vatPercentage?: number | null;
  quantity?: number | null;
  unitPrice?: number | null;
  discountCode?: string | null;
  description?: string | null;
  serviceType?: string | null;
  serviceName?: string | null;
  patientType?: string | null;
  paymentType?: string | null;
  reference?: string | null;
  bankName?: string | null;
  accountNumber?: string | null;
  billType: string;
  billName: string;
  serviceDetails?: BillServiceDetailInput[] | null;
  splitPayment?: boolean | null;
  additionalPayments?: AdditionalPaymentInput[] | null;
  splitReference?: string | null;
  paymentMethod?: string | null;
  professionalFeeAmount?: number | null;
  professionalFeePercentage?: number | null;
  walletTransactionRef?: string | null;
  excluded?: boolean | null;
  subServiceType?: string | null;
  manuallyCreated?: string | null;
  subBills?: SubBillInput[] | null;
}

export interface BillFilter {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  archive?: boolean | null;
  referral?: boolean | null;
  autoGenerated?: boolean | null;
  status?: BillStatus | null;
  clinifyId?: string | null;
  recalled?: boolean | null;
  serviceType?: string | null;
  coverageType?: string | null;
  walkInBill?: boolean | null;
  paymentType?: string | null;
}

export interface BillInfoUpdateInput {
  priceId?: string | null;
  type?: string | null;
  name?: string | null;
  quantity?: string | null;
  pricePerUnit?: number | null;
  itemId?: string | null;
  patientType?: string | null;
  paymentType?: string | null;
  reference?: string | null;
  ref?: string | null;
  recordName?: string | null;
}

export interface BillServiceDetailInput {
  serviceType?: string | null;
  serviceName?: string | null;
}

export interface BillUpdate {
  receiverClinifyId?: string | null;
  totalAmount?: number | null;
  discountAmount?: number | null;
  vatAmount?: number | null;
  description?: string | null;
  serviceType?: ServiceType | null;
  invoiceNumber?: string | null;
}

export interface BillableClaimInput {
  recordType: BillableClaimModel;
  recordId: string;
  hmoProviderId: string;
}

export interface BloodGlucoseVitalFields {
  id?: string | null;
  readingDateTime?: any | null;
  reading?: string | null;
  isReadingCritical?: boolean | null;
  readingUnit?: string | null;
  mealTime?: string | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
}

export interface BloodPressureVitalFields {
  id?: string | null;
  readingDateTime?: any | null;
  diastolic?: string | null;
  isDiastolicCritical?: boolean | null;
  systolic?: string | null;
  isSystolicCritical?: boolean | null;
  position?: string | null;
  meanArterialPressure?: string | null;
  heartRate?: string | null;
  fetalHeartRate?: string | null;
  rhythm?: string | null;
  isHeartRateCritical?: boolean | null;
  isFetalHeartRateCritical?: boolean | null;
  location?: string | null;
  method?: string | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
}

export interface BloodTransfusionInput {
  id?: string | null;
  transfusionDateTime?: any | null;
  transfusionOrderGiven: string;
  transfusionDoctor?: string | null;
  transfusionNurse?: string | null;
  patientBloodGroup?: string | null;
  patientGenoType?: string | null;
  crossMatchingTime?: string | null;
  bloodProduct?: string | null;
  bloodLabel?: string | null;
  expiryDate?: string | null;
  donorBloodType?: string | null;
  otherBloodType?: string | null;
  bloodPint?: string | null;
  lengthOfTransfusion?: string | null;
  transfusionStartDateTime?: any | null;
  transfusionEndDateTime?: any | null;
  adverseReaction?: string | null;
  reaction?: string | null;
  transfusionNote?: string | null;
  concealTransfusionNote?: boolean | null;
  patientConsent?: string | null;
  patientConsentSignature?: string | null;
  patientConsentSignatureType?: string | null;
  patientConsentSignatureDateTime?: any | null;
  consentReason?: string | null;
  bloodSource?: string | null;
  bloodDonorStatus?: string | null;
  diuretic?: string | null;
  diureticType?: string | null;
  postTransfusionFBC?: string | null;
  concealPostTransfusionFBC?: boolean | null;
}

export interface BranchInformationInput {
  name: string;
  address: string;
  website?: string | null;
  supportMail?: string | null;
  administratorTitle?: string | null;
  administratorFirstName: string;
  administratorLastName: string;
  administratorMiddleName?: string | null;
  phoneNumber?: PhoneNumberInput | null;
  secondaryPhoneNumber?: PhoneNumberInput | null;
  useHQFacilityTariffs?: boolean | null;
  useHQFacilityInventory?: boolean | null;
}

export interface BulkEnrolleeRegistrationInput {
  enrolleeTitle?: string | null;
  enrolleeFirstName: string;
  enrolleeLastName: string;
  enrolleeMiddleName?: string | null;
  enrolleeGender?: string | null;
  enrolleeDateOfBirth?: string | null;
  enrolleePrimaryPhoneNumber?: string | null;
  enrolleeSecondaryPhoneNumber?: string | null;
  enrolleeMemberNumber?: string | null;
  enrolleeEmailAddress?: string | null;
  enrolleeLGAOfResidence: string;
  enrolleeCountryOfOrigin?: string | null;
  enrolleeStateOfOrigin?: string | null;
  enrolleeContactAddress?: string | null;
  planCategory?: string | null;
  planCode?: string | null;
  planStartDate?: string | null;
  planDueDate?: string | null;
  paymentFrequency?: string | null;
  planStatus?: string | null;
  registrationDateAndTime?: string | null;
  enrolledBy?: string | null;
  paymentDateAndTime?: string | null;
  pictureUrl?: string | null;
  primaryProviderName?: string | null;
  enrolleeMaritalStatus?: string | null;
  enrolleePlaceOfBirth?: string | null;
  enrolleeBloodGroup?: string | null;
  enrolleeCity?: string | null;
  enrolleeWeight?: string | null;
  enrolleeHeight?: string | null;
  enrolleeOccupation?: string | null;
  enrolleePlaceOfWork?: string | null;
  referralCode?: string | null;
  enrolleeEmployeeNumber?: string | null;
  enrolleeGroupCode?: string | null;
  agentEmail?: string | null;
  memberOrDependent?: MemberOrDependent | null;
  relationship?: string | null;
  employerName?: string | null;
  employerNumber?: string | null;
  employerCode?: string | null;
}

export interface BulkEnrolleesRegistrationInput {
  enrollees: BulkEnrolleeRegistrationInput[];
}

export interface BusinessRuleFilterInput {
  skip?: number | null;
  take?: number | null;
  keyword?: string | null;
}

export interface BusinessRuleItemExtraInput {
  frequencyUnit?: string | null;
  frequencyTarget?: string | null;
  frequencyTargetValue?: string | null;
  frequencyTargetUnit?: string | null;
  frequencyTargetQuantity?: string | null;
  frequencyTargetOperator?: string | null;
}

export interface CancerScreeningInput {
  id?: string | null;
  clinifyId?: string | null;
  screeningToBeDone?: string | null;
  screeningType?: string | null;
  seenRiskAssessmentForm?: string | null;
  mammogramDate?: any | null;
  breastUltrasoundDate?: any | null;
  cervicalScreeningDate?: any | null;
  clinicalBreastExaminationDate?: any | null;
  digitalRectalExaminationDate?: any | null;
  prostrateSpecificAntigenDate?: any | null;
  faecalImmunochemicalOccultDate?: any | null;
  colonoscopyDateTime?: any | null;
  hpvDnaDate?: any | null;
  lastHpvVaccineDate?: any | null;
  otherScreeningInformation?: string | null;
  lastMenstrualPeriod?: any | null;
  existingPastMedicalCondition?: string | null;
  currentDrugs?: string | null;
  pregnancies?: string | null;
  terminationsOrMiscarriages?: string | null;
  contraceptiveMethod?: string | null;
  tubalLitigation?: string | null;
  vasectomy?: string | null;
  pastBiopsies?: string | null;
  otherMedicalInformation?: string | null;
  pastResultsAvailable?: string | null;
  specifyPastResult?: string | null;
  procedureAndFindingDocumented?: string | null;
  laboratoryFormRequested?: string | null;
  planOrRecommendations?: string | null;
  otherComments?: string | null;
  followUpAppointmentWithResult?: string | null;
  recallAppointment?: string[] | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
  documentUrl?: string[] | null;
}

export interface CapitationPayoutInput {
  hospitalId: string;
  enrolleeCount: number;
  totalCapitationAmount: number;
  totalAmountForPayout?: number | null;
  transferFundId?: string | null;
  hmoPlanTypeId?: string | null;
  payoutDecreasePercentage?: number | null;
  perPlanPayoutDecreasePercentage?: PerPlanPayoutDecreasePercentage[] | null;
}

export interface CasesAnalyticsFilter {
  startDate?: string | null;
  endDate?: string | null;
  duration?: string | null;
  ageRange?: string | null;
  gender?: string | null;
  type?: string | null;
  isPregnant?: boolean | null;
  result?: string | null;
  inPatient?: boolean | null;
  overall?: boolean | null;
  hospitalId?: string | null;
}

export interface CervixDecent {
  readingDate?: any | null;
  cervix?: string | null;
  decent?: string | null;
  period?: any | null;
  time?: string | null;
}

export interface ChargeCardInput {
  amount?: string | null;
  passCode?: string | null;
  cardId?: string | null;
}

export interface ChargePatientWalletInput {
  amount: number;
  senderClinifyId: string;
  receiverClinifyId: string;
  phoneNumber: string;
  otpCode?: string | null;
  transactionReference: string;
}

export interface ChargePatientWalletOrgBillInput {
  amount: number;
  senderClinifyId: string;
  receiverClinifyId: string;
  phoneNumber: string;
  otpCode?: string | null;
  transactionReference: string;
  billDetailId: string;
}

export interface ChatMessageFilters {
  limit?: number | null;
  offset?: number | null;
  before?: any | null;
  after?: any | null;
  messageType?: MessageType | null;
}

export interface CheckinInput {
  providerId: string;
  enrollees: EnrolleesToCheckin[];
}

export interface ChemoDiagnosisCycleTemplateInput {
  id?: string | null;
  cycleNumber: number;
  drugs: ChemoDiagnosisDrugTemplateInput[];
  investigationDetails?: ChemoInvestigationDetailsInput[] | null;
}

export interface ChemoDiagnosisDrugTemplateInput {
  ref: string;
  day: string;
  drugName: string;
  drugId?: string | null;
  dosage?: string | null;
  dosagePercentage?: string | null;
  totalDose?: string | null;
  adjustedDose?: string | null;
  quantity?: string | null;
  route?: string | null;
  infusionUsed?: string | null;
  frequency?: string | null;
  note?: string | null;
  inventoryClass?: string | null;
}

export interface ChemoDiagnosisTemplateInput {
  facilityPreferenceId: string;
  type: string;
  combinationName: string;
  cycles: ChemoDiagnosisCycleTemplateInput[];
  section: string;
}

export interface ChemoInvestigationDetailsInput {
  investigationType?: string | null;
  investigationName?: string | null;
  investigationPerformed?: boolean | null;
  investigationVerified?: boolean | null;
}

export interface ClaimsApprovalInput {
  status?: string | null;
  comment?: string | null;
  rejectionReason?: string[] | null;
  specifyReasonForRejection?: string | null;
  statusDescription?: string | null;
  serviceAmount?: number | null;
  vettingGroup: string;
  creatorId: string;
  creatorName: string;
  createdDate: any;
  updatedDate?: any | null;
  isAutoProcessed?: boolean | null;
}

export interface CompleteRegistrationInput {
  details: CreatePersonalInformationInput;
  nextOfKin?: SelfRegistrationNextOfKinInput | null;
  userType: SubUserType;
  subUserType?: UserType | null;
  email?: string | null;
  country?: string | null;
  nationality?: string | null;
  state?: string | null;
  phoneNumber?: string | null;
  enrolleeId?: string | null;
  enrolleePlanType?: string | null;
  facilityId?: string | null;
  providerId?: string | null;
  providerCode?: string | null;
  coverageName?: string | null;
  contactAddress?: string | null;
  companyName?: string | null;
  companyAddress?: string | null;
  familyName?: string | null;
  familyAddress?: string | null;
  coverageType?: string | null;
  tempUserId?: string | null;
  enrolleePlanCategory?: string | null;
  enrolleePlanFrequency?: string | null;
  registrationSource?: string | null;
  dependents?: SelfRegistrationDependentInput[] | null;
}

export interface CompleteSignupInput {
  tempPhoneNumberId: string;
  country: string;
  passCode: string;
  email: string;
}

export interface ConditionInput {
  diagnosisICD10?: string | null;
  diagnosisICD11?: string | null;
  diagnosisSNOMED?: string | null;
  ageOfOnset?: string | null;
}

export interface ConsultationFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  outPatient?: boolean | null;
  referralOption?: ReferralOptions | null;
  providerInsight?: boolean | null;
  hospitalId?: string | null;
}

export interface ConsultationInput {
  id?: string | null;
  clinifyId?: string | null;
  consultationDateTime?: any | null;
  duration?: string | null;
  doctorName?: string | null;
  priority?: string | null;
  specialty?: string | null;
  class?: string | null;
  department?: string | null;
  category?: string | null;
  provider?: string | null;
  providerServiceName?: string | null;
  nextAppointmentDateTime?: any | null;
  nextAppointmentEndDateTime?: any | null;
  nextAppointmentDuration?: string | null;
  nextAppointmentSpecialty?: string | null;
  nextAppointmentRole?: string | null;
  nextAppointmentCategory?: string | null;
  nextAppointmentSpecialistId?: string | null;
  nextAppointmentSpecialistName?: string | null;
  nextAppointmentUrgency?: string | null;
  nextAppointmentDeliveryMethod?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  consultationStartDate?: any | null;
  consultationEndDate?: any | null;
  clinicName?: string | null;
  clinicAddress?: string | null;
  documentUrl?: string[] | null;
  complaint?: string | null;
  concealComplaint?: boolean | null;
  complaintHistory?: string | null;
  concealComplaintHistory?: boolean | null;
  healthEducation?: string | null;
  concealHealthEducation?: boolean | null;
  observationNote?: string | null;
  concealObservationNote?: boolean | null;
  systemReview?: string | null;
  systemReviewSmartText?: string | null;
  systemReviewSmartSelection?: SelectionInput | null;
  complaintSmartText?: string | null;
  complaintSmartSelection?: SelectionInput | null;
  complaintGender?: string | null;
  positiveSymptoms?: string[] | null;
  concealSystemReview?: boolean | null;
  physicalExam?: string | null;
  physicalExamSmartText?: string | null;
  physicalExamSmartSelection?: SelectionInput | null;
  concealPhysicalExam?: boolean | null;
  treatmentPlan?: string | null;
  patientConsentSignature?: string | null;
  patientConsentSignatureType?: string | null;
  patientConsentSignatureDateTime?: string | null;
  concealTreatmentPlan?: boolean | null;
  patientAdmitted?: string | null;
  admissionConsent?: string | null;
  adverseEffectsFollowingTreatment?: string | null;
  stateEffects?: string | null;
  adverseEffectsInvestigated?: string | null;
  outcomeOfInvestigation?: string | null;
  treatmentGiven?: string | null;
  treatmentStatus?: string | null;
  provisionalDiagnosis?: DiagnosisInput[] | null;
  finalDiagnosis?: DiagnosisInput[] | null;
  admissions?: string[] | null;
  allergies?: string[] | null;
  labTests?: string[] | null;
  investigations?: string[] | null;
  medications?: string[] | null;
  radiology?: string[] | null;
  surgeries?: string[] | null;
  vitals?: string[] | null;
  nursingServices?: string[] | null;
  referral?: boolean | null;
  externalReferral?: boolean | null;
  specialtyReferredTo?: string | null;
  referredToProfileId?: string | null;
  referralReason?: string | null;
  referralDate?: any | null;
  concealReferralReason?: boolean | null;
  audiometry?: string | null;
  concealAudiometry?: boolean | null;
  hmoProviderId?: string | null;
}

export interface ConsultationsTemplateInput {
  name: string;
  facilityPreferenceId: string;
  complaints?: string | null;
  historyComplaints?: string | null;
  reviewSystems?: string | null;
  physicalExamination?: string | null;
  audiometry?: string | null;
  healthEducation?: string | null;
  treatmentPlan?: string | null;
  id: string;
}

export interface ContactFilterInput {
  type?: ContactType | null;
}

export interface Contraction {
  readingDate?: any | null;
  value?: string | null;
  duration?: string | null;
  time?: string | null;
}

export interface ConversationFilters {
  limit?: number | null;
  offset?: number | null;
  search?: string | null;
}

export interface CorporateLoginInput {
  email: string;
  password: string;
}

export interface CoverageDetailsInput {
  id?: string | null;
  clinifyId?: string | null;
  memberNumber?: string | null;
  membershipNumber?: string | null;
  memberPlan?: string | null;
  memberPlanId?: string | null;
  parentMemberUniqueId?: string | null;
  memberUniqueId?: string | null;
  employeeNumber?: string | null;
  providerId?: string | null;
  memberStartDate?: any | null;
  memberDueDate?: any | null;
  memberStatus?: string | null;
  enrollmentDateTime?: any | null;
  enrolledBy?: string | null;
  capturedBy?: string | null;
  companyName?: string | null;
  companyAddress?: string | null;
  primaryProviderId?: string | null;
  primaryProviderName?: string | null;
  secondaryProviderName?: string | null;
  tertiaryProviderName?: string | null;
  primaryProviderAddress?: string | null;
  secondaryProviderAddress?: string | null;
  tertiaryProviderAddress?: string | null;
  capitatedMember?: string | null;
  capitatedAmount?: string | null;
  capturedDate?: any | null;
  employeeType?: string | null;
  employeeDivision?: string | null;
  memberPlanGroup?: string | null;
  memberPlanSubGroup?: string | null;
  occupation?: string | null;
  retired?: boolean | null;
  approvedBy?: string | null;
  paymentDateTime?: any | null;
  paymentFrequency?: string | null;
  terminationDate?: any | null;
  premiumCollected?: string | null;
  premiumOutstanding?: string | null;
  registrationSource?: string | null;
  administrationAgency?: string | null;
  commissionRate?: string | null;
  commissionPayable?: string | null;
  tpaNonTpaCommissionRate?: string | null;
  tpaNonTpaCommissionPayable?: string | null;
  enrollmentAgent?: string | null;
  enrollmentAgency?: string | null;
  enrollmentTpaNonTpa?: string | null;
  enrollmentAgentId?: string | null;
  enrollmentAgencyId?: string | null;
  enrollmentTpaNonTpaId?: string | null;
  planCategory?: string | null;
  salesWing?: string | null;
  channelDivisions?: string | null;
  agentName?: string | null;
  agentPhoneNumber?: string | null;
  sponsorType?: string | null;
  sponsorName?: string | null;
  sponsorRef?: string | null;
  activationDatetime?: any | null;
  sponsorCode?: string | null;
  referrerCode?: string | null;
  referrer?: string | null;
  referrerNumber?: string | null;
  referrerCommissionRate?: string | null;
  referrerCommissionPayable?: string | null;
  employerCode?: string | null;
  planCode?: string | null;
  nextRenewalDate?: any | null;
  questionnaireData?: QuestionnaireInput | null;
  totalPremiumAmountPaid?: string | null;
  groupCode?: string | null;
  agentEmail?: string | null;
  coverageType?: string | null;
  employerId?: string | null;
  name?: string | null;
  contactAddress?: string | null;
  familyName?: string | null;
  familyAddress?: string | null;
  provider?: CoverageHmoProviderInput | null;
  fileNumber?: string | null;
  existingFamily?: boolean | null;
  virtualServicesPayment?: CoverageVirtualServicesPaymentInput | null;
}

export interface CoverageHmoProviderInput {
  id?: string | null;
  name?: string | null;
}

export interface CoverageLookupOptions {
  providerId: string;
  enrolleeId: string;
}

export interface CoverageVirtualServicesPaymentInput {
  id?: string | null;
  paymentMethod?: string | null;
  amountDue?: number | null;
  amountPaid?: number | null;
  paymentStatus?: string | null;
  registrationHmoProfileId?: string | null;
  controlledCommissionFee?: PercentOrAmount | null;
  commissionFeeAmount?: number | null;
  commissionFeePercentage?: number | null;
  createdDate: any;
  updatedDate?: any | null;
}

export interface CreateAndAssignToHospitalInput {
  email: string;
  phoneNumber?: PhoneNumberInput | null;
  firstName: string;
  lastName: string;
  middleName?: string | null;
  title?: string | null;
  gender: Gender;
  department?: string | null;
  speciality?: string | null;
  rank?: string | null;
  userType: HospitalUserType;
  typeAlias?: string | null;
  userRole?: string | null;
  active?: boolean | null;
}

export interface CreateBillInput {
  receiverClinifyId: string;
  totalAmount?: number | null;
  discountAmount?: number | null;
  vatAmount?: number | null;
  description: string;
  reference?: string | null;
  serviceType: ServiceType;
  invoiceNumber?: string | null;
  receiverProfileType: AccountHolder;
  passCode: string;
}

export interface CreateDraftInput {
  type: string;
  value: string;
}

export interface CreateEmployeeInput {
  title?: string | null;
  firstName: string;
  lastName: string;
  middleName?: string | null;
  employeeId?: string | null;
  jobTitle?: string | null;
  department?: string | null;
  phoneNumber?: PhoneNumberInput | null;
  emailAddress?: string | null;
  address?: string | null;
  employerId?: string | null;
  hmoPlanTypeId?: string | null;
  planCategory?: string | null;
  enrolleeId?: string | null;
  enrolledBy?: string | null;
  paymentDate?: any | null;
  paymentFrequency?: string | null;
  enrollmentDateTime?: any | null;
  planTypeCategory?: string | null;
  premiumAmount?: string | null;
  registrationSource?: string | null;
  administrationAgency?: string | null;
  enrollmentAgent?: string | null;
  enrollmentAgency?: string | null;
  enrollmentTpaNonTpa?: string | null;
  enrollmentAgentId?: string | null;
  enrollmentAgencyId?: string | null;
  enrollmentTpaNonTpaId?: string | null;
  salesWing?: string | null;
  commissionPayable?: string | null;
  commissionRate?: string | null;
  tpaNonTpaCommissionRate?: string | null;
  tpaNonTpaCommissionPayable?: string | null;
  sponsorType?: string | null;
  sponsorName?: string | null;
  activationDatetime?: any | null;
  sponsorCode?: string | null;
  referrerCode?: string | null;
  referrer?: string | null;
  referrerNumber?: string | null;
  referrerCommissionRate?: string | null;
  referrerCommissionPayable?: string | null;
  planStartDate?: any | null;
  planDueDate?: any | null;
  employerCode?: string | null;
  planCode?: string | null;
  nextRenewalDate?: any | null;
  hmoPlanStatus?: HmoPlanStatus | null;
  gender?: Gender | null;
  dateOfBirth?: any | null;
  employeeType?: string | null;
  dependants?: CreateEmployerDependantInput[] | null;
  primaryProviderId?: string | null;
  primaryProviderName?: string | null;
  primaryProviderAddress?: string | null;
  displayPictureUrl?: string | null;
}

export interface CreateEmployerDependantInput {
  title?: string | null;
  firstName: string;
  lastName: string;
  middleName?: string | null;
  gender?: string | null;
  dateOfBirth?: any | null;
  phoneNumber?: PhoneNumberInput | null;
  emailAddress?: string | null;
  relationship: string;
  enrolleeId?: string | null;
  primaryProviderId?: string | null;
  primaryProviderName?: string | null;
  primaryProviderAddress?: string | null;
  displayPictureUrl?: string | null;
}

export interface CreateEmployerInput {
  displayUrl?: string | null;
  employerName: string;
  employerAddress?: string | null;
  country?: string | null;
  state?: string | null;
  localGovernmentArea?: string | null;
  ward?: string | null;
  employerPrimaryPhoneNumber?: PhoneNumberInput | null;
  employerPrimaryEmailAddress?: string | null;
  employerSecondaryPhoneNumber?: PhoneNumberInput | null;
  employerSecondaryEmailAddress?: string | null;
  contactPersonTitle?: string | null;
  contactPersonFirstName?: string | null;
  contactPersonMiddleName?: string | null;
  contactPersonLastName?: string | null;
  contactPersonPhoneNumber?: PhoneNumberInput | null;
  contactPersonEmailAddress?: string | null;
  contactPersonAltTitle?: string | null;
  contactPersonAltFirstName?: string | null;
  contactPersonAltMiddleName?: string | null;
  contactPersonAltLastName?: string | null;
  contactPersonAltPhoneNumber?: PhoneNumberInput | null;
  contactPersonAltEmailAddress?: string | null;
  numberOfEmployees?: number | null;
  planAmount?: number | null;
  planAmountFlag?: string | null;
  planAgent?: string | null;
  paymentFrequency?: string | null;
  employerNumber?: string | null;
  employerPlanCode?: string | null;
  planGroup?: string | null;
  planSubGroup?: string | null;
  referrer?: string | null;
  referrerNumber?: string | null;
  referrerCode?: string | null;
  commissionRate?: string | null;
  commissionPayable?: string | null;
  additionalNote?: string | null;
  hmoProviderId?: string | null;
  employees?: CreateEmployeeInput[] | null;
  selectedMemberPlans?: SelectedMemberPlanInput[] | null;
}

export interface CreateHandoverNoteInput {
  handoverDateTime: any;
  handoverById: string;
  name: string;
  department?: string | null;
  specialty?: string | null;
  staffs: HandoverStaffInput[];
  items: HandoverNoteItemInput[];
  additionalNotes: AdditionalNoteInput[];
}

export interface CreateHospitalInput {
  country: string;
  hospitalName: string;
  hospitalAddress: string;
  hospitalWebsite?: string | null;
  hospitalContactTitle?: string | null;
  hospitalContactFirstName: string;
  hospitalContactMiddleName?: string | null;
  hospitalContactLastName: string;
  hospitalContactEmail: string;
  hospitalContactPhoneNumber: PhoneNumberInput;
  ownership?: string | null;
  state?: string | null;
  lga?: string | null;
  politicalWard?: string | null;
  city?: string | null;
  level?: string | null;
  hospitalLicenseNumber?: string | null;
  documentUrl?: string[] | null;
  plan: HospitalPlan;
  partnerCode?: string | null;
  wemaAccountNumber?: string | null;
  wemaAccountName?: string | null;
  providerCode?: string | null;
}

export interface CreatePaymentInput {
  amount: number;
  receiverClinifyId: string;
  transactionType: TransactionType;
  passCode: string;
  description?: string | null;
  transactionReference: string;
}

export interface CreatePersonalInformationInput {
  id?: string | null;
  displayPictureUrl?: string | null;
  title?: string | null;
  gender?: Gender | null;
  firstName: string;
  middleName?: string | null;
  lastName: string;
  secondaryEmail?: string | null;
  secondaryPhoneNumber?: PhoneNumberInput | null;
  dateOfBirth?: any | null;
  consultationFee?: number | null;
  consultationLength?: number | null;
  bloodGroup?: string | null;
  genoType?: string | null;
  weight?: string | null;
  weightUnit?: string | null;
  height?: string | null;
  heightUnit?: string | null;
  address?: string | null;
  bio?: string | null;
  registrationNumber?: string | null;
  registrationDate?: any | null;
  folioNumber?: string | null;
  folioNumberIssueDate?: string | null;
  folioNumberExpiryDate?: string | null;
  folioNumberIssuer?: string | null;
  department?: string | null;
  rank?: string | null;
  expiryDate?: any | null;
  speciality?: string | null;
  patientFileOrCardNo?: string | null;
  yearsOfPractice?: number | null;
  serviceDetails?: ServiceDetailInput[] | null;
  hospital?: string | null;
  fileNumbers?: FileNumber[] | null;
  clinicalTrials?: string | null;
  coverageDetails?: CoverageDetailsInput[] | null;
  coverageIdsToDelete?: string[] | null;
  shareData?: boolean | null;
  documentUrl?: string[] | null;
  dataAccessType?: string | null;
  patientStatus?: string | null;
  deathDateTime?: any | null;
  deathLocation?: string | null;
  causeOfDeath?: string | null;
  unavailableDates?: string[] | null;
  registrationNote?: string | null;
  nin?: string | null;
  votersId?: string | null;
  passportNumber?: string | null;
  bvn?: string | null;
  lga?: string | null;
  ward?: string | null;
  buildingName?: string | null;
  buildingLevel?: string | null;
  countryOfResidence?: string | null;
  stateOfResidence?: string | null;
  patientCategory?: string[] | null;
  nationality?: string | null;
  state?: string | null;
  originLga?: string | null;
  city?: string | null;
  placeOfBirth?: string | null;
  userRole?: string | null;
}

export interface DateRangeInput {
  from?: any | null;
  to?: any | null;
}

export interface DeathsAnalyticsFilter {
  startDate?: string | null;
  endDate?: string | null;
  duration?: string | null;
  ageRange?: string | null;
  gender?: string | null;
  type?: string | null;
  isPregnant?: boolean | null;
  result?: string | null;
  inPatient?: boolean | null;
  overall?: boolean | null;
  hospitalId?: string | null;
}

export interface DeleteAllVitalsInput {
  anthropometryId?: string | null;
  bloodPressureId?: string | null;
  temperatureId?: string | null;
  pulseRateId?: string | null;
  respiratoryRateId?: string | null;
  bloodGlucoseId?: string | null;
  painId?: string | null;
}

export interface DeleteCardInput {
  id?: string | null;
}

export interface DependentInfoInput {
  fullName: string;
  enrolleeId?: string | null;
  relationship?: string | null;
}

export interface DependentInput {
  id?: string | null;
  clinifyId: string;
  title?: string | null;
  firstName?: string | null;
  middleName?: string | null;
  lastName?: string | null;
  gender?: Gender | null;
  dateOfBirth?: any | null;
  bloodGroup?: string | null;
  genoType?: string | null;
  relationship?: string | null;
  enrolleeId?: string | null;
  phoneNumber?: PhoneNumberInput | null;
  emailAddress?: string | null;
  primaryProviderName?: string | null;
  primaryProviderId?: string | null;
  primaryProviderAddress?: string | null;
  displayPictureUrl?: string | null;
  principalPhoneNumber?: string | null;
}

export interface DevelopmentalHistoryInput {
  id?: string | null;
  clinifyId: string;
  groupName: string;
  ageRange: string;
  milestone: string;
  whenMet?: string | null;
}

export interface DiagnosisInput {
  diagnosisICD10?: string | null;
  diagnosisICD11?: string | null;
  diagnosisSNOMED?: string | null;
}

export interface DiagnosisSurveillanceInput {
  startDate?: string | null;
  endDate?: string | null;
  duration?: string | null;
  diagnosisType?: string | null;
  hospitalId?: string | null;
  ageRange?: string | null;
  lga?: string | null;
}

export interface DisabilityInput {
  id?: string | null;
  clinifyId: string;
  disability?: string | null;
  type?: string | null;
  additionalNote?: string | null;
}

export interface DischargePatientInput {
  id?: string | null;
  dischargeDate?: any | null;
  dischargeDiagnosis?: DiagnosisInput[] | null;
  dischargedStatus?: string | null;
  deathDateTime?: any | null;
  deathCause?: string | null;
  deathLocation?: string | null;
  deathCertificateIssued?: string | null;
  followupDate?: any | null;
  dischargeSummary: string;
  concealDischargeSummary?: boolean | null;
  dischargedBy?: string | null;
  causeOfDeath?: string | null;
  dischargedBySignature?: string | null;
  dischargedBySignatureType?: string | null;
  dischargedBySignatureDateTime?: any | null;
  dischargeLocation?: string | null;
  dischargeAddress?: string | null;
}

export interface DischargeSummaryTemplateInput {
  name: string;
  facilityPreferenceId: string;
  summary: string;
  id: string;
}

export interface DispenseAudit {
  fullName?: string | null;
  dateTime?: any | null;
  desc?: string | null;
  profileId?: string | null;
  checkId?: number | null;
}

export interface DispenseConsumable {
  name?: string | null;
  drugInventoryId?: string | null;
  quantityConsumed?: string | null;
  quantityRemaining?: string | null;
  unitPrice?: number | null;
  inventoryClass?: string | null;
}

export interface DispenseDetailsInput {
  id?: string | null;
  clinifyId?: string | null;
  dispenseDate?: any | null;
  medicationDetailId?: string | null;
  dispenseNote?: string | null;
  concealDispenseNote?: boolean | null;
  medicationName?: string[] | null;
  dispenseServiceDetails?: ServiceDetailInput[] | null;
  dispensedBy?: string | null;
  quantityRemaining?: string | null;
  quantityDispensed?: string | null;
  unitPrice?: number | null;
  dispenseConsumables?: DispenseConsumable[] | null;
  drugInventoryId?: string | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  inventoryClass?: string | null;
  option?: MedicationOptionType | null;
  hmoProviderId?: string | null;
}

export interface DrugGiven {
  readingDate?: any | null;
  time?: string | null;
  drugName?: string | null;
  dosage?: string | null;
  routeOfAdministration?: string | null;
  dosageUnit?: string | null;
}

export interface DrugIv {
  readingDate?: any | null;
  drugName?: string[] | null;
  time?: string | null;
}

export interface EditHandoverNoteInput {
  handoverDateTime: any;
  handoverById: string;
  name: string;
  department?: string | null;
  specialty?: string | null;
}

export interface EditHmoClaimInput {
  clinifyId: string;
  claimDate: any;
  treatmentStartDate?: any | null;
  treatmentEndDate?: any | null;
  facilityName?: string | null;
  status?: string | null;
  facilityAddress?: string | null;
  rank?: string | null;
  department?: string | null;
  specialty?: string | null;
  presentingComplain?: string | null;
  referredBy?: string | null;
  referralCode?: string | null;
  referredFrom?: string | null;
  referredTo?: string | null;
  providerId: string;
  serviceType: string;
  serviceTypeCode: string;
  serviceName?: string | null;
  priority?: string | null;
  diagnosis: DiagnosisInput[];
  utilizations: PreauthUtilisationInput[];
  patientType?: string | null;
  paymentType?: string | null;
  additionalNote?: string | null;
  documentUrl?: string[] | null;
  memberUniqueId?: string | null;
  memberPlanId?: string | null;
  receiptNo?: string | null;
  autoGenerated?: boolean | null;
  facilityId?: string | null;
  enrolleePhoneNumber?: PhoneNumberInput | null;
  enrolleeEmail?: string | null;
  isExternalPlanType?: boolean | null;
  externalPlanTypeId?: string | null;
  id: string;
  claimId?: string | null;
  claimIdentity?: string | null;
}

export interface EditHospitalStaffInput {
  email: string;
  phoneNumber?: PhoneNumberInput | null;
  firstName: string;
  lastName: string;
  middleName?: string | null;
  title?: string | null;
  gender: Gender;
  department?: string | null;
  speciality?: string | null;
  rank?: string | null;
  userRole?: string | null;
  active?: boolean | null;
}

export interface EditInvoiceInput {
  issueDate?: any | null;
  dueDate?: any | null;
  description?: string | null;
  recipient: InvoiceRecipientInput;
  profileId?: string | null;
  employerId?: string | null;
  employeesDetails?: EmployeesInfoInput[] | null;
  controlledVat?: PercentOrAmount | null;
  sponsorRef?: string | null;
  sponsorName?: string | null;
  sponsorDetails?: SponsorEnrolleeDetailsInput[] | null;
  vatAmount?: number | null;
  vatPercentage?: number | null;
  controlledDiscount?: PercentOrAmount | null;
  discountAmount?: number | null;
  discountPercentage?: number | null;
  controlledProfessionalFee?: PercentOrAmount | null;
  professionalFeeAmount?: number | null;
  professionalFeePercentage?: number | null;
  isDraft?: boolean | null;
  additionalNote?: string | null;
  sponsorLivesCovered?: number | null;
  agencyLivesCovered?: number | null;
  sponsorPremiumPerLives?: number | null;
  nextYearlyPremium?: number | null;
  periodStartDate?: any | null;
  periodEndDate?: any | null;
  paymentFrequency?: string | null;
  plasticIdCardCount?: number | null;
  plasticIdCardAmount?: number | null;
  laminatedIdCardCount?: number | null;
  laminatedIdCardAmount?: number | null;
}

export interface EditPaymentDepositInput {
  depositDate: any;
  currency?: string | null;
  amountDeposited?: number | null;
  amountUsed?: number | null;
  collectedById: string;
  withdrawnById?: string | null;
  depositMethod: string;
  description?: string | null;
  additionalNote?: string | null;
}

export interface EditPaymentDepositRefundInput {
  refundDate: any;
  currency?: string | null;
  refundedBy?: string | null;
  amountRefunded?: number | null;
  description?: string | null;
  additionalNote?: string | null;
}

export interface EditVirtualCareAppointmentInput {
  appointmentDate: any;
  startDateTime?: any | null;
  endDateTime?: any | null;
  priority?: string | null;
  category?: string | null;
  rank?: string | null;
  patientType?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  paymentType?: string | null;
  reason?: string | null;
  additionalNote?: string | null;
  specialists?: SpecialistAssignedTo[] | null;
  duration?: string | null;
  hostIds?: string[] | null;
}

export interface EmailTransactionReceiptInput {
  id: string;
  amount?: number | null;
}

export interface EmployeesInfoInput {
  fullName: string;
  enrolleeId?: string | null;
  planCategory?: string | null;
  planType?: string | null;
  planTypeCode?: string | null;
  planAmount?: number | null;
  paymentFrequency?: string | null;
  planStartDate?: any | null;
  planDueDate?: any | null;
  planStatus?: string | null;
  isCovered?: boolean | null;
  dependents?: DependentInfoInput[] | null;
}

export interface EmployerFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  phoneNumber?: string | null;
  emailAddress?: string | null;
  employerName?: string | null;
  enrolleeStatus?: string | null;
  planId?: string | null;
  facilityId?: string | null;
  enrollmentAgentId?: string | null;
  enrollmentAgencyId?: string | null;
}

export interface EnrolleeReferralResponseInput {
  name?: string | null;
  referrerCode?: string | null;
  accountNumber?: string | null;
  accountName?: string | null;
  bankName?: string | null;
  bvn?: string | null;
  branchName?: string | null;
  phoneNumber?: PhoneNumberInput | null;
  email?: string | null;
  status?: string | null;
}

export interface EnrolleesToCheckin {
  enrolleeId?: string | null;
  profileId?: string | null;
}

export interface EnrollmentAgencyFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  tpaNonTpaId?: string | null;
}

export interface EnrollmentAgencyInput {
  id?: string | null;
  agencyType?: string | null;
  tpaNonTpaId?: string | null;
  isTpa?: boolean | null;
  administrationAgency?: string | null;
  name?: string | null;
  agencyCode?: string | null;
  address?: string | null;
  country?: string | null;
  state?: string | null;
  localGovernmentArea?: string | null;
  primaryPhoneNumber?: PhoneNumberInput | null;
  primaryEmailAddress?: string | null;
  secondaryPhoneNumber?: PhoneNumberInput | null;
  secondaryEmailAddress?: string | null;
  contactPersonTitle?: string | null;
  contactPersonGender?: string | null;
  contactPersonFirstName?: string | null;
  contactPersonMiddleName?: string | null;
  contactPersonLastName?: string | null;
  contactPersonPhoneNumber?: PhoneNumberInput | null;
  contactPersonEmailAddress?: string | null;
  contactPersonAltTitle?: string | null;
  contactPersonAltFirstName?: string | null;
  contactPersonAltMiddleName?: string | null;
  contactPersonAltLastName?: string | null;
  contactPersonAltPhoneNumber?: PhoneNumberInput | null;
  contactPersonAltEmailAddress?: string | null;
  accountNumber?: string | null;
  accountName?: string | null;
  bankName?: string | null;
  bvn?: string | null;
  branchName?: string | null;
  status?: string | null;
}

export interface EnrollmentAgentFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  agencyId?: string | null;
}

export interface EnrollmentAgentInput {
  id?: string | null;
  accountNumber?: string | null;
  accountName?: string | null;
  bankName?: string | null;
  bvn?: string | null;
  branchName?: string | null;
  status?: string | null;
  agencyId?: string | null;
  tpaNonTpaId?: string | null;
}

export interface EnrollmentCommissionRateDetailsInput {
  name?: string | null;
  id?: string | null;
  commissionRate?: number | null;
  referralCommissionRate?: number | null;
}

export interface EnrollmentTpaNonTpaInput {
  id?: string | null;
  name?: string | null;
  profileId?: string | null;
  address?: string | null;
  isTpa?: boolean | null;
  country?: string | null;
  state?: string | null;
  localGovernmentArea?: string | null;
  primaryPhoneNumber?: PhoneNumberInput | null;
  primaryEmailAddress?: string | null;
  secondaryPhoneNumber?: PhoneNumberInput | null;
  secondaryEmailAddress?: string | null;
  contactPersonTitle?: string | null;
  contactPersonFirstName?: string | null;
  contactPersonMiddleName?: string | null;
  contactPersonLastName?: string | null;
  contactPersonGender?: Gender | null;
  contactPersonPhoneNumber?: PhoneNumberInput | null;
  contactPersonEmailAddress?: string | null;
  contactPersonAltTitle?: string | null;
  contactPersonAltFirstName?: string | null;
  contactPersonAltMiddleName?: string | null;
  contactPersonAltLastName?: string | null;
  contactPersonAltPhoneNumber?: PhoneNumberInput | null;
  contactPersonAltEmailAddress?: string | null;
  tpaNumber?: string | null;
  tpaCode?: string | null;
  accountNumber?: string | null;
  accountName?: string | null;
  bankName?: string | null;
  bvn?: string | null;
  branchName?: string | null;
  status?: string | null;
}

export interface EventTime {
  readingDate?: any | null;
  eventName?: string | null;
  time?: any | null;
}

export interface FacilityBillingInformationInput {
  bankName: string;
  accountName: string;
  accountNumber: string;
  bvn?: string | null;
  branchName?: string | null;
  id?: string | null;
}

export interface FacilityStaffsAnalyticsFilter {
  startDate?: string | null;
  endDate?: string | null;
  duration?: string | null;
  overall?: boolean | null;
  hospitalId?: string | null;
}

export interface FamilyHistoryInput {
  id?: string | null;
  clinifyId: string;
  title?: string | null;
  firstName?: string | null;
  middleName?: string | null;
  lastName?: string | null;
  gender?: Gender | null;
  dateOfBirth?: any | null;
  bloodGroup?: string | null;
  relationship?: string | null;
  conditions?: ConditionInput[] | null;
  additionalNote?: string | null;
}

export interface FamilyPlanningAnalyticsFilter {
  startDate?: string | null;
  endDate?: string | null;
  duration?: string | null;
  ageRange?: string | null;
  gender?: string | null;
  overall?: boolean | null;
  hospitalId?: string | null;
}

export interface FeedbackFilterOptions {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  status?: FeedbackStatusOptions | null;
  hmoProviderId?: string | null;
  category?: string | null;
  hospitalId?: string | null;
}

export interface FhrDetail {
  readingDate?: any | null;
  value?: string | null;
  time?: string | null;
}

export interface FieldOfficerResponseInput {
  profileId?: string | null;
  administrationAgency?: string | null;
  enrollmentAgency?: string | null;
  accountNumber?: string | null;
  accountName?: string | null;
  bankName?: string | null;
  bvn?: string | null;
  branchName?: string | null;
  status?: string | null;
}

export interface FileNumber {
  coverageRef?: string | null;
  fileNumber?: string | null;
  existingFamily?: boolean | null;
}

export interface FilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
}

export interface FinanceAnalyticsFilter {
  startDate?: string | null;
  endDate?: string | null;
  duration?: string | null;
  orderBy?: string | null;
  overall?: boolean | null;
  billType?: FinanceBillType | null;
  valueMultiplier?: FinanceValueMultiplier | null;
  hospitalId?: string | null;
}

export interface FindingsTemplateInput {
  name: string;
  facilityPreferenceId: string;
  findings: string;
  impression?: string | null;
  id: string;
}

export interface FliudData {
  readingDate?: any | null;
  time?: string | null;
  ivFluids?: string | null;
  bloodLoss?: string | null;
  urineOutput?: string | null;
}

export interface ForgotPasscodeInput {
  email?: string | null;
  phoneNumber?: string | null;
  mode?: ForgotPasscodeInputMode | null;
}

export interface GenerateTransactionReferenceInput {
  walletId: string;
  transactionType: TransactionType;
}

export interface GrantFacilityPatientAccessInput {
  facilityId: string;
  staffIds: string[];
}

export interface GrantOrganizationPermissionInput {
  recipientId: string;
  permissions: OrganizationPermissionInput[];
}

export interface GrantPatientAccessPermissionInput {
  accessType: PatientAccessType;
  facilitiesAccess?: GrantFacilityPatientAccessInput[] | null;
}

export interface GynecologicHistoryInput {
  id?: string | null;
  clinifyId: string;
  firstMenstrualAge?: string | null;
  menstrualCycleLength?: string | null;
  menstrualFlowDuration?: string | null;
  lastMenstrualPeriod?: any | null;
  menstrualFlow?: string | null;
  contraceptiveUse?: string | null;
  contraceptiveType?: string | null;
  specifyContraceptiveType?: string | null;
  newAcceptor?: boolean | null;
  revisit?: boolean | null;
  removeContraceptive?: boolean | null;
  quantityGiven?: string | null;
  miscarriageOrAbortion?: string | null;
  miscarriageOrAbortionCount?: number | null;
  menstrualStatus?: string | null;
  breastFeeding?: string | null;
  pregnancyCount?: string | null;
  currentlyPregnant?: string | null;
  babyDelivered?: string | null;
  sourceOfReferral?: string | null;
  counselledOnFp?: string | null;
  conunselledOnPpfp?: string | null;
  firstTimeFpUser?: string | null;
  emergencyContraception?: string | null;
  familyPlanningClientType?: string | null;
  referredOut?: string | null;
  followupVisit?: any | null;
  additionalNote?: string | null;
}

export interface HabitInput {
  id?: string | null;
  clinifyId: string;
  socialHabit?: string | null;
  level?: string | null;
  duration?: string | null;
  typeSpecified?: string | null;
  cigrattesPerDay?: string | null;
  unitPerWeek?: string | null;
  additionalNote?: string | null;
}

export interface HandoverAdditionalNoteInput {
  additionalNote?: string | null;
}

export interface HandoverNoteFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  archive?: boolean | null;
  showOthers?: boolean | null;
  status?: HandoverNoteStatus | null;
}

export interface HandoverNoteItemInput {
  patientProfileId: string;
  patientInformation: PatientInformationInput;
  admissionWard?: string | null;
  priority?: string | null;
  note?: string | null;
}

export interface HandoverStaffInput {
  staffProfileId?: string | null;
  status?: string | null;
  currentShift?: string | null;
  nextShift?: string | null;
}

export interface HmoClaimFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  status?: string | null;
  providerId?: string | null;
  hospitalId?: string | null;
  providerInsight?: boolean | null;
  profileId?: string | null;
  providerType?: string | null;
  flag?: string | null;
  filterDateField?: HmoClaimDateFilterType | null;
  showCompleted?: boolean | null;
  showNotCompleted?: boolean | null;
  paymentModel?: BenefitCategory | null;
  timeSortOrder?: TimeSortOrder | null;
}

export interface HmoFilterOptions {
  skip?: number | null;
  take?: number | null;
  name?: string | null;
  providersNotIn?: string[] | null;
  providersIn?: string[] | null;
  lga?: string | null;
  plans?: string[] | null;
  ignorePlans?: string[] | null;
  planStatus?: string | null;
  onlyRelated?: boolean | null;
}

export interface HmoPlanBeneficiariesFilterOptions {
  skip?: number | null;
  take?: number | null;
  planTypeId?: string | null;
  keyword?: string | null;
  withoutPlan?: boolean | null;
}

export interface HmoPlanFilterOptions {
  skip?: number | null;
  take?: number | null;
  keyword?: string | null;
  category?: string | null;
  archive?: boolean | null;
  status?: HmoPlanStatus | null;
  dateRange?: DateRangeInput | null;
}

export interface HmoPlanInput {
  planDateTime?: any | null;
  name: string;
  planCode?: string | null;
  plannedBy?: string | null;
  premiumCountry?: string | null;
  status?: string | null;
  premiumDetails?: PremiumDetailsInput[] | null;
  benefits: UpdatePlanBenefit[];
  isExternalPlan?: boolean | null;
  isSponsor?: boolean | null;
  id: string;
}

export interface HmoProfileFilterInput {
  skip?: number | null;
  take?: number | null;
}

export interface HmoProfileInput {
  id?: string | null;
  clinifyId: string;
  memberNumber?: string | null;
  membershipNumber?: string | null;
  memberPlan?: string | null;
  memberPlanId?: string | null;
  parentMemberUniqueId?: string | null;
  memberUniqueId?: string | null;
  employeeNumber?: string | null;
  providerId?: string | null;
  memberStartDate?: any | null;
  memberDueDate?: any | null;
  memberStatus?: string | null;
  enrollmentDateTime?: any | null;
  enrolledBy?: string | null;
  capturedBy?: string | null;
  companyName?: string | null;
  companyAddress?: string | null;
  primaryProviderId?: string | null;
  primaryProviderName?: string | null;
  secondaryProviderName?: string | null;
  tertiaryProviderName?: string | null;
  primaryProviderAddress?: string | null;
  secondaryProviderAddress?: string | null;
  tertiaryProviderAddress?: string | null;
  capitatedMember?: string | null;
  capitatedAmount?: string | null;
  capturedDate?: any | null;
  employeeType?: string | null;
  employeeDivision?: string | null;
  memberPlanGroup?: string | null;
  memberPlanSubGroup?: string | null;
  occupation?: string | null;
  retired?: boolean | null;
  approvedBy?: string | null;
  paymentDateTime?: any | null;
  paymentFrequency?: string | null;
  terminationDate?: any | null;
  premiumCollected?: string | null;
  premiumOutstanding?: string | null;
  registrationSource?: string | null;
  administrationAgency?: string | null;
  commissionRate?: string | null;
  commissionPayable?: string | null;
  tpaNonTpaCommissionRate?: string | null;
  tpaNonTpaCommissionPayable?: string | null;
  enrollmentAgent?: string | null;
  enrollmentAgency?: string | null;
  enrollmentTpaNonTpa?: string | null;
  enrollmentAgentId?: string | null;
  enrollmentAgencyId?: string | null;
  enrollmentTpaNonTpaId?: string | null;
  planCategory?: string | null;
  salesWing?: string | null;
  channelDivisions?: string | null;
  agentName?: string | null;
  agentPhoneNumber?: string | null;
  sponsorType?: string | null;
  sponsorName?: string | null;
  sponsorRef?: string | null;
  activationDatetime?: any | null;
  sponsorCode?: string | null;
  referrerCode?: string | null;
  referrer?: string | null;
  referrerNumber?: string | null;
  referrerCommissionRate?: string | null;
  referrerCommissionPayable?: string | null;
  employerCode?: string | null;
  planCode?: string | null;
  nextRenewalDate?: any | null;
  questionnaireData?: QuestionnaireInput | null;
  totalPremiumAmountPaid?: string | null;
  groupCode?: string | null;
  agentEmail?: string | null;
}

export interface HmoUtilisationInput {
  id: string;
  name: string;
  code: string;
  price?: string | null;
  bandBPrice?: string | null;
  bandCPrice?: string | null;
  benefitCategory: BenefitCategory;
  benefitCoverage: BenefitCoverage;
  benefitLimit?: string | null;
  visitLimit?: string | null;
  annualLimitPerPerson?: string | null;
  waitingPeriodDays?: number | null;
  quantity?: number | null;
}

export interface HmoUtilizationFilter {
  startDate?: string | null;
  endDate?: string | null;
  patientType?: string | null;
  hospitalId?: string | null;
  duration?: string | null;
  orderBy?: string | null;
  lga?: string | null;
}

export interface HmosAnalyticsFilter {
  startDate?: string | null;
  endDate?: string | null;
  duration?: string | null;
  orderBy?: string | null;
  overall?: boolean | null;
  hospitalId?: string | null;
}

export interface HospitalFilterInput {
  skip?: number | null;
  take?: number | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  referral?: boolean | null;
  state?: string | null;
  lga?: string | null;
  level?: string | null;
  ownership?: string | null;
  status?: HospitalStatusTypes | null;
  clinifyId?: string | null;
  name?: string | null;
  email?: string | null;
  phoneNumber?: string | null;
  partnerCode?: string | null;
  hmoCode?: string | null;
  hmoProviderLookup?: boolean | null;
  partnerProviderLookup?: boolean | null;
  providerId?: string | null;
  providerCode?: string | null;
  hospitalId?: string | null;
  providerCategory?: string | null;
  providerTariffBand?: string | null;
}

export interface HospitalInventoryItemInput {
  id?: string | null;
  name?: string | null;
  description?: string | null;
  createdOn?: any | null;
  updatedOn?: any | null;
  creatorName?: string | null;
}

export interface HospitalProviderInput {
  id?: string | null;
  name?: string | null;
  code?: string | null;
  createdOn?: any | null;
  description?: string | null;
  updatedOn?: any | null;
  creatorName?: string | null;
}

export interface HospitalServiceInput {
  id?: string | null;
  name?: string | null;
  description?: string | null;
  createdOn?: any | null;
  updatedOn?: any | null;
  creatorName?: string | null;
}

export interface ImmunizationDetailInput {
  id?: string | null;
  administeredDate?: any | null;
  duration?: string | null;
  priority?: string | null;
  itemId?: string | null;
  class?: string | null;
  category?: immunizationCategory | null;
  period?: string | null;
  code?: string | null;
  npi?: string | null;
  immunizationName: string;
  batchNumber?: string | null;
  expiryDate?: any | null;
  administratorName?: string | null;
  method?: string | null;
  quantity?: string | null;
  dosage?: string | null;
  dosageUnit?: string | null;
  administrationType?: string | null;
  administrationSource?: string | null;
  adverseEffectsFollowingImmunization?: string | null;
  stateEffects?: string | null;
  adverseEffectsInvestigated?: string | null;
  outcomeOfInvestigation?: string | null;
  provider?: string | null;
}

export interface ImmunizationFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  category?: immunizationCategory | null;
}

export interface ImmunizationInput {
  id: string;
  clinifyId: string;
  details?: ImmunizationDetailInput[] | null;
  nextAppointmentDateTime?: any | null;
  nextAppointmentEndDateTime?: any | null;
  nextAppointmentDuration?: string | null;
  nextAppointmentSpecialty?: string | null;
  nextAppointmentRole?: string | null;
  nextAppointmentCategory?: string | null;
  nextAppointmentSpecialistId?: string | null;
  nextAppointmentSpecialistName?: string | null;
  nextAppointmentUrgency?: string | null;
  nextAppointmentDeliveryMethod?: string | null;
  remindMe?: boolean | null;
  serviceDetails?: ServiceDetailInput[] | null;
  hospitalName?: string | null;
  hospitalAddress?: string | null;
  documentUrl?: string[] | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
  hmoProviderId?: string | null;
  billInfo?: BillInfoUpdateInput[] | null;
}

export interface InputDetailsInput {
  id?: string | null;
  administrationDateTime?: any | null;
  administratorName?: string | null;
  inputFluidType?: string | null;
  routeOfAdministration?: string | null;
  inputQuantity?: string | null;
  inputQuantityUnit?: string | null;
  duration?: string | null;
}

export interface InventoryFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  status?: InventoryStatus | null;
  ownRequests?: boolean | null;
  section?: string | null;
  name?: string | null;
  unique?: boolean | null;
  latest?: boolean | null;
  class?: string | null;
  filterColorOption?: FilterColorOption | null;
  hospitalId?: string | null;
}

export interface InventoryItemInput {
  id?: string | null;
  supplier?: string | null;
  addedBy?: string | null;
  addedDateTime?: string | null;
  invoiceNumber?: string | null;
  bedAvailable?: string | null;
  colour?: string | null;
  vin?: string | null;
  plateNumber?: string | null;
  purchasedBy?: string | null;
  status?: string | null;
  model?: string | null;
  year?: string | null;
  name?: string | null;
  type?: string | null;
  size?: string | null;
  group?: string | null;
  flag?: string | null;
  description?: string | null;
  category?: string | null;
  code?: string | null;
  batchNumber?: string | null;
  section?: string | null;
  barcode?: string | null;
  expiryDate?: string | null;
  expiryStatus?: string | null;
  damagedCount?: string | null;
  markup?: string | null;
  strength?: string | null;
  unitCost?: string | null;
  unitSellingPrice?: string | null;
  totalCost?: string | null;
  totalSale?: string | null;
  quantityRemaining?: string | null;
  quantityPurchased?: string | null;
  quantityAvailable?: string | null;
  quantityOrdered?: string | null;
  bedNumber?: string | null;
  ward?: string | null;
  manufacturer?: string | null;
  recievedDateTime?: string | null;
  receivedBy?: string | null;
  comments?: string | null;
  reorderLevel?: string | null;
  purchasedDate?: string | null;
  images?: string | null;
  class?: string | null;
}

export interface InventoryOrderFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  received: boolean;
  status?: InventoryStatus | null;
  section?: string | null;
  hospitalId?: string | null;
}

export interface InventoryOrderInput {
  id?: string | null;
  supplier?: string | null;
  addedBy?: string | null;
  addedDateTime?: string | null;
  invoiceNumber?: string | null;
  bedAvailable?: string | null;
  colour?: string | null;
  vin?: string | null;
  plateNumber?: string | null;
  purchasedBy?: string | null;
  status?: string | null;
  model?: string | null;
  year?: string | null;
  name?: string | null;
  type?: string | null;
  size?: string | null;
  group?: string | null;
  flag?: string | null;
  description?: string | null;
  category?: string | null;
  code?: string | null;
  batchNumber?: string | null;
  section?: string | null;
  barcode?: string | null;
  expiryDate?: string | null;
  expiryStatus?: string | null;
  damagedCount?: string | null;
  markup?: string | null;
  strength?: string | null;
  unitCost?: string | null;
  unitSellingPrice?: string | null;
  totalCost?: string | null;
  totalSale?: string | null;
  quantityRemaining?: string | null;
  quantityPurchased?: string | null;
  quantityAvailable?: string | null;
  quantityOrdered: string;
  bedNumber?: string | null;
  ward?: string | null;
  manufacturer?: string | null;
  recievedDateTime?: string | null;
  receivedBy?: string | null;
  comments?: string | null;
  reorderLevel?: string | null;
  purchasedDate?: string | null;
  images?: string | null;
  class?: string | null;
  orderToId: string;
  quantityDelivered?: string | null;
  deliveredBy?: string | null;
}

export interface InventorySupplyInput {
  id?: string | null;
  supplier?: string | null;
  addedBy?: string | null;
  addedDateTime?: string | null;
  invoiceNumber?: string | null;
  bedAvailable?: string | null;
  colour?: string | null;
  vin?: string | null;
  plateNumber?: string | null;
  purchasedBy?: string | null;
  status?: string | null;
  model?: string | null;
  year?: string | null;
  name?: string | null;
  type?: string | null;
  size?: string | null;
  group?: string | null;
  flag?: string | null;
  description?: string | null;
  category?: string | null;
  code?: string | null;
  batchNumber?: string | null;
  section?: string | null;
  barcode?: string | null;
  expiryDate?: string | null;
  expiryStatus?: string | null;
  damagedCount?: string | null;
  markup?: string | null;
  strength?: string | null;
  unitCost?: string | null;
  unitSellingPrice?: string | null;
  totalCost?: string | null;
  totalSale?: string | null;
  quantityRemaining?: string | null;
  quantityPurchased?: string | null;
  quantityAvailable?: string | null;
  quantityOrdered?: string | null;
  bedNumber?: string | null;
  ward?: string | null;
  manufacturer?: string | null;
  recievedDateTime?: string | null;
  receivedBy?: string | null;
  comments?: string | null;
  reorderLevel?: string | null;
  purchasedDate?: string | null;
  images?: string | null;
  class?: string | null;
  quantityDelivered?: string | null;
  deliveredBy?: string | null;
}

export interface InvestigationExamTypeInput {
  ref?: string | null;
  examType: string;
  priority?: string | null;
  loinc?: string | null;
  provider?: string | null;
  itemId?: string | null;
  preauthorizationDetailsId?: string | null;
  indication?: string | null;
}

export interface InvestigationFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  requestType?: requestType | null;
  status?: investigationStatus | null;
  clinifyId?: string | null;
  showReferringHospital?: boolean | null;
  external?: boolean | null;
  providerInsight?: boolean | null;
  hospitalId?: string | null;
}

export interface InvestigationInput {
  id?: string | null;
  clinifyId: string;
  requestType?: requestType | null;
  testInfo?: InvestigationTestInfoInput[] | null;
  requestDate?: any | null;
  priority?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  rank?: string | null;
  department?: string | null;
  status?: investigationStatus | null;
  orderedBy?: string | null;
  specialty?: string | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  examinationType?: InvestigationExamTypeInput[] | null;
  clinicalHistory?: string | null;
  documentUrl?: string[] | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
  external?: boolean | null;
  externalHospital?: string | null;
  clinicalDiagnosis?: DiagnosisInput[] | null;
  billInfo?: BillInfoUpdateInput[] | null;
  billRefsToDelete?: string[] | null;
}

export interface InvestigationTestInfoInput {
  ref?: string | null;
  testName: string;
  priority?: string | null;
  testCategory?: string | null;
  clinicalDiagnosisICD10?: string | null;
  clinicalDiagnosisICD11?: string | null;
  clinicalDiagnosisSNOMED?: string | null;
  specimen?: string[] | null;
  loinc?: string | null;
  provider?: string | null;
  itemId?: string | null;
  preauthorizationDetailsId?: string | null;
}

export interface InvestigationWithLabInput {
  id?: string | null;
  clinifyId: string;
  requestType?: requestType | null;
  testInfo?: InvestigationTestInfoInput[] | null;
  requestDate?: any | null;
  priority?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  rank?: string | null;
  department?: string | null;
  status?: investigationStatus | null;
  orderedBy?: string | null;
  specialty?: string | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  examinationType?: InvestigationExamTypeInput[] | null;
  clinicalHistory?: string | null;
  documentUrl?: string[] | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
  external?: boolean | null;
  externalHospital?: string | null;
  clinicalDiagnosis?: DiagnosisInput[] | null;
  details?: LabTestDetail[] | null;
  patientType?: string | null;
  paymentType?: string | null;
  billInfo?: BillInfoUpdateInput[] | null;
  billRefsToDelete?: string[] | null;
}

export interface InvestigationWithRadiologyInput {
  id?: string | null;
  clinifyId: string;
  requestType?: requestType | null;
  requestDate?: any | null;
  priority?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  rank?: string | null;
  department?: string | null;
  status?: investigationStatus | null;
  orderedBy?: string | null;
  specialty?: string | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  examinationType?: InvestigationExamTypeInput[] | null;
  clinicalHistory?: string | null;
  documentUrl?: string[] | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
  external?: boolean | null;
  externalHospital?: string | null;
  clinicalDiagnosis?: DiagnosisInput[] | null;
  details?: RadiologyExamDetail[] | null;
  patientType?: string | null;
  paymentType?: string | null;
  billInfo?: BillInfoUpdateInput[] | null;
  billRefsToDelete?: string[] | null;
}

export interface InvoiceInput {
  issueDate?: any | null;
  dueDate?: any | null;
  invoiceItems: InvoiceItemInput[];
  invoicePayments: InvoicePaymentInput[];
  description?: string | null;
  recipient: InvoiceRecipientInput;
  profileId?: string | null;
  employerId?: string | null;
  employeesDetails?: EmployeesInfoInput[] | null;
  controlledVat?: PercentOrAmount | null;
  sponsorRef?: string | null;
  sponsorName?: string | null;
  sponsorDetails?: SponsorEnrolleeDetailsInput[] | null;
  vatAmount?: number | null;
  vatPercentage?: number | null;
  controlledDiscount?: PercentOrAmount | null;
  discountAmount?: number | null;
  discountPercentage?: number | null;
  controlledProfessionalFee?: PercentOrAmount | null;
  professionalFeeAmount?: number | null;
  professionalFeePercentage?: number | null;
  isDraft?: boolean | null;
  additionalNote?: string | null;
  sponsorLivesCovered?: number | null;
  agencyLivesCovered?: number | null;
  sponsorPremiumPerLives?: number | null;
  nextYearlyPremium?: number | null;
  periodStartDate?: any | null;
  periodEndDate?: any | null;
  paymentFrequency?: string | null;
  plasticIdCardCount?: number | null;
  plasticIdCardAmount?: number | null;
  laminatedIdCardCount?: number | null;
  laminatedIdCardAmount?: number | null;
}

export interface InvoiceItemInput {
  quantity?: number | null;
  unitPrice?: number | null;
  currency?: Currency | null;
  description: string;
  discountAmount?: number | null;
  discountPercentage?: number | null;
  invoiceType?: string | null;
}

export interface InvoiceListFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  status?: InvoiceStatus | null;
  paymentStatus?: string | null;
  employerInvoice?: boolean | null;
  sponsorInvoice?: boolean | null;
}

export interface InvoicePaymentInput {
  paymentMethod: string;
  amountDue: number;
  amountPaid?: number | null;
  paymentStatus?: string | null;
  controlledCommissionFee?: PercentOrAmount | null;
  commissionFeeAmount?: number | null;
  commissionFeePercentage?: number | null;
  commissionPayer?: CommissionPayer | null;
  discountAmount?: number | null;
}

export interface InvoiceRecipientInput {
  name: string;
  address?: string | null;
  phone?: string | null;
  email?: string | null;
  clinifyId?: string | null;
}

export interface LabCommentsTemplateInput {
  name: string;
  facilityPreferenceId: string;
  comment: string;
  id: string;
}

export interface LabResultInput {
  details: LabTestDetail[];
  patientType?: PatientType | null;
  paymentType?: string | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  documentUrl?: string[] | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
  hmoProviderId?: string | null;
  hmoClaim?: NewHmoClaimInput | null;
}

export interface LabResultTrendFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  profileId: string;
  hospitalId?: string | null;
  sortBy: string;
  testName: string;
}

export interface LabTestDetail {
  testDate?: any | null;
  duration?: string | null;
  resultDate?: any | null;
  specimenCollected?: specimenCollection | null;
  specimenNumber?: string | null;
  specimenTypes?: string[] | null;
  natureSiteOfSpecimen?: string | null;
  specimenReceivedDate?: any | null;
  collectedBy?: string | null;
  collectionDate?: any | null;
  performedBy?: string | null;
  performedBySignature?: string | null;
  performedBySignatureType?: string | null;
  performedBySignatureDateTime?: any | null;
  patientType?: PatientType | null;
  verifiedBy?: string | null;
  verifiedBySignature?: string | null;
  verifiedBySignatureType?: string | null;
  verifiedBySignatureDateTime?: any | null;
  testName?: string | null;
  paymentType?: string | null;
  serviceDetails?: ServiceDetailInput | null;
  done?: boolean | null;
  reportDate?: any | null;
  finalReportDate?: any | null;
  pathologistName?: string | null;
  pathologistSignature?: string | null;
  pathologistSignatureType?: string | null;
  pathologistSignatureDateTime?: any | null;
  pathologistReport?: string | null;
  pertinentHistory?: string | null;
  grossExam?: string | null;
  microscopicExam?: string | null;
  conclusion?: string | null;
  testResults?: TestResults[] | null;
  extraTestResults?: TestResults[] | null;
  additionalNote?: string | null;
}

export interface LabourDeliveryFilterOptions {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
}

export interface LabourDeliveryInput {
  id: string;
  clinifyId: string;
  provider?: string | null;
  visitationDateTime?: any | null;
  gestationAge?: string | null;
  estimatedDeliveryDate?: any | null;
  induction?: string | null;
  inductionDateTime?: any | null;
  inducedBy?: string | null;
  inducedMethod?: string | null;
  preTerm?: string | null;
  bloodGroup?: string | null;
  decisionSeekingCare?: string | null;
  modeOfTransportation?: string | null;
  postAbortionCare?: string | null;
  admittedForComplicationsOfUnsafeAbortion?: string | null;
  abortion?: string | null;
  membranesRuptureDateTime?: any | null;
  firstStageStartDateTime?: any | null;
  firstStageDuration?: string | null;
  firstStageSupportPersonPresent?: string | null;
  firstStageEpiduralGiven?: string | null;
  partographUsed?: string | null;
  firstStageMaternalBloodPressure?: string | null;
  firstStageFetalHeartRate?: string | null;
  firstStageFetalMonitoring?: string | null;
  firstStagePresentation?: string | null;
  firstStageSpecifyBreech?: string | null;
  firstStagePosition?: string | null;
  firstStageLie?: string | null;
  firstStageFetalMovement?: string | null;
  augmentation?: string | null;
  augmentationMethod?: string | null;
  firstStageMotherStatus?: string | null;
  firstStageMdaConducted?: string | null;
  firstStageMotherCauseOfDeath?: string | null;
  firstStageBabyStatus?: string | null;
  firstStageDoctorName?: string | null;
  firstStageSpecialty?: string | null;
  firstStageRank?: string | null;
  firstStageDepartment?: string | null;
  firstStageNurseName?: string | null;
  firstStageMidWife?: string | null;
  firstStageAdditionalNote?: string | null;
  concealFirstStageAdditionalNote?: boolean | null;
  secondStageStartDateTime?: any | null;
  secondStageBabyDeliveryDateTime?: any | null;
  secondStageDuration?: string | null;
  secondStageSupportPersonPresent?: string | null;
  secondStageEpiduralGiven?: string | null;
  secondStageDeliveryMethod?: string | null;
  placeOfBirth?: string | null;
  referredOut?: string | null;
  admitted?: string | null;
  discharged?: string | null;
  secondStageMaternalBloodPressure?: string | null;
  secondStageFetalHeartRate?: string | null;
  secondStagePresentation?: string | null;
  secondStageSpecifyBreech?: string | null;
  secondStagePosition?: string | null;
  secondStageLie?: string | null;
  secondStageFetalMovement?: string | null;
  episiotomy?: string | null;
  gender?: string | null;
  liveBirthWeight?: string | null;
  birthWeight?: string | null;
  birthWeightUnit?: string | null;
  notBreathingOrCrying?: string | null;
  babyResuscitated?: string | null;
  cordClampTime?: string | null;
  chxGelApplied?: string | null;
  secondStageMotherStatus?: string | null;
  secondStageMdaConducted?: string | null;
  secondStageMotherCauseOfDeath?: string | null;
  secondStageBabyStatus?: string | null;
  stillBirth?: string | null;
  liveHivBirth?: string | null;
  deliveredBy?: string | null;
  secondStageDoctorName?: string | null;
  secondStageSpecialty?: string | null;
  secondStageRank?: string | null;
  secondStageDepartment?: string | null;
  secondStageNurseName?: string | null;
  secondStageMidWife?: string | null;
  secondStageMarternalComplication?: string | null;
  secondStageAdditionalNote?: string | null;
  concealSecondStageAdditionalNote?: boolean | null;
  thirdStageStartDateTime?: any | null;
  placentaDateTime?: any | null;
  thirdStageDuration?: string | null;
  placentaDeliveryMethod?: string | null;
  placentaComplete?: string | null;
  configuration?: string | null;
  bloodLossEstimate?: string | null;
  osytocinReceiveed?: string | null;
  misoprostolReceived?: string | null;
  receivedMgso4WithEclampsia?: string | null;
  cervicalTear?: string | null;
  perinealLaceration?: string | null;
  birthInjury?: string | null;
  otherTrauma?: string | null;
  repair?: string | null;
  specifyRepair?: string | null;
  anesthesiaType?: string | null;
  anesthesiaGiven?: string | null;
  thirdStageMotherStatus?: string | null;
  thirdStageMdaConducted?: string | null;
  thirdStageMotherCauseOfDeath?: string | null;
  thirdStageBabyStatus?: string | null;
  thirdStageDoctorName?: string | null;
  thirdStageSpecialty?: string | null;
  thirdStageRank?: string | null;
  thirdStageDepartment?: string | null;
  thirdStageNurseName?: string | null;
  thirdStageMidWife?: string | null;
  thirdStageAdditionalNote?: string | null;
  concealThirdStageAdditionalNote?: boolean | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  documentUrl?: string[] | null;
  subBillRef?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  nextAppointmentDateTime?: any | null;
  nextAppointmentEndDateTime?: any | null;
  nextAppointmentDuration?: string | null;
  nextAppointmentSpecialty?: string | null;
  nextAppointmentRole?: string | null;
  nextAppointmentCategory?: string | null;
  nextAppointmentSpecialistId?: string | null;
  nextAppointmentSpecialistName?: string | null;
  nextAppointmentUrgency?: string | null;
  nextAppointmentDeliveryMethod?: string | null;
  hmoProviderId?: string | null;
  typeOfClient?: string | null;
  currentPregnancyNumber?: string | null;
  parity?: string | null;
  firstStageMaternalTemperature?: string | null;
  firstStageMaternalTemperatureUnit?: string | null;
  firstStageMaternalPulseRate?: string | null;
  firstStageMaternalRespiratoryRate?: string | null;
  secondStageMaternalTemperature?: string | null;
  secondStageMaternalTemperatureUnit?: string | null;
  secondStageMaternalPulseRate?: string | null;
  secondStageMaternalRespiratoryRate?: string | null;
  secondStageMouthAndNoseSuctioned?: string | null;
  babyCauseOfDeath?: string | null;
  secondStageHeadCircumferenceOfBaby?: string | null;
  secondStageChestCircumferenceOfBaby?: string | null;
  secondStageBodyLengthOfBaby?: string | null;
  secondStageBabyRespiratoryRate?: string | null;
  secondStageBabyHeartRate?: string | null;
  secondStageTemperatureAt1hr?: string | null;
  secondStageTemperatureAt1hrUnit?: string | null;
  fourthStagePostpartumComplications?: string | null;
  babyPutToBreast?: string | null;
  fourthStageTimeOfInitiatingBreastfeeding?: string | null;
  fourthStagePostDeliveryMassageGiven?: string | null;
  fourthStageCervicalTear?: string | null;
  fourthStagePerinealLaceration?: string | null;
  fourthStageBirthInjury?: string | null;
  fourthStageVulvaCareGiven?: string | null;
  fourthStageOtherTrauma?: string | null;
  fourthStageRepair?: string | null;
  fourthStageSpecifyRepair?: string | null;
  fourthStageAnesthesiaType?: string | null;
  fourthStageAnesthesiaGiven?: string | null;
  fourthStageVitaminKGivenToBaby?: string | null;
  fourthStageVitaminKDoseGivenToBaby?: string | null;
  fourthStageMaternalTemperature?: string | null;
  fourthStageMaternalTemperatureUnit?: string | null;
  fourthStageMaternalPulseRate?: string | null;
  fourthStageBabyTemperature?: string | null;
  fourthStageBabyTemperatureUnit?: string | null;
  fourthStageBabyHeartRate?: string | null;
  fourthStageBabyRespiratoryRate?: string | null;
  fourthStageStatusOfMother?: string | null;
  fourthStageMdaConducted?: string | null;
  fourthStageMotherCauseOfDeath?: string | null;
  fourthStageStatusOfBaby?: string | null;
  fourthStagePostpartumDepression?: string | null;
  dangerSigns?: string | null;
  firstDoseOfAntibioticsAndReferred?: string | null;
  neonatalTetanus?: string | null;
  neonatalJaundice?: string | null;
  admittedOnKMC?: string | null;
  dischargedAfterKMC?: string | null;
  givenLlin?: string | null;
  birthCertificateIssued?: string | null;
  birthCertificateCollected?: string | null;
  fourthStageApgarScoreA?: string | null;
  fourthStageApgarScoreP?: string | null;
  fourthStageApgarScoreG?: string | null;
  fourthStageApgarScoreApperance?: string | null;
  fourthStageApgarScoreR?: string | null;
  fourthStageApgarScore?: string | null;
  fourthStageCordCare?: string | null;
  fourthStageGeneralConditionOfTheBaby?: string | null;
  fourthStageGeneralConditionOfTheMother?: string | null;
  fourthStageDoctorName?: string | null;
  fourthStageSpecialty?: string | null;
  fourthStageRank?: string | null;
  fourthStageDepartment?: string | null;
  fourthStageNurseName?: string | null;
  fourthStageMidWife?: string | null;
  fourthStageAdditionalNote?: string | null;
  concealFourthStageAdditionalNote?: boolean | null;
  concealFourthStageCordCare?: boolean | null;
  concealFourthStageGeneralConditionOfTheBaby?: boolean | null;
  concealFourthStageGeneralConditionOfTheMother?: boolean | null;
}

export interface LeadwayProviderLoginInput {
  username: string;
  password: string;
}

export interface LinesDrainsTubesDetail {
  intravenousInfusions?: string | null;
  others?: string | null;
  dressingChecked?: string | null;
  dressingCheckedTime?: any | null;
  drainsChecked?: string | null;
  drainsCheckedTime?: any | null;
  quantityDrained?: string | null;
  catheterChecked?: string | null;
  catheterCheckedTime?: any | null;
}

export interface LoginInput {
  phoneNumber: string;
  passCode: string;
}

export interface MailTemplateInput {
  subject?: string | null;
  body?: string | null;
}

export interface MandatoryFieldsInput {
  admission?: string[] | null;
  allergy?: string[] | null;
  antenatal?: string[] | null;
  antenatalDetails?: string[] | null;
  consultation?: string[] | null;
  immunization?: string[] | null;
  medication?: string[] | null;
  medicationDetails?: string[] | null;
  procedure?: string[] | null;
  dischargePatient?: string[] | null;
  transferPatient?: string[] | null;
  bloodTransfusion?: string[] | null;
  admissionInput?: string[] | null;
  admissionOutput?: string[] | null;
  admissionLine?: string[] | null;
  treatmentPlan?: string[] | null;
  vitals?: string[] | null;
  anthropometry?: string[] | null;
  bloodGlucose?: string[] | null;
  bloodPressure?: string[] | null;
  pain?: string[] | null;
  respiratoryRate?: string[] | null;
  temperature?: string[] | null;
  urineDipstick?: string[] | null;
  visualAcuity?: string[] | null;
  pulseRate?: string[] | null;
  hmoClaims?: string[] | null;
  investigation?: string[] | null;
  radiologyExam?: string[] | null;
  postnatal?: string[] | null;
  labourAndDelivery?: string[] | null;
  nextOfKin?: string[] | null;
  dependents?: string[] | null;
  medicalReport?: string[] | null;
  admissionNotes?: string[] | null;
  nursingServices?: string[] | null;
  oncology?: string[] | null;
  requestProcedure?: string[] | null;
  laboratory?: string[] | null;
  radiology?: string[] | null;
  postOperationChecklist?: string[] | null;
  preChemoEducation?: string[] | null;
  cancerScreening?: string[] | null;
}

export interface MaternalHealthAnalyticsFilter {
  startDate?: string | null;
  endDate?: string | null;
  duration?: string | null;
  attendance?: string | null;
  ageRange?: string | null;
  birthWeight?: string | null;
  gender?: string | null;
  hospitalId?: string | null;
}

export interface MedFilterOptions {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  dispenseType?: DispenseType | null;
  status?: DispenseStatus | null;
  providerInsight?: boolean | null;
  hospitalId?: string | null;
  agencyId?: string | null;
}

export interface MedPriceDetailInput {
  type?: string | null;
  name?: string | null;
  pricePerUnit?: string | null;
  quantity?: string | null;
  patientType?: string | null;
  paymentType?: string | null;
}

export interface MedicalReportFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  reportType?: string | null;
  status?: MedicalReportStatus | null;
  clinifyId?: string | null;
}

export interface MedicalReportInput {
  reportDate?: any | null;
  reportType?: ReportTypeInput[] | null;
  patientClinifyId?: string | null;
  patientFullname?: string | null;
  patientPhone?: string | null;
  patientEmail?: string | null;
  report?: string[] | null;
  rank?: string | null;
  department?: string | null;
  doctorName?: string | null;
  specialty?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  clinicalHistory?: string | null;
  additionalNote?: string | null;
  status?: MedicalReportStatus | null;
  documentUrl?: string[] | null;
  id?: string | null;
  billInfo?: BillInfoUpdateInput[] | null;
  billRefsToDelete?: string[] | null;
}

export interface MedicalReportMailInput {
  id: string;
  reportPdf: string;
}

export interface MedicalReportTemplateInput {
  facilityPreferenceId: string;
  template: string;
  id: string;
}

export interface MedicationBundleInput {
  id?: string | null;
  clinifyId: string;
  bundleName: string;
  additionalNote?: string | null;
  documentUrl?: string[] | null;
}

export interface MedicationBundleItemInput {
  id?: string | null;
  bundleDate?: any | null;
  duration?: string | null;
  medicationName?: string | null;
  medicationCategory?: string | null;
  medicationType?: string | null;
  diagnosis?: DiagnosisInput[] | null;
  purpose?: string | null;
  administrationMethod?: string | null;
  quantity?: string | null;
  unitPrice?: string | null;
  dosage?: string | null;
  dosageUnit?: string | null;
  frequency?: string | null;
  option?: MedicationOptionType | null;
  priceDetails?: MedPriceDetailInput | null;
  bank?: BankType | null;
  provider?: string | null;
  drugInventoryId?: string | null;
  inventoryClass?: string | null;
  medicationConsumables?: MedicationConsumable[] | null;
  prescriptionNote?: string | null;
}

export interface MedicationConsumable {
  name?: string | null;
  drugInventoryId?: string | null;
  quantity?: string | null;
  unitPrice?: number | null;
  inventoryClass?: string | null;
}

export interface MedicationDetail {
  medicationName?: string | null;
  prescriptionTime?: any | null;
  dosage?: string | null;
  dosageUnit?: string | null;
}

export interface MedicationDetailsInput {
  datePrescribed?: any | null;
  duration?: string | null;
  medicationName?: string | null;
  medicationCategory?: string | null;
  medicationType?: string | null;
  diagnosis?: DiagnosisInput[] | null;
  purpose: string;
  administrationMethod?: string | null;
  type?: string | null;
  quantity?: string | null;
  unitPrice?: string | null;
  dosage?: string | null;
  dosageUnit?: string | null;
  startDate?: any | null;
  endDate?: any | null;
  frequency?: string | null;
  discontinue?: string | null;
  discontinueReason?: string | null;
  adverseEffectsFollowingMedication?: string | null;
  stateEffects?: string | null;
  adverseEffectsInvestigated?: string | null;
  outcomeOfInvestigation?: string | null;
  priceDetails?: MedPriceDetailInput | null;
  option?: MedicationOptionType | null;
  bank?: BankType | null;
  provider?: string | null;
  drugInventoryId?: string | null;
  medicationConsumables?: MedicationConsumable[] | null;
  prescriptionNote?: string | null;
  concealPrescriptionNote?: boolean | null;
  refillNumber?: number | null;
  fromBundle?: string | null;
  dispenseStatus?: string | null;
  hmoClaim?: NewHmoClaimInput | null;
  inventoryClass?: string | null;
  id?: string | null;
  itemId?: string | null;
}

export interface MedicationInfusionDetail {
  typeOrStrength?: string | null;
  volume?: string | null;
  drugName?: string | null;
  dosage?: string | null;
  route?: string | null;
  dripRate?: string | null;
  batchNumber?: string | null;
  prescribedBy?: string | null;
  administrationDateTime?: any | null;
  administeredBy?: string | null;
  checkedBy?: string | null;
}

export interface MedicationInfusionInput {
  details?: MedicationInfusionDetail[] | null;
}

export interface MedicationInput {
  clinifyId?: string | null;
  setReminder?: boolean | null;
  reminderStartDate?: any | null;
  reminderEndDate?: any | null;
  medicationStartTime?: string | null;
  medicationEndTime?: string | null;
  interval?: number | null;
  intervalUnit?: string | null;
  prescribedBy?: string | null;
  dispenseDetails?: NewDispenseDetailsInput[] | null;
  details: MedicationDetailsInput[];
  specialty?: string | null;
  rank?: string | null;
  department?: string | null;
  remindMe?: string | null;
  hospitalName?: string | null;
  hospitalAddress?: string | null;
  discontinue?: string | null;
  refillNumber?: number | null;
  documentUrl?: string[] | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
  addToBundle?: boolean | null;
  medicationBundleName?: string | null;
  totalQuantity?: string | null;
  totalPrice?: string | null;
  administrationNote?: string | null;
  concealAdministrationNote?: boolean | null;
  id?: string | null;
}

export interface MedsHistoryFilter {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  date: string;
  orderBy: string;
  patientId: string;
}

export interface MergeProfilesInput {
  sources: string[];
  target: string;
}

export interface NewAdmissionInput {
  id?: string | null;
  clinifyId: string;
  admissionDate?: any | null;
  duration?: string | null;
  priority?: string | null;
  category?: string | null;
  severeness?: string | null;
  admittedBy?: string | null;
  fileNumber?: string | null;
  ward?: string | null;
  specialty?: string | null;
  rank?: string | null;
  department?: string | null;
  nurseName?: string | null;
  roomType?: string | null;
  roomNumber?: string | null;
  bedNumber?: string | null;
  presentMedicalHistory?: string | null;
  valuablesOrBelongings?: string | null;
  patientEnvironmentOrientation?: string | null;
  currentMedications?: string | null;
  medicinesDeposition?: string | null;
  instructedToSendHome?: string | null;
  nonBroughtToHospital?: string | null;
  otherPlacement?: string | null;
  medicationOrDrug?: string | null;
  bloodTransfusion?: string | null;
  food?: string | null;
  latex?: string | null;
  adultAge?: string | null;
  adultSedativeMedication?: string | null;
  adultAmbulatorySupport?: string | null;
  mentalStatus?: string | null;
  childAge?: string | null;
  dehydration?: string | null;
  dizziness?: string | null;
  respirationDistress?: string | null;
  childAmbulatorySupport?: string | null;
  childSedativeMedication?: string | null;
  painScore?: string | null;
  painDescriptors?: string[] | null;
  specifyPainDescriptors?: string | null;
  painLocation?: string[] | null;
  specifyPainLocation?: string | null;
  acuity?: string | null;
  modifying?: string | null;
  tobaccoUse?: string | null;
  tobaccoUseDuration?: string | null;
  alcoholUse?: string | null;
  alcoholUseDuration?: string | null;
  psychologicalStatus?: string | null;
  specifyPsychologicalStatus?: string | null;
  sleep?: string | null;
  specifySleepDifficulty?: string | null;
  sleepRoutine?: string | null;
  specifySleepRoutine?: string | null;
  whatMakesYouSleep?: string | null;
  bathing?: string | null;
  dressing?: string | null;
  eating?: string | null;
  mobility?: string | null;
  stairClimbing?: string | null;
  toiletUse?: string | null;
  impairedHearing?: string | null;
  impairedVision?: string | null;
  canPerformAdl?: string | null;
  canRead?: string | null;
  canWrite?: string | null;
  hearingAid?: string | null;
  glasses?: string | null;
  contacts?: string | null;
  dentures?: string | null;
  partial?: string | null;
  difficultyInChewing?: string | null;
  difficultyInSwallowing?: string | null;
  specialDiet?: string | null;
  specifySpecialDiet?: string | null;
  pallorSunkenEyesDehydrationAnorexia?: string | null;
  vomittingDiarrheaEdema?: string | null;
  newlyDiagnosedDiabeticOrHypertensive?: string | null;
  hairOrSkinChange?: string | null;
  nursingNeeds?: string | null;
  reAdmission?: string | null;
  lastAdmissionDateTime?: any | null;
  specialCourtesy?: string | null;
  specialArrangement?: string | null;
  nursingDiagnosis?: string[] | null;
  objectives?: string | null;
  nursingOrders?: string | null;
  evaluation?: string | null;
  provider?: string | null;
  providerServiceName?: string | null;
  roomInventoryId?: string | null;
  nextAppointmentDateTime?: any | null;
  nextAppointmentEndDateTime?: any | null;
  nextAppointmentDuration?: string | null;
  nextAppointmentSpecialty?: string | null;
  nextAppointmentRole?: string | null;
  nextAppointmentCategory?: string | null;
  nextAppointmentSpecialistId?: string | null;
  nextAppointmentSpecialistName?: string | null;
  nextAppointmentUrgency?: string | null;
  nextAppointmentDeliveryMethod?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  dischargePatients?: DischargePatientInput[] | null;
  bloodTransfusions?: BloodTransfusionInput[] | null;
  transferPatients?: TransferPatientInput[] | null;
  admissionNotes?: AdmissionNoteInput[] | null;
  dischargeDate?: any | null;
  transferDate?: any | null;
  clinicName: string;
  clinicAddress?: string | null;
  hospitalUnit?: string | null;
  admissionDiagnosis?: DiagnosisInput[] | null;
  doctorInCharge?: string | null;
  roomOption?: string | null;
  patientConsent?: string | null;
  patientConsentSignature?: string | null;
  patientConsentSignatureType?: string | null;
  patientConsentSignatureDateTime?: any | null;
  bedAvailable?: string | null;
  finding?: string | null;
  documentUrl?: string[] | null;
  additionalNote?: string | null;
  allergies?: string[] | null;
  consultations?: string[] | null;
  labTests?: string[] | null;
  medications?: string[] | null;
  radiology?: string[] | null;
  investigations?: string[] | null;
  surgeries?: string[] | null;
  vitals?: string[] | null;
  nursingServices?: string[] | null;
  hmoClaim?: NewHmoClaimInput | null;
  hmoProviderId?: string | null;
}

export interface NewAllergyInput {
  id?: string | null;
  clinifyId: string;
  occurenceDate?: any | null;
  duration?: string | null;
  details: AllergyFieldsInput[];
  doctorName?: string | null;
  specialty?: string | null;
  rank?: string | null;
  department?: string | null;
  hospitalName?: string | null;
  hospitalAddress?: string | null;
  medications?: string[] | null;
  documentUrl?: string[] | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
}

export interface NewAntenatalInput {
  id?: string | null;
  concealAdditionalNote?: boolean | null;
  clinifyId?: string | null;
  additionalNote?: string | null;
  documentUrl?: string[] | null;
  hospitalName?: string | null;
  hospitalAddress?: string | null;
  provider?: string | null;
  nextAppointmentDateTime?: any | null;
  nextAppointmentEndDateTime?: any | null;
  nextAppointmentDuration?: string | null;
  nextAppointmentSpecialty?: string | null;
  nextAppointmentRole?: string | null;
  nextAppointmentCategory?: string | null;
  nextAppointmentSpecialistId?: string | null;
  nextAppointmentSpecialistName?: string | null;
  nextAppointmentUrgency?: string | null;
  nextAppointmentDeliveryMethod?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  details: AntenatalDetailsInput[];
  medications?: string[] | null;
  vitals?: string[] | null;
  investigations?: string[] | null;
  labTests?: string[] | null;
  radiology?: string[] | null;
  nursingServices?: string[] | null;
  hmoProviderId?: string | null;
  hmoClaim?: NewHmoClaimInput | null;
  facilityAddress?: string | null;
}

export interface NewBusinessRuleInput {
  items: NewBusinessRuleItemInput[];
  flag?: string | null;
  matchAll?: boolean | null;
  sumAll?: boolean | null;
}

export interface NewBusinessRuleItemInput {
  category: string;
  operator: string;
  value: string;
  type: BusinessRuleItemType;
  id?: string | null;
  extra?: BusinessRuleItemExtraInput | null;
}

export interface NewChemoDiagnosisTemplateInput {
  facilityPreferenceId: string;
  type: string;
  combinationName: string;
  cycles: ChemoDiagnosisCycleTemplateInput[];
  section: string;
}

export interface NewConsultationInput {
  id?: string | null;
  clinifyId: string;
  consultationDateTime?: any | null;
  duration?: string | null;
  doctorName: string;
  priority?: string | null;
  specialty?: string | null;
  class?: string | null;
  department?: string | null;
  category?: string | null;
  provider?: string | null;
  providerServiceName?: string | null;
  nextAppointmentDateTime?: any | null;
  nextAppointmentEndDateTime?: any | null;
  nextAppointmentDuration?: string | null;
  nextAppointmentSpecialty?: string | null;
  nextAppointmentRole?: string | null;
  nextAppointmentCategory?: string | null;
  nextAppointmentSpecialistId?: string | null;
  nextAppointmentSpecialistName?: string | null;
  nextAppointmentUrgency?: string | null;
  nextAppointmentDeliveryMethod?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  consultationStartDate?: any | null;
  consultationEndDate?: any | null;
  clinicName?: string | null;
  clinicAddress?: string | null;
  documentUrl?: string[] | null;
  complaint?: string | null;
  concealComplaint?: boolean | null;
  complaintHistory?: string | null;
  concealComplaintHistory?: boolean | null;
  healthEducation?: string | null;
  concealHealthEducation?: boolean | null;
  observationNote?: string | null;
  concealObservationNote?: boolean | null;
  systemReview?: string | null;
  systemReviewSmartText?: string | null;
  systemReviewSmartSelection?: SelectionInput | null;
  complaintSmartText?: string | null;
  complaintSmartSelection?: SelectionInput | null;
  complaintGender?: string | null;
  positiveSymptoms?: string[] | null;
  concealSystemReview?: boolean | null;
  physicalExam?: string | null;
  physicalExamSmartText?: string | null;
  physicalExamSmartSelection?: SelectionInput | null;
  concealPhysicalExam?: boolean | null;
  treatmentPlan?: string | null;
  patientConsentSignature?: string | null;
  patientConsentSignatureType?: string | null;
  patientConsentSignatureDateTime?: string | null;
  concealTreatmentPlan?: boolean | null;
  patientAdmitted?: string | null;
  admissionConsent?: string | null;
  adverseEffectsFollowingTreatment?: string | null;
  stateEffects?: string | null;
  adverseEffectsInvestigated?: string | null;
  outcomeOfInvestigation?: string | null;
  treatmentGiven?: string | null;
  treatmentStatus?: string | null;
  provisionalDiagnosis: DiagnosisInput[];
  finalDiagnosis?: DiagnosisInput[] | null;
  admissions?: string[] | null;
  allergies?: string[] | null;
  labTests?: string[] | null;
  investigations?: string[] | null;
  medications?: string[] | null;
  radiology?: string[] | null;
  surgeries?: string[] | null;
  vitals?: string[] | null;
  nursingServices?: string[] | null;
  referral?: boolean | null;
  externalReferral?: boolean | null;
  specialtyReferredTo?: string | null;
  referredToProfileId?: string | null;
  referralReason?: string | null;
  referralDate?: any | null;
  concealReferralReason?: boolean | null;
  audiometry?: string | null;
  concealAudiometry?: boolean | null;
  hmoClaim?: NewHmoClaimInput | null;
  hmoProviderId?: string | null;
}

export interface NewConsultationsTemplateInput {
  name: string;
  facilityPreferenceId: string;
  complaints?: string | null;
  historyComplaints?: string | null;
  reviewSystems?: string | null;
  physicalExamination?: string | null;
  audiometry?: string | null;
  healthEducation?: string | null;
  treatmentPlan?: string | null;
}

export interface NewDischargeSummaryTemplateInput {
  name: string;
  facilityPreferenceId: string;
  summary: string;
}

export interface NewDispenseDetailsInput {
  id?: string | null;
  clinifyId: string;
  dispenseDate?: any | null;
  medicationDetailId: string;
  dispenseNote?: string | null;
  concealDispenseNote?: boolean | null;
  medicationName?: string[] | null;
  dispenseServiceDetails?: ServiceDetailInput[] | null;
  dispensedBy?: string | null;
  quantityRemaining?: string | null;
  quantityDispensed?: string | null;
  unitPrice?: number | null;
  dispenseConsumables?: DispenseConsumable[] | null;
  drugInventoryId?: string | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  inventoryClass?: string | null;
  option?: MedicationOptionType | null;
  hmoProviderId?: string | null;
  hmoClaim?: NewHmoClaimInput | null;
}

export interface NewFeedbackInput {
  feedbackDateTime?: any | null;
  title: string;
  category: string;
  message: string;
  documentUrl?: string[] | null;
  providerCode: string;
}

export interface NewFindingsTemplateInput {
  name: string;
  facilityPreferenceId: string;
  findings: string;
  impression?: string | null;
}

export interface NewHmoClaimInput {
  clinifyId: string;
  enrolleeId: string;
  claimDate: any;
  treatmentStartDate?: any | null;
  treatmentEndDate?: any | null;
  facilityName?: string | null;
  status?: string | null;
  facilityAddress?: string | null;
  rank?: string | null;
  department?: string | null;
  specialty?: string | null;
  presentingComplain?: string | null;
  referredBy?: string | null;
  referralCode?: string | null;
  referredFrom?: string | null;
  referredTo?: string | null;
  providerId: string;
  serviceType: string;
  serviceTypeCode: string;
  serviceName?: string | null;
  priority?: string | null;
  diagnosis: DiagnosisInput[];
  utilizations: PreauthUtilisationInput[];
  patientType?: string | null;
  paymentType?: string | null;
  additionalNote?: string | null;
  documentUrl?: string[] | null;
  memberUniqueId?: string | null;
  memberPlanId?: string | null;
  receiptNo?: string | null;
  autoGenerated?: boolean | null;
  facilityId?: string | null;
  enrolleePhoneNumber?: PhoneNumberInput | null;
  enrolleeEmail?: string | null;
  isExternalPlanType?: boolean | null;
  externalPlanTypeId?: string | null;
}

export interface NewHmoPlanInput {
  planDateTime?: any | null;
  name: string;
  planCode?: string | null;
  plannedBy?: string | null;
  premiumCountry?: string | null;
  status?: string | null;
  premiumDetails?: PremiumDetailsInput[] | null;
  benefits: PlanBenefit[];
  isExternalPlan?: boolean | null;
  isSponsor?: boolean | null;
}

export interface NewImmunizationInput {
  id?: string | null;
  clinifyId: string;
  details?: ImmunizationDetailInput[] | null;
  nextAppointmentDateTime?: any | null;
  nextAppointmentEndDateTime?: any | null;
  nextAppointmentDuration?: string | null;
  nextAppointmentSpecialty?: string | null;
  nextAppointmentRole?: string | null;
  nextAppointmentCategory?: string | null;
  nextAppointmentSpecialistId?: string | null;
  nextAppointmentSpecialistName?: string | null;
  nextAppointmentUrgency?: string | null;
  nextAppointmentDeliveryMethod?: string | null;
  remindMe?: boolean | null;
  serviceDetails?: ServiceDetailInput[] | null;
  hospitalName?: string | null;
  hospitalAddress?: string | null;
  documentUrl?: string[] | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
  hmoProviderId?: string | null;
  hmoClaim?: NewHmoClaimInput | null;
}

export interface NewInvestigationInput {
  id?: string | null;
  clinifyId: string;
  requestType: requestType;
  testInfo: InvestigationTestInfoInput[];
  requestDate?: any | null;
  priority?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  rank?: string | null;
  department?: string | null;
  status?: investigationStatus | null;
  orderedBy: string;
  specialty?: string | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  examinationType?: InvestigationExamTypeInput[] | null;
  clinicalHistory?: string | null;
  documentUrl?: string[] | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
  external?: boolean | null;
  externalHospital?: string | null;
  clinicalDiagnosis?: DiagnosisInput[] | null;
}

export interface NewInvestigationWithLabResult {
  id?: string | null;
  clinifyId: string;
  requestType: requestType;
  testInfo: InvestigationTestInfoInput[];
  requestDate?: any | null;
  priority?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  rank?: string | null;
  department?: string | null;
  status?: investigationStatus | null;
  orderedBy: string;
  specialty?: string | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  examinationType?: InvestigationExamTypeInput[] | null;
  clinicalHistory?: string | null;
  documentUrl?: string[] | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
  external?: boolean | null;
  externalHospital?: string | null;
  clinicalDiagnosis?: DiagnosisInput[] | null;
  details: LabTestDetail[];
  patientType?: string | null;
  paymentType?: string | null;
}

export interface NewInvestigationWithRadioResult {
  id?: string | null;
  clinifyId?: string | null;
  requestType?: requestType | null;
  requestDate?: any | null;
  priority?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  rank?: string | null;
  department?: string | null;
  status?: investigationStatus | null;
  orderedBy?: string | null;
  specialty?: string | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  examinationType?: InvestigationExamTypeInput[] | null;
  clinicalHistory?: string | null;
  documentUrl?: string[] | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
  external?: boolean | null;
  externalHospital?: string | null;
  clinicalDiagnosis?: DiagnosisInput[] | null;
  details: RadiologyExamDetail[];
  patientType?: string | null;
  paymentType?: string | null;
}

export interface NewLabCommentsTemplateInput {
  name: string;
  facilityPreferenceId: string;
  comment: string;
}

export interface NewLabourDeliveryInput {
  id?: string | null;
  clinifyId: string;
  provider?: string | null;
  visitationDateTime?: any | null;
  gestationAge?: string | null;
  estimatedDeliveryDate?: any | null;
  induction?: string | null;
  inductionDateTime?: any | null;
  inducedBy?: string | null;
  inducedMethod?: string | null;
  preTerm?: string | null;
  bloodGroup?: string | null;
  decisionSeekingCare?: string | null;
  modeOfTransportation?: string | null;
  postAbortionCare?: string | null;
  admittedForComplicationsOfUnsafeAbortion?: string | null;
  abortion?: string | null;
  membranesRuptureDateTime?: any | null;
  firstStageStartDateTime?: any | null;
  firstStageDuration?: string | null;
  firstStageSupportPersonPresent?: string | null;
  firstStageEpiduralGiven?: string | null;
  partographUsed?: string | null;
  firstStageMaternalBloodPressure?: string | null;
  firstStageFetalHeartRate?: string | null;
  firstStageFetalMonitoring?: string | null;
  firstStagePresentation?: string | null;
  firstStageSpecifyBreech?: string | null;
  firstStagePosition?: string | null;
  firstStageLie?: string | null;
  firstStageFetalMovement?: string | null;
  augmentation?: string | null;
  augmentationMethod?: string | null;
  firstStageMotherStatus?: string | null;
  firstStageMdaConducted?: string | null;
  firstStageMotherCauseOfDeath?: string | null;
  firstStageBabyStatus?: string | null;
  firstStageDoctorName?: string | null;
  firstStageSpecialty?: string | null;
  firstStageRank?: string | null;
  firstStageDepartment?: string | null;
  firstStageNurseName?: string | null;
  firstStageMidWife?: string | null;
  firstStageAdditionalNote?: string | null;
  concealFirstStageAdditionalNote?: boolean | null;
  secondStageStartDateTime?: any | null;
  secondStageBabyDeliveryDateTime?: any | null;
  secondStageDuration?: string | null;
  secondStageSupportPersonPresent?: string | null;
  secondStageEpiduralGiven?: string | null;
  secondStageDeliveryMethod?: string | null;
  placeOfBirth?: string | null;
  referredOut?: string | null;
  admitted?: string | null;
  discharged?: string | null;
  secondStageMaternalBloodPressure?: string | null;
  secondStageFetalHeartRate?: string | null;
  secondStagePresentation?: string | null;
  secondStageSpecifyBreech?: string | null;
  secondStagePosition?: string | null;
  secondStageLie?: string | null;
  secondStageFetalMovement?: string | null;
  episiotomy?: string | null;
  gender?: string | null;
  liveBirthWeight?: string | null;
  birthWeight?: string | null;
  birthWeightUnit?: string | null;
  notBreathingOrCrying?: string | null;
  babyResuscitated?: string | null;
  cordClampTime?: string | null;
  chxGelApplied?: string | null;
  secondStageMotherStatus?: string | null;
  secondStageMdaConducted?: string | null;
  secondStageMotherCauseOfDeath?: string | null;
  secondStageBabyStatus?: string | null;
  stillBirth?: string | null;
  liveHivBirth?: string | null;
  deliveredBy?: string | null;
  secondStageDoctorName?: string | null;
  secondStageSpecialty?: string | null;
  secondStageRank?: string | null;
  secondStageDepartment?: string | null;
  secondStageNurseName?: string | null;
  secondStageMidWife?: string | null;
  secondStageMarternalComplication?: string | null;
  secondStageAdditionalNote?: string | null;
  concealSecondStageAdditionalNote?: boolean | null;
  thirdStageStartDateTime?: any | null;
  placentaDateTime?: any | null;
  thirdStageDuration?: string | null;
  placentaDeliveryMethod?: string | null;
  placentaComplete?: string | null;
  configuration?: string | null;
  bloodLossEstimate?: string | null;
  osytocinReceiveed?: string | null;
  misoprostolReceived?: string | null;
  receivedMgso4WithEclampsia?: string | null;
  cervicalTear?: string | null;
  perinealLaceration?: string | null;
  birthInjury?: string | null;
  otherTrauma?: string | null;
  repair?: string | null;
  specifyRepair?: string | null;
  anesthesiaType?: string | null;
  anesthesiaGiven?: string | null;
  thirdStageMotherStatus?: string | null;
  thirdStageMdaConducted?: string | null;
  thirdStageMotherCauseOfDeath?: string | null;
  thirdStageBabyStatus?: string | null;
  thirdStageDoctorName?: string | null;
  thirdStageSpecialty?: string | null;
  thirdStageRank?: string | null;
  thirdStageDepartment?: string | null;
  thirdStageNurseName?: string | null;
  thirdStageMidWife?: string | null;
  thirdStageAdditionalNote?: string | null;
  concealThirdStageAdditionalNote?: boolean | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  documentUrl?: string[] | null;
  subBillRef?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  nextAppointmentDateTime?: any | null;
  nextAppointmentEndDateTime?: any | null;
  nextAppointmentDuration?: string | null;
  nextAppointmentSpecialty?: string | null;
  nextAppointmentRole?: string | null;
  nextAppointmentCategory?: string | null;
  nextAppointmentSpecialistId?: string | null;
  nextAppointmentSpecialistName?: string | null;
  nextAppointmentUrgency?: string | null;
  nextAppointmentDeliveryMethod?: string | null;
  hmoProviderId?: string | null;
  hmoClaim?: NewHmoClaimInput | null;
  typeOfClient?: string | null;
  currentPregnancyNumber?: string | null;
  parity?: string | null;
  firstStageMaternalTemperature?: string | null;
  firstStageMaternalTemperatureUnit?: string | null;
  firstStageMaternalPulseRate?: string | null;
  firstStageMaternalRespiratoryRate?: string | null;
  secondStageMaternalTemperature?: string | null;
  secondStageMaternalTemperatureUnit?: string | null;
  secondStageMaternalPulseRate?: string | null;
  secondStageMaternalRespiratoryRate?: string | null;
  secondStageMouthAndNoseSuctioned?: string | null;
  babyCauseOfDeath?: string | null;
  secondStageHeadCircumferenceOfBaby?: string | null;
  secondStageChestCircumferenceOfBaby?: string | null;
  secondStageBodyLengthOfBaby?: string | null;
  secondStageBabyRespiratoryRate?: string | null;
  secondStageBabyHeartRate?: string | null;
  secondStageTemperatureAt1hr?: string | null;
  secondStageTemperatureAt1hrUnit?: string | null;
  fourthStagePostpartumComplications?: string | null;
  babyPutToBreast?: string | null;
  fourthStageTimeOfInitiatingBreastfeeding?: string | null;
  fourthStagePostDeliveryMassageGiven?: string | null;
  fourthStageCervicalTear?: string | null;
  fourthStagePerinealLaceration?: string | null;
  fourthStageBirthInjury?: string | null;
  fourthStageVulvaCareGiven?: string | null;
  fourthStageOtherTrauma?: string | null;
  fourthStageRepair?: string | null;
  fourthStageSpecifyRepair?: string | null;
  fourthStageAnesthesiaType?: string | null;
  fourthStageAnesthesiaGiven?: string | null;
  fourthStageVitaminKGivenToBaby?: string | null;
  fourthStageVitaminKDoseGivenToBaby?: string | null;
  fourthStageMaternalTemperature?: string | null;
  fourthStageMaternalTemperatureUnit?: string | null;
  fourthStageMaternalPulseRate?: string | null;
  fourthStageBabyTemperature?: string | null;
  fourthStageBabyTemperatureUnit?: string | null;
  fourthStageBabyHeartRate?: string | null;
  fourthStageBabyRespiratoryRate?: string | null;
  fourthStageStatusOfMother?: string | null;
  fourthStageMdaConducted?: string | null;
  fourthStageMotherCauseOfDeath?: string | null;
  fourthStageStatusOfBaby?: string | null;
  fourthStagePostpartumDepression?: string | null;
  dangerSigns?: string | null;
  firstDoseOfAntibioticsAndReferred?: string | null;
  neonatalTetanus?: string | null;
  neonatalJaundice?: string | null;
  admittedOnKMC?: string | null;
  dischargedAfterKMC?: string | null;
  givenLlin?: string | null;
  birthCertificateIssued?: string | null;
  birthCertificateCollected?: string | null;
  fourthStageApgarScoreA?: string | null;
  fourthStageApgarScoreP?: string | null;
  fourthStageApgarScoreG?: string | null;
  fourthStageApgarScoreApperance?: string | null;
  fourthStageApgarScoreR?: string | null;
  fourthStageApgarScore?: string | null;
  fourthStageCordCare?: string | null;
  fourthStageGeneralConditionOfTheBaby?: string | null;
  fourthStageGeneralConditionOfTheMother?: string | null;
  fourthStageDoctorName?: string | null;
  fourthStageSpecialty?: string | null;
  fourthStageRank?: string | null;
  fourthStageDepartment?: string | null;
  fourthStageNurseName?: string | null;
  fourthStageMidWife?: string | null;
  fourthStageAdditionalNote?: string | null;
  concealFourthStageAdditionalNote?: boolean | null;
  concealFourthStageCordCare?: boolean | null;
  concealFourthStageGeneralConditionOfTheBaby?: boolean | null;
  concealFourthStageGeneralConditionOfTheMother?: boolean | null;
}

export interface NewMedicalReportInput {
  reportDate?: any | null;
  reportType?: ReportTypeInput[] | null;
  patientClinifyId?: string | null;
  patientFullname?: string | null;
  patientPhone?: string | null;
  patientEmail?: string | null;
  report?: string[] | null;
  rank?: string | null;
  department?: string | null;
  doctorName: string;
  specialty?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  clinicalHistory?: string | null;
  additionalNote?: string | null;
  status?: MedicalReportStatus | null;
  documentUrl?: string[] | null;
}

export interface NewMedicalReportTemplateInput {
  name: string;
  facilityPreferenceId: string;
  template: string;
}

export interface NewMedicationBundleInput {
  id?: string | null;
  clinifyId: string;
  bundleName: string;
  additionalNote?: string | null;
  documentUrl?: string[] | null;
  medicationBundleItems?: MedicationBundleItemInput[] | null;
}

export interface NewMedicationDetailsInput {
  datePrescribed?: any | null;
  duration?: string | null;
  medicationName?: string | null;
  medicationCategory?: string | null;
  medicationType?: string | null;
  diagnosis?: DiagnosisInput[] | null;
  purpose: string;
  administrationMethod?: string | null;
  type?: string | null;
  quantity?: string | null;
  unitPrice?: string | null;
  dosage?: string | null;
  dosageUnit?: string | null;
  startDate?: any | null;
  endDate?: any | null;
  frequency?: string | null;
  discontinue?: string | null;
  discontinueReason?: string | null;
  adverseEffectsFollowingMedication?: string | null;
  stateEffects?: string | null;
  adverseEffectsInvestigated?: string | null;
  outcomeOfInvestigation?: string | null;
  priceDetails?: MedPriceDetailInput | null;
  option?: MedicationOptionType | null;
  bank?: BankType | null;
  provider?: string | null;
  drugInventoryId?: string | null;
  medicationConsumables?: MedicationConsumable[] | null;
  prescriptionNote?: string | null;
  concealPrescriptionNote?: boolean | null;
  refillNumber?: number | null;
  fromBundle?: string | null;
  dispenseStatus?: string | null;
  hmoClaim?: NewHmoClaimInput | null;
  inventoryClass?: string | null;
}

export interface NewMedicationInput {
  clinifyId: string;
  setReminder?: boolean | null;
  reminderStartDate?: any | null;
  reminderEndDate?: any | null;
  medicationStartTime?: string | null;
  medicationEndTime?: string | null;
  interval?: number | null;
  intervalUnit?: string | null;
  prescribedBy?: string | null;
  dispenseDetails?: NewDispenseDetailsInput[] | null;
  details: NewMedicationDetailsInput[];
  specialty?: string | null;
  rank?: string | null;
  department?: string | null;
  remindMe?: string | null;
  hospitalName?: string | null;
  hospitalAddress?: string | null;
  discontinue?: string | null;
  refillNumber?: number | null;
  documentUrl?: string[] | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
  addToBundle?: boolean | null;
  medicationBundleName?: string | null;
  totalQuantity?: string | null;
  totalPrice?: string | null;
  administrationNote?: string | null;
  concealAdministrationNote?: boolean | null;
}

export interface NewNursingServicesInput {
  id?: string | null;
  details: NursingServicesDetailsInput[];
  clinifyId?: string | null;
  provider?: string | null;
  nurseName?: string | null;
  assistantNurseName?: string | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  additionalNote?: string | null;
  documentUrl?: string[] | null;
  allergies?: string[] | null;
  labTests?: string[] | null;
  investigations?: string[] | null;
  radiology?: string[] | null;
  surgeries?: string[] | null;
  consultations?: string[] | null;
  medications?: string[] | null;
  admissions?: string[] | null;
  immunizations?: string[] | null;
  vitals?: string[] | null;
  progressNotes?: NursingServiceProgressNoteInput[] | null;
  specialty?: string | null;
  department?: string | null;
  hmoProviderId?: string | null;
  hmoClaim?: NewHmoClaimInput | null;
  serviceDetails?: ServiceDetailInput[] | null;
  concealAdditionalNote?: boolean | null;
}

export interface NewOncologyConsultationInput {
  id?: string | null;
  clinifyId: string;
  consultationDateTime?: any | null;
  duration?: string | null;
  priority?: string | null;
  category?: string | null;
  doctorName?: string | null;
  specialty?: string | null;
  rank?: string | null;
  department?: string | null;
  consultationStartDate?: any | null;
  consultationEndDate?: any | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  initialDiagnosisICD10?: string | null;
  initialDiagnosisICD11?: string | null;
  initialDiagnosisSNOMED?: string | null;
  finalDiagnosisICD10?: string | null;
  finalDiagnosisICD11?: string | null;
  finalDiagnosisSNOMED?: string | null;
  diagnosedBy?: string | null;
  diagnosisDateTime?: any | null;
  additionalNote?: string | null;
  stageDiagnosisICD10?: string | null;
  stageDiagnosisICD11?: string | null;
  stageDiagnosisSNOMED?: string | null;
  stageDiagnosisDateTime?: any | null;
  classification?: string | null;
  stage?: string | null;
  stageDate?: any | null;
  histopathologicType?: string | null;
  stageTiming?: string | null;
  primaryTumor?: string | null;
  residualTumor?: string | null;
  tumorDetails?: TumorDetailsInput[] | null;
  lymphovascularInvasion?: string | null;
  regionalLymphNodes?: string | null;
  numberOfNodes?: string | null;
  distantMetastasis?: string | null;
  grade?: string | null;
  stageStatus?: string | null;
  cancerType?: string | null;
  progression?: string | null;
  relapse?: string | null;
  remission?: string | null;
  stageTreatmentType?: string | null;
  stageAdditionalNote?: string | null;
  nuclearGradeOrPleomorphism?: string | null;
  mitoticCountScore?: string | null;
  tubuleFormation?: string | null;
  stageGroupingScore?: string | null;
  scarffBloomRichardsonScore?: string | null;
  nottinghamModificationSbrGrade?: string | null;
  stageLymphovascularInvasion?: string | null;
  stageHistopathologicType?: string | null;
  diagnosticInformation?: string | null;
  typeOfSpecimen?: string | null;
  stagingRole?: string | null;
  stagingDate?: any | null;
  treatmentType?: string | null;
  treatmentSite?: string | null;
  intentOfTreatment?: string | null;
  lineOfTreatment?: string | null;
  concurrentTreatment?: string | null;
  treatmentPlanProvider?: string | null;
  treatmentDepartment?: string | null;
  treatmentStatus?: string | null;
  treatmentPriority?: string | null;
  treatmentInterval?: string | null;
  treatmentStartDate?: any | null;
  treatmentEndDate?: any | null;
  treatmentCycleDays?: string | null;
  treatmentCycleNumber?: string | null;
  treatmentPatientType?: string | null;
  treatmentAdverseReaction?: string | null;
  treatmentSpecificReaction?: string | null;
  treatmentOutcome?: string | null;
  treatmentResponse?: string | null;
  treatmentFollowupDate?: any | null;
  treatmentAdditionalNote?: string | null;
  therapyType?: string | null;
  therapySite?: string | null;
  intentOfTherapy?: string | null;
  lineOfTherapy?: string | null;
  concurrentTherapy?: string | null;
  therapyPlanProvider?: string | null;
  therapyDepartment?: string | null;
  therapyStatus?: string | null;
  therapyPriority?: string | null;
  therapyInterval?: string | null;
  therapyStartDate?: any | null;
  therapyEndDate?: any | null;
  therapyCycleDays?: string | null;
  therapyCycleNumber?: string | null;
  therapyPatientType?: string | null;
  therapyAdverseReaction?: string | null;
  therapySpecificReaction?: string | null;
  therapyOutcome?: string | null;
  therapyResponse?: string | null;
  therapyFollowupDate?: any | null;
  therapyAdditionalNote?: string | null;
  nottinghamGradeAbove?: string | null;
  estrogenReceptorExpression?: string | null;
  erPercentagePositive?: string | null;
  erAllredScore?: string | null;
  progesteroneReceptor?: string | null;
  prPercentagePositive?: string | null;
  prAllredScore?: string | null;
  overallHer2Status?: string | null;
  ihcScore?: string | null;
  fishResult?: string | null;
  fishCopyNumber?: string | null;
  her2OrCep17Ratio?: string | null;
  circulatingTumorCells?: string | null;
  complaintGender?: string | null;
  complaintSmartText?: string | null;
  complaintSmartSelection?: SelectionInput | null;
  systemReviewSmartText?: string | null;
  systemReviewSmartSelection?: SelectionInput | null;
  physicalExamSmartText?: string | null;
  physicalExamSmartSelection?: SelectionInput | null;
  concealComplaint?: boolean | null;
  complaint?: string | null;
  concealComplaintHistory?: boolean | null;
  complaintHistory?: string | null;
  concealSystemReview?: boolean | null;
  systemReview?: string | null;
  concealPhysicalExam?: boolean | null;
  physicalExam?: string | null;
  concealAudiometry?: boolean | null;
  audiometry?: string | null;
  concealHealthEducation?: boolean | null;
  healthEducation?: string | null;
  treatmentPlan?: string | null;
  concealTreatmentPlan?: boolean | null;
  treatmentGiven?: string | null;
  adverseEffectsFollowingTreatment?: string | null;
  stateEffects?: string | null;
  adverseEffectsInvestigated?: string | null;
  outcomeOfInvestigation?: string | null;
  patientAdmitted?: string | null;
  admissionConsent?: string | null;
  patientConsentSignature?: string | null;
  patientConsentSignatureType?: string | null;
  patientConsentSignatureDateTime?: string | null;
  observationNote?: string | null;
  concealObservationNote?: boolean | null;
  nextAppointmentDateTime?: any | null;
  nextAppointmentEndDateTime?: any | null;
  nextAppointmentDuration?: string | null;
  nextAppointmentSpecialty?: string | null;
  nextAppointmentRole?: string | null;
  nextAppointmentCategory?: string | null;
  nextAppointmentSpecialistId?: string | null;
  nextAppointmentSpecialistName?: string | null;
  nextAppointmentUrgency?: string | null;
  nextAppointmentDeliveryMethod?: string | null;
  documentUrl?: string[] | null;
  provider?: string | null;
  providerServiceName?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  hmoClaim?: NewHmoClaimInput | null;
  hmoProviderId?: string | null;
  oncologyChemoDrugs?: OncologyChemoDrugInput[] | null;
  saveAsTemplate?: string[] | null;
  preChemoTemplateName?: string | null;
  chemoTemplateName?: string | null;
  postChemoTemplateName?: string | null;
  concealChemoNote?: boolean | null;
  chemoNote?: string | null;
  laboratoryTestVerified?: string | null;
  radiologyExaminationVerified?: string | null;
  admissions?: string[] | null;
  allergies?: string[] | null;
  labTests?: string[] | null;
  investigations?: string[] | null;
  medications?: string[] | null;
  radiology?: string[] | null;
  surgeries?: string[] | null;
  vitals?: string[] | null;
  nursingServices?: string[] | null;
}

export interface NewOperationNoteTemplateInput {
  name: string;
  facilityPreferenceId: string;
  note: string;
  postNote: string;
}

export interface NewOrganisationAppointmentInput {
  profileId?: string | null;
  patientInformation: PatientInformationInput;
  startDateTime: any;
  endDateTime: any;
  category?: string | null;
  rank?: string | null;
  reason?: string | null;
  venue?: string | null;
  liveSessionUrl?: string | null;
  hospitalId?: string | null;
  specialty?: string | null;
  role?: string | null;
  specialistId?: string | null;
  paymentType?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  patientType?: string | null;
  urgency?: string | null;
  deliveryMethod?: string | null;
  duration?: string | null;
  additionalNote?: string | null;
  appointmentDateTime?: any | null;
  documentUrl?: string[] | null;
  status?: OrganisationApointmentStatus | null;
  origin?: string | null;
  reminderTransportOptions?: string[] | null;
}

export interface NewPackageInput {
  id?: string | null;
  clinifyId: string;
  packageDate: any;
  name: string;
  price?: string | null;
  gender?: string | null;
  serviceDetails?: PackageServiceInput[] | null;
}

export interface NewPostnatalInput {
  id?: string | null;
  clinifyId: string;
  provider?: string | null;
  visitationDateTime?: any | null;
  seenBy?: string | null;
  deliveryDate?: any | null;
  hasMenstruationStarted?: string | null;
  menstruationStartDate?: any | null;
  uterus?: string | null;
  breastFeeding?: string | null;
  familyPlanning?: string | null;
  contraceptivePillCount?: string | null;
  typeOfImplant?: string | null;
  nameOfInjectable?: string | null;
  firstTimeFpUser?: string | null;
  sourceOfReferral?: string | null;
  familyPlanningClientType?: string | null;
  familyPlanningMethod?: string | null;
  vulva?: string | null;
  vitaminAGivenToBaby?: string | null;
  vitaminADoseGivenToBaby?: string | null;
  vitaminADoseUnitGivenToBaby?: string | null;
  babyComplaint?: string | null;
  stateBabyComplaint?: string | null;
  clinicalDiagnosis?: DiagnosisInput[] | null;
  visitationNote?: string | null;
  concealVisitationNote?: boolean | null;
  rank?: string | null;
  department?: string | null;
  healthEducation?: string | null;
  concealHealthEducation?: boolean | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
  treatmentPlan?: string | null;
  concealTreatmentPlan?: boolean | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  documentUrl?: string[] | null;
  serviceDetails?: ServiceDetailInput[] | null;
  nextAppointmentDateTime?: any | null;
  nextAppointmentEndDateTime?: any | null;
  nextAppointmentDuration?: string | null;
  nextAppointmentSpecialty?: string | null;
  nextAppointmentRole?: string | null;
  nextAppointmentCategory?: string | null;
  nextAppointmentSpecialistId?: string | null;
  nextAppointmentSpecialistName?: string | null;
  nextAppointmentUrgency?: string | null;
  nextAppointmentDeliveryMethod?: string | null;
  postpartumDepression?: string | null;
  hmoProviderId?: string | null;
  hmoClaim?: NewHmoClaimInput | null;
}

export interface NewPricesInput {
  name?: string | null;
  code?: string | null;
  providerCode?: string | null;
  price?: string | null;
  aliasCode?: string | null;
  aliasName?: string | null;
  serviceType?: string | null;
  category?: string | null;
}

export interface NewRequestPackageInput {
  id?: string | null;
  clinifyId: string;
  requestDate: any;
  packageName: string;
  priority?: string | null;
  category?: string | null;
  serviceDetails?: PackageServiceInput[] | null;
  price: string;
  orderedBy?: string | null;
  specialty?: string | null;
  rank?: string | null;
  department?: string | null;
  patientType?: string | null;
  paymentType?: string | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  additionalNote?: string | null;
}

export interface NewRequestProcedureInput {
  id?: string | null;
  clinifyId: string;
  surgeryDate?: any | null;
  duration?: string | null;
  provider?: string | null;
  procedureType: ProcedureTypeInput[];
  requestedBy?: string | null;
  operatedBy: string;
  specialty?: string | null;
  rank?: string | null;
  department?: string | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  patientConsent?: string | null;
  patientConsentSignature?: string | null;
  patientConsentSignatureType?: string | null;
  patientConsentSignatureDateTime?: any | null;
  serviceDetails?: ServiceDetailInput[] | null;
  reason?: string | null;
  surgeryStartDate?: any | null;
  surgeryEndDate?: any | null;
  nextAppointmentDateTime?: any | null;
  nextAppointmentEndDateTime?: any | null;
  nextAppointmentDuration?: string | null;
  nextAppointmentSpecialty?: string | null;
  nextAppointmentRole?: string | null;
  nextAppointmentCategory?: string | null;
  nextAppointmentSpecialistId?: string | null;
  nextAppointmentSpecialistName?: string | null;
  nextAppointmentUrgency?: string | null;
  nextAppointmentDeliveryMethod?: string | null;
  documentUrl?: string[] | null;
  hmoProviderId?: string | null;
  hmoClaim?: NewHmoClaimInput | null;
}

export interface NewSurgeryInput {
  id?: string | null;
  clinifyId: string;
  surgeryDate?: any | null;
  duration?: string | null;
  provider?: string | null;
  procedureType: ProcedureTypeInput[];
  requestedBy?: string | null;
  operatedBy: string;
  specialty?: string | null;
  rank?: string | null;
  department?: string | null;
  assistantSurgeon?: string | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  priority?: string | null;
  patientConsent?: string | null;
  patientConsentSignature?: string | null;
  patientConsentSignatureType?: string | null;
  patientConsentSignatureDateTime?: any | null;
  serviceDetails?: ServiceDetailInput[] | null;
  reason?: string | null;
  operatingRoomNurse?: string | null;
  anesthetistName?: string | null;
  anesthesia?: string | null;
  surgeryStartDate?: any | null;
  surgeryEndDate?: any | null;
  nextAppointmentDateTime?: any | null;
  nextAppointmentEndDateTime?: any | null;
  nextAppointmentDuration?: string | null;
  nextAppointmentSpecialty?: string | null;
  nextAppointmentRole?: string | null;
  nextAppointmentCategory?: string | null;
  nextAppointmentSpecialistId?: string | null;
  nextAppointmentSpecialistName?: string | null;
  nextAppointmentUrgency?: string | null;
  nextAppointmentDeliveryMethod?: string | null;
  documentUrl?: string[] | null;
  operationNote?: string | null;
  postOperationNote?: string | null;
  concealOperationNote?: boolean | null;
  medications?: string[] | null;
  vitals?: string[] | null;
  investigations?: string[] | null;
  labTests?: string[] | null;
  radiology?: string[] | null;
  hmoProviderId?: string | null;
  hmoClaim?: NewHmoClaimInput | null;
  visitingFacilityName?: string | null;
  visitingSpecialistName?: string | null;
  nursingServices?: string[] | null;
}

export interface NewVitalInput {
  id?: string | null;
  clinifyId: string;
  type?: VitalType | null;
  anthropometry?: AnthropometryVitalFields[] | null;
  bloodGlucose?: BloodGlucoseVitalFields[] | null;
  bloodPressure?: BloodPressureVitalFields[] | null;
  pulseRate?: PulseRateVitalFields[] | null;
  respiratoryRate?: RespiratoryRateVitalFields[] | null;
  temperature?: TemperatureVitalFields[] | null;
  urineDipstick?: UrineDipstickVitalFields[] | null;
  visualAcuity?: VisualAcuityVitalFields[] | null;
  pain?: PainVitalFields[] | null;
  hospitalName?: string | null;
  hospitalAddress?: string | null;
  documentUrl?: string[] | null;
}

export interface NewWalkInHospitalBillInput {
  id?: string | null;
  billingDateTime?: any | null;
  raisedBy?: string | null;
  clinifyId?: string | null;
  additionalNote?: string | null;
  totalAmount?: number | null;
  discountAmount?: number | null;
  vatAmount?: number | null;
  professionalFeeAmount?: number | null;
  amountDue?: number | null;
  amountPaid?: number | null;
  unitPrice?: number | null;
  amountOutstanding?: number | null;
  amountOwning?: number | null;
  amountUnderpaid?: number | null;
  amountOverpaid?: number | null;
  hospitalName?: string | null;
  hospitalAddress?: string | null;
  reference?: string | null;
  invoiceNumber?: string | null;
  details?: BillDetailInput[] | null;
  collectedBy?: string | null;
  collectionDateTime?: any | null;
  profileId?: string | null;
  patientInformation?: PatientInformationInput | null;
}

export interface NewWalkInReferralInput {
  id?: string | null;
  referralDateTime?: any | null;
  referredBy?: string | null;
  concealReferralReason?: boolean | null;
  referralReason?: string | null;
  referralFacilityName?: string | null;
  referralFacilityAddress?: string | null;
  profileId?: string | null;
  patientInformation?: PatientInformationInput | null;
  documentUrl?: string[] | null;
}

export interface NewWalkInTransferInput {
  id?: string | null;
  transferDateTime?: any | null;
  transferredBy?: string | null;
  concealTransferReason?: boolean | null;
  transferReason?: string | null;
  transferFacilityName?: string | null;
  transferFacilityAddress?: string | null;
  profileId?: string | null;
  patientInformation?: PatientInformationInput | null;
  documentUrl?: string[] | null;
}

export interface NextOfKinInput {
  id?: string | null;
  clinifyId: string;
  title?: string | null;
  firstName?: string | null;
  middleName?: string | null;
  lastName?: string | null;
  gender?: string | null;
  bloodGroup?: string | null;
  genoType?: string | null;
  phoneNumber?: PhoneNumberInput | null;
  phoneNumberAlt?: PhoneNumberInput | null;
  email?: string | null;
  emailAlt?: string | null;
  relationship?: string | null;
  occupation?: string | null;
  companyName?: string | null;
  address?: string | null;
  lga?: string | null;
  state?: string | null;
  country?: string | null;
}

export interface NotificationFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  tag?: NotificationTag | null;
}

export interface NursingServiceProgressNoteInput {
  id?: string | null;
  conceal?: boolean | null;
  note?: string | null;
}

export interface NursingServicesDetailsInput {
  duration?: string | null;
  procedureDateTime?: any | null;
  procedureType?: string | null;
  procedureStartDateTime?: any | null;
  procedureEndDateTime?: any | null;
  procedureName?: string | null;
  location?: string | null;
  procedureNote?: string | null;
  provider?: string | null;
  itemId?: string | null;
  providerServiceName?: string | null;
  priority?: string | null;
  category?: string | null;
  dateOfBirth?: any | null;
  consentGiven?: string | null;
  parentGuardianPresent?: string | null;
  parentGuardianName?: string | null;
  anaesthesiaGiven?: string | null;
  anaesthesiaType?: string | null;
  vitaminKGiven?: string | null;
  castLocation?: string | null;
  reasonForCasting?: string | null;
  isItARepeatedCasting?: string | null;
  hasRadiologicalInvestigationBeenDone?: string | null;
  bathing?: string | null;
  dressing?: string | null;
  eating?: string | null;
  mobility?: string | null;
  stairClimbing?: string | null;
  toiletUse?: string | null;
  woundLocation?: string | null;
  dressingType?: string | null;
  dressingAppearance?: string | null;
  dressingIntervention?: string | null;
  lastDressingChange?: any | null;
  dressingChangeDue?: any | null;
  painScore?: string | null;
  painDescriptors?: string[] | null;
  otherPainDescriptors?: string | null;
  signOfInfection?: string | null;
  whichEar?: string | null;
  observation?: string | null;
  informedConsent?: string | null;
  method?: string | null;
  otherMethods?: string | null;
  councelled?: string | null;
  typeOfInjectable?: string | null;
  typeOfImplant?: string | null;
  patientConsentSignature?: string | null;
  patientConsentSignatureType?: string | null;
  patientConsentSignatureDateTime?: any | null;
  medications?: MedicationDetail[] | null;
  heartRateBefore?: string | null;
  heartRateAfter?: string | null;
  respiratoryRateBefore?: string | null;
  respiratoryRateAfter?: string | null;
  oxygenSaturationBefore?: string | null;
  oxygenSaturationAfter?: string | null;
  injectionName?: string | null;
  InjectionLocation?: string | null;
  injectionPrepared?: string | null;
  equipmentsSterilized?: string | null;
  medicalHistoryChecked?: string | null;
  repeatInjection?: string | null;
}

export interface NursingServicesFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
}

export interface NursingServicesInput {
  id: string;
  details?: NursingServicesDetailsInput[] | null;
  clinifyId?: string | null;
  provider?: string | null;
  nurseName?: string | null;
  assistantNurseName?: string | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  additionalNote?: string | null;
  documentUrl?: string[] | null;
  allergies?: string[] | null;
  labTests?: string[] | null;
  investigations?: string[] | null;
  radiology?: string[] | null;
  surgeries?: string[] | null;
  consultations?: string[] | null;
  medications?: string[] | null;
  admissions?: string[] | null;
  immunizations?: string[] | null;
  vitals?: string[] | null;
  specialty?: string | null;
  department?: string | null;
  hmoProviderId?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  concealAdditionalNote?: boolean | null;
  billInfo?: BillInfoUpdateInput[] | null;
}

export interface NutritionalHistoryGrowthInput {
  id?: string | null;
  clinifyId: string;
  months?: number | null;
  years?: number | null;
  measurementType: string;
  weight?: string | null;
  babyLength?: string | null;
  height?: string | null;
  headCircumference?: string | null;
}

export interface NutritionalHistoryInput {
  id?: string | null;
  clinifyId: string;
  feedingMethod?: string | null;
  feedingDifficulties?: string | null;
  counseledOnNutrition?: string | null;
  receivedNutritionServices?: string | null;
  childIsGrowingWell?: string | null;
  receivedMicroNutrientPowder?: string | null;
  admittedToTreatSevereMalnutrition?: string | null;
  outcomeTreatingSevereMalnutrition?: string | null;
  dietaryRecallNote?: string | null;
  feedingSinceBirthNote?: string | null;
  additionalNote?: string | null;
}

export interface ObstetricHistoryInput {
  id?: string | null;
  clinifyId: string;
  placeOfBirth?: string | null;
  modeOfBirth?: string | null;
  childrenCount: number;
  deliveryCount?: number | null;
  pregnancyCount?: number | null;
  lastBirth?: any | null;
  typeOfClient?: string | null;
  decisionSeekingCare?: string | null;
  modeOfTransportation?: string | null;
  parity?: string | null;
  partographUsed?: string | null;
  osytocinReceiveed?: string | null;
  misoprostolReceived?: string | null;
  marternalComplication?: string | null;
  receivedMgso4WithEclampsia?: string | null;
  counselledOnBreastFeeding?: string | null;
  counselledOnFamilyPlanning?: string | null;
  admitted?: string | null;
  discharged?: string | null;
  referredOut?: string | null;
  postAbortionCare?: string | null;
  motherStatus?: string | null;
  mdaConducted?: string | null;
  deliveryDateTime?: any | null;
  gender?: string | null;
  abortion?: string | null;
  preTerm?: string | null;
  notBreathingOrCrying?: string | null;
  babyResuscitated?: string | null;
  liveBirthWeight?: string | null;
  stillBirth?: string | null;
  deadLessThan7days?: string | null;
  babyStatus?: string | null;
  liveHivBirth?: string | null;
  deliveredBy?: string | null;
  timeCordClamped?: any | null;
  chxGelApplied?: string | null;
  babyPutToBreast?: string | null;
  temperatureAt1hr?: string | null;
  temperatureAt1hrUnit?: string | null;
  additionalNote?: string | null;
  ancSyphilisTestDone?: string | null;
  ancSyphilisTestResult?: string | null;
  ancSyphilisTestTreated?: string | null;
  ancHepatitisBTestDone?: string | null;
  ancHepatitisBTestResult?: string | null;
  ancHepatitisBReferredForTreatment?: string | null;
  ancHepatitisCTestDone?: string | null;
  ancHepatitisCTestResult?: string | null;
  ancHepatitisCTestReferredForTreatment?: string | null;
  ipt1?: string | null;
  ipt2?: string | null;
  ipt3?: string | null;
  ipt4?: string | null;
  motherCauseOfDeath?: string | null;
  admittedForComplicationsOfUnsafeAbortion?: string | null;
  admittedOnKMC?: string | null;
  dischargedAfterKMC?: string | null;
  babyCauseOfDeath?: string | null;
  dangerSigns?: string | null;
  firstDoseOfAntibioticsAndReferred?: string | null;
  neonatalTetanus?: string | null;
  neonatalJaundice?: string | null;
  birthCertificateIssued?: string | null;
  birthCertificateCollected?: string | null;
}

export interface OncologyChemoCommentInput {
  cycleNumber: number;
  section: string;
  comment?: string | null;
}

export interface OncologyChemoDrugInput {
  day: string;
  drugName: string;
  drugId?: string | null;
  dosage?: string | null;
  dosagePercentage?: string | null;
  totalDose?: string | null;
  adjustedDose?: string | null;
  quantity?: string | null;
  route?: string | null;
  infusionUsed?: string | null;
  note?: string | null;
  type?: string | null;
  combinationName?: string | null;
  chemoDiagnosis?: string | null;
  investigationDetails?: ChemoInvestigationDetailsInput[] | null;
  combinationGroupName?: string | null;
  cycleNumber: number;
  id?: string | null;
  section: string;
  frequency?: string | null;
  inventoryClass?: string | null;
  administrationRegister?: OncologyDrugAdministrationRegistrationInput[] | null;
}

export interface OncologyConsultationInput {
  id?: string | null;
  clinifyId?: string | null;
  consultationDateTime?: any | null;
  duration?: string | null;
  priority?: string | null;
  category?: string | null;
  doctorName?: string | null;
  specialty?: string | null;
  rank?: string | null;
  department?: string | null;
  consultationStartDate?: any | null;
  consultationEndDate?: any | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  initialDiagnosisICD10?: string | null;
  initialDiagnosisICD11?: string | null;
  initialDiagnosisSNOMED?: string | null;
  finalDiagnosisICD10?: string | null;
  finalDiagnosisICD11?: string | null;
  finalDiagnosisSNOMED?: string | null;
  diagnosedBy?: string | null;
  diagnosisDateTime?: any | null;
  additionalNote?: string | null;
  stageDiagnosisICD10?: string | null;
  stageDiagnosisICD11?: string | null;
  stageDiagnosisSNOMED?: string | null;
  stageDiagnosisDateTime?: any | null;
  classification?: string | null;
  stage?: string | null;
  stageDate?: any | null;
  histopathologicType?: string | null;
  stageTiming?: string | null;
  primaryTumor?: string | null;
  residualTumor?: string | null;
  tumorDetails?: TumorDetailsInput[] | null;
  lymphovascularInvasion?: string | null;
  regionalLymphNodes?: string | null;
  numberOfNodes?: string | null;
  distantMetastasis?: string | null;
  grade?: string | null;
  stageStatus?: string | null;
  cancerType?: string | null;
  progression?: string | null;
  relapse?: string | null;
  remission?: string | null;
  stageTreatmentType?: string | null;
  stageAdditionalNote?: string | null;
  nuclearGradeOrPleomorphism?: string | null;
  mitoticCountScore?: string | null;
  tubuleFormation?: string | null;
  stageGroupingScore?: string | null;
  scarffBloomRichardsonScore?: string | null;
  nottinghamModificationSbrGrade?: string | null;
  stageLymphovascularInvasion?: string | null;
  stageHistopathologicType?: string | null;
  diagnosticInformation?: string | null;
  typeOfSpecimen?: string | null;
  stagingRole?: string | null;
  stagingDate?: any | null;
  treatmentType?: string | null;
  treatmentSite?: string | null;
  intentOfTreatment?: string | null;
  lineOfTreatment?: string | null;
  concurrentTreatment?: string | null;
  treatmentPlanProvider?: string | null;
  treatmentDepartment?: string | null;
  treatmentStatus?: string | null;
  treatmentPriority?: string | null;
  treatmentInterval?: string | null;
  treatmentStartDate?: any | null;
  treatmentEndDate?: any | null;
  treatmentCycleDays?: string | null;
  treatmentCycleNumber?: string | null;
  treatmentPatientType?: string | null;
  treatmentAdverseReaction?: string | null;
  treatmentSpecificReaction?: string | null;
  treatmentOutcome?: string | null;
  treatmentResponse?: string | null;
  treatmentFollowupDate?: any | null;
  treatmentAdditionalNote?: string | null;
  therapyType?: string | null;
  therapySite?: string | null;
  intentOfTherapy?: string | null;
  lineOfTherapy?: string | null;
  concurrentTherapy?: string | null;
  therapyPlanProvider?: string | null;
  therapyDepartment?: string | null;
  therapyStatus?: string | null;
  therapyPriority?: string | null;
  therapyInterval?: string | null;
  therapyStartDate?: any | null;
  therapyEndDate?: any | null;
  therapyCycleDays?: string | null;
  therapyCycleNumber?: string | null;
  therapyPatientType?: string | null;
  therapyAdverseReaction?: string | null;
  therapySpecificReaction?: string | null;
  therapyOutcome?: string | null;
  therapyResponse?: string | null;
  therapyFollowupDate?: any | null;
  therapyAdditionalNote?: string | null;
  nottinghamGradeAbove?: string | null;
  estrogenReceptorExpression?: string | null;
  erPercentagePositive?: string | null;
  erAllredScore?: string | null;
  progesteroneReceptor?: string | null;
  prPercentagePositive?: string | null;
  prAllredScore?: string | null;
  overallHer2Status?: string | null;
  ihcScore?: string | null;
  fishResult?: string | null;
  fishCopyNumber?: string | null;
  her2OrCep17Ratio?: string | null;
  circulatingTumorCells?: string | null;
  complaintGender?: string | null;
  complaintSmartText?: string | null;
  complaintSmartSelection?: SelectionInput | null;
  systemReviewSmartText?: string | null;
  systemReviewSmartSelection?: SelectionInput | null;
  physicalExamSmartText?: string | null;
  physicalExamSmartSelection?: SelectionInput | null;
  concealComplaint?: boolean | null;
  complaint?: string | null;
  concealComplaintHistory?: boolean | null;
  complaintHistory?: string | null;
  concealSystemReview?: boolean | null;
  systemReview?: string | null;
  concealPhysicalExam?: boolean | null;
  physicalExam?: string | null;
  concealAudiometry?: boolean | null;
  audiometry?: string | null;
  concealHealthEducation?: boolean | null;
  healthEducation?: string | null;
  treatmentPlan?: string | null;
  concealTreatmentPlan?: boolean | null;
  treatmentGiven?: string | null;
  adverseEffectsFollowingTreatment?: string | null;
  stateEffects?: string | null;
  adverseEffectsInvestigated?: string | null;
  outcomeOfInvestigation?: string | null;
  patientAdmitted?: string | null;
  admissionConsent?: string | null;
  patientConsentSignature?: string | null;
  patientConsentSignatureType?: string | null;
  patientConsentSignatureDateTime?: string | null;
  observationNote?: string | null;
  concealObservationNote?: boolean | null;
  nextAppointmentDateTime?: any | null;
  nextAppointmentEndDateTime?: any | null;
  nextAppointmentDuration?: string | null;
  nextAppointmentSpecialty?: string | null;
  nextAppointmentRole?: string | null;
  nextAppointmentCategory?: string | null;
  nextAppointmentSpecialistId?: string | null;
  nextAppointmentSpecialistName?: string | null;
  nextAppointmentUrgency?: string | null;
  nextAppointmentDeliveryMethod?: string | null;
  documentUrl?: string[] | null;
  provider?: string | null;
  providerServiceName?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  hmoProviderId?: string | null;
  oncologyChemoDrugs?: OncologyChemoDrugInput[] | null;
  saveAsTemplate?: string[] | null;
  preChemoTemplateName?: string | null;
  chemoTemplateName?: string | null;
  postChemoTemplateName?: string | null;
  concealChemoNote?: boolean | null;
  chemoNote?: string | null;
  laboratoryTestVerified?: string | null;
  radiologyExaminationVerified?: string | null;
  admissions?: string[] | null;
  allergies?: string[] | null;
  labTests?: string[] | null;
  investigations?: string[] | null;
  medications?: string[] | null;
  radiology?: string[] | null;
  surgeries?: string[] | null;
  vitals?: string[] | null;
  nursingServices?: string[] | null;
}

export interface OncologyCycleInput {
  cycleNo: number;
  value?: any | null;
}

export interface OncologyDrugAdministrationRegistrationInput {
  period: string;
  administeredBy?: string | null;
  administratorId?: string | null;
  administrationDateTime?: any | null;
}

export interface OncologyHistoryInput {
  id?: string | null;
  clinifyId: string;
  initialDiagnosisICD10?: string | null;
  initialDiagnosisICD11?: string | null;
  initialDiagnosisSNOMED?: string | null;
  finalDiagnosisICD10?: string | null;
  finalDiagnosisICD11?: string | null;
  finalDiagnosisSNOMED?: string | null;
  diagnosedBy?: string | null;
  diagnosisDateTime?: any | null;
  additionalNote?: string | null;
  stageDiagnosisICD10?: string | null;
  stageDiagnosisICD11?: string | null;
  stageDiagnosisSNOMED?: string | null;
  stageDiagnosisDateTime?: any | null;
  classification?: string | null;
  stage?: string | null;
  stageDate?: any | null;
  histopathologicType?: string | null;
  stageTiming?: string | null;
  primaryTumor?: string | null;
  residualTumor?: string | null;
  tumorDetails?: TumorDetailsInput[] | null;
  lymphovascularInvasion?: string | null;
  regionalLymphNodes?: string | null;
  numberOfNodes?: string | null;
  distantMetastasis?: string | null;
  grade?: string | null;
  stageStatus?: string | null;
  cancerType?: string | null;
  progression?: string | null;
  relapse?: string | null;
  remission?: string | null;
  stageTreatmentType?: string | null;
  stageAdditionalNote?: string | null;
  treatmentType?: string | null;
  treatmentSite?: string | null;
  intentOfTreatment?: string | null;
  lineOfTreatment?: string | null;
  concurrentTreatment?: string | null;
  treatmentPlanProvider?: string | null;
  treatmentDepartment?: string | null;
  treatmentStatus?: string | null;
  treatmentPriority?: string | null;
  treatmentInterval?: string | null;
  treatmentStartDate?: any | null;
  treatmentEndDate?: any | null;
  treatmentCycleDays?: string | null;
  treatmentCycleNumber?: string | null;
  treatmentPatientType?: string | null;
  treatmentAdverseReaction?: string | null;
  treatmentSpecificReaction?: string | null;
  treatmentOutcome?: string | null;
  treatmentResponse?: string | null;
  treatmentFollowupDate?: any | null;
  treatmentAdditionalNote?: string | null;
  therapyType?: string | null;
  therapySite?: string | null;
  intentOfTherapy?: string | null;
  lineOfTherapy?: string | null;
  concurrentTherapy?: string | null;
  therapyPlanProvider?: string | null;
  therapyDepartment?: string | null;
  therapyStatus?: string | null;
  therapyPriority?: string | null;
  therapyInterval?: string | null;
  therapyStartDate?: any | null;
  therapyEndDate?: any | null;
  therapyCycleDays?: string | null;
  therapyCycleNumber?: string | null;
  therapyPatientType?: string | null;
  therapyAdverseReaction?: string | null;
  therapySpecificReaction?: string | null;
  therapyOutcome?: string | null;
  therapyResponse?: string | null;
  therapyFollowupDate?: any | null;
  therapyAdditionalNote?: string | null;
}

export interface OncologyPeriodInput {
  day: number;
  cycles: OncologyCycleInput[];
}

export interface OncologyRegisterChart {
  noOfCycles: number;
  periods: OncologyPeriodInput[];
}

export interface OncologyTreatmentPlanInput {
  id?: string | null;
  treatmentPlan?: string | null;
  conceal?: boolean | null;
  patientAdmitted?: string | null;
  observationNote?: string | null;
  concealObservationNote?: boolean | null;
  admissionConsent?: string | null;
  adverseEffectsFollowingTreatment?: string | null;
  stateEffects?: string | null;
  adverseEffectsInvestigated?: string | null;
  outcomeOfInvestigation?: string | null;
  treatmentGiven?: string | null;
  patientConsentSignature?: string | null;
  patientConsentSignatureType?: string | null;
  patientConsentSignatureDateTime?: string | null;
  treatmentStatus?: string | null;
}

export interface OnlineStatusInput {
  id: string;
  hospitalId: string;
  status: boolean;
}

export interface OperationNoteInput {
  id?: string | null;
  operationNote?: string | null;
  postOperationNote?: string | null;
  conceal?: boolean | null;
}

export interface OperationNoteTemplateInput {
  name: string;
  facilityPreferenceId: string;
  note: string;
  postNote: string;
  id: string;
}

export interface OrderDetail {
  checked?: boolean | null;
  orderName?: string | null;
  date?: any | null;
  checkedBy?: string | null;
  title?: string | null;
  names?: string[] | null;
  isMultiple?: boolean | null;
}

export interface OrganisationAppointmentFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  status?: OrganisationApointmentStatus | null;
  providerInsight?: boolean | null;
  hospitalId?: string | null;
  serviceType?: string | null;
  profileId?: string | null;
}

export interface OrganizationPermissionInput {
  subject: Subject;
  action: OrganizationAction;
}

export interface OutputDetailsInput {
  id?: string | null;
  administrationDateTime?: any | null;
  administratorName?: string | null;
  outputFluidType?: string | null;
  outputQuantity?: string | null;
  outputQuantityUnit?: string | null;
  observations?: string | null;
  concealObservations?: boolean | null;
}

export interface OxygenLevelData {
  readingDate?: any | null;
  time?: string | null;
  ecgRythm?: string | null;
  saturated?: string | null;
  airFGF?: string | null;
  endTidal?: string | null;
  oxygenFi?: string | null;
  agentFe?: string | null;
  airwayPressure?: string | null;
  airwayPressureUnit?: string | null;
}

export interface OxygenTherapyDetail {
  saturation?: string | null;
  administrationDateTime?: any | null;
  administeredBy?: string | null;
  administrationHour?: string | null;
  therapyType?: string | null;
}

export interface OxygenTherapyInput {
  details?: OxygenTherapyDetail[] | null;
}

export interface Oxytocin {
  readingDate?: any | null;
  value?: string | null;
  drops?: string | null;
  time?: string | null;
}

export interface PackageFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  gender?: string | null;
  genderWithAll?: boolean | null;
}

export interface PackageInput {
  id?: string | null;
  clinifyId: string;
  packageDate: any;
  name: string;
  price?: string | null;
  gender?: string | null;
  serviceDetails?: PackageServiceInput[] | null;
}

export interface PackageServiceInput {
  serviceType?: string | null;
  serviceName?: string | null;
  extraInformation?: string | null;
}

export interface PainVitalFields {
  id?: string | null;
  score?: string | null;
  isScoreCritical?: boolean | null;
  type?: string | null;
  dateTimePainStarted?: any | null;
  location?: string[] | null;
  specifyLocation?: string | null;
  orientation?: string[] | null;
  specifyOrientation?: string | null;
  radiatingTowards?: string | null;
  descriptors?: string[] | null;
  specifyDescriptors?: string | null;
  frequency?: string[] | null;
  specifyFrequency?: string | null;
  onset?: string[] | null;
  specifyOnset?: string | null;
  clinicalProgression?: string[] | null;
  specifyClinicalProgression?: string | null;
  aggravatingFactors?: string[] | null;
  specifyAggravatingFactors?: string | null;
  painCausedAsResultOfInjury?: string | null;
  workRelatedInjury?: string | null;
  painGoal?: string | null;
  interventions?: string[] | null;
  specifyInterventions?: string | null;
  responsetoInterventions?: string | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
}

export interface PartographInput {
  id?: string | null;
  clinifyId?: string | null;
  fhr?: FhrDetail[] | null;
  amnoiticFluid?: AmnoiticFluid[] | null;
  cervixDecent?: CervixDecent[] | null;
  contraction?: Contraction[] | null;
  oxytocin?: Oxytocin[] | null;
  drugIv?: DrugIv[] | null;
  pulseBP?: PulseBP[] | null;
  temperature?: Temperature[] | null;
  urine?: Urine[] | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  notes?: string | null;
}

export interface PastEncountersFieldInput {
  id?: string | null;
  diagnosisDate?: any | null;
  duration?: string | null;
  diagnosisICD10?: string | null;
  diagnosisICD11?: string | null;
  diagnosisSNOMED?: string | null;
  diagnosedBy?: string | null;
  specialty?: string | null;
  symptoms?: string[] | null;
}

export interface PastEncountersInput {
  id?: string | null;
  clinifyId: string;
  details?: PastEncountersFieldInput[] | null;
  clinicName?: string | null;
  clinicAddress?: string | null;
  additionalNote?: string | null;
}

export interface PastSurgeryInput {
  id?: string | null;
  clinifyId: string;
  type: string;
  operationDate?: any | null;
  additionalNote?: string | null;
}

export interface PatientCareSpecialistInput {
  specialistId?: string | null;
  specialty?: string | null;
  specialistFullName?: string | null;
  specialistTitle?: string | null;
  specialistRole?: string | null;
  rank?: string | null;
}

export interface PatientCareTeamInput {
  profileId: string;
  team?: (PatientCareSpecialistInput | null)[] | null;
}

export interface PatientInformationInput {
  fullName: string;
  email?: string | null;
  phone?: string | null;
  clinifyId?: string | null;
}

export interface PatientRegistrationInput {
  personalInformation: RegisterPersonalInformation;
  backgroundInformation?: BackgroundInformationInput | null;
  dependents?: DependentInput[] | null;
  nextOfKins?: NextOfKinInput[] | null;
  registeredWithId?: string | null;
  coverageInformation?: CoverageDetailsInput[] | null;
  email?: string | null;
  phoneNumber?: string | null;
  patientProfileType?: PatientProfileType | null;
  country: string;
  createdFromHmo?: boolean | null;
  partnerCode?: string | null;
  draftId?: string | null;
}

export interface PatientVisitationHistoryFilter {
  date: string;
  patientId: string;
}

export interface PayBillInput {
  id: string;
  amount: number;
  passCode: string;
}

export interface PaymentDepositFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  showRefunds?: boolean | null;
}

export interface PaymentDepositInput {
  depositDate: any;
  currency?: string | null;
  amountDeposited?: number | null;
  amountUsed?: number | null;
  profileId: string;
  collectedById: string;
  withdrawnById?: string | null;
  depositMethod: string;
  description?: string | null;
  additionalNote?: string | null;
}

export interface PaymentDepositRefundInput {
  refundDate: any;
  currency?: string | null;
  refundedBy?: string | null;
  amountRefunded?: number | null;
  description?: string | null;
  additionalNote?: string | null;
  profileId: string;
}

export interface PayoutFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  status?: PayoutStatus | null;
}

export interface PerPlanPayoutDecreasePercentage {
  hmoPlanTypeId: string;
  payoutDecreasePercentage?: number | null;
}

export interface Periods {
  no?: number | null;
  count?: number | null;
  values?: string | null;
  audits?: DispenseAudit[] | null;
}

export interface PersonalInformationInput {
  id?: string | null;
  displayPictureUrl?: string | null;
  title?: string | null;
  gender?: Gender | null;
  firstName?: string | null;
  middleName?: string | null;
  lastName?: string | null;
  secondaryEmail?: string | null;
  secondaryPhoneNumber?: PhoneNumberInput | null;
  dateOfBirth?: any | null;
  consultationFee?: number | null;
  consultationLength?: number | null;
  bloodGroup?: string | null;
  genoType?: string | null;
  weight?: string | null;
  weightUnit?: string | null;
  height?: string | null;
  heightUnit?: string | null;
  address?: string | null;
  bio?: string | null;
  registrationNumber?: string | null;
  registrationDate?: any | null;
  folioNumber?: string | null;
  folioNumberIssueDate?: string | null;
  folioNumberExpiryDate?: string | null;
  folioNumberIssuer?: string | null;
  department?: string | null;
  rank?: string | null;
  expiryDate?: any | null;
  speciality?: string | null;
  patientFileOrCardNo?: string | null;
  yearsOfPractice?: number | null;
  serviceDetails?: ServiceDetailInput[] | null;
  hospital?: string | null;
  fileNumbers?: FileNumber[] | null;
  clinicalTrials?: string | null;
  coverageDetails?: CoverageDetailsInput[] | null;
  coverageIdsToDelete?: string[] | null;
  shareData?: boolean | null;
  documentUrl?: string[] | null;
  dataAccessType?: string | null;
  patientStatus?: string | null;
  deathDateTime?: any | null;
  deathLocation?: string | null;
  causeOfDeath?: string | null;
  unavailableDates?: string[] | null;
  registrationNote?: string | null;
  nin?: string | null;
  votersId?: string | null;
  passportNumber?: string | null;
  bvn?: string | null;
  lga?: string | null;
  ward?: string | null;
  buildingName?: string | null;
  buildingLevel?: string | null;
  countryOfResidence?: string | null;
  stateOfResidence?: string | null;
  patientCategory?: string[] | null;
  nationality?: string | null;
  state?: string | null;
  originLga?: string | null;
  city?: string | null;
  placeOfBirth?: string | null;
  userRole?: string | null;
}

export interface PhoneNumberInput {
  countryCode?: string | null;
  value?: string | null;
  countryName?: string | null;
}

export interface PhysicalActivityInput {
  id?: string | null;
  clinifyId: string;
  type?: string | null;
  name?: string | null;
  additionalNote?: string | null;
}

export interface PlanBenefit {
  visitType: string;
  code?: string | null;
  utilisationCategory: string;
  benefitLimit?: string | null;
  visitLimit?: string | null;
  annualLimitPerPerson?: string | null;
  waitingPeriodDays?: number | null;
  utilisationTypes: HmoUtilisationInput[];
}

export interface PlanVisibilityInput {
  id: string;
  name: string;
}

export interface PostOperationChecklistInput {
  id?: string | null;
  clinifyId?: string | null;
  serviceStartDateTime?: any | null;
  chartType?: string | null;
  surgeryStartDateTime?: any | null;
  surgeryEndDateTime?: any | null;
  dietOrderSheet?: OrderDetail[] | null;
  ambutationOrderSheet?: OrderDetail[] | null;
  urethralCatheterizationOrderSheet?: OrderDetail[] | null;
  fluidTherapyOrderSheet?: OrderDetail[] | null;
  antiBioticsOrderSheet?: OrderDetail[] | null;
  analgesicOrderSheet?: OrderDetail[] | null;
  vitalSigns?: VitalSign[] | null;
  medications?: MedicationDetail[] | null;
  discontinueMedication?: boolean | null;
  urineOutput?: UrineOutputDetail[] | null;
  linesDrainsTubes?: LinesDrainsTubesDetail[] | null;
  newOrders?: OrderDetail[] | null;
  SurgeonName?: string | null;
  SurgeonSpecialty?: string | null;
  SurgeonAssistantName?: string | null;
  operatingRoomNurse?: string | null;
  anesthetistName?: string | null;
  recoveryNurse?: string | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
  surgeonNameSignature?: string | null;
  surgeonNameSignatureType?: string | null;
  surgeonNameSignatureDateTime?: any | null;
  surgeonAssistantNameSignature?: string | null;
  surgeonAssistantNameSignatureType?: string | null;
  surgeonAssistantNameSignatureDateTime?: any | null;
  operatingRoomNurseSignature?: string | null;
  operatingRoomNurseSignatureType?: string | null;
  operatingRoomNurseSignatureDateTime?: any | null;
  anesthetistNameSignature?: string | null;
  anesthetistNameSignatureType?: string | null;
  anesthetistNameSignatureDateTime?: any | null;
  recoveryNurseSignature?: string | null;
  recoveryNurseSignatureType?: string | null;
  recoveryNurseSignatureDateTime?: any | null;
  visitingFacilityName?: string | null;
  visitingSpecialistName?: string | null;
  visitingSpecialistSignature?: string | null;
  visitingSpecialistSignatureType?: string | null;
  visitingSpecialistSignatureDateTime?: any | null;
  clexane40mgFor3DaysOrderSheet?: OrderDetail[] | null;
}

export interface PostnatalFilterOptions {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
}

export interface PostnatalInput {
  id: string;
  clinifyId: string;
  provider?: string | null;
  visitationDateTime?: any | null;
  seenBy?: string | null;
  deliveryDate?: any | null;
  hasMenstruationStarted?: string | null;
  menstruationStartDate?: any | null;
  uterus?: string | null;
  breastFeeding?: string | null;
  familyPlanning?: string | null;
  contraceptivePillCount?: string | null;
  typeOfImplant?: string | null;
  nameOfInjectable?: string | null;
  firstTimeFpUser?: string | null;
  sourceOfReferral?: string | null;
  familyPlanningClientType?: string | null;
  familyPlanningMethod?: string | null;
  vulva?: string | null;
  vitaminAGivenToBaby?: string | null;
  vitaminADoseGivenToBaby?: string | null;
  vitaminADoseUnitGivenToBaby?: string | null;
  babyComplaint?: string | null;
  stateBabyComplaint?: string | null;
  clinicalDiagnosis?: DiagnosisInput[] | null;
  visitationNote?: string | null;
  concealVisitationNote?: boolean | null;
  rank?: string | null;
  department?: string | null;
  healthEducation?: string | null;
  concealHealthEducation?: boolean | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
  treatmentPlan?: string | null;
  concealTreatmentPlan?: boolean | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  documentUrl?: string[] | null;
  serviceDetails?: ServiceDetailInput[] | null;
  nextAppointmentDateTime?: any | null;
  nextAppointmentEndDateTime?: any | null;
  nextAppointmentDuration?: string | null;
  nextAppointmentSpecialty?: string | null;
  nextAppointmentRole?: string | null;
  nextAppointmentCategory?: string | null;
  nextAppointmentSpecialistId?: string | null;
  nextAppointmentSpecialistName?: string | null;
  nextAppointmentUrgency?: string | null;
  nextAppointmentDeliveryMethod?: string | null;
  postpartumDepression?: string | null;
  hmoProviderId?: string | null;
}

export interface PreChemoEducationInput {
  id?: string | null;
  clinifyId?: string | null;
  preChemoChecklist?: string[] | null;
  treatmentPlan?: string | null;
  parentGuardianName?: string | null;
  drugInformationProvided?: string | null;
  diseaseOrDiagnosis?: string | null;
  dataLibrary?: string | null;
  handOutsProvided?: string | null;
  steroidEducation?: string | null;
  ivAccessEvaluated?: string | null;
  fertilityIssues?: string | null;
  spiritualCareService?: string | null;
  spiritualGroup?: string | null;
  financialIssues?: string | null;
  comfortItems?: string | null;
  herbalInteraction?: string | null;
  appointmentFlow?: string | null;
  clinicalProcessDiscussed?: string | null;
  alcoholBasedHandRub?: string | null;
  anaemia?: string | null;
  diarrhea?: string | null;
  hairLossSyndrome?: string | null;
  mouthSores?: string | null;
  neuropathy?: string | null;
  thrombocytopenia?: string | null;
  handAndFoot?: string | null;
  nauseaAndVomitting?: string | null;
  neutropenia?: string | null;
  nadir?: string | null;
  fatigue?: string | null;
  doesPatientWork?: string | null;
  patientWithLargeGroups?: string | null;
  cancerDiagnosis?: string | null;
  pastPresentProcedure?: string | null;
  radiationTherapy?: string | null;
  hormonalAgent?: string | null;
  allergies?: string | null;
  pastMedicalHistory?: string | null;
  medicationReview?: string | null;
  symptomsReview?: string | null;
  distress?: string | null;
  performanceStatus?: string | null;
  heightAndWeight?: string | null;
  labValues?: string | null;
  tumourInfo?: string | null;
  baselineVitalSigns?: string | null;
  otherDeformities?: string | null;
  gestationalAge?: string | null;
  treatmentPlanOrProtocol?: string | null;
  participatingInClinicalTrials?: string | null;
  painScore?: string | null;
  informedConsentForChemo?: string | null;
  consentGiven?: string | null;
  patientConsentSignature?: string | null;
  patientConsentSignatureType?: string | null;
  patientConsentSignatureDateTime?: any | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
  documentUrl?: string[] | null;
}

export interface PreExistingConditionInput {
  id?: string | null;
  clinifyId: string;
  conditionICD10?: string | null;
  conditionICD11?: string | null;
  conditionSNOMED?: string | null;
  ageOfOnset?: string | null;
  diagnosedDate?: any | null;
  duration?: string | null;
  additionalNote?: string | null;
}

export interface PreOperationChecklistInput {
  id?: string | null;
  clinifyId?: string | null;
  operationDate?: any | null;
  preSurgeryReadiness?: boolean | null;
  completeConsent?: string | null;
  bloodProductConent?: string | null;
  alternateForm?: string | null;
  historyAvailable?: string | null;
  allergiesMarked?: string | null;
  bpmhComplete?: string | null;
  vitalsComplete?: string | null;
  heightWeightDoc?: string | null;
  anaestheticHistory?: string | null;
  completeConsult?: string | null;
  aroDocument?: string | null;
  interpreterNeeded?: string | null;
  fallsRisk?: string | null;
  bariatricRequired?: string | null;
  vteProphylaxis?: string | null;
  presurgicalScrub?: string | null;
  patientPreparation?: boolean | null;
  idBand?: string | null;
  facilityDetails?: string | null;
  allergyBand?: string | null;
  tsinBand?: string | null;
  preprocedureCollected?: string | null;
  implants?: string | null;
  belongingsDocumented?: string | null;
  cbap?: string | null;
  verifiedSurgicalSite?: string | null;
  nutritionStatusDoc?: string | null;
  preoperationMedications?: string | null;
  orStaffReviewed?: string | null;
  surgicalSafetyChecklist?: boolean | null;
  patientConfirmation?: string | null;
  isSiteMarked?: string | null;
  anaesthesiaMedicationCheck?: string | null;
  pulseOximeterFunctioning?: string | null;
  knownAllergy?: string | null;
  difficultAirway?: string | null;
  riskOfBloodLoss?: string | null;
  teamMembersIntroduced?: string | null;
  pantientNameAndProcedure?: string | null;
  antibioticBeenGiven?: string | null;
  criticalSteps?: string | null;
  casePeriod?: string | null;
  anticipatedBloodLoss?: string | null;
  patientSpecificConcern?: string | null;
  specifyPatientConcern?: string | null;
  sterilityConfirmed?: string | null;
  equipmentIssues?: string | null;
  specifyEquipmentIssues?: string | null;
  essentialImagingDisplayed?: string | null;
  confirmedProcedureName?: string | null;
  confirmSpecimensLabeled?: string | null;
  confirmInstrumentCount?: string | null;
  equipmentProblemsToAddress?: string | null;
  recoveryKeyConcerns?: string | null;
  additionalNote?: string | null;
  medicationProfileAvailable?: string | null;
  identifiedPatient?: string | null;
  patientCaseFileAvailable?: string | null;
  assessedDvtRisk?: string | null;
  equipmentImplantAvailable?: string | null;
  verifiedAvailabilityOrderedBloodProduct?: string | null;
  urinaryOutputAmount?: string | null;
  urinaryOutputAmountUnit?: string | null;
  catheterDateTime?: string | null;
  denturePlateRemoved?: string | null;
  contactLensRemoved?: string | null;
  prosthesisRemoved?: string | null;
  preoperationMedicationDateTime?: string | null;
  bowelEmptied?: string | null;
  lastMealDateTime?: string | null;
  shavedSurgicalSite?: string | null;
  jewelryRemoved?: string | null;
  concealAdditionalNote?: boolean | null;
}

export interface PreauthUtilisationInput {
  id?: string | null;
  category?: string | null;
  type?: string | null;
  quantity?: string | null;
  price?: string | null;
  medicationCategory?: string | null;
  dosage?: string | null;
  dosageUnit?: string | null;
  frequency?: string | null;
  duration?: string | null;
  birthCount?: string | null;
  deliveryDateTime?: any | null;
  gestationalAge?: string | null;
  specialty?: string | null;
  utilizationId?: string | null;
  utilizationCode?: string | null;
  paCode?: string | null;
  status?: string | null;
  rejectionReason?: string[] | null;
  statusDescription?: string | null;
  specifyReasonForRejection?: string | null;
  utilisationStatus?: ClaimsApprovalInput[] | null;
  serviceName?: string | null;
  paymentModel?: string | null;
  rhesusFactor?: string | null;
}

export interface PreauthorisationInput {
  clinifyId: string;
  enrolleeId: string;
  requestDateTime: any;
  treatmentStartDate?: any | null;
  treatmentEndDate?: any | null;
  requestedBy: string;
  facilityName?: string | null;
  facilityAddress?: string | null;
  rank?: string | null;
  department?: string | null;
  specialty?: string | null;
  presentingComplain?: string | null;
  referredBy?: string | null;
  referralCode?: string | null;
  referredFrom?: string | null;
  referredTo?: string | null;
  providerId: string;
  serviceType: string;
  serviceTypeCode: string;
  serviceName?: string | null;
  priority?: string | null;
  diagnosis: DiagnosisInput[];
  utilizations: PreauthUtilisationInput[];
  patientType?: string | null;
  paymentType?: string | null;
  additionalNote?: string | null;
  documentUrl?: string[] | null;
  visitId?: string | null;
  visitDate?: any | null;
  memberUniqueId?: string | null;
  staffEmail?: string | null;
  facilityId?: string | null;
  enrolleePhoneNumber?: PhoneNumberInput | null;
  enrolleeEmail?: string | null;
  isExternalPlanType?: boolean | null;
  externalPlanTypeId?: string | null;
}

export interface PreauthorisationReferralInput {
  clinifyId: string;
  enrolleeId: string;
  requestDateTime: any;
  requestedBy: string;
  referredBy?: string | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  rank?: string | null;
  department?: string | null;
  specialty?: string | null;
  presentingComplain?: string | null;
  providerId: string;
  serviceType: string;
  serviceTypeCode: string;
  serviceName?: string | null;
  referredProviderId: string;
  referredProviderName: string;
  referringProviderId: string;
  referringProviderName: string;
  referralProviderRemark?: string | null;
  priority?: string | null;
  diagnosis: DiagnosisInput[];
  utilizations: PreauthUtilisationInput[];
  patientType?: string | null;
  paymentType?: string | null;
  additionalNote?: string | null;
  referralRemarks?: string | null;
  documentUrl?: string[] | null;
  visitId?: string | null;
  memberUniqueId?: string | null;
  staffEmail?: string | null;
  isExternalPlanType?: boolean | null;
  externalPlanTypeId?: string | null;
}

export interface PreauthorisationReferralUpdateInput {
  clinifyId?: string | null;
  enrolleeId?: string | null;
  requestDateTime?: any | null;
  requestedBy?: string | null;
  referredBy?: string | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  rank?: string | null;
  department?: string | null;
  specialty?: string | null;
  presentingComplain?: string | null;
  providerId?: string | null;
  serviceType?: string | null;
  serviceTypeCode?: string | null;
  serviceName?: string | null;
  referredProviderId?: string | null;
  referredProviderName?: string | null;
  referringProviderId?: string | null;
  referringProviderName?: string | null;
  referralProviderRemark?: string | null;
  priority?: string | null;
  diagnosis?: DiagnosisInput[] | null;
  utilizations?: PreauthUtilisationInput[] | null;
  patientType?: string | null;
  paymentType?: string | null;
  additionalNote?: string | null;
  referralRemarks?: string | null;
  documentUrl?: string[] | null;
  visitId?: string | null;
  memberUniqueId?: string | null;
  staffEmail?: string | null;
  isExternalPlanType?: boolean | null;
  externalPlanTypeId?: string | null;
  id?: string | null;
}

export interface PreauthorisationUpdateInput {
  clinifyId: string;
  enrolleeId: string;
  requestDateTime: any;
  treatmentStartDate?: any | null;
  treatmentEndDate?: any | null;
  requestedBy: string;
  facilityName?: string | null;
  facilityAddress?: string | null;
  rank?: string | null;
  department?: string | null;
  specialty?: string | null;
  presentingComplain?: string | null;
  referredBy?: string | null;
  referralCode?: string | null;
  referredFrom?: string | null;
  referredTo?: string | null;
  providerId: string;
  serviceType: string;
  serviceTypeCode: string;
  serviceName?: string | null;
  priority?: string | null;
  diagnosis: DiagnosisInput[];
  utilizations: PreauthUtilisationInput[];
  patientType?: string | null;
  paymentType?: string | null;
  additionalNote?: string | null;
  documentUrl?: string[] | null;
  visitId?: string | null;
  visitDate?: any | null;
  memberUniqueId?: string | null;
  staffEmail?: string | null;
  facilityId?: string | null;
  enrolleePhoneNumber?: PhoneNumberInput | null;
  enrolleeEmail?: string | null;
  isExternalPlanType?: boolean | null;
  externalPlanTypeId?: string | null;
  id?: string | null;
}

export interface PreauthorizationFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  status?: string | null;
  hmo?: string | null;
  hospitalId?: string | null;
  profileId?: string | null;
  providerInsight?: boolean | null;
  providerType?: string | null;
  filterDateField?: FilterDateType | null;
  showCompleted?: boolean | null;
  showNotCompleted?: boolean | null;
  showSubmitted?: boolean | null;
  showNotSubmitted?: boolean | null;
  timeSortOrder?: TimeSortOrder | null;
}

export interface PreauthorizationReferralFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  status?: string | null;
  hmo?: string | null;
  hospitalId?: string | null;
  providerInsight?: boolean | null;
  providerType?: string | null;
  filterDateField?: PreauthReferralDateFilterType | null;
  showCompleted?: boolean | null;
  showNotCompleted?: boolean | null;
  timeSortOrder?: TimeSortOrder | null;
  profileId?: string | null;
}

export interface PremiumDetailsInput {
  category?: string | null;
  frequency?: string | null;
  amount?: number | null;
  administrationAgency?: string | null;
  providerLedCommissionRate?: number | null;
  individualCommissionRate?: number | null;
  commissionRate?: number | null;
  referralCommissionRate?: number | null;
  enrollmentCommissionRate?: EnrollmentCommissionRateDetailsInput[] | null;
}

export interface PricesFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  serviceName?: string | null;
  serviceType?: string | null;
  providerId?: string | null;
  sort?: SortOrder | null;
}

export interface PricesServiceNameFilterInput {
  keyword?: string | null;
  serviceType?: string | null;
  providerId?: string | null;
}

export interface PrivateDoctorsFilterInput {
  skip?: number | null;
  take?: number | null;
  enrolleeId?: string | null;
  referral?: boolean | null;
  rating?: number | null;
  consultationFee?: number | null;
  availiability?: DateRangeInput | null;
  specialty?: string | null;
}

export interface ProcedureTypeInput {
  ref?: string | null;
  type: string;
  priority?: string | null;
  provider?: string | null;
  itemId?: string | null;
  preauthorizationDetailsId?: string | null;
}

export interface ProfileDetailsInput {
  clinifyId: string;
  corporatePhoneNumber?: string | null;
  country?: string | null;
  email?: string | null;
  personalInformation?: PersonalInformationInput | null;
  backgroundInformation?: BackgroundInformationInput | null;
}

export interface ProfileInfosFilterInput {
  skip?: number | null;
  take?: number | null;
  enrolleeId?: string | null;
  referral?: boolean | null;
}

export interface ProfileUnavailableDatesInput {
  startDate: any;
  endDate?: any | null;
  isAllDay: boolean;
  reason?: string | null;
}

export interface ProfilesFilterInputs {
  skip?: number | null;
  take?: number | null;
  enrolleeId?: string | null;
  referral?: boolean | null;
  clinifyId?: string | null;
  specialty?: string | null;
  fullName?: string | null;
  registeredWith?: string | null;
  fetchHospitalStaff?: boolean | null;
  usersRegisteredUnder?: LookupPatientType | null;
  profileType?: UserListType | null;
  phoneNumber?: string | null;
  email?: string | null;
  archive?: boolean | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  fileNumber?: string | null;
  onLookup?: boolean | null;
  partnerCode?: string | null;
  status?: string | null;
  ids?: string[] | null;
  notInIds?: string[] | null;
  coverageType?: string | null;
  coverageName?: string | null;
  hmoProviderId?: string | null;
  skipPagination?: boolean | null;
  enrolleeStatus?: string | null;
  planId?: string | null;
  facilityId?: string | null;
  enrollmentAgentId?: string | null;
  enrollmentAgencyId?: string | null;
  employerId?: string | null;
  employerRegistered?: boolean | null;
  staffProfileType?: OrganisationProfileType | null;
  verificationCode?: string | null;
}

export interface ProviderRegistrationInput {
  country: string;
  hospitalName: string;
  hospitalAddress?: string | null;
  hospitalWebsite?: string | null;
  hospitalContactTitle?: string | null;
  hospitalContactFirstName: string;
  hospitalContactMiddleName?: string | null;
  hospitalContactLastName: string;
  hospitalContactEmail: string;
  hospitalContactPhoneNumber: PhoneNumberInput;
  ownership?: string | null;
  state?: string | null;
  lga?: string | null;
  politicalWard?: string | null;
  city?: string | null;
  level?: string | null;
  hospitalLicenseNumber?: string | null;
  documentUrl?: string[] | null;
  plan: HospitalPlan;
  partnerCode?: string | null;
  wemaAccountNumber?: string | null;
  wemaAccountName?: string | null;
  providerCode: string;
  providerPrimaryPhoneNumber: PhoneNumberInput;
  providerSecondaryPhoneNumber?: PhoneNumberInput | null;
  providerId?: string | null;
  supportMail?: string | null;
  planStatus?: PlanStatus | null;
  tariffBand?: HMOTariffBand | null;
  providerCategory?: string | null;
  classification?: string | null;
  enrolleeCount?: string | null;
  enrolleeLimit?: string | null;
  planVisibility?: PlanVisibilityInput[] | null;
}

export interface PulseAndBp {
  readingDate?: any | null;
  time?: string | null;
  pulse?: string | null;
  bpSystolic?: string | null;
  bpDiastolic?: string | null;
}

export interface PulseBP {
  readingDate?: any | null;
  pulse?: string | null;
  bp?: string | null;
  time?: string | null;
  bpSystolic?: string | null;
  bpDiastolic?: string | null;
}

export interface PulseRateVitalFields {
  id?: string | null;
  readingDateTime?: any | null;
  checkMethod?: string | null;
  checkMethodSpecify?: string | null;
  reading?: string | null;
  isReadingCritical?: boolean | null;
  rhythm?: string | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
}

export interface QuestionnaireInput {
  maritalStatus?: string | null;
  gender?: string | null;
  highestFormalEducationalLevel?: string | null;
  employmentStatus?: string | null;
  occupationalGroup?: string | null;
  occupation?: string | null;
  numberOfHouseholdMembers?: string | null;
  numberOfRooms?: string | null;
  numberOfMattresses?: string | null;
  typeOfRoof?: string | null;
  typeOfToilet?: string | null;
  primaryCookingImplement?: string | null;
  numberOfMobilePhones?: string | null;
  numberOfTVs?: string | null;
  numberOfVehicles?: string | null;
  relationshipToHouseholdHead?: string | null;
  questionnaireScore?: number | null;
}

export interface RadiologyExamDetail {
  examinationDate?: any | null;
  duration?: string | null;
  patientType?: PatientType | null;
  radiographerName?: string | null;
  radiographerSignature?: string | null;
  radiographerSignatureType?: string | null;
  radiographerSignatureDateTime?: any | null;
  examinationNumber?: string | null;
  indication?: string | null;
  comparison?: string | null;
  technique?: string | null;
  examType?: string | null;
  paymentType?: string | null;
  serviceDetails?: ServiceDetailInput | null;
  radiographerReport?: string | null;
  impression?: string | null;
  done?: boolean | null;
  contrastConfirmed?: boolean | null;
  radiologistName?: string | null;
  radiologistSignature?: string | null;
  radiologistSignatureType?: string | null;
  radiologistSignatureDateTime?: any | null;
  verifiedBy?: string | null;
  verifiedBySignature?: string | null;
  verifiedBySignatureType?: string | null;
  verifiedBySignatureDateTime?: any | null;
}

export interface RadiologyResultInput {
  details: RadiologyExamDetail[];
  patientType?: PatientType | null;
  paymentType?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  documentUrl?: string[] | null;
  hmoProviderId?: string | null;
  hmoClaim?: NewHmoClaimInput | null;
}

export interface RegisterPersonalInformation {
  id?: string | null;
  displayPictureUrl?: string | null;
  title?: string | null;
  gender: Gender;
  firstName: string;
  middleName?: string | null;
  lastName: string;
  secondaryEmail?: string | null;
  secondaryPhoneNumber?: PhoneNumberInput | null;
  dateOfBirth: any;
  consultationFee?: number | null;
  consultationLength?: number | null;
  bloodGroup?: string | null;
  genoType?: string | null;
  weight?: string | null;
  weightUnit?: string | null;
  height?: string | null;
  heightUnit?: string | null;
  address?: string | null;
  bio?: string | null;
  registrationNumber?: string | null;
  registrationDate?: any | null;
  folioNumber?: string | null;
  folioNumberIssueDate?: string | null;
  folioNumberExpiryDate?: string | null;
  folioNumberIssuer?: string | null;
  department?: string | null;
  rank?: string | null;
  expiryDate?: any | null;
  speciality?: string | null;
  patientFileOrCardNo?: string | null;
  yearsOfPractice?: number | null;
  serviceDetails?: ServiceDetailInput[] | null;
  hospital?: string | null;
  fileNumbers?: FileNumber[] | null;
  clinicalTrials?: string | null;
  coverageDetails?: CoverageDetailsInput[] | null;
  coverageIdsToDelete?: string[] | null;
  shareData?: boolean | null;
  documentUrl?: string[] | null;
  dataAccessType?: string | null;
  patientStatus?: string | null;
  deathDateTime?: any | null;
  deathLocation?: string | null;
  causeOfDeath?: string | null;
  unavailableDates?: string[] | null;
  registrationNote?: string | null;
  nin?: string | null;
  votersId?: string | null;
  passportNumber?: string | null;
  bvn?: string | null;
  lga?: string | null;
  ward?: string | null;
  buildingName?: string | null;
  buildingLevel?: string | null;
  countryOfResidence?: string | null;
  stateOfResidence?: string | null;
  patientCategory?: string[] | null;
  nationality?: string | null;
  state?: string | null;
  originLga?: string | null;
  city?: string | null;
  placeOfBirth?: string | null;
  userRole?: string | null;
  provider?: string | null;
  providerServiceName?: string | null;
}

export interface RegisteredEnrolleesMetricsInput {
  dateRange?: DateRangeInput | null;
}

export interface RegistrationInput {
  phoneNumber: string;
}

export interface ReminderFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  tag?: ReminderTag | null;
  icon?: ReminderIcons | null;
  hospitalId?: string | null;
}

export interface ReportTypeInput {
  ref?: string | null;
  name: string;
  itemId?: string | null;
}

export interface RequestPackageFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
}

export interface RequestPackageInput {
  id?: string | null;
  clinifyId: string;
  requestDate: any;
  packageName: string;
  priority?: string | null;
  category?: string | null;
  serviceDetails?: PackageServiceInput[] | null;
  price: string;
  orderedBy?: string | null;
  specialty?: string | null;
  rank?: string | null;
  department?: string | null;
  patientType?: string | null;
  paymentType?: string | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  additionalNote?: string | null;
}

export interface RequestPayoutInput {
  requestPayoutDateTime: any;
  initiatedBy: string;
  receiverAccount: BankInformationInput;
  payoutDescription?: string | null;
  transactionStartDate: any;
  transactionEndDate: any;
  virtualServicesPaymentsIds: string[];
  invoicePaymentsIds: string[];
  requestAmount?: number | null;
  totalCommissionFeeAmount?: number | null;
  additionalNote?: string | null;
}

export interface RequestProcedureFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
}

export interface RequestProcedureInput {
  id?: string | null;
  clinifyId: string;
  surgeryDate?: any | null;
  duration?: string | null;
  provider?: string | null;
  procedureType: ProcedureTypeInput[];
  requestedBy?: string | null;
  operatedBy: string;
  specialty?: string | null;
  rank?: string | null;
  department?: string | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  patientConsent?: string | null;
  patientConsentSignature?: string | null;
  patientConsentSignatureType?: string | null;
  patientConsentSignatureDateTime?: any | null;
  serviceDetails?: ServiceDetailInput[] | null;
  reason?: string | null;
  surgeryStartDate?: any | null;
  surgeryEndDate?: any | null;
  nextAppointmentDateTime?: any | null;
  nextAppointmentEndDateTime?: any | null;
  nextAppointmentDuration?: string | null;
  nextAppointmentSpecialty?: string | null;
  nextAppointmentRole?: string | null;
  nextAppointmentCategory?: string | null;
  nextAppointmentSpecialistId?: string | null;
  nextAppointmentSpecialistName?: string | null;
  nextAppointmentUrgency?: string | null;
  nextAppointmentDeliveryMethod?: string | null;
  documentUrl?: string[] | null;
  hmoProviderId?: string | null;
  billInfo?: BillInfoUpdateInput[] | null;
  billRefsToDelete?: string[] | null;
}

export interface ResetPasscodeInput {
  oldPasscode: string;
  newPasscode: string;
}

export interface ResetPasswordInput {
  oldPassword: string;
  newPassword: string;
}

export interface RespiratoryRateVitalFields {
  id?: string | null;
  readingDateTime?: any | null;
  reading?: string | null;
  isReadingCritical?: boolean | null;
  oxygenSaturation?: string | null;
  isOxygenSaturationCritical?: boolean | null;
  spO2Site?: string | null;
  O2FlowRate?: string | null;
  isO2FlowRateCritical?: boolean | null;
  fIO2?: string | null;
  isFIO2Critical?: boolean | null;
  O2Therapy?: string | null;
  etco2?: string | null;
  isEtco2Critical?: boolean | null;
  etco2Unit?: string | null;
  cardiacRythm?: string | null;
  rhythm?: string | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
}

export interface RevokeOrganizationPermissionInput {
  recipientId: string;
  permissions: OrganizationPermissionInput[];
}

export interface SelectedMemberPlanInput {
  id: string;
  name: string;
  frequency?: string | null;
}

export interface SelectionInput {
  positives?: string[] | null;
  negatives?: string[] | null;
  vitalSignRevied?: boolean | null;
  nurseNoteReviewed?: boolean | null;
  allOtherSystemNegative?: boolean | null;
  otherSystemNegative?: string[] | null;
  topLevelChecks?: string[] | null;
  systemChecks?: string[] | null;
  systemPositives?: string[] | null;
  systemNegatives?: string[] | null;
}

export interface SelfRegistrationDependentInput {
  enrolleeId?: string | null;
  title?: string | null;
  firstName?: string | null;
  middleName?: string | null;
  lastName?: string | null;
  gender?: string | null;
  dateOfBirth?: any | null;
  bloodGroup?: string | null;
  relationship?: string | null;
  phoneNumber?: PhoneNumberInput | null;
  email?: string | null;
  displayPictureUrl?: string | null;
}

export interface SelfRegistrationNextOfKinInput {
  title?: string | null;
  firstName?: string | null;
  middleName?: string | null;
  lastName?: string | null;
  gender?: string | null;
  relationship?: string | null;
  phoneNumber?: PhoneNumberInput | null;
  email?: string | null;
}

export interface SendFundsInput {
  amount: number;
  receiverClinifyId: string;
  passcode: string;
  description?: string | null;
}

export interface SendMessageInput {
  conversationId?: string | null;
  participantId: string;
  content: string;
  messageType?: MessageType | null;
  replyToId?: string | null;
  agencyId?: string | null;
  participantHospitalId?: string | null;
}

export interface ServiceDetailInput {
  priceId?: string | null;
  type?: string | null;
  name?: string | null;
  quantity?: string | null;
  pricePerUnit?: number | null;
  itemId?: string | null;
  patientType?: string | null;
  paymentType?: string | null;
  reference?: string | null;
}

export interface ServicesAnalyticsFilter {
  startDate?: string | null;
  endDate?: string | null;
  duration?: string | null;
  overall?: boolean | null;
  diagnosisType?: DiagnosisType | null;
  diagnosisGroup?: DiagnosisGroup | null;
  inPatient?: boolean | null;
  gender?: GenderOption | null;
  ageRange?: string | null;
  orderBy?: string | null;
  coverage?: string | null;
  investigationType?: string | null;
  investigation?: string | null;
  hospitalId?: string | null;
}

export interface SignatureInput {
  patientConsentSignature?: string | null;
  patientConsentSignatureType?: string | null;
}

export interface SpecialistAssignedTo {
  specialty?: string | null;
  role?: string | null;
  specialistId?: string | null;
}

export interface SponsorBillingInformationInput {
  bankName: string;
  accountName: string;
  accountNumber: string;
  bvn?: string | null;
  branchName?: string | null;
  id?: string | null;
  sponsorName?: string | null;
}

export interface SponsorEnrolleeDetailsInput {
  memberNumber?: string | null;
  fullName?: string | null;
  status?: string | null;
  planStartDate?: any | null;
  planDueDate?: any | null;
  isCovered?: boolean | null;
}

export interface SponsorResponseInput {
  ref?: string | null;
  sponsorName?: string | null;
  sponsorType?: string | null;
  sponsorLives?: string | null;
  agencyLives?: string | null;
  amountDue?: string | null;
  paymentFrequency?: string | null;
  nextRenewalDate?: any | null;
  renewalCount?: string | null;
  paymentStatus?: string | null;
  paymentDateTime?: any | null;
  sponsoredPremiumPerLife?: string | null;
  totalSponsoredPremium?: string | null;
  status?: string | null;
  percentageCovered?: string | null;
  sponsorPhoneNumber?: PhoneNumberInput | null;
  sponsorEmailAddress?: string | null;
}

export interface StaffActivitiesCategoryInput {
  category: string;
  serviceIds: string[];
}

export interface StaffActivitiesFilter {
  startDate: string;
  endDate: string;
  staffId?: string | null;
  patientsId?: string[] | null;
  categories?: StaffActivitiesCategoryInput[] | null;
}

export interface StaffActivitiesSummeryFilter {
  keyword?: string | null;
  startDate: string;
  endDate: string;
  staffId?: string | null;
  serviceType?: StaffActivitiesServiceType[] | null;
  patientsId?: string[] | null;
  take?: number | null;
  skip?: number | null;
}

export interface StaffsFilterInputs {
  skip?: number | null;
  take?: number | null;
  enrolleeId?: string | null;
  referral?: boolean | null;
  profileType?: OrganisationProfileType | null;
  specialty?: string | null;
  showSelf?: boolean | null;
  withActiveWaiters?: boolean | null;
  keyword?: string | null;
  ids?: string[] | null;
  notInIds?: string[] | null;
}

export interface StocksAnalyticsFilter {
  startDate?: string | null;
  endDate?: string | null;
  duration?: string | null;
  overall?: boolean | null;
  orderBy?: string | null;
  hospitalId?: string | null;
}

export interface SubBillInput {
  id?: string | null;
  amount?: number | null;
  amountDue?: number | null;
  amountPaid?: number | null;
  amountOwing?: number | null;
  amountOutstanding?: number | null;
  discountAmount?: number | null;
  discountPercentage?: number | null;
  vatAmount?: number | null;
  vatPercentage?: number | null;
  quantity?: number | null;
  unitPrice?: number | null;
  discountCode?: string | null;
  description?: string | null;
  serviceType?: string | null;
  serviceName?: string | null;
  patientType?: string | null;
  paymentType?: string | null;
  reference?: string | null;
  bankName?: string | null;
  accountNumber?: string | null;
}

export interface SupplyFilter {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
}

export interface SupplyItemInput {
  id?: string | null;
  supplier?: string | null;
  invoiceNumber?: string | null;
  bedAvailable?: string | null;
  colour?: string | null;
  vin?: string | null;
  plateNumber?: string | null;
  purchasedBy?: string | null;
  status?: string | null;
  model?: string | null;
  year?: string | null;
  name?: string | null;
  type?: string | null;
  size?: string | null;
  group?: string | null;
  flag?: string | null;
  description?: string | null;
  category?: string | null;
  code?: string | null;
  batchNumber?: string | null;
  barcode?: string | null;
  expiryDate?: string | null;
  expiryStatus?: string | null;
  damagedCount?: string | null;
  markup?: string | null;
  strength?: string | null;
  unitCost?: string | null;
  unitSellingPrice?: string | null;
  totalCost?: string | null;
  totalSale?: string | null;
  quantityRemaining?: string | null;
  quantityPurchased?: string | null;
  quantityAvailable?: string | null;
  quantityOrdered?: string | null;
  bedNumber?: string | null;
  ward?: string | null;
  manufacturer?: string | null;
  recievedDateTime?: string | null;
  receivedBy?: string | null;
  comments?: string | null;
  reorderLevel?: string | null;
  purchasedDate?: string | null;
  images?: string | null;
  class?: string | null;
  additionalNote?: string | null;
}

export interface SurgeryFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
}

export interface SurgeryInput {
  id?: string | null;
  clinifyId: string;
  surgeryDate?: any | null;
  duration?: string | null;
  provider?: string | null;
  procedureType: ProcedureTypeInput[];
  requestedBy?: string | null;
  operatedBy: string;
  specialty?: string | null;
  rank?: string | null;
  department?: string | null;
  assistantSurgeon?: string | null;
  facilityName?: string | null;
  facilityAddress?: string | null;
  priority?: string | null;
  patientConsent?: string | null;
  patientConsentSignature?: string | null;
  patientConsentSignatureType?: string | null;
  patientConsentSignatureDateTime?: any | null;
  serviceDetails?: ServiceDetailInput[] | null;
  reason?: string | null;
  operatingRoomNurse?: string | null;
  anesthetistName?: string | null;
  anesthesia?: string | null;
  surgeryStartDate?: any | null;
  surgeryEndDate?: any | null;
  nextAppointmentDateTime?: any | null;
  nextAppointmentEndDateTime?: any | null;
  nextAppointmentDuration?: string | null;
  nextAppointmentSpecialty?: string | null;
  nextAppointmentRole?: string | null;
  nextAppointmentCategory?: string | null;
  nextAppointmentSpecialistId?: string | null;
  nextAppointmentSpecialistName?: string | null;
  nextAppointmentUrgency?: string | null;
  nextAppointmentDeliveryMethod?: string | null;
  documentUrl?: string[] | null;
  operationNote?: string | null;
  postOperationNote?: string | null;
  concealOperationNote?: boolean | null;
  medications?: string[] | null;
  vitals?: string[] | null;
  investigations?: string[] | null;
  labTests?: string[] | null;
  radiology?: string[] | null;
  hmoProviderId?: string | null;
  visitingFacilityName?: string | null;
  visitingSpecialistName?: string | null;
  nursingServices?: string[] | null;
  billInfo?: BillInfoUpdateInput[] | null;
  billRefsToDelete?: string[] | null;
}

export interface Temperature {
  readingDate?: any | null;
  value?: string | null;
  unit?: string | null;
  time?: string | null;
}

export interface TemperatureVitalFields {
  id?: string | null;
  readingDateTime?: any | null;
  checkMethod?: string | null;
  checkMethodSpecify?: string | null;
  reading?: string | null;
  isReadingCritical?: boolean | null;
  readingUnit?: string | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
}

export interface TestReferenceRangeInput {
  facilityPreferenceId: string;
  rangesString: string;
  maleRangesString?: string | null;
  femaleRangesString?: string | null;
  testName: string;
}

export interface TestResults {
  name?: string | null;
  value?: string | null;
  valueTwo?: string | null;
  tabular?: string[] | null;
  unit?: string | null;
  dropdown?: string[] | null;
  box?: boolean | null;
  hasExtraValue?: boolean | null;
  referenceRange?: boolean | null;
  sensitivityBox?: boolean | null;
  units?: string[] | null;
  dependsOn?: string[] | null;
  range?: string | null;
  extraValue?: string | null;
}

export interface TicknessSite {
  tick?: boolean | null;
  site?: string | null;
}

export interface TicknessValue {
  tick?: boolean | null;
  value?: string | null;
}

export interface TimeVolume {
  time?: string | null;
  volume?: string | null;
}

export interface TrackboardFilter {
  startDate: string;
  endDate: string;
  skip?: number | null;
  take?: number | null;
}

export interface TransferFundFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  status?: FundTransactionStatus | null;
  hospitalId?: string | null;
  isEnrolleePayout?: boolean | null;
  hmoProviderId?: boolean | null;
}

export interface TransferPatientInput {
  id?: string | null;
  transferDateTime?: any | null;
  transferReason: string;
  transferSource?: string | null;
  concealTransferReason?: boolean | null;
  transferredBy?: string | null;
  transferHospital: string;
  roomOption?: string | null;
  roomInventoryId?: string | null;
}

export interface TreatmentPlanInput {
  id?: string | null;
  treatmentPlan?: string | null;
  conceal?: boolean | null;
  patientAdmitted?: string | null;
  observationNote?: string | null;
  concealObservationNote?: boolean | null;
  admissionConsent?: string | null;
  adverseEffectsFollowingTreatment?: string | null;
  stateEffects?: string | null;
  adverseEffectsInvestigated?: string | null;
  outcomeOfInvestigation?: string | null;
  treatmentGiven?: string | null;
  patientConsentSignature?: string | null;
  patientConsentSignatureType?: string | null;
  patientConsentSignatureDateTime?: string | null;
  treatmentStatus?: string | null;
}

export interface TumorDetailsInput {
  laterality?: string | null;
  size?: string | null;
}

export interface UpdateBillInput {
  id: string;
  passCode: string;
  billDetail: BillUpdate;
}

export interface UpdateBusinessRuleInput {
  items: NewBusinessRuleItemInput[];
  flag?: string | null;
  matchAll?: boolean | null;
  sumAll?: boolean | null;
  id: string;
}

export interface UpdateDefaultEmailInput {
  email: string;
}

export interface UpdateDispenseRegisterInput {
  reference: string;
  periods: Periods[];
}

export interface UpdateDraftInput {
  type?: string | null;
  value?: string | null;
}

export interface UpdateEmployeeInput {
  title?: string | null;
  firstName: string;
  lastName: string;
  middleName?: string | null;
  employeeId?: string | null;
  jobTitle?: string | null;
  department?: string | null;
  phoneNumber?: PhoneNumberInput | null;
  emailAddress?: string | null;
  address?: string | null;
  employerId?: string | null;
  hmoPlanTypeId?: string | null;
  planCategory?: string | null;
  enrolleeId?: string | null;
  enrolledBy?: string | null;
  paymentDate?: any | null;
  paymentFrequency?: string | null;
  enrollmentDateTime?: any | null;
  planTypeCategory?: string | null;
  premiumAmount?: string | null;
  registrationSource?: string | null;
  administrationAgency?: string | null;
  enrollmentAgent?: string | null;
  enrollmentAgency?: string | null;
  enrollmentTpaNonTpa?: string | null;
  enrollmentAgentId?: string | null;
  enrollmentAgencyId?: string | null;
  enrollmentTpaNonTpaId?: string | null;
  salesWing?: string | null;
  commissionPayable?: string | null;
  commissionRate?: string | null;
  tpaNonTpaCommissionRate?: string | null;
  tpaNonTpaCommissionPayable?: string | null;
  sponsorType?: string | null;
  sponsorName?: string | null;
  activationDatetime?: any | null;
  sponsorCode?: string | null;
  referrerCode?: string | null;
  referrer?: string | null;
  referrerNumber?: string | null;
  referrerCommissionRate?: string | null;
  referrerCommissionPayable?: string | null;
  planStartDate?: any | null;
  planDueDate?: any | null;
  employerCode?: string | null;
  planCode?: string | null;
  nextRenewalDate?: any | null;
  hmoPlanStatus?: HmoPlanStatus | null;
  gender?: Gender | null;
  dateOfBirth?: any | null;
  employeeType?: string | null;
  dependants?: UpdateEmployerDependantInput[] | null;
  primaryProviderId?: string | null;
  primaryProviderName?: string | null;
  primaryProviderAddress?: string | null;
  displayPictureUrl?: string | null;
  id?: string | null;
}

export interface UpdateEmployerDependantInput {
  title?: string | null;
  firstName: string;
  lastName: string;
  middleName?: string | null;
  gender?: string | null;
  dateOfBirth?: any | null;
  phoneNumber?: PhoneNumberInput | null;
  emailAddress?: string | null;
  relationship: string;
  enrolleeId?: string | null;
  primaryProviderId?: string | null;
  primaryProviderName?: string | null;
  primaryProviderAddress?: string | null;
  displayPictureUrl?: string | null;
  id: string;
}

export interface UpdateEmployerInput {
  displayUrl?: string | null;
  employerName: string;
  employerAddress?: string | null;
  country?: string | null;
  state?: string | null;
  localGovernmentArea?: string | null;
  ward?: string | null;
  employerPrimaryPhoneNumber?: PhoneNumberInput | null;
  employerPrimaryEmailAddress?: string | null;
  employerSecondaryPhoneNumber?: PhoneNumberInput | null;
  employerSecondaryEmailAddress?: string | null;
  contactPersonTitle?: string | null;
  contactPersonFirstName?: string | null;
  contactPersonMiddleName?: string | null;
  contactPersonLastName?: string | null;
  contactPersonPhoneNumber?: PhoneNumberInput | null;
  contactPersonEmailAddress?: string | null;
  contactPersonAltTitle?: string | null;
  contactPersonAltFirstName?: string | null;
  contactPersonAltMiddleName?: string | null;
  contactPersonAltLastName?: string | null;
  contactPersonAltPhoneNumber?: PhoneNumberInput | null;
  contactPersonAltEmailAddress?: string | null;
  numberOfEmployees?: number | null;
  planAmount?: number | null;
  planAmountFlag?: string | null;
  planAgent?: string | null;
  paymentFrequency?: string | null;
  employerNumber?: string | null;
  employerPlanCode?: string | null;
  planGroup?: string | null;
  planSubGroup?: string | null;
  referrer?: string | null;
  referrerNumber?: string | null;
  referrerCode?: string | null;
  commissionRate?: string | null;
  commissionPayable?: string | null;
  additionalNote?: string | null;
  hmoProviderId?: string | null;
  employees?: UpdateEmployeeInput[] | null;
  selectedMemberPlans?: SelectedMemberPlanInput[] | null;
  id: string;
}

export interface UpdateFeedbackCommentInput {
  id: string;
  comment: string;
}

export interface UpdateFeedbackInput {
  feedbackDateTime?: any | null;
  title?: string | null;
  category?: string | null;
  message?: string | null;
  documentUrl?: string[] | null;
  providerCode?: string | null;
  id: string;
}

export interface UpdateFeedbackStatusInput {
  id: string;
  isResolved: boolean;
}

export interface UpdateHospitalInput {
  country?: string | null;
  hospitalName?: string | null;
  hospitalAddress?: string | null;
  hospitalWebsite?: string | null;
  hospitalContactTitle?: string | null;
  hospitalContactPhoneNumber?: PhoneNumberInput | null;
  ownership?: string | null;
  state?: string | null;
  lga?: string | null;
  politicalWard?: string | null;
  city?: string | null;
  level?: string | null;
  hospitalLicenseNumber?: string | null;
  documentUrl?: string[] | null;
  plan?: HospitalPlan | null;
  partnerCode?: string | null;
  wemaAccountNumber?: string | null;
  wemaAccountName?: string | null;
  providerCode?: string | null;
  facebook?: string | null;
  instagram?: string | null;
  twitter?: string | null;
  supportMail?: string | null;
  secondaryPhoneNumber: PhoneNumberInput;
  hospitalSupportPhoneNumber: PhoneNumberInput;
}

export interface UpdateOrgBillInput {
  billingDateTime?: any | null;
  additionalNote?: string | null;
  totalAmount?: number | null;
  discountAmount?: number | null;
  vatAmount?: number | null;
  professionalFeeAmount?: number | null;
  amountDue?: number | null;
  amountPaid?: number | null;
  unitPrice?: number | null;
  amountOutstanding?: number | null;
  amountOwning?: number | null;
  amountUnderpaid?: number | null;
  amountOverpaid?: number | null;
  hospitalName?: string | null;
  hospitalAddress?: string | null;
  reference?: string | null;
  invoiceNumber?: string | null;
  details?: BillDetailInput[] | null;
  collectedBy?: string | null;
  collectionDateTime?: any | null;
  commissionPayer?: CommissionPayer | null;
}

export interface UpdateOrganisationAppointmentInput {
  profileId?: string | null;
  startDateTime?: any | null;
  endDateTime?: any | null;
  category?: string | null;
  rank?: string | null;
  reason?: string | null;
  venue?: string | null;
  liveSessionUrl?: string | null;
  hospitalId?: string | null;
  specialty?: string | null;
  role?: string | null;
  specialistId?: string | null;
  paymentType?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  patientType?: string | null;
  urgency?: string | null;
  deliveryMethod?: string | null;
  duration?: string | null;
  additionalNote?: string | null;
  appointmentDateTime?: any | null;
  documentUrl?: string[] | null;
  origin?: string | null;
  reminderTransportOptions?: string[] | null;
  clinifyId?: string | null;
  confirmedBy?: string | null;
}

export interface UpdateOrganisationAppointmentTimeInput {
  startDateTime?: any | null;
  endDateTime?: any | null;
  duration?: string | null;
  origin?: string | null;
}

export interface UpdatePhoneNumberInput {
  phoneNumber: string;
  country: string;
  otpCode: string;
}

export interface UpdatePinInput {
  oldPin: string;
  newPin: string;
}

export interface UpdatePlanBenefit {
  visitType: string;
  code?: string | null;
  utilisationCategory: string;
  benefitLimit?: string | null;
  visitLimit?: string | null;
  annualLimitPerPerson?: string | null;
  waitingPeriodDays?: number | null;
  utilisationTypes: HmoUtilisationInput[];
  id?: string | null;
  visitTypeId?: string | null;
}

export interface UpdatePlanTypeBenefit {
  id?: string | null;
  name?: string | null;
  code?: string | null;
  visitTypeId?: string | null;
  visitTypeName?: string | null;
  utilisationCategory: string;
  limit?: string | null;
  benefitLimit?: string | null;
  visitLimit?: string | null;
  annualLimitPerPerson?: string | null;
  waitingPeriodDays?: number | null;
  utilisationTypes: HmoUtilisationInput[];
}

export interface UpdatePreAuthUtilizationStatusInput {
  id: string;
  status?: PaCodeStatus | null;
  rejectionReason?: string[] | null;
  specifyReasonForRejection?: string | null;
  statusDescription?: string | null;
  paCode?: string | null;
  comment?: string | null;
}

export interface UpdatePreauthorizationDetailsInput {
  paCode?: string | null;
  paStatus?: string | null;
  id?: string | null;
  model: BillableRecords;
  modelId: string;
  ref?: string | null;
  providerCode?: string | null;
}

export interface UpdateProfilePreferenceInput {
  autoProcessClaims?: boolean | null;
  autoProcessPreauthorizations?: boolean | null;
}

export interface UpdateProviderInput {
  country: string;
  hospitalName: string;
  hospitalAddress?: string | null;
  hospitalWebsite?: string | null;
  hospitalContactTitle?: string | null;
  hospitalContactFirstName: string;
  hospitalContactMiddleName?: string | null;
  hospitalContactLastName: string;
  hospitalContactEmail: string;
  hospitalContactPhoneNumber: PhoneNumberInput;
  ownership?: string | null;
  state?: string | null;
  lga?: string | null;
  politicalWard?: string | null;
  city?: string | null;
  level?: string | null;
  hospitalLicenseNumber?: string | null;
  documentUrl?: string[] | null;
  plan: HospitalPlan;
  partnerCode?: string | null;
  wemaAccountNumber?: string | null;
  wemaAccountName?: string | null;
  providerCode: string;
  providerPrimaryPhoneNumber: PhoneNumberInput;
  providerSecondaryPhoneNumber?: PhoneNumberInput | null;
  providerId?: string | null;
  supportMail?: string | null;
  planStatus?: PlanStatus | null;
  tariffBand?: HMOTariffBand | null;
  providerCategory?: string | null;
  classification?: string | null;
  enrolleeCount?: string | null;
  enrolleeLimit?: string | null;
  planVisibility?: PlanVisibilityInput[] | null;
  facebook?: string | null;
  instagram?: string | null;
  twitter?: string | null;
}

export interface UpdateReferralUtilisationStatusInput {
  id: string;
  status: string;
  rejectionReason?: string[] | null;
  specifyReasonForRejection?: string | null;
}

export interface UpdateUtilizationsStatusInput {
  ids: string[];
  status?: PaCodeStatus | null;
  rejectionReason?: string[] | null;
  specifyReasonForRejection?: string | null;
  statusDescription?: string | null;
}

export interface UpdateVitalInput {
  id?: string | null;
  clinifyId: string;
  type?: VitalType | null;
  hospitalName?: string | null;
  hospitalAddress?: string | null;
  documentUrl?: string[] | null;
}

export interface Urine {
  readingDate?: any | null;
  protein?: string | null;
  acetone?: string | null;
  volume?: string | null;
  time?: string | null;
}

export interface UrineDipstickVitalFields {
  id?: string | null;
  readingDateTime?: any | null;
  blood?: string | null;
  glucose?: string | null;
  ketones?: string | null;
  ph?: string | null;
  isPhCritical?: boolean | null;
  protein?: string | null;
  nitrites?: string | null;
  leucocyte?: string | null;
  urobilinogen?: string | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
}

export interface UrineOutputDetail {
  timeIn?: string | null;
  output?: string | null;
}

export interface VTEAndBleedingRiskInput {
  field: string;
  ref?: string | null;
  changeFor: string;
  assessment: AssessmentInput;
}

export interface VerifyEmailInput {
  emailAddress: string;
}

export interface VerifyOtpInput {
  phoneNumber: string;
  otpCode: string;
}

export interface VerifyPassCodeForUpdatePhoneNumberInput {
  phoneNumber: string;
  passCode: string;
}

export interface VirtualBankAccountKYCInput {
  bvn: string;
  phoneNumber: string;
}

export interface VirtualCareAppointmentFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  archive?: boolean | null;
  showAll?: boolean | null;
  status?: string | null;
  providerInsight?: boolean | null;
  desOrderSequence?: boolean | null;
  profileId?: string | null;
}

export interface VisitationAnalyticsFilter {
  startDate?: string | null;
  endDate?: string | null;
  duration?: string | null;
  overall?: boolean | null;
  ageRange?: string | null;
  gender?: GenderOption | null;
  hospitalId?: string | null;
}

export interface VisualAcuityVitalFields {
  id?: string | null;
  readingDateTime?: any | null;
  withGlassesLeft?: string | null;
  withGlassesRight?: string | null;
  withoutGlassesLeft?: string | null;
  withoutGlassesRight?: string | null;
  additionalNote?: string | null;
  concealAdditionalNote?: boolean | null;
}

export interface VitalFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  limitSub?: boolean | null;
}

export interface VitalSign {
  timeIn?: string | null;
  systolic?: string | null;
  diastolic?: string | null;
  pulseRate?: string | null;
  oxygenSaturation?: string | null;
  respiratoryRate?: string | null;
  temperature?: string | null;
  temperatureUnit?: string | null;
  nausea?: string | null;
  painScore?: string | null;
  state?: string | null;
}

export interface WaitingListFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  creator?: RecordCreator | null;
  archive?: boolean | null;
  referral?: boolean | null;
  status?: string | null;
  ownList?: boolean | null;
  providerInsight?: boolean | null;
  hospitalId?: string | null;
}

export interface WaitingListInput {
  patient: string;
  assignedTo?: AssignedTo[] | null;
  clinifyId?: string | null;
  arrivalDateTime?: any | null;
  priority?: string | null;
  patientType?: string | null;
  paymentType?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  visitType?: string | null;
  appointmentBooked?: string | null;
  vitalsTaken?: QuestionOption | null;
  vitalsTakenBy?: string | null;
  visitationReason?: string | null;
  waitStatus?: string | null;
  transferReason?: string | null;
  emergencyType?: string | null;
  resuscitationActionPlan?: string | null;
  appointmentDateTime?: any | null;
}

export interface WaitingListUpdateInput {
  assignedTo?: AssignedTo[] | null;
  clinifyId?: string | null;
  arrivalDateTime?: any | null;
  priority?: string | null;
  patientType?: string | null;
  paymentType?: string | null;
  serviceDetails?: ServiceDetailInput[] | null;
  visitType?: string | null;
  appointmentBooked?: string | null;
  vitalsTaken?: QuestionOption | null;
  vitalsTakenBy?: string | null;
  visitationReason?: string | null;
  waitStatus?: string | null;
  transferReason?: string | null;
  emergencyType?: string | null;
  resuscitationActionPlan?: string | null;
  appointmentDateTime?: any | null;
  status?: WaitingListStatus | null;
  waitTime?: string | null;
  archive?: boolean | null;
}

export interface WalkInFilterInput {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  archive?: boolean | null;
}

export interface WalkInReferralInput {
  id?: string | null;
  referralDateTime?: any | null;
  referredBy?: string | null;
  concealReferralReason?: boolean | null;
  referralReason?: string | null;
  referralFacilityName?: string | null;
  referralFacilityAddress?: string | null;
  profileId?: string | null;
  documentUrl?: string[] | null;
}

export interface WalkInTransferInput {
  id?: string | null;
  transferDateTime?: any | null;
  transferredBy?: string | null;
  concealTransferReason?: boolean | null;
  transferReason?: string | null;
  transferFacilityName?: string | null;
  transferFacilityAddress?: string | null;
  profileId?: string | null;
  documentUrl?: string[] | null;
}

export interface WalletTransactionFilter {
  skip?: number | null;
  take?: number | null;
  dateRange?: DateRangeInput | null;
  keyword?: string | null;
  enrolleeId?: string | null;
  archive?: boolean | null;
  referral?: boolean | null;
  transactionType?: TransactionType | null;
  status?: TransactionStatus | null;
}

export interface addOrgBillInput {
  id?: string | null;
  billingDateTime?: any | null;
  raisedBy?: string | null;
  clinifyId: string;
  additionalNote?: string | null;
  totalAmount?: number | null;
  discountAmount?: number | null;
  vatAmount?: number | null;
  professionalFeeAmount?: number | null;
  amountDue?: number | null;
  amountPaid?: number | null;
  unitPrice?: number | null;
  amountOutstanding?: number | null;
  amountOwning?: number | null;
  amountUnderpaid?: number | null;
  amountOverpaid?: number | null;
  hospitalName?: string | null;
  hospitalAddress?: string | null;
  reference?: string | null;
  invoiceNumber?: string | null;
  details: BillDetailInput[];
  collectedBy?: string | null;
  collectionDateTime?: any | null;
}

export interface excludeBillDetailInput {
  id: string;
  excluded: boolean;
}

export interface fetchAppointmentFilterInput {
  profile?: boolean | null;
  hospital?: boolean | null;
  specialist?: boolean | null;
}

//==============================================================
// END Enums and Input Objects
//==============================================================
