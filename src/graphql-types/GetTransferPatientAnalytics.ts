/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { AdmissionAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetTransferPatientAnalytics
// ====================================================

export interface GetTransferPatientAnalytics_getTransferPatient {
  __typename: "AdmissionSummary";
  totalTransfers: number | null;
  name: number | null;
}

export interface GetTransferPatientAnalytics {
  getTransferPatient: GetTransferPatientAnalytics_getTransferPatient[];
}

export interface GetTransferPatientAnalyticsVariables {
  filter?: AdmissionAnalyticsFilter | null;
}
