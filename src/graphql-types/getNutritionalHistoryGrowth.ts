/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ProfileInfosFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: getNutritionalHistoryGrowth
// ====================================================

export interface getNutritionalHistoryGrowth_profile_nutritionalHistoryGrowths_list {
  __typename: "NutritionalHistoryGrowthModel";
  id: string;
  months: number;
  years: number;
  weight: string | null;
  height: string | null;
  babyLength: string | null;
  headCircumference: string | null;
  measurementType: string;
}

export interface getNutritionalHistoryGrowth_profile_nutritionalHistoryGrowths {
  __typename: "NutritionalHistoryGrowthsResponse";
  totalCount: number;
  list: getNutritionalHistoryGrowth_profile_nutritionalHistoryGrowths_list[];
}

export interface getNutritionalHistoryGrowth_profile {
  __typename: "ProfileModel";
  id: string;
  nutritionalHistoryGrowths: getNutritionalHistoryGrowth_profile_nutritionalHistoryGrowths;
}

export interface getNutritionalHistoryGrowth {
  profile: getNutritionalHistoryGrowth_profile;
}

export interface getNutritionalHistoryGrowthVariables {
  filterInput: ProfileInfosFilterInput;
  id: string;
}
