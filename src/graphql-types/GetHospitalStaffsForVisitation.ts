/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { StaffsFilterInputs, Gender } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHospitalStaffsForVisitation
// ====================================================

export interface GetHospitalStaffsForVisitation_hospital_staffs_list_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  rank: string | null;
  title: string | null;
  department: string | null;
  dateOfBirth: any | null;
  height: string | null;
  gender: Gender | null;
  speciality: string | null;
}

export interface GetHospitalStaffsForVisitation_hospital_staffs_list {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
  title: string | null;
  clinifyId: string;
  active: boolean;
  personalInformation: GetHospitalStaffsForVisitation_hospital_staffs_list_personalInformation | null;
}

export interface GetHospitalStaffsForVisitation_hospital_staffs {
  __typename: "ProfilesResponse";
  totalCount: number;
  list: GetHospitalStaffsForVisitation_hospital_staffs_list[];
}

export interface GetHospitalStaffsForVisitation_hospital {
  __typename: "HospitalModel";
  id: string;
  staffs: GetHospitalStaffsForVisitation_hospital_staffs;
}

export interface GetHospitalStaffsForVisitation {
  hospital: GetHospitalStaffsForVisitation_hospital;
}

export interface GetHospitalStaffsForVisitationVariables {
  filterOptions?: StaffsFilterInputs | null;
  hospitalId?: string | null;
}
