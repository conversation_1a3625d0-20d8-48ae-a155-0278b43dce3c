/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { EnrollmentTpaNonTpaInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateEnrollmentTpaNonTpaAgency
// ====================================================

export interface UpdateEnrollmentTpaNonTpaAgency_updateEnrollmentTpaNonTpa_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface UpdateEnrollmentTpaNonTpaAgency_updateEnrollmentTpaNonTpa_primaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrollmentTpaNonTpaAgency_updateEnrollmentTpaNonTpa_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrollmentTpaNonTpaAgency_updateEnrollmentTpaNonTpa_contactPersonPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrollmentTpaNonTpaAgency_updateEnrollmentTpaNonTpa_contactPersonAltPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrollmentTpaNonTpaAgency_updateEnrollmentTpaNonTpa {
  __typename: "EnrollmentTpaNonTpaModel";
  id: string;
  name: string | null;
  profile: UpdateEnrollmentTpaNonTpaAgency_updateEnrollmentTpaNonTpa_profile | null;
  address: string | null;
  country: string | null;
  state: string | null;
  tpaCode: string | null;
  localGovernmentArea: string | null;
  primaryPhoneNumber: UpdateEnrollmentTpaNonTpaAgency_updateEnrollmentTpaNonTpa_primaryPhoneNumber | null;
  primaryEmailAddress: string | null;
  secondaryPhoneNumber: UpdateEnrollmentTpaNonTpaAgency_updateEnrollmentTpaNonTpa_secondaryPhoneNumber | null;
  secondaryEmailAddress: string | null;
  contactPersonTitle: string | null;
  contactPersonFirstName: string | null;
  contactPersonMiddleName: string | null;
  contactPersonLastName: string | null;
  contactPersonPhoneNumber: UpdateEnrollmentTpaNonTpaAgency_updateEnrollmentTpaNonTpa_contactPersonPhoneNumber | null;
  contactPersonEmailAddress: string | null;
  contactPersonAltTitle: string | null;
  contactPersonAltFirstName: string | null;
  contactPersonAltMiddleName: string | null;
  contactPersonAltLastName: string | null;
  contactPersonAltPhoneNumber: UpdateEnrollmentTpaNonTpaAgency_updateEnrollmentTpaNonTpa_contactPersonAltPhoneNumber | null;
  contactPersonAltEmailAddress: string | null;
  accountName: string | null;
  accountNumber: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface UpdateEnrollmentTpaNonTpaAgency {
  updateEnrollmentTpaNonTpa: UpdateEnrollmentTpaNonTpaAgency_updateEnrollmentTpaNonTpa;
}

export interface UpdateEnrollmentTpaNonTpaAgencyVariables {
  id: string;
  input: EnrollmentTpaNonTpaInput;
}
