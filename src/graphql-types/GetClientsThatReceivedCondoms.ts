/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FamilyPlanningAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetClientsThatReceivedCondoms
// ====================================================

export interface GetClientsThatReceivedCondoms_getClientsThatReceivedCondoms {
  __typename: "FamilyPlanningSummary";
  name: number | null;
  totalClientsThatReceivedCondoms: number | null;
  category: string | null;
}

export interface GetClientsThatReceivedCondoms {
  getClientsThatReceivedCondoms: GetClientsThatReceivedCondoms_getClientsThatReceivedCondoms[];
}

export interface GetClientsThatReceivedCondomsVariables {
  filter?: FamilyPlanningAnalyticsFilter | null;
}
