/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { InventoryOrderFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHospitalInventorySupplyList
// ====================================================

export interface GetHospitalInventorySupplyList_hospital_inventorySupplies_list_hospital {
  __typename: "HospitalModel";
  name: string | null;
}

export interface GetHospitalInventorySupplyList_hospital_inventorySupplies_list_supplyTo {
  __typename: "HospitalModel";
  name: string | null;
}

export interface GetHospitalInventorySupplyList_hospital_inventorySupplies_list_order {
  __typename: "InventoryOrderModel";
  id: string;
  creatorName: string | null;
  createdDate: any | null;
}

export interface GetHospitalInventorySupplyList_hospital_inventorySupplies_list {
  __typename: "InventorySupplyModel";
  id: string;
  sn: number | null;
  addedDateTime: string | null;
  deliveredDateTime: any | null;
  section: string | null;
  supplier: string | null;
  invoiceNumber: string | null;
  name: string | null;
  type: string | null;
  size: string | null;
  bedNumber: string | null;
  ward: string | null;
  group: string | null;
  flag: string | null;
  description: string | null;
  strength: string | null;
  category: string | null;
  code: string | null;
  batchNumber: string | null;
  barcode: string | null;
  bedAvailable: string | null;
  expiryDate: string | null;
  expiryStatus: string | null;
  damagedCount: string | null;
  markup: string | null;
  unitCost: string | null;
  unitSellingPrice: string | null;
  totalCost: string | null;
  totalSale: string | null;
  quantityRemaining: string | null;
  quantityPurchased: string | null;
  quantityAvailable: string | null;
  quantityDispensed: string | null;
  quantitySold: string | null;
  manufacturer: string | null;
  recievedDateTime: string | null;
  reorderLevel: string | null;
  receivedBy: string | null;
  addedBy: string | null;
  purchasedBy: string | null;
  vin: string | null;
  plateNumber: string | null;
  year: string | null;
  model: string | null;
  colour: string | null;
  status: string | null;
  comments: string | null;
  images: string | null;
  creatorId: string | null;
  createdDate: any | null;
  creatorName: string | null;
  quantityOrdered: string | null;
  canceled: boolean | null;
  orderStatus: string | null;
  quantityDelivered: string | null;
  deliveredBy: string | null;
  hospital: GetHospitalInventorySupplyList_hospital_inventorySupplies_list_hospital | null;
  supplyTo: GetHospitalInventorySupplyList_hospital_inventorySupplies_list_supplyTo | null;
  order: GetHospitalInventorySupplyList_hospital_inventorySupplies_list_order | null;
}

export interface GetHospitalInventorySupplyList_hospital_inventorySupplies {
  __typename: "InventorySupplyResponse";
  totalCount: number;
  list: GetHospitalInventorySupplyList_hospital_inventorySupplies_list[];
}

export interface GetHospitalInventorySupplyList_hospital {
  __typename: "HospitalModel";
  id: string;
  inventorySupplies: GetHospitalInventorySupplyList_hospital_inventorySupplies;
}

export interface GetHospitalInventorySupplyList {
  hospital: GetHospitalInventorySupplyList_hospital;
}

export interface GetHospitalInventorySupplyListVariables {
  filterOptions?: InventoryOrderFilterInput | null;
}
