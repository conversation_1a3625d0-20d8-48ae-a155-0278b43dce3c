/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: UpdatePreauthUtilizationQuantity
// ====================================================

export interface UpdatePreauthUtilizationQuantity_updatePreauthUtilizationQuantity {
  __typename: "PreAuthUtilisationsModel";
  id: string;
  quantity: string | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
}

export interface UpdatePreauthUtilizationQuantity {
  updatePreauthUtilizationQuantity: UpdatePreauthUtilizationQuantity_updatePreauthUtilizationQuantity;
}

export interface UpdatePreauthUtilizationQuantityVariables {
  id: string;
  quantity: number;
}
