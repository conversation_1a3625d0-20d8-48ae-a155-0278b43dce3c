/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealLabourAndDeliveryAdditionalNote
// ====================================================

export interface ConcealLabourAndDeliveryAdditionalNote_concealLabourAndDeliveryAdditionalNote {
  __typename: "LabourDeliveryModel";
  id: string;
  concealAdditionalNote: boolean | null;
  additionalNote: string | null;
}

export interface ConcealLabourAndDeliveryAdditionalNote {
  concealLabourAndDeliveryAdditionalNote: ConcealLabourAndDeliveryAdditionalNote_concealLabourAndDeliveryAdditionalNote;
}

export interface ConcealLabourAndDeliveryAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
}
