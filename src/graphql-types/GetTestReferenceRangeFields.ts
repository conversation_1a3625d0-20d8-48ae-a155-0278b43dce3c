/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetTestReferenceRangeFields
// ====================================================

export interface GetTestReferenceRangeFields_fetchTestReferenceRanges {
  __typename: "TestReferenceRangeModel";
  id: string;
  testName: string;
  maleReferenceRange: string | null;
  femaleReferenceRange: string | null;
  referenceRange: string;
  updatedDate: any;
  lastModifierName: string | null;
}

export interface GetTestReferenceRangeFields {
  fetchTestReferenceRanges: GetTestReferenceRangeFields_fetchTestReferenceRanges[];
}

export interface GetTestReferenceRangeFieldsVariables {
  facilityPreferenceId: string;
  testNames?: string[] | null;
}
