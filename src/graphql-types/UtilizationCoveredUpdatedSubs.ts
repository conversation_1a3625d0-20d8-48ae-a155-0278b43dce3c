/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: UtilizationCoveredUpdatedSubs
// ====================================================

export interface UtilizationCoveredUpdatedSubs_UtilizationCoveredUpdated {
  __typename: "PreAuthUtilisationsModel";
  id: string;
  percentageCovered: number | null;
  amountCovered: number | null;
}

export interface UtilizationCoveredUpdatedSubs {
  UtilizationCoveredUpdated: UtilizationCoveredUpdatedSubs_UtilizationCoveredUpdated;
}

export interface UtilizationCoveredUpdatedSubsVariables {
  hospitalId: string;
}
