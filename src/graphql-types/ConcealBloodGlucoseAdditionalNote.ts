/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealBloodGlucoseAdditionalNote
// ====================================================

export interface ConcealBloodGlucoseAdditionalNote_concealBloodGlucoseAdditionalNote {
  __typename: "BloodGlucoseModel";
  id: string;
  concealAdditionalNote: boolean | null;
  additionalNote: string | null;
}

export interface ConcealBloodGlucoseAdditionalNote {
  concealBloodGlucoseAdditionalNote: ConcealBloodGlucoseAdditionalNote_concealBloodGlucoseAdditionalNote;
}

export interface ConcealBloodGlucoseAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
  clinifyId: string;
}
