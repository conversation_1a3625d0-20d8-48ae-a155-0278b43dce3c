/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { OncologyRegisterChart } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateOncologyConsultationRegister
// ====================================================

export interface UpdateOncologyConsultationRegister_updateOncologyConsultationRegister_treatmentChart_periods_cycles {
  __typename: "OncologyCycle";
  cycleNo: number;
  value: any | null;
}

export interface UpdateOncologyConsultationRegister_updateOncologyConsultationRegister_treatmentChart_periods {
  __typename: "OncologyPeriod";
  day: number;
  cycles: UpdateOncologyConsultationRegister_updateOncologyConsultationRegister_treatmentChart_periods_cycles[];
}

export interface UpdateOncologyConsultationRegister_updateOncologyConsultationRegister_treatmentChart {
  __typename: "OncologyChart";
  noOfCycles: number;
  periods: UpdateOncologyConsultationRegister_updateOncologyConsultationRegister_treatmentChart_periods[];
}

export interface UpdateOncologyConsultationRegister_updateOncologyConsultationRegister_therapyChart_periods_cycles {
  __typename: "OncologyCycle";
  cycleNo: number;
  value: any | null;
}

export interface UpdateOncologyConsultationRegister_updateOncologyConsultationRegister_therapyChart_periods {
  __typename: "OncologyPeriod";
  day: number;
  cycles: UpdateOncologyConsultationRegister_updateOncologyConsultationRegister_therapyChart_periods_cycles[];
}

export interface UpdateOncologyConsultationRegister_updateOncologyConsultationRegister_therapyChart {
  __typename: "OncologyChart";
  noOfCycles: number;
  periods: UpdateOncologyConsultationRegister_updateOncologyConsultationRegister_therapyChart_periods[];
}

export interface UpdateOncologyConsultationRegister_updateOncologyConsultationRegister {
  __typename: "OncologyConsultationRegisterModel";
  id: string;
  creatorName: string | null;
  lastModifierName: string | null;
  treatmentChart: UpdateOncologyConsultationRegister_updateOncologyConsultationRegister_treatmentChart | null;
  therapyChart: UpdateOncologyConsultationRegister_updateOncologyConsultationRegister_therapyChart | null;
  createdDate: any;
  updatedDate: any;
}

export interface UpdateOncologyConsultationRegister {
  updateOncologyConsultationRegister: UpdateOncologyConsultationRegister_updateOncologyConsultationRegister;
}

export interface UpdateOncologyConsultationRegisterVariables {
  clinifyId: string;
  input: OncologyRegisterChart;
  chartType: string;
  id: string;
  pin?: string | null;
}
