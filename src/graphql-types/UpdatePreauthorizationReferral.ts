/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PreauthorisationReferralUpdateInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdatePreauthorizationReferral
// ====================================================

export interface UpdatePreauthorizationReferral_updatePreauthorizationReferral_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface UpdatePreauthorizationReferral_updatePreauthorizationReferral_diagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface UpdatePreauthorizationReferral_updatePreauthorizationReferral_utilizations_statusHistory {
  __typename: "ReferalStatusHistoryType";
  status: string;
  creatorName: string;
  createdDate: any;
}

export interface UpdatePreauthorizationReferral_updatePreauthorizationReferral_utilizations {
  __typename: "PreAuthReferralUtilisationsModel";
  id: string;
  category: string | null;
  type: string | null;
  quantity: string | null;
  /**
   * Value is in Naira
   */
  price: string | null;
  status: string | null;
  paCode: string | null;
  utilizationCode: string | null;
  utilizationId: string | null;
  rhesusFactor: string | null;
  specialty: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusHistory: UpdatePreauthorizationReferral_updatePreauthorizationReferral_utilizations_statusHistory[] | null;
}

export interface UpdatePreauthorizationReferral_updatePreauthorizationReferral_referredProvider {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
}

export interface UpdatePreauthorizationReferral_updatePreauthorizationReferral_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface UpdatePreauthorizationReferral_updatePreauthorizationReferral_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  fullName: string;
}

export interface UpdatePreauthorizationReferral_updatePreauthorizationReferral {
  __typename: "PreauthorisationReferralModel";
  id: string;
  requestDateTime: any;
  requestedBy: string | null;
  referredBy: string | null;
  serviceType: string;
  serviceTypeCode: string;
  provider: UpdatePreauthorizationReferral_updatePreauthorizationReferral_provider;
  serviceName: string | null;
  priority: string | null;
  diagnosis: UpdatePreauthorizationReferral_updatePreauthorizationReferral_diagnosis[] | null;
  additionalNote: string | null;
  documentUrl: string[] | null;
  code: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  rank: string | null;
  department: string | null;
  specialty: string | null;
  utilizations: UpdatePreauthorizationReferral_updatePreauthorizationReferral_utilizations[] | null;
  totalQuantity: string;
  grandTotal: string;
  profileId: string | null;
  referralProviderRemark: string | null;
  referredProvider: UpdatePreauthorizationReferral_updatePreauthorizationReferral_referredProvider | null;
  hospital: UpdatePreauthorizationReferral_updatePreauthorizationReferral_hospital | null;
  enrolleeNumber: string | null;
  isExternalPlanType: boolean | null;
  externalPlanTypeId: string | null;
  profile: UpdatePreauthorizationReferral_updatePreauthorizationReferral_profile | null;
}

export interface UpdatePreauthorizationReferral {
  updatePreauthorizationReferral: UpdatePreauthorizationReferral_updatePreauthorizationReferral;
}

export interface UpdatePreauthorizationReferralVariables {
  id: string;
  input: PreauthorisationReferralUpdateInput;
}
