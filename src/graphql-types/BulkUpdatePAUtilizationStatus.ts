/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UpdateUtilizationsStatusInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: BulkUpdatePAUtilizationStatus
// ====================================================

export interface BulkUpdatePAUtilizationStatus_updatePreAuthUtilizationsStatus_utilizations {
  __typename: "PreAuthUtilisationsModel";
  id: string;
  status: string | null;
  updatedDate: any;
  lastModifierName: string | null;
}

export interface BulkUpdatePAUtilizationStatus_updatePreAuthUtilizationsStatus {
  __typename: "PreauthorisationModel";
  id: string;
  utilizations: BulkUpdatePAUtilizationStatus_updatePreAuthUtilizationsStatus_utilizations[] | null;
}

export interface BulkUpdatePAUtilizationStatus {
  updatePreAuthUtilizationsStatus: BulkUpdatePAUtilizationStatus_updatePreAuthUtilizationsStatus[];
}

export interface BulkUpdatePAUtilizationStatusVariables {
  input: UpdateUtilizationsStatusInput;
}
