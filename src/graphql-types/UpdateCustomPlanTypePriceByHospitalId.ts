/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BenefitCustomPriceInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateCustomPlanTypePriceByHospitalId
// ====================================================

export interface UpdateCustomPlanTypePriceByHospitalId_updateCustomPlanTypePriceByHospitalId {
  __typename: "BenefitCustomPrice";
  hospitalId: string;
  benefitId: string;
  price: number;
  utilisationCode: string;
}

export interface UpdateCustomPlanTypePriceByHospitalId {
  updateCustomPlanTypePriceByHospitalId: UpdateCustomPlanTypePriceByHospitalId_updateCustomPlanTypePriceByHospitalId[];
}

export interface UpdateCustomPlanTypePriceByHospitalIdVariables {
  hospitalId: string;
  planTypeId: string;
  input: BenefitCustomPriceInput[];
}
