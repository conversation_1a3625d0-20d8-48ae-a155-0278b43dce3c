/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HospitalFilterInput, HospitalPlan, FundTransactionStatus, PayoutCommissionPayer, HMOTariffBand } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHospitalsWithAdmin
// ====================================================

export interface GetHospitalsWithAdmin_hospitals_list_hospitalSupportPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface GetHospitalsWithAdmin_hospitals_list_preferredPayoutAccount {
  __typename: "BankAccountDetailsResponse";
  accountNumber: string;
  bankName: string;
  accountName: string | null;
}

export interface GetHospitalsWithAdmin_hospitals_list_capitationDetails_detailsByPlanType {
  __typename: "CapitationDetailByPlanType";
  enrolleeCount: number;
  planType: string;
  planTypeName: string;
  totalCapitationAmount: number;
  payoutDecreasePercentage: number | null;
}

export interface GetHospitalsWithAdmin_hospitals_list_capitationDetails_transferFund_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
  clinifyId: string;
  title: string | null;
}

export interface GetHospitalsWithAdmin_hospitals_list_capitationDetails_transferFund_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface GetHospitalsWithAdmin_hospitals_list_capitationDetails_transferFund_auditApproval {
  __typename: "ClaimsAccountApprovalType";
  createdDate: any;
  creatorName: string;
  approvalGroup: string;
  creatorId: string;
}

export interface GetHospitalsWithAdmin_hospitals_list_capitationDetails_transferFund_hmoPlanType {
  __typename: "HmoPlanTypeModel";
  id: string;
  name: string | null;
}

export interface GetHospitalsWithAdmin_hospitals_list_capitationDetails_transferFund_detailsByPlanType {
  __typename: "CapitationDetailByPlanType";
  enrolleeCount: number;
  planType: string;
  planTypeName: string;
  totalCapitationAmount: number;
  payoutDecreasePercentage: number | null;
}

export interface GetHospitalsWithAdmin_hospitals_list_capitationDetails_transferFund {
  __typename: "TransferFundModel";
  id: string;
  createdBy: GetHospitalsWithAdmin_hospitals_list_capitationDetails_transferFund_createdBy | null;
  amount: number;
  createdDate: any;
  updatedDate: any;
  destinationAccountName: string | null;
  destinationAccountNumber: string | null;
  destinationBankCode: string | null;
  destinationBankName: string | null;
  narration: string | null;
  originatorName: string | null;
  sourceAccountNumber: string | null;
  transferReference: string | null;
  transferStatus: FundTransactionStatus;
  additionalNote: string | null;
  serviceChargeAmount: number | null;
  hospitalId: string | null;
  hmoPlanTypeId: string | null;
  hospital: GetHospitalsWithAdmin_hospitals_list_capitationDetails_transferFund_hospital | null;
  enrolleeCount: number | null;
  isEnrolleePayout: boolean | null;
  totalCapitationAmount: number | null;
  auditApproval: GetHospitalsWithAdmin_hospitals_list_capitationDetails_transferFund_auditApproval[] | null;
  hmoPlanType: GetHospitalsWithAdmin_hospitals_list_capitationDetails_transferFund_hmoPlanType | null;
  detailsByPlanType: GetHospitalsWithAdmin_hospitals_list_capitationDetails_transferFund_detailsByPlanType[] | null;
  payoutCommissionPayer: PayoutCommissionPayer | null;
  payoutDecreasePercentage: number | null;
  enrolleeCommissionAmount: number | null;
}

export interface GetHospitalsWithAdmin_hospitals_list_capitationDetails {
  __typename: "CapitationDetails";
  enrolleeCount: number;
  totalCapitationAmount: number;
  detailsByPlanType: GetHospitalsWithAdmin_hospitals_list_capitationDetails_detailsByPlanType[] | null;
  payoutDecreasePercentage: number | null;
  transferFund: GetHospitalsWithAdmin_hospitals_list_capitationDetails_transferFund | null;
}

export interface GetHospitalsWithAdmin_hospitals_list_hmoHospitals_planVisibility {
  __typename: "PlanVisibilityDto";
  id: string;
  name: string;
}

export interface GetHospitalsWithAdmin_hospitals_list_hmoHospitals {
  __typename: "HmoHospitalModel";
  id: string;
  hmoProviderUniqueId: string | null;
  hmoProviderId: string;
  tariffBand: HMOTariffBand | null;
  category: string | null;
  planVisibility: GetHospitalsWithAdmin_hospitals_list_hmoHospitals_planVisibility[] | null;
}

export interface GetHospitalsWithAdmin_hospitals_list_phoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
}

export interface GetHospitalsWithAdmin_hospitals_list_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
}

export interface GetHospitalsWithAdmin_hospitals_list_orgAdmin_personalInformation {
  __typename: "PersonalInformation";
  firstName: string | null;
  lastName: string | null;
}

export interface GetHospitalsWithAdmin_hospitals_list_orgAdmin_user {
  __typename: "UserModel";
  id: string;
  email: string | null;
  corporatePhoneNumber: string | null;
}

export interface GetHospitalsWithAdmin_hospitals_list_orgAdmin {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  personalInformation: GetHospitalsWithAdmin_hospitals_list_orgAdmin_personalInformation | null;
  user: GetHospitalsWithAdmin_hospitals_list_orgAdmin_user;
}

export interface GetHospitalsWithAdmin_hospitals_list {
  __typename: "HospitalModel";
  id: string;
  clinifyId: string | null;
  name: string | null;
  address: string | null;
  ownership: string | null;
  lga: string | null;
  politicalWard: string | null;
  city: string | null;
  state: string | null;
  level: string | null;
  supportMail: string | null;
  country: string | null;
  classification: string | null;
  plan: HospitalPlan;
  hospitalSupportPhoneNumber: GetHospitalsWithAdmin_hospitals_list_hospitalSupportPhoneNumber | null;
  preferredPayoutAccount: GetHospitalsWithAdmin_hospitals_list_preferredPayoutAccount | null;
  capitationDetails: GetHospitalsWithAdmin_hospitals_list_capitationDetails[] | null;
  hmoHospitals: GetHospitalsWithAdmin_hospitals_list_hmoHospitals[] | null;
  phoneNumber: GetHospitalsWithAdmin_hospitals_list_phoneNumber | null;
  secondaryPhoneNumber: GetHospitalsWithAdmin_hospitals_list_secondaryPhoneNumber | null;
  /**
   * Organization administrator
   */
  orgAdmin: GetHospitalsWithAdmin_hospitals_list_orgAdmin;
}

export interface GetHospitalsWithAdmin_hospitals {
  __typename: "HospitalResponse";
  totalCount: number;
  list: GetHospitalsWithAdmin_hospitals_list[];
}

export interface GetHospitalsWithAdmin {
  hospitals: GetHospitalsWithAdmin_hospitals;
}

export interface GetHospitalsWithAdminVariables {
  filterOptions?: HospitalFilterInput | null;
  startDate?: string | null;
  endDate?: string | null;
  planId?: string | null;
}
