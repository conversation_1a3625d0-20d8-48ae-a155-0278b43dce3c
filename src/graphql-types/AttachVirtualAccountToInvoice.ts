/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { VirtualAccountProvider, InvoiceStatus, PercentOrAmount, Currency } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AttachVirtualAccountToInvoice
// ====================================================

export interface AttachVirtualAccountToInvoice_attachVirtualAccountToInvoice_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface AttachVirtualAccountToInvoice_attachVirtualAccountToInvoice_updatedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface AttachVirtualAccountToInvoice_attachVirtualAccountToInvoice_recipient {
  __typename: "InvoiceRecipient";
  address: string | null;
  email: string | null;
  name: string;
  phone: string | null;
  clinifyId: string | null;
}

export interface AttachVirtualAccountToInvoice_attachVirtualAccountToInvoice_senderHospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
  facilityLogo: string | null;
  website: string | null;
}

export interface AttachVirtualAccountToInvoice_attachVirtualAccountToInvoice_virtualAccount {
  __typename: "VirtualBankAccountModel";
  id: string;
  currency: Currency;
  updatedDate: any;
  createdDate: any;
  accountName: string;
  accountNumber: string;
  bank: VirtualAccountProvider;
  expiryDate: any | null;
}

export interface AttachVirtualAccountToInvoice_attachVirtualAccountToInvoice_employeesDetails_dependents {
  __typename: "DependentInfo";
  fullName: string;
  enrolleeId: string | null;
  relationship: string | null;
}

export interface AttachVirtualAccountToInvoice_attachVirtualAccountToInvoice_employeesDetails {
  __typename: "EmployeesInfo";
  enrolleeId: string | null;
  fullName: string;
  planCategory: string | null;
  planType: string | null;
  planAmount: number | null;
  paymentFrequency: string | null;
  planStartDate: any | null;
  planDueDate: any | null;
  planStatus: string | null;
  isCovered: boolean | null;
  dependents: AttachVirtualAccountToInvoice_attachVirtualAccountToInvoice_employeesDetails_dependents[] | null;
}

export interface AttachVirtualAccountToInvoice_attachVirtualAccountToInvoice_sponsorDetails {
  __typename: "SponsorEnrolleeDetails";
  status: string | null;
  fullName: string | null;
  isCovered: boolean | null;
  memberNumber: string | null;
  planDueDate: any | null;
  planStartDate: any | null;
}

export interface AttachVirtualAccountToInvoice_attachVirtualAccountToInvoice {
  __typename: "InvoiceModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  discountPercentage: number | null;
  discountAmount: number | null;
  profileId: string | null;
  employerId: string | null;
  creatorId: string;
  description: string | null;
  subTotal: number;
  /**
   * Returns total amount in lowest denomination
   */
  totalAmount: number;
  createdBy: AttachVirtualAccountToInvoice_attachVirtualAccountToInvoice_createdBy;
  lastModifierId: string | null;
  updatedBy: AttachVirtualAccountToInvoice_attachVirtualAccountToInvoice_updatedBy | null;
  additionalNote: string | null;
  amountPaid: number;
  bankTransactionIds: string[] | null;
  dueDate: any;
  invoiceReference: string;
  invoiceStatus: InvoiceStatus;
  issueDate: any;
  paymentDate: any | null;
  sponsorName: string | null;
  sponsorRef: string | null;
  nextYearlyPremium: number | null;
  sponsorLivesCovered: number | null;
  agencyLivesCovered: number | null;
  sponsorPremiumPerLives: number | null;
  recipient: AttachVirtualAccountToInvoice_attachVirtualAccountToInvoice_recipient;
  senderHospitalId: string | null;
  senderHospital: AttachVirtualAccountToInvoice_attachVirtualAccountToInvoice_senderHospital;
  professionalFeeAmount: number | null;
  professionalFeePercentage: number | null;
  controlledDiscount: PercentOrAmount | null;
  controlledProfessionalFee: PercentOrAmount | null;
  vatPercentage: number | null;
  vatAmount: number | null;
  controlledVat: PercentOrAmount | null;
  virtualAccount: AttachVirtualAccountToInvoice_attachVirtualAccountToInvoice_virtualAccount | null;
  employeesDetails: AttachVirtualAccountToInvoice_attachVirtualAccountToInvoice_employeesDetails[] | null;
  sponsorDetails: AttachVirtualAccountToInvoice_attachVirtualAccountToInvoice_sponsorDetails[] | null;
  paymentFrequency: string | null;
  plasticIdCardCount: number | null;
  plasticIdCardAmount: number | null;
  laminatedIdCardCount: number | null;
  laminatedIdCardAmount: number | null;
  creatorName: string;
  lastModifierName: string | null;
  periodStartDate: any | null;
  periodEndDate: any | null;
}

export interface AttachVirtualAccountToInvoice {
  attachVirtualAccountToInvoice: AttachVirtualAccountToInvoice_attachVirtualAccountToInvoice;
}

export interface AttachVirtualAccountToInvoiceVariables {
  id: string;
  virtualAccountProvider?: VirtualAccountProvider | null;
}
