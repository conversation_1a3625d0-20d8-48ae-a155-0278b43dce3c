/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealLabourAndDeliveryFourthStageGeneralConditionOfTheMother
// ====================================================

export interface ConcealLabourAndDeliveryFourthStageGeneralConditionOfTheMother_concealLabourAndDeliveryFourthStageGeneralConditionOfTheMother {
  __typename: "LabourDeliveryModel";
  id: string;
  concealFourthStageGeneralConditionOfTheMother: boolean | null;
  fourthStageGeneralConditionOfTheMother: string | null;
}

export interface ConcealLabourAndDeliveryFourthStageGeneralConditionOfTheMother {
  concealLabourAndDeliveryFourthStageGeneralConditionOfTheMother: ConcealLabourAndDeliveryFourthStageGeneralConditionOfTheMother_concealLabourAndDeliveryFourthStageGeneralConditionOfTheMother;
}

export interface ConcealLabourAndDeliveryFourthStageGeneralConditionOfTheMotherVariables {
  id: string;
  concealStatus: boolean;
}
