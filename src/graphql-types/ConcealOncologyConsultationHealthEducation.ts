/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealOncologyConsultationHealthEducation
// ====================================================

export interface ConcealOncologyConsultationHealthEducation_concealOncologyConsultationHealthEducation {
  __typename: "OncologyConsultationHistoryModel";
  id: string;
  concealHealthEducation: boolean | null;
  healthEducation: string | null;
}

export interface ConcealOncologyConsultationHealthEducation {
  concealOncologyConsultationHealthEducation: ConcealOncologyConsultationHealthEducation_concealOncologyConsultationHealthEducation;
}

export interface ConcealOncologyConsultationHealthEducationVariables {
  id: string;
  concealStatus: boolean;
}
