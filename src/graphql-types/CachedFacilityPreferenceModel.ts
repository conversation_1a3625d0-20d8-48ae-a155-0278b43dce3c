/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { CommissionPayer } from "./globalTypes";

// ====================================================
// GraphQL fragment: CachedFacilityPreferenceModel
// ====================================================

export interface CachedFacilityPreferenceModel {
  __typename: "FacilityPreferenceModel";
  id: string;
  commissionPayer: CommissionPayer | null;
}
