/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Action, Subject } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: organizationPermissionGrantedSubs
// ====================================================

export interface organizationPermissionGrantedSubs_OrganizationPermissionGranted_rules_conditions {
  __typename: "ConditionModel";
  organizationId: string | null;
}

export interface organizationPermissionGrantedSubs_OrganizationPermissionGranted_rules {
  __typename: "PermissionModelInput";
  action: Action;
  subject: Subject;
  conditions: organizationPermissionGrantedSubs_OrganizationPermissionGranted_rules_conditions | null;
}

export interface organizationPermissionGrantedSubs_OrganizationPermissionGranted {
  __typename: "PermissionModel";
  id: string;
  rules: organizationPermissionGrantedSubs_OrganizationPermissionGranted_rules[] | null;
}

export interface organizationPermissionGrantedSubs {
  OrganizationPermissionGranted: organizationPermissionGrantedSubs_OrganizationPermissionGranted;
}

export interface organizationPermissionGrantedSubsVariables {
  recipientId: string;
}
