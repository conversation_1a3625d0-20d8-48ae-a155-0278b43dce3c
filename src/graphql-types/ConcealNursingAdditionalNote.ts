/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealNursingAdditionalNote
// ====================================================

export interface ConcealNursingAdditionalNote_concealNursingServiceAdditionalNote {
  __typename: "NursingServiceModel";
  id: string;
  concealAdditionalNote: boolean | null;
  additionalNote: string | null;
}

export interface ConcealNursingAdditionalNote {
  concealNursingServiceAdditionalNote: ConcealNursingAdditionalNote_concealNursingServiceAdditionalNote;
}

export interface ConcealNursingAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
}
