/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MedicationOptionType } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: MedicationBundleItemUpdated
// ====================================================

export interface MedicationBundleItemUpdated_MedicationBundleItemUpdated_medicationConsumables {
  __typename: "MedicationConsumables";
  drugInventoryId: string | null;
  name: string | null;
  quantity: string | null;
  unitPrice: number | null;
  inventoryClass: string | null;
}

export interface MedicationBundleItemUpdated_MedicationBundleItemUpdated_priceDetails {
  __typename: "MedPriceDetailInputType";
  type: string | null;
  name: string | null;
  pricePerUnit: string | null;
  patientType: string | null;
  paymentType: string | null;
  quantity: string | null;
}

export interface MedicationBundleItemUpdated_MedicationBundleItemUpdated_diagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface MedicationBundleItemUpdated_MedicationBundleItemUpdated {
  __typename: "MedicationBundleItemModel";
  id: string;
  administrationMethod: string | null;
  bank: string | null;
  bundleDate: any;
  createdDate: any;
  creatorId: string | null;
  creatorName: string | null;
  dosage: string | null;
  dosageUnit: string | null;
  duration: string | null;
  frequency: string | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
  medicationCategory: string | null;
  drugInventoryId: string | null;
  inventoryClass: string | null;
  medicationConsumables: MedicationBundleItemUpdated_MedicationBundleItemUpdated_medicationConsumables[] | null;
  priceDetails: MedicationBundleItemUpdated_MedicationBundleItemUpdated_priceDetails | null;
  provider: string | null;
  medicationName: string | null;
  option: MedicationOptionType | null;
  purpose: string | null;
  quantity: string | null;
  unitPrice: string | null;
  updatedDate: any;
  prescriptionNote: string | null;
  medicationType: string | null;
  diagnosis: MedicationBundleItemUpdated_MedicationBundleItemUpdated_diagnosis[] | null;
  medicationBundleId: string;
}

export interface MedicationBundleItemUpdated {
  MedicationBundleItemUpdated: MedicationBundleItemUpdated_MedicationBundleItemUpdated;
}

export interface MedicationBundleItemUpdatedVariables {
  profileId: string;
}
