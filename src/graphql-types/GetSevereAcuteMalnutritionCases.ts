/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { CasesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetSevereAcuteMalnutritionCases
// ====================================================

export interface GetSevereAcuteMalnutritionCases_getSevereAcuteMalnutritionCases_ageRanges {
  __typename: "CategoryCount";
  category: string | null;
  count: number | null;
}

export interface GetSevereAcuteMalnutritionCases_getSevereAcuteMalnutritionCases {
  __typename: "CasesSummary";
  name: number | null;
  totalFemale: number | null;
  totalMale: number | null;
  ageRanges: GetSevereAcuteMalnutritionCases_getSevereAcuteMalnutritionCases_ageRanges[] | null;
}

export interface GetSevereAcuteMalnutritionCases {
  getSevereAcuteMalnutritionCases: GetSevereAcuteMalnutritionCases_getSevereAcuteMalnutritionCases[];
}

export interface GetSevereAcuteMalnutritionCasesVariables {
  filter?: CasesAnalyticsFilter | null;
}
