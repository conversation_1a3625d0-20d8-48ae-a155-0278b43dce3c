/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { StaffActivitiesSummeryFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetStaffActivitiesSummaryList
// ====================================================

export interface GetStaffActivitiesSummaryList_getStaffActivitiesSummery_list {
  __typename: "StaffActivitiesSummery";
  id: string;
  patientId: string;
  serviceIds: string[];
  clinifyId: string;
  fullName: string;
  age: string | null;
  gender: string | null;
  serviceTypes: string[] | null;
  serviceDates: string[] | null;
  billIds: string[] | null;
  billStatuses: string[] | null;
}

export interface GetStaffActivitiesSummaryList_getStaffActivitiesSummery {
  __typename: "StaffActivitiesSummeryResponse";
  totalCount: number | null;
  list: GetStaffActivitiesSummaryList_getStaffActivitiesSummery_list[] | null;
}

export interface GetStaffActivitiesSummaryList {
  getStaffActivitiesSummery: GetStaffActivitiesSummaryList_getStaffActivitiesSummery;
}

export interface GetStaffActivitiesSummaryListVariables {
  filter: StaffActivitiesSummeryFilter;
}
