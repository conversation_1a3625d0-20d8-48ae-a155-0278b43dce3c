/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealAnthropometryAdditionalNote
// ====================================================

export interface ConcealAnthropometryAdditionalNote_concealAnthropometryAdditionalNote {
  __typename: "AnthropometryModel";
  id: string;
  concealAdditionalNote: boolean | null;
  additionalNote: string | null;
}

export interface ConcealAnthropometryAdditionalNote {
  concealAnthropometryAdditionalNote: ConcealAnthropometryAdditionalNote_concealAnthropometryAdditionalNote;
}

export interface ConcealAnthropometryAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
  clinifyId: string;
}
