/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ClaimAccountHmoClaimApproval
// ====================================================

export interface ClaimAccountHmoClaimApproval_claimFinanceApproval_financeApproval {
  __typename: "ClaimsAccountApprovalType";
  createdDate: any;
  creatorId: string;
  creatorName: string;
  approvalGroup: string;
}

export interface ClaimAccountHmoClaimApproval_claimFinanceApproval {
  __typename: "HmoClaimModel";
  id: string;
  financeApproval: ClaimAccountHmoClaimApproval_claimFinanceApproval_financeApproval[] | null;
}

export interface ClaimAccountHmoClaimApproval {
  claimFinanceApproval: ClaimAccountHmoClaimApproval_claimFinanceApproval[];
}

export interface ClaimAccountHmoClaimApprovalVariables {
  ids: string[];
  status: boolean;
}
