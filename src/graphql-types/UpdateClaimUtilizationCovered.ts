/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: UpdateClaimUtilizationCovered
// ====================================================

export interface UpdateClaimUtilizationCovered_updateClaimUtilizationCovered {
  __typename: "PreAuthUtilisationsModel";
  id: string;
  percentageCovered: number | null;
  amountCovered: number | null;
}

export interface UpdateClaimUtilizationCovered {
  updateClaimUtilizationCovered: UpdateClaimUtilizationCovered_updateClaimUtilizationCovered;
}

export interface UpdateClaimUtilizationCoveredVariables {
  claimId: string;
  utilizationId: string;
  percentageCovered?: number | null;
  amountCovered?: number | null;
}
