/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Gender } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: VirtualCareAppointmentAddedSubs
// ====================================================

export interface VirtualCareAppointmentAddedSubs_VirtualCareAppointmentAdded_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface VirtualCareAppointmentAddedSubs_VirtualCareAppointmentAdded_specialists_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  speciality: string | null;
}

export interface VirtualCareAppointmentAddedSubs_VirtualCareAppointmentAdded_specialists {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
  personalInformation: VirtualCareAppointmentAddedSubs_VirtualCareAppointmentAdded_specialists_personalInformation | null;
}

export interface VirtualCareAppointmentAddedSubs_VirtualCareAppointmentAdded_patientProfile_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
  dateOfBirth: any | null;
  displayPictureUrl: string | null;
  bloodGroup: string | null;
}

export interface VirtualCareAppointmentAddedSubs_VirtualCareAppointmentAdded_patientProfile_user {
  __typename: "UserModel";
  id: string;
  nonCorporateEmail: string | null;
  phoneNumber: string | null;
  country: string | null;
}

export interface VirtualCareAppointmentAddedSubs_VirtualCareAppointmentAdded_patientProfile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  gender: Gender | null;
  clinifyId: string;
  personalInformation: VirtualCareAppointmentAddedSubs_VirtualCareAppointmentAdded_patientProfile_personalInformation | null;
  user: VirtualCareAppointmentAddedSubs_VirtualCareAppointmentAdded_patientProfile_user;
}

export interface VirtualCareAppointmentAddedSubs_VirtualCareAppointmentAdded {
  __typename: "VirtualCareAppointmentModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  liveSessionUrl: string | null;
  category: string | null;
  appointmentDate: any;
  startDateTime: any | null;
  endDateTime: any | null;
  priority: string | null;
  duration: string | null;
  patientType: string | null;
  paymentType: string | null;
  hospital: VirtualCareAppointmentAddedSubs_VirtualCareAppointmentAdded_hospital | null;
  specialists: VirtualCareAppointmentAddedSubs_VirtualCareAppointmentAdded_specialists[] | null;
  patientProfile: VirtualCareAppointmentAddedSubs_VirtualCareAppointmentAdded_patientProfile | null;
}

export interface VirtualCareAppointmentAddedSubs {
  VirtualCareAppointmentAdded: VirtualCareAppointmentAddedSubs_VirtualCareAppointmentAdded;
}

export interface VirtualCareAppointmentAddedSubsVariables {
  hospitalId?: string | null;
  profileId: string;
}
