/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { SponsorBillingInformationInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddSponsorBillingInformation
// ====================================================

export interface AddSponsorBillingInformation_addSponsorBillingInformation {
  __typename: "SponsorBillingInformationModel";
  id: string;
  accountName: string;
  accountNumber: string;
  bankName: string;
  branchName: string | null;
  bvn: string | null;
  sponsorName: string | null;
  hospitalId: string;
  createdDate: any;
  updatedDate: any;
  creatorName: string | null;
  lastModifierName: string | null;
}

export interface AddSponsorBillingInformation {
  addSponsorBillingInformation: AddSponsorBillingInformation_addSponsorBillingInformation;
}

export interface AddSponsorBillingInformationVariables {
  hospitalId: string;
  input: SponsorBillingInformationInput;
}
