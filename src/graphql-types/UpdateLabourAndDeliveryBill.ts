/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { LabourDeliveryInput, BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateLabourAndDeliveryBill
// ====================================================

export interface UpdateLabourAndDeliveryBill_updateLabourAndDeliveryBill_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  paymentType: string | null;
  patientType: string | null;
  reference: string | null;
}

export interface UpdateLabourAndDeliveryBill_updateLabourAndDeliveryBill_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface UpdateLabourAndDeliveryBill_updateLabourAndDeliveryBill_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: UpdateLabourAndDeliveryBill_updateLabourAndDeliveryBill_profile_coverageDetails_provider | null;
}

export interface UpdateLabourAndDeliveryBill_updateLabourAndDeliveryBill_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: UpdateLabourAndDeliveryBill_updateLabourAndDeliveryBill_profile_coverageDetails[] | null;
}

export interface UpdateLabourAndDeliveryBill_updateLabourAndDeliveryBill_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface UpdateLabourAndDeliveryBill_updateLabourAndDeliveryBill_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: UpdateLabourAndDeliveryBill_updateLabourAndDeliveryBill_preauthorizationDetails_provider | null;
}

export interface UpdateLabourAndDeliveryBill_updateLabourAndDeliveryBill_hmoClaim {
  __typename: "HmoClaimModel";
  id: string;
}

export interface UpdateLabourAndDeliveryBill_updateLabourAndDeliveryBill {
  __typename: "LabourDeliveryModel";
  id: string;
  provider: string | null;
  visitationDateTime: any | null;
  gestationAge: string | null;
  estimatedDeliveryDate: any | null;
  induction: string | null;
  inductionDateTime: any | null;
  inducedBy: string | null;
  inducedMethod: string | null;
  preTerm: string | null;
  bloodGroup: string | null;
  decisionSeekingCare: string | null;
  modeOfTransportation: string | null;
  postAbortionCare: string | null;
  admittedForComplicationsOfUnsafeAbortion: string | null;
  abortion: string | null;
  parity: string | null;
  membranesRuptureDateTime: any | null;
  firstStageStartDateTime: any | null;
  firstStageDuration: string | null;
  firstStageSupportPersonPresent: string | null;
  firstStageEpiduralGiven: string | null;
  partographUsed: string | null;
  firstStageMaternalBloodPressure: string | null;
  firstStageFetalHeartRate: string | null;
  firstStageFetalMonitoring: string | null;
  firstStagePresentation: string | null;
  firstStageSpecifyBreech: string | null;
  firstStagePosition: string | null;
  firstStageLie: string | null;
  firstStageFetalMovement: string | null;
  augmentation: string | null;
  augmentationMethod: string | null;
  firstStageMotherStatus: string | null;
  firstStageMdaConducted: string | null;
  firstStageMotherCauseOfDeath: string | null;
  firstStageBabyStatus: string | null;
  firstStageDoctorName: string | null;
  firstStageSpecialty: string | null;
  firstStageRank: string | null;
  firstStageDepartment: string | null;
  firstStageNurseName: string | null;
  firstStageMidWife: string | null;
  firstStageAdditionalNote: string | null;
  concealFirstStageAdditionalNote: boolean | null;
  secondStageStartDateTime: any | null;
  secondStageBabyDeliveryDateTime: any | null;
  secondStageDuration: string | null;
  secondStageSupportPersonPresent: string | null;
  secondStageEpiduralGiven: string | null;
  secondStageDeliveryMethod: string | null;
  placeOfBirth: string | null;
  referredOut: string | null;
  admitted: string | null;
  discharged: string | null;
  secondStageMaternalBloodPressure: string | null;
  secondStageFetalHeartRate: string | null;
  secondStagePresentation: string | null;
  secondStageSpecifyBreech: string | null;
  secondStagePosition: string | null;
  secondStageLie: string | null;
  secondStageFetalMovement: string | null;
  episiotomy: string | null;
  gender: string | null;
  liveBirthWeight: string | null;
  birthWeight: string | null;
  birthWeightUnit: string | null;
  notBreathingOrCrying: string | null;
  babyResuscitated: string | null;
  cordClampTime: string | null;
  chxGelApplied: string | null;
  secondStageMotherStatus: string | null;
  secondStageMdaConducted: string | null;
  secondStageMotherCauseOfDeath: string | null;
  secondStageBabyStatus: string | null;
  stillBirth: string | null;
  liveHivBirth: string | null;
  deliveredBy: string | null;
  secondStageDoctorName: string | null;
  secondStageSpecialty: string | null;
  secondStageRank: string | null;
  secondStageDepartment: string | null;
  secondStageNurseName: string | null;
  secondStageMidWife: string | null;
  secondStageMarternalComplication: string | null;
  secondStageAdditionalNote: string | null;
  concealSecondStageAdditionalNote: boolean | null;
  thirdStageStartDateTime: any | null;
  placentaDateTime: any | null;
  thirdStageDuration: string | null;
  placentaDeliveryMethod: string | null;
  placentaComplete: string | null;
  configuration: string | null;
  bloodLossEstimate: string | null;
  osytocinReceiveed: string | null;
  misoprostolReceived: string | null;
  receivedMgso4WithEclampsia: string | null;
  cervicalTear: string | null;
  perinealLaceration: string | null;
  birthInjury: string | null;
  otherTrauma: string | null;
  repair: string | null;
  specifyRepair: string | null;
  anesthesiaType: string | null;
  anesthesiaGiven: string | null;
  thirdStageMotherStatus: string | null;
  thirdStageMdaConducted: string | null;
  thirdStageMotherCauseOfDeath: string | null;
  thirdStageBabyStatus: string | null;
  thirdStageDoctorName: string | null;
  thirdStageSpecialty: string | null;
  thirdStageRank: string | null;
  thirdStageDepartment: string | null;
  thirdStageNurseName: string | null;
  thirdStageMidWife: string | null;
  thirdStageAdditionalNote: string | null;
  concealThirdStageAdditionalNote: boolean | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  appointmentId: string | null;
  hmoProviderId: string | null;
  typeOfClient: string | null;
  currentPregnancyNumber: string | null;
  firstStageMaternalTemperature: string | null;
  firstStageMaternalTemperatureUnit: string | null;
  firstStageMaternalPulseRate: string | null;
  firstStageMaternalRespiratoryRate: string | null;
  secondStageMaternalTemperature: string | null;
  secondStageMaternalTemperatureUnit: string | null;
  secondStageMaternalPulseRate: string | null;
  secondStageMaternalRespiratoryRate: string | null;
  secondStageMouthAndNoseSuctioned: string | null;
  babyCauseOfDeath: string | null;
  secondStageHeadCircumferenceOfBaby: string | null;
  secondStageChestCircumferenceOfBaby: string | null;
  secondStageBodyLengthOfBaby: string | null;
  secondStageBabyRespiratoryRate: string | null;
  secondStageBabyHeartRate: string | null;
  secondStageTemperatureAt1hr: string | null;
  secondStageTemperatureAt1hrUnit: string | null;
  fourthStagePostpartumComplications: string | null;
  babyPutToBreast: string | null;
  fourthStageTimeOfInitiatingBreastfeeding: string | null;
  fourthStagePostDeliveryMassageGiven: string | null;
  fourthStageCervicalTear: string | null;
  fourthStagePerinealLaceration: string | null;
  fourthStageBirthInjury: string | null;
  fourthStageVulvaCareGiven: string | null;
  fourthStageOtherTrauma: string | null;
  fourthStageRepair: string | null;
  fourthStageSpecifyRepair: string | null;
  fourthStageAnesthesiaType: string | null;
  fourthStageAnesthesiaGiven: string | null;
  fourthStageVitaminKGivenToBaby: string | null;
  fourthStageVitaminKDoseGivenToBaby: string | null;
  fourthStageMaternalTemperature: string | null;
  fourthStageMaternalTemperatureUnit: string | null;
  fourthStageMaternalPulseRate: string | null;
  fourthStageBabyTemperature: string | null;
  fourthStageBabyTemperatureUnit: string | null;
  fourthStageBabyHeartRate: string | null;
  fourthStageBabyRespiratoryRate: string | null;
  fourthStageStatusOfMother: string | null;
  fourthStageMdaConducted: string | null;
  fourthStageMotherCauseOfDeath: string | null;
  fourthStageStatusOfBaby: string | null;
  fourthStagePostpartumDepression: string | null;
  dangerSigns: string | null;
  firstDoseOfAntibioticsAndReferred: string | null;
  neonatalTetanus: string | null;
  neonatalJaundice: string | null;
  admittedOnKMC: string | null;
  dischargedAfterKMC: string | null;
  givenLlin: string | null;
  birthCertificateIssued: string | null;
  birthCertificateCollected: string | null;
  fourthStageApgarScoreA: string | null;
  fourthStageApgarScoreP: string | null;
  fourthStageApgarScoreG: string | null;
  fourthStageApgarScoreApperance: string | null;
  fourthStageApgarScoreR: string | null;
  fourthStageApgarScore: string | null;
  fourthStageCordCare: string | null;
  fourthStageGeneralConditionOfTheBaby: string | null;
  fourthStageGeneralConditionOfTheMother: string | null;
  fourthStageDoctorName: string | null;
  fourthStageSpecialty: string | null;
  fourthStageRank: string | null;
  fourthStageDepartment: string | null;
  fourthStageNurseName: string | null;
  fourthStageMidWife: string | null;
  fourthStageAdditionalNote: string | null;
  concealFourthStageCordCare: boolean | null;
  concealFourthStageAdditionalNote: boolean | null;
  concealFourthStageGeneralConditionOfTheBaby: boolean | null;
  concealFourthStageGeneralConditionOfTheMother: boolean | null;
  serviceDetails: UpdateLabourAndDeliveryBill_updateLabourAndDeliveryBill_serviceDetails[] | null;
  isPackage: boolean;
  billStatus: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  hospitalId: string | null;
  profileId: string | null;
  profile: UpdateLabourAndDeliveryBill_updateLabourAndDeliveryBill_profile | null;
  preauthorizationDetails: UpdateLabourAndDeliveryBill_updateLabourAndDeliveryBill_preauthorizationDetails | null;
  hmoClaim: UpdateLabourAndDeliveryBill_updateLabourAndDeliveryBill_hmoClaim | null;
}

export interface UpdateLabourAndDeliveryBill {
  updateLabourAndDeliveryBill: UpdateLabourAndDeliveryBill_updateLabourAndDeliveryBill;
}

export interface UpdateLabourAndDeliveryBillVariables {
  id: string;
  input: LabourDeliveryInput;
}
