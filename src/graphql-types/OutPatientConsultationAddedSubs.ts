/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: OutPatientConsultationAddedSubs
// ====================================================

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_complaintSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_systemReviewSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_physicalExamSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_profile_coverageDetails_provider | null;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_profile_coverageDetails[] | null;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_provisionalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_finalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_preauthorizationDetails_provider | null;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_allergies {
  __typename: "AllergyModel";
  id: string;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_surgeries {
  __typename: "SurgeryModel";
  id: string;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_admissions {
  __typename: "AdmissionModel";
  id: string;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_labTests {
  __typename: "LabResultModel";
  id: string;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_radiology {
  __typename: "RadiologyResultModel";
  id: string;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_investigations {
  __typename: "LabResultModel";
  id: string;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_nursingServices {
  __typename: "NursingServiceModel";
  id: string;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded_bill {
  __typename: "BillModel";
  id: string;
  createdDate: any;
}

export interface OutPatientConsultationAddedSubs_OutpatientConsultationAdded {
  __typename: "ConsultationModel";
  id: string;
  consultationDateTime: any | null;
  duration: string | null;
  doctorName: string;
  priority: string | null;
  specialty: string | null;
  class: string | null;
  hmoProviderId: string | null;
  serviceDetails: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_serviceDetails[] | null;
  isPackage: boolean;
  clinicName: string | null;
  clinicAddress: string | null;
  complaint: string | null;
  provider: string | null;
  providerServiceName: string | null;
  complaintHistory: string | null;
  healthEducation: string | null;
  systemReview: string | null;
  systemReviewSmartText: string | null;
  complaintGender: string | null;
  complaintSmartSelection: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_complaintSmartSelection | null;
  systemReviewSmartSelection: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_systemReviewSmartSelection | null;
  category: string | null;
  department: string | null;
  physicalExam: string | null;
  physicalExamSmartText: string | null;
  physicalExamSmartSelection: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_physicalExamSmartSelection | null;
  referral: boolean | null;
  referralDate: any | null;
  referralReason: string | null;
  concealReferralReason: boolean | null;
  specialtyReferredTo: string | null;
  profileId: string | null;
  profile: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_profile | null;
  externalReferral: boolean | null;
  provisionalDiagnosis: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_provisionalDiagnosis[] | null;
  finalDiagnosis: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_finalDiagnosis[] | null;
  consultationStartDate: any | null;
  consultationEndDate: any | null;
  appointmentId: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  concealComplaint: boolean | null;
  concealComplaintHistory: boolean | null;
  concealPhysicalExam: boolean | null;
  concealSystemReview: boolean | null;
  concealHealthEducation: boolean | null;
  billStatus: string | null;
  audiometry: string | null;
  concealAudiometry: boolean | null;
  hospitalId: string | null;
  preauthorizationDetails: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_preauthorizationDetails | null;
  allergies: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_allergies[];
  medications: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_medications[];
  surgeries: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_surgeries[];
  admissions: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_admissions[];
  vitals: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_vitals[];
  labTests: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_labTests[];
  radiology: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_radiology[];
  investigations: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_investigations[];
  nursingServices: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_nursingServices[];
  bill: OutPatientConsultationAddedSubs_OutpatientConsultationAdded_bill | null;
}

export interface OutPatientConsultationAddedSubs {
  OutpatientConsultationAdded: OutPatientConsultationAddedSubs_OutpatientConsultationAdded;
}

export interface OutPatientConsultationAddedSubsVariables {
  hospitalId: string;
}
