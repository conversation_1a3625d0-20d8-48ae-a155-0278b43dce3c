/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: AppointmentUnvailabilityUpdatedSub
// ====================================================

export interface AppointmentUnvailabilityUpdatedSub_AppointmentUnvailabilityUpdated_list {
  __typename: "unavailableDates";
  startDate: any;
  endDate: any | null;
  isAllDay: boolean;
  reason: string | null;
}

export interface AppointmentUnvailabilityUpdatedSub_AppointmentUnvailabilityUpdated {
  __typename: "ProfileUnavailableDates";
  id: string;
  list: AppointmentUnvailabilityUpdatedSub_AppointmentUnvailabilityUpdated_list[] | null;
}

export interface AppointmentUnvailabilityUpdatedSub {
  AppointmentUnvailabilityUpdated: AppointmentUnvailabilityUpdatedSub_AppointmentUnvailabilityUpdated;
}

export interface AppointmentUnvailabilityUpdatedSubVariables {
  hospitalId: string;
}
