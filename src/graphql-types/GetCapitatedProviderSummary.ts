/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetCapitatedProviderSummary
// ====================================================

export interface GetCapitatedProviderSummary_getCapitatedProviderSummary {
  __typename: "CapitatedProviderSummary";
  totalCapitatedProvidersCount: number | null;
  totalCapitatedEnrolleeCount: number | null;
  totalCapitatedAmount: number | null;
}

export interface GetCapitatedProviderSummary {
  getCapitatedProviderSummary: GetCapitatedProviderSummary_getCapitatedProviderSummary;
}

export interface GetCapitatedProviderSummaryVariables {
  startDate?: string | null;
  endDate?: string | null;
  planId?: string | null;
  hospitalId?: string | null;
}
