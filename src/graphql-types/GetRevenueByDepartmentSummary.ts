/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FinanceAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetRevenueByDepartmentSummary
// ====================================================

export interface GetRevenueByDepartmentSummary_getRevenueByDepartmentSummary {
  __typename: "FinanceDataSummaryResponse";
  totalAmountDue: number | null;
  totalAmount: number | null;
  category: string;
  name: number | null;
  totalAmountOutstanding: number | null;
  totalAmountPaid: number | null;
  totalDiscountAmount: number | null;
  totalRevenue: number | null;
}

export interface GetRevenueByDepartmentSummary {
  getRevenueByDepartmentSummary: GetRevenueByDepartmentSummary_getRevenueByDepartmentSummary[];
}

export interface GetRevenueByDepartmentSummaryVariables {
  filter: FinanceAnalyticsFilter;
}
