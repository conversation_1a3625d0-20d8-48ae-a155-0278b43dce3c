/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NutritionalHistoryGrowthInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateNutritionalHistoryGrowth
// ====================================================

export interface UpdateNutritionalHistoryGrowth_updateNutritionalHistoryGrowth {
  __typename: "NutritionalHistoryGrowthModel";
  id: string;
  months: number;
  years: number;
  weight: string | null;
  height: string | null;
  babyLength: string | null;
  headCircumference: string | null;
  measurementType: string;
}

export interface UpdateNutritionalHistoryGrowth {
  updateNutritionalHistoryGrowth: UpdateNutritionalHistoryGrowth_updateNutritionalHistoryGrowth;
}

export interface UpdateNutritionalHistoryGrowthVariables {
  input: NutritionalHistoryGrowthInput;
  id: string;
}
