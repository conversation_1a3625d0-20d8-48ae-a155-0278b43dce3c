/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ServicesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetRadiologyInvestigationData
// ====================================================

export interface GetRadiologyInvestigationData_getRadiologyInvestigationData_servicesSummary {
  __typename: "ServicesSummary";
  totalRadiology: number | null;
  name: number | null;
}

export interface GetRadiologyInvestigationData_getRadiologyInvestigationData_list_examTypes_male {
  __typename: "ListByPatientName";
  name: string;
  count: number | null;
  totalAmount: number | null;
  quantity: string | null;
}

export interface GetRadiologyInvestigationData_getRadiologyInvestigationData_list_examTypes_female {
  __typename: "ListByPatientName";
  name: string;
  count: number | null;
  totalAmount: number | null;
  quantity: string | null;
}

export interface GetRadiologyInvestigationData_getRadiologyInvestigationData_list_examTypes {
  __typename: "InvestigationExamTypeInputByGender";
  male: GetRadiologyInvestigationData_getRadiologyInvestigationData_list_examTypes_male[] | null;
  female: GetRadiologyInvestigationData_getRadiologyInvestigationData_list_examTypes_female[] | null;
}

export interface GetRadiologyInvestigationData_getRadiologyInvestigationData_list_byExaminationDate {
  __typename: "ListByExaminationDate";
  examinationDate: string | null;
  investigationNames: string[] | null;
  radiographerName: string | null;
}

export interface GetRadiologyInvestigationData_getRadiologyInvestigationData_list_byPatientName {
  __typename: "ListByPatientName";
  patientName: string | null;
  examinationDate: string | null;
  name: string;
  radiographerName: string | null;
  totalAmount: number | null;
  clinifyId: string | null;
  quantity: string | null;
  paymentType: string | null;
}

export interface GetRadiologyInvestigationData_getRadiologyInvestigationData_list {
  __typename: "ServiceSummaryList";
  examTypes: GetRadiologyInvestigationData_getRadiologyInvestigationData_list_examTypes | null;
  byExaminationDate: GetRadiologyInvestigationData_getRadiologyInvestigationData_list_byExaminationDate[] | null;
  byPatientName: GetRadiologyInvestigationData_getRadiologyInvestigationData_list_byPatientName[] | null;
}

export interface GetRadiologyInvestigationData_getRadiologyInvestigationData {
  __typename: "ServiceSummaryWithList";
  servicesSummary: GetRadiologyInvestigationData_getRadiologyInvestigationData_servicesSummary[];
  list: GetRadiologyInvestigationData_getRadiologyInvestigationData_list;
}

export interface GetRadiologyInvestigationData {
  getRadiologyInvestigationData: GetRadiologyInvestigationData_getRadiologyInvestigationData;
}

export interface GetRadiologyInvestigationDataVariables {
  filter?: ServicesAnalyticsFilter | null;
}
