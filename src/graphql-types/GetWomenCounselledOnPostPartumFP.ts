/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FamilyPlanningAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetWomenCounselledOnPostPartumFP
// ====================================================

export interface GetWomenCounselledOnPostPartumFP_getWomenCounselledOnPostPartumFamilyPlanning {
  __typename: "FamilyPlanningSummary";
  name: number | null;
  totalClientCounselled: number | null;
}

export interface GetWomenCounselledOnPostPartumFP {
  getWomenCounselledOnPostPartumFamilyPlanning: GetWomenCounselledOnPostPartumFP_getWomenCounselledOnPostPartumFamilyPlanning;
}

export interface GetWomenCounselledOnPostPartumFPVariables {
  filter?: FamilyPlanningAnalyticsFilter | null;
}
