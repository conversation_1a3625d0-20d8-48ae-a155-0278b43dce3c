/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FacilityBillingInformationInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateFacilityBillingInformation
// ====================================================

export interface UpdateFacilityBillingInformation_updateFacilityBillingInformation {
  __typename: "FacilityBillingInformationModel";
  id: string;
  accountName: string;
  accountNumber: string;
  bankName: string;
  bvn: string | null;
  branchName: string | null;
  isPreferredPayoutAccount: boolean | null;
}

export interface UpdateFacilityBillingInformation {
  updateFacilityBillingInformation: UpdateFacilityBillingInformation_updateFacilityBillingInformation;
}

export interface UpdateFacilityBillingInformationVariables {
  id: string;
  input: FacilityBillingInformationInput;
}
