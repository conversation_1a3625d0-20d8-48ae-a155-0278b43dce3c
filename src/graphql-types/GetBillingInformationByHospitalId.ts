/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetBillingInformationByHospitalId
// ====================================================

export interface GetBillingInformationByHospitalId_billingInformationByHospitalId {
  __typename: "FacilityBillingInformationModel";
  id: string;
  accountName: string;
  accountNumber: string;
  bankName: string;
  bvn: string | null;
  branchName: string | null;
  isPreferredPayoutAccount: boolean | null;
}

export interface GetBillingInformationByHospitalId {
  billingInformationByHospitalId: GetBillingInformationByHospitalId_billingInformationByHospitalId | null;
}

export interface GetBillingInformationByHospitalIdVariables {
  hospitalId: string;
}
