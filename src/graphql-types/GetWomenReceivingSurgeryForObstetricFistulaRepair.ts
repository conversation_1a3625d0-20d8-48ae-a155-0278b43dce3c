/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { CasesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetWomenReceivingSurgeryForObstetricFistulaRepair
// ====================================================

export interface GetWomenReceivingSurgeryForObstetricFistulaRepair_getWomenReceivingSurgeryForObstetricFistulaRepair_ageRanges {
  __typename: "CategoryCount";
  category: string | null;
  count: number | null;
}

export interface GetWomenReceivingSurgeryForObstetricFistulaRepair_getWomenReceivingSurgeryForObstetricFistulaRepair {
  __typename: "CasesSummary";
  name: number | null;
  totalVVF: number | null;
  totalRVF: number | null;
  ageRanges: GetWomenReceivingSurgeryForObstetricFistulaRepair_getWomenReceivingSurgeryForObstetricFistulaRepair_ageRanges[] | null;
}

export interface GetWomenReceivingSurgeryForObstetricFistulaRepair {
  getWomenReceivingSurgeryForObstetricFistulaRepair: GetWomenReceivingSurgeryForObstetricFistulaRepair_getWomenReceivingSurgeryForObstetricFistulaRepair[];
}

export interface GetWomenReceivingSurgeryForObstetricFistulaRepairVariables {
  filter?: CasesAnalyticsFilter | null;
}
