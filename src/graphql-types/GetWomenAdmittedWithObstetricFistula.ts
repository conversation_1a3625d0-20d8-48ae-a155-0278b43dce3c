/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { CasesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetWomenAdmittedWithObstetricFistula
// ====================================================

export interface GetWomenAdmittedWithObstetricFistula_getWomenAdmittedWithObstetricFistula_ageRanges {
  __typename: "CategoryCount";
  category: string | null;
  count: number | null;
}

export interface GetWomenAdmittedWithObstetricFistula_getWomenAdmittedWithObstetricFistula {
  __typename: "CasesSummary";
  name: number | null;
  totalVVF: number | null;
  totalRVF: number | null;
  ageRanges: GetWomenAdmittedWithObstetricFistula_getWomenAdmittedWithObstetricFistula_ageRanges[] | null;
}

export interface GetWomenAdmittedWithObstetricFistula {
  getWomenAdmittedWithObstetricFistula: GetWomenAdmittedWithObstetricFistula_getWomenAdmittedWithObstetricFistula[];
}

export interface GetWomenAdmittedWithObstetricFistulaVariables {
  filter?: CasesAnalyticsFilter | null;
}
