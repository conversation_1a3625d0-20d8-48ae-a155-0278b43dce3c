/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BillStatus, investigationStatus, specimenCollection, PatientType } from "./globalTypes";

// ====================================================
// GraphQL fragment: InvestigationNoAdditionalNote
// ====================================================

export interface InvestigationNoAdditionalNote_testInfo {
  __typename: "InvestigationTestInfoInputType";
  testName: string;
  priority: string | null;
  testCategory: string | null;
  clinicalDiagnosisICD10: string | null;
  clinicalDiagnosisICD11: string | null;
  clinicalDiagnosisSNOMED: string | null;
  ref: string | null;
  specimen: string[] | null;
  loinc: string | null;
  provider: string | null;
  itemId: string | null;
  preauthorizationDetailsId: string | null;
}

export interface InvestigationNoAdditionalNote_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface InvestigationNoAdditionalNote_referringHospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface InvestigationNoAdditionalNote_bill {
  __typename: "BillModel";
  id: string;
  billStatus: BillStatus;
  createdDate: any;
}

export interface InvestigationNoAdditionalNote_examinationType {
  __typename: "InvestigationExamTypeInputType";
  ref: string | null;
  priority: string | null;
  examType: string;
  loinc: string | null;
  provider: string | null;
  itemId: string | null;
  preauthorizationDetailsId: string | null;
  indication: string | null;
}

export interface InvestigationNoAdditionalNote_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface InvestigationNoAdditionalNote_labResult_details_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface InvestigationNoAdditionalNote_labResult_details_testResults {
  __typename: "TestResultsType";
  name: string | null;
  value: string | null;
  unit: string | null;
  dropdown: string[] | null;
  box: boolean | null;
  sensitivityBox: boolean | null;
  units: string[] | null;
  dependsOn: string[] | null;
  range: string | null;
  extraValue: string | null;
  hasExtraValue: boolean | null;
  valueTwo: string | null;
  tabular: string[] | null;
  referenceRange: boolean | null;
}

export interface InvestigationNoAdditionalNote_labResult_details_extraTestResults {
  __typename: "TestResultsType";
  name: string | null;
  value: string | null;
  unit: string | null;
  dropdown: string[] | null;
  box: boolean | null;
  sensitivityBox: boolean | null;
  units: string[] | null;
  dependsOn: string[] | null;
  range: string | null;
}

export interface InvestigationNoAdditionalNote_labResult_details {
  __typename: "LabTestDetailType";
  testDate: any | null;
  duration: string | null;
  serviceDetails: InvestigationNoAdditionalNote_labResult_details_serviceDetails | null;
  specimenCollected: specimenCollection | null;
  specimenTypes: string[] | null;
  specimenNumber: string | null;
  natureSiteOfSpecimen: string | null;
  specimenReceivedDate: any | null;
  collectionDate: any | null;
  collectedBy: string | null;
  performedBy: string | null;
  performedBySignature: string | null;
  performedBySignatureType: string | null;
  performedBySignatureDateTime: any | null;
  verifiedBy: string | null;
  verifiedBySignature: string | null;
  verifiedBySignatureType: string | null;
  verifiedBySignatureDateTime: any | null;
  resultDate: any | null;
  pathologistName: string | null;
  pathologistSignature: string | null;
  pathologistSignatureType: string | null;
  pathologistSignatureDateTime: any | null;
  pathologistReport: string | null;
  conclusion: string | null;
  microscopicExam: string | null;
  grossExam: string | null;
  pertinentHistory: string | null;
  reportDate: any | null;
  finalReportDate: any | null;
  done: boolean | null;
  testName: string | null;
  additionalNote: string | null;
  testResults: InvestigationNoAdditionalNote_labResult_details_testResults[] | null;
  extraTestResults: InvestigationNoAdditionalNote_labResult_details_extraTestResults[] | null;
}

export interface InvestigationNoAdditionalNote_labResult {
  __typename: "LabResultModel";
  id: string;
  documentUrl: string[] | null;
  details: InvestigationNoAdditionalNote_labResult_details[] | null;
  facilityName: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean;
  facilityAddress: string | null;
}

export interface InvestigationNoAdditionalNote_radiologyResult_details_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface InvestigationNoAdditionalNote_radiologyResult_details {
  __typename: "RadiologyExamDetailType";
  examinationDate: any | null;
  duration: string | null;
  patientType: PatientType | null;
  paymentType: string | null;
  serviceDetails: InvestigationNoAdditionalNote_radiologyResult_details_serviceDetails | null;
  radiographerName: string | null;
  radiographerSignature: string | null;
  radiographerSignatureType: string | null;
  radiographerSignatureDateTime: any | null;
  examinationNumber: string | null;
  indication: string | null;
  comparison: string | null;
  technique: string | null;
  radiographerReport: string | null;
  impression: string | null;
  done: boolean | null;
  examType: string | null;
  contrastConfirmed: boolean | null;
  radiologistName: string | null;
  radiologistSignature: string | null;
  radiologistSignatureType: string | null;
  radiologistSignatureDateTime: any | null;
  verifiedBy: string | null;
  verifiedBySignature: string | null;
  verifiedBySignatureType: string | null;
  verifiedBySignatureDateTime: any | null;
}

export interface InvestigationNoAdditionalNote_radiologyResult {
  __typename: "RadiologyResultModel";
  id: string;
  documentUrl: string[] | null;
  details: InvestigationNoAdditionalNote_radiologyResult_details[] | null;
  facilityName: string | null;
  facilityAddress: string | null;
}

export interface InvestigationNoAdditionalNote {
  __typename: "InvestigationModel";
  id: string;
  requestType: string;
  requestDate: any | null;
  clinifyId: string | null;
  testInfo: InvestigationNoAdditionalNote_testInfo[] | null;
  priority: string | null;
  serviceDetails: InvestigationNoAdditionalNote_serviceDetails[] | null;
  isPackage: boolean;
  orderedBy: string | null;
  specialty: string | null;
  rank: string | null;
  department: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  external: boolean;
  referringHospital: InvestigationNoAdditionalNote_referringHospital | null;
  bill: InvestigationNoAdditionalNote_bill | null;
  examinationType: InvestigationNoAdditionalNote_examinationType[] | null;
  clinicalHistory: string | null;
  status: investigationStatus | null;
  isRequested: boolean | null;
  documentUrl: string[] | null;
  radiologyContrastConfirmation: boolean | null;
  hospital: InvestigationNoAdditionalNote_hospital | null;
  labResult: InvestigationNoAdditionalNote_labResult | null;
  radiologyResult: InvestigationNoAdditionalNote_radiologyResult | null;
  billStatus: string | null;
}
