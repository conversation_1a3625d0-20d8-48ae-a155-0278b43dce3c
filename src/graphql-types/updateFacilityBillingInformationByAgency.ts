/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FacilityBillingInformationInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: updateFacilityBillingInformationByAgency
// ====================================================

export interface updateFacilityBillingInformationByAgency_updateFacilityBillingInformationByAgency {
  __typename: "FacilityBillingInformationModel";
  id: string;
  accountName: string;
  accountNumber: string;
  bankName: string;
  bvn: string | null;
  branchName: string | null;
  isPreferredPayoutAccount: boolean | null;
}

export interface updateFacilityBillingInformationByAgency {
  updateFacilityBillingInformationByAgency: updateFacilityBillingInformationByAgency_updateFacilityBillingInformationByAgency;
}

export interface updateFacilityBillingInformationByAgencyVariables {
  hospitalId: string;
  input: FacilityBillingInformationInput;
}
