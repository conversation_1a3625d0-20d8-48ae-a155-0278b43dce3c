/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: PreExistingConditionAddedSubs
// ====================================================

export interface PreExistingConditionAddedSubs_PreExistingConditionAdded_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface PreExistingConditionAddedSubs_PreExistingConditionAdded {
  __typename: "PreExistingConditionModel";
  id: string;
  ageOfOnset: string | null;
  diagnosedDate: any | null;
  duration: string | null;
  additionalNote: string | null;
  conditionICD10: string | null;
  conditionICD11: string | null;
  conditionSNOMED: string | null;
  profile: PreExistingConditionAddedSubs_PreExistingConditionAdded_profile | null;
}

export interface PreExistingConditionAddedSubs {
  PreExistingConditionAdded: PreExistingConditionAddedSubs_PreExistingConditionAdded;
}

export interface PreExistingConditionAddedSubsVariables {
  profileId: string;
}
