/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BusinessRuleFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetBusinessRuleUtilizationTypes
// ====================================================

export interface GetBusinessRuleUtilizationTypes {
  getBusinessRuleUtilizationTypes: string[];
}

export interface GetBusinessRuleUtilizationTypesVariables {
  filterOptions: BusinessRuleFilterInput;
}
