/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { VisitationAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetAverageWaitTimeByDepartment
// ====================================================

export interface GetAverageWaitTimeByDepartment_getAverageWaitTimeByDepartment {
  __typename: "VisitationSummary";
  name: number | null;
  category: string | null;
  totalWaitTime: number | null;
}

export interface GetAverageWaitTimeByDepartment {
  getAverageWaitTimeByDepartment: GetAverageWaitTimeByDepartment_getAverageWaitTimeByDepartment[];
}

export interface GetAverageWaitTimeByDepartmentVariables {
  filter?: VisitationAnalyticsFilter | null;
}
