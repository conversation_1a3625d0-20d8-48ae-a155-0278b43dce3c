/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ArchivePreauthorizationReferrals
// ====================================================

export interface ArchivePreauthorizationReferrals_archivePreauthorizationReferrals_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface ArchivePreauthorizationReferrals_archivePreauthorizationReferrals_diagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface ArchivePreauthorizationReferrals_archivePreauthorizationReferrals_utilizations_statusHistory {
  __typename: "ReferalStatusHistoryType";
  status: string;
  creatorName: string;
  createdDate: any;
}

export interface ArchivePreauthorizationReferrals_archivePreauthorizationReferrals_utilizations {
  __typename: "PreAuthReferralUtilisationsModel";
  id: string;
  category: string | null;
  type: string | null;
  quantity: string | null;
  /**
   * Value is in Naira
   */
  price: string | null;
  status: string | null;
  paCode: string | null;
  utilizationCode: string | null;
  utilizationId: string | null;
  rhesusFactor: string | null;
  specialty: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusHistory: ArchivePreauthorizationReferrals_archivePreauthorizationReferrals_utilizations_statusHistory[] | null;
}

export interface ArchivePreauthorizationReferrals_archivePreauthorizationReferrals_referredProvider {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
}

export interface ArchivePreauthorizationReferrals_archivePreauthorizationReferrals_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface ArchivePreauthorizationReferrals_archivePreauthorizationReferrals_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  fullName: string;
}

export interface ArchivePreauthorizationReferrals_archivePreauthorizationReferrals {
  __typename: "PreauthorisationReferralModel";
  id: string;
  requestDateTime: any;
  requestedBy: string | null;
  referredBy: string | null;
  serviceType: string;
  serviceTypeCode: string;
  provider: ArchivePreauthorizationReferrals_archivePreauthorizationReferrals_provider;
  serviceName: string | null;
  priority: string | null;
  diagnosis: ArchivePreauthorizationReferrals_archivePreauthorizationReferrals_diagnosis[] | null;
  additionalNote: string | null;
  documentUrl: string[] | null;
  code: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  rank: string | null;
  department: string | null;
  specialty: string | null;
  utilizations: ArchivePreauthorizationReferrals_archivePreauthorizationReferrals_utilizations[] | null;
  totalQuantity: string;
  grandTotal: string;
  profileId: string | null;
  referralProviderRemark: string | null;
  referredProvider: ArchivePreauthorizationReferrals_archivePreauthorizationReferrals_referredProvider | null;
  hospital: ArchivePreauthorizationReferrals_archivePreauthorizationReferrals_hospital | null;
  enrolleeNumber: string | null;
  isExternalPlanType: boolean | null;
  externalPlanTypeId: string | null;
  profile: ArchivePreauthorizationReferrals_archivePreauthorizationReferrals_profile | null;
}

export interface ArchivePreauthorizationReferrals {
  archivePreauthorizationReferrals: ArchivePreauthorizationReferrals_archivePreauthorizationReferrals[];
}

export interface ArchivePreauthorizationReferralsVariables {
  ids: string[];
  archive: boolean;
}
