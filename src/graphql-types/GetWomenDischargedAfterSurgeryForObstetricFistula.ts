/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { CasesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetWomenDischargedAfterSurgeryForObstetricFistula
// ====================================================

export interface GetWomenDischargedAfterSurgeryForObstetricFistula_getWomenDischargedAfterSurgeryForObstetricFistula_ageRanges {
  __typename: "CategoryCount";
  category: string | null;
  count: number | null;
}

export interface GetWomenDischargedAfterSurgeryForObstetricFistula_getWomenDischargedAfterSurgeryForObstetricFistula {
  __typename: "CasesSummary";
  name: number | null;
  totalVVF: number | null;
  totalRVF: number | null;
  ageRanges: GetWomenDischargedAfterSurgeryForObstetricFistula_getWomenDischargedAfterSurgeryForObstetricFistula_ageRanges[] | null;
}

export interface GetWomenDischargedAfterSurgeryForObstetricFistula {
  getWomenDischargedAfterSurgeryForObstetricFistula: GetWomenDischargedAfterSurgeryForObstetricFistula_getWomenDischargedAfterSurgeryForObstetricFistula[];
}

export interface GetWomenDischargedAfterSurgeryForObstetricFistulaVariables {
  filter?: CasesAnalyticsFilter | null;
}
