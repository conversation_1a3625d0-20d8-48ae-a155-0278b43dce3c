/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NursingServiceProgressNoteInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateNursingServicesProgressNotes
// ====================================================

export interface UpdateNursingServicesProgressNotes_updateNursingServicesProgressNote {
  __typename: "NursingServiceProgressNoteModel";
  id: string;
  note: string;
  conceal: boolean | null;
  nursingServiceId: string | null;
}

export interface UpdateNursingServicesProgressNotes {
  updateNursingServicesProgressNote: UpdateNursingServicesProgressNotes_updateNursingServicesProgressNote;
}

export interface UpdateNursingServicesProgressNotesVariables {
  input: NursingServiceProgressNoteInput;
  id: string;
  clinifyId: string;
  pin?: string | null;
}
