/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealConsultationReferralReason
// ====================================================

export interface ConcealConsultationReferralReason_concealConsultationReferralReason {
  __typename: "ConsultationModel";
  id: string;
  concealReferralReason: boolean | null;
  referralReason: string | null;
}

export interface ConcealConsultationReferralReason {
  concealConsultationReferralReason: ConcealConsultationReferralReason_concealConsultationReferralReason;
}

export interface ConcealConsultationReferralReasonVariables {
  id: string;
  concealStatus: boolean;
}
