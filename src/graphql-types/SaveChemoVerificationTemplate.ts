/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NewChemoDiagnosisTemplateInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: SaveChemoVerificationTemplate
// ====================================================

export interface SaveChemoVerificationTemplate_saveChemoDiagnosisTemplate_cycles_investigationDetails {
  __typename: "ChemoInvestigationDetails";
  investigationName: string | null;
  investigationType: string | null;
}

export interface SaveChemoVerificationTemplate_saveChemoDiagnosisTemplate_cycles_drugs {
  __typename: "ChemoDiagnosisDrugTemplate";
  day: string;
  dosage: string | null;
  dosagePercentage: string | null;
  infusionUsed: string | null;
  route: string | null;
  drugName: string;
  drugId: string | null;
  frequency: string | null;
  ref: string;
  note: string | null;
  inventoryClass: string | null;
}

export interface SaveChemoVerificationTemplate_saveChemoDiagnosisTemplate_cycles {
  __typename: "ChemoDiagnosisCycleModel";
  id: string;
  cycleNumber: number;
  investigationDetails: SaveChemoVerificationTemplate_saveChemoDiagnosisTemplate_cycles_investigationDetails[] | null;
  drugs: SaveChemoVerificationTemplate_saveChemoDiagnosisTemplate_cycles_drugs[] | null;
}

export interface SaveChemoVerificationTemplate_saveChemoDiagnosisTemplate {
  __typename: "ChemoDiagnosisTemplateModel";
  id: string;
  combinationName: string;
  creatorName: string | null;
  createdDate: any;
  type: string;
  section: string;
  cycles: SaveChemoVerificationTemplate_saveChemoDiagnosisTemplate_cycles[];
  updatedDate: any;
  lastModifierName: string | null;
}

export interface SaveChemoVerificationTemplate {
  saveChemoDiagnosisTemplate: SaveChemoVerificationTemplate_saveChemoDiagnosisTemplate;
}

export interface SaveChemoVerificationTemplateVariables {
  input: NewChemoDiagnosisTemplateInput;
}
