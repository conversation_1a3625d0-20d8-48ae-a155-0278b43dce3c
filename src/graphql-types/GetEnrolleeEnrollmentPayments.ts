/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { CommissionPayer, PayoutStatus, VirtualAccountProvider } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetEnrolleeEnrollmentPayments
// ====================================================

export interface GetEnrolleeEnrollmentPayments_profile_enrollmentPayments_virtualBankAccount {
  __typename: "VirtualBankAccountModel";
  id: string;
  accountName: string;
  accountNumber: string;
  bank: VirtualAccountProvider;
}

export interface GetEnrolleeEnrollmentPayments_profile_enrollmentPayments {
  __typename: "VirtualServicesPaymentModel";
  id: string;
  amountDue: number;
  amountPaid: number;
  paymentStatus: string;
  commissionPayer: CommissionPayer | null;
  commissionFeeAmount: number | null;
  payoutId: string | null;
  payoutStatus: PayoutStatus;
  createdDate: any;
  updatedDate: any | null;
  registrationHmoProfileId: string | null;
  virtualBankAccount: GetEnrolleeEnrollmentPayments_profile_enrollmentPayments_virtualBankAccount | null;
}

export interface GetEnrolleeEnrollmentPayments_profile {
  __typename: "ProfileModel";
  id: string;
  enrollmentPayments: GetEnrolleeEnrollmentPayments_profile_enrollmentPayments[] | null;
}

export interface GetEnrolleeEnrollmentPayments {
  profile: GetEnrolleeEnrollmentPayments_profile;
}

export interface GetEnrolleeEnrollmentPaymentsVariables {
  clinifyId: string;
}
