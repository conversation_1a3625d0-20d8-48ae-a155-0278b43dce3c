/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { EditHospitalStaffInput, Gender } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateFacilityStaffInformation
// ====================================================

export interface UpdateFacilityStaffInformation_updateFacilityStaffInformation_personalInformation {
  __typename: "PersonalInformation";
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
  rank: string | null;
  speciality: string | null;
  department: string | null;
  userRole: string | null;
}

export interface UpdateFacilityStaffInformation_updateFacilityStaffInformation_user {
  __typename: "UserModel";
  id: string;
  phoneNumber: string | null;
  email: string | null;
}

export interface UpdateFacilityStaffInformation_updateFacilityStaffInformation {
  __typename: "ProfileModel";
  id: string;
  gender: Gender | null;
  title: string | null;
  fullName: string;
  personalInformation: UpdateFacilityStaffInformation_updateFacilityStaffInformation_personalInformation | null;
  user: UpdateFacilityStaffInformation_updateFacilityStaffInformation_user;
}

export interface UpdateFacilityStaffInformation {
  updateFacilityStaffInformation: UpdateFacilityStaffInformation_updateFacilityStaffInformation;
}

export interface UpdateFacilityStaffInformationVariables {
  facilityId: string;
  input: EditHospitalStaffInput;
  profileId: string;
}
