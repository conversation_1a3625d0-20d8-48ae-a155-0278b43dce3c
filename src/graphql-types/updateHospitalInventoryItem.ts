/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HospitalInventoryItemInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: updateHospitalInventoryItem
// ====================================================

export interface updateHospitalInventoryItem_updateHospitalInventoryItem_inventoryItems {
  __typename: "HospitalInventoryItem";
  id: string | null;
  name: string | null;
  description: string | null;
  createdOn: any | null;
  creatorName: string | null;
}

export interface updateHospitalInventoryItem_updateHospitalInventoryItem {
  __typename: "HospitalModel";
  id: string;
  inventoryItems: updateHospitalInventoryItem_updateHospitalInventoryItem_inventoryItems[] | null;
}

export interface updateHospitalInventoryItem {
  updateHospitalInventoryItem: updateHospitalInventoryItem_updateHospitalInventoryItem;
}

export interface updateHospitalInventoryItemVariables {
  inventoryItem: HospitalInventoryItemInput;
  id: string;
}
