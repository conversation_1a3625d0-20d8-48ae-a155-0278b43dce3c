/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetChemoVerificationTemplate
// ====================================================

export interface GetChemoVerificationTemplate_fetchChemoDiagnosisTemplate_cycles_investigationDetails {
  __typename: "ChemoInvestigationDetails";
  investigationName: string | null;
  investigationType: string | null;
}

export interface GetChemoVerificationTemplate_fetchChemoDiagnosisTemplate_cycles_drugs {
  __typename: "ChemoDiagnosisDrugTemplate";
  day: string;
  dosage: string | null;
  dosagePercentage: string | null;
  infusionUsed: string | null;
  route: string | null;
  drugName: string;
  drugId: string | null;
  frequency: string | null;
  ref: string;
  note: string | null;
  inventoryClass: string | null;
}

export interface GetChemoVerificationTemplate_fetchChemoDiagnosisTemplate_cycles {
  __typename: "ChemoDiagnosisCycleModel";
  id: string;
  cycleNumber: number;
  investigationDetails: GetChemoVerificationTemplate_fetchChemoDiagnosisTemplate_cycles_investigationDetails[] | null;
  drugs: GetChemoVerificationTemplate_fetchChemoDiagnosisTemplate_cycles_drugs[] | null;
}

export interface GetChemoVerificationTemplate_fetchChemoDiagnosisTemplate {
  __typename: "ChemoDiagnosisTemplateModel";
  id: string;
  combinationName: string;
  creatorName: string | null;
  createdDate: any;
  type: string;
  section: string;
  cycles: GetChemoVerificationTemplate_fetchChemoDiagnosisTemplate_cycles[];
  updatedDate: any;
  lastModifierName: string | null;
}

export interface GetChemoVerificationTemplate {
  fetchChemoDiagnosisTemplate: GetChemoVerificationTemplate_fetchChemoDiagnosisTemplate;
}

export interface GetChemoVerificationTemplateVariables {
  id: string;
}
