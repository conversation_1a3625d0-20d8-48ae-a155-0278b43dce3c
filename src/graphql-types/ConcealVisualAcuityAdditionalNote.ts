/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealVisualAcuityAdditionalNote
// ====================================================

export interface ConcealVisualAcuityAdditionalNote_concealVisualAcuityAdditionalNote {
  __typename: "VisualAcuityModel";
  id: string;
  concealAdditionalNote: boolean | null;
  additionalNote: string | null;
}

export interface ConcealVisualAcuityAdditionalNote {
  concealVisualAcuityAdditionalNote: ConcealVisualAcuityAdditionalNote_concealVisualAcuityAdditionalNote;
}

export interface ConcealVisualAcuityAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
  clinifyId: string;
}
