/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { CommissionPayer, PayoutStatus } from "./globalTypes";

// ====================================================
// GraphQL fragment: VirtualServicesPaymentFields
// ====================================================

export interface VirtualServicesPaymentFields_bill_receiverProfile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface VirtualServicesPaymentFields_bill {
  __typename: "BillModel";
  id: string;
  billingDateTime: any | null;
  raisedBy: string | null;
  receiverProfile: VirtualServicesPaymentFields_bill_receiverProfile | null;
}

export interface VirtualServicesPaymentFields {
  __typename: "VirtualServicesPaymentModel";
  id: string;
  billId: string | null;
  bill: VirtualServicesPaymentFields_bill | null;
  amountDue: number;
  amountPaid: number;
  paymentStatus: string;
  commissionPayer: CommissionPayer | null;
  commissionFeeAmount: number | null;
  payoutId: string | null;
  payoutStatus: PayoutStatus;
  createdDate: any;
  updatedDate: any | null;
}
