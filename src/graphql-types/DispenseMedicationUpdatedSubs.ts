/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MedicationOptionType } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: DispenseMedicationUpdatedSubs
// ====================================================

export interface DispenseMedicationUpdatedSubs_DispenseMedicationUpdated_dispenseServiceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
}

export interface DispenseMedicationUpdatedSubs_DispenseMedicationUpdated_dispenseConsumables {
  __typename: "DispenseConsumables";
  name: string | null;
  drugInventoryId: string | null;
  quantityConsumed: string | null;
  quantityRemaining: string | null;
  inventoryClass: string | null;
}

export interface DispenseMedicationUpdatedSubs_DispenseMedicationUpdated_hmoClaim_utilizations {
  __typename: "PreAuthUtilisationsModel";
  id: string;
  type: string | null;
  /**
   * Value is in Naira
   */
  price: string | null;
}

export interface DispenseMedicationUpdatedSubs_DispenseMedicationUpdated_hmoClaim {
  __typename: "HmoClaimModel";
  id: string;
  claimDate: any | null;
  enrolleeNumber: string | null;
  utilizations: DispenseMedicationUpdatedSubs_DispenseMedicationUpdated_hmoClaim_utilizations[] | null;
}

export interface DispenseMedicationUpdatedSubs_DispenseMedicationUpdated {
  __typename: "DispenseDetailsModel";
  id: string;
  dispenseDate: any | null;
  dispensedBy: string | null;
  medicationName: string[] | null;
  medicationDetailId: string | null;
  dispenseNote: string | null;
  concealDispenseNote: boolean;
  hmoProviderId: string | null;
  hospitalId: string;
  dispenseServiceDetails: DispenseMedicationUpdatedSubs_DispenseMedicationUpdated_dispenseServiceDetails[] | null;
  facilityName: string | null;
  facilityAddress: string | null;
  medicationId: string | null;
  billingId: string | null;
  quantityRemaining: string | null;
  quantityDispensed: string | null;
  dispenseConsumables: DispenseMedicationUpdatedSubs_DispenseMedicationUpdated_dispenseConsumables[] | null;
  option: MedicationOptionType | null;
  hmoClaimId: string | null;
  inventoryClass: string | null;
  hmoClaim: DispenseMedicationUpdatedSubs_DispenseMedicationUpdated_hmoClaim | null;
}

export interface DispenseMedicationUpdatedSubs {
  DispenseMedicationUpdated: DispenseMedicationUpdatedSubs_DispenseMedicationUpdated;
}

export interface DispenseMedicationUpdatedSubsVariables {
  profileId: string;
  hospitalId: string;
}
