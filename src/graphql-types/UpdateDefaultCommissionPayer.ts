/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { CommissionPayer } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateDefaultCommissionPayer
// ====================================================

export interface UpdateDefaultCommissionPayer_updateDefaultCommissionPayer {
  __typename: "CommissionPayerResponse";
  commissionPayer: CommissionPayer | null;
  facilityPreferenceId: string;
  hospitalId: string;
}

export interface UpdateDefaultCommissionPayer {
  updateDefaultCommissionPayer: UpdateDefaultCommissionPayer_updateDefaultCommissionPayer;
}

export interface UpdateDefaultCommissionPayerVariables {
  facilityPreferenceId: string;
  commissionPayer: CommissionPayer;
}
