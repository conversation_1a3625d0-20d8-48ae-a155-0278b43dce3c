/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BenefitCategory, BenefitCoverage } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetExternalPlanTypeWithBenefits
// ====================================================

export interface GetExternalPlanTypeWithBenefits_getExternalPlanType_premiumDetails_enrollmentCommissionRate {
  __typename: "EnrollmentCommissionRateDetails";
  name: string | null;
  id: string | null;
  commissionRate: number | null;
  referralCommissionRate: number | null;
}

export interface GetExternalPlanTypeWithBenefits_getExternalPlanType_premiumDetails {
  __typename: "PremiumDetails";
  amount: number | null;
  category: string | null;
  frequency: string | null;
  administrationAgency: string | null;
  commissionRate: number | null;
  referralCommissionRate: number | null;
  providerLedCommissionRate: number | null;
  individualCommissionRate: number | null;
  enrollmentCommissionRate: GetExternalPlanTypeWithBenefits_getExternalPlanType_premiumDetails_enrollmentCommissionRate[] | null;
}

export interface GetExternalPlanTypeWithBenefits_getExternalPlanType_benefits_visitType {
  __typename: "HmoVisitTypeModel";
  id: string;
  name: string | null;
}

export interface GetExternalPlanTypeWithBenefits_getExternalPlanType_benefits_utilisationTypes {
  __typename: "HmoUtilisationType";
  name: string;
  annualLimitPerPerson: string | null;
  benefitCategory: BenefitCategory;
  benefitCoverage: BenefitCoverage;
  benefitLimit: string | null;
  code: string;
  id: string;
  price: string | null;
  bandBPrice: string | null;
  bandCPrice: string | null;
  visitLimit: string | null;
  waitingPeriodDays: number | null;
  quantity: number | null;
}

export interface GetExternalPlanTypeWithBenefits_getExternalPlanType_benefits {
  __typename: "HmoPlanBenefitModel";
  id: string;
  name: string | null;
  code: string | null;
  limit: string | null;
  visitLimit: string | null;
  annualLimitPerPerson: string | null;
  waitingPeriodDays: number | null;
  planTypeId: string | null;
  utilisationCategory: string;
  visitTypeId: string | null;
  visitType: GetExternalPlanTypeWithBenefits_getExternalPlanType_benefits_visitType | null;
  utilisationTypes: GetExternalPlanTypeWithBenefits_getExternalPlanType_benefits_utilisationTypes[] | null;
  createdDate: any;
}

export interface GetExternalPlanTypeWithBenefits_getExternalPlanType {
  __typename: "HmoPlanTypeModel";
  id: string;
  name: string | null;
  premiumCountry: string | null;
  status: string | null;
  planDateTime: any | null;
  plannedBy: string | null;
  isExternalPlan: boolean | null;
  isSponsor: boolean | null;
  premiumDetails: GetExternalPlanTypeWithBenefits_getExternalPlanType_premiumDetails[] | null;
  benefits: GetExternalPlanTypeWithBenefits_getExternalPlanType_benefits[] | null;
}

export interface GetExternalPlanTypeWithBenefits {
  getExternalPlanType: GetExternalPlanTypeWithBenefits_getExternalPlanType;
}

export interface GetExternalPlanTypeWithBenefitsVariables {
  hmoProviderId: string;
}
