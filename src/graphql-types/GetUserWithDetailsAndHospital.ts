/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PatientProfileType, Gender, HospitalPlan, Action, Subject } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetUserWithDetailsAndHospital
// ====================================================

export interface GetUserWithDetailsAndHospital_profile_enrollmentTpaNonTpa {
  __typename: "EnrollmentTpaNonTpaModel";
  id: string;
  name: string | null;
}

export interface GetUserWithDetailsAndHospital_profile_enrollmentAgency {
  __typename: "EnrollmentAgencyModel";
  id: string;
  name: string | null;
}

export interface GetUserWithDetailsAndHospital_profile_coverageDetails_questionnaireData {
  __typename: "QuestionnaireData";
  employmentStatus: string | null;
  gender: string | null;
  highestFormalEducationalLevel: string | null;
  maritalStatus: string | null;
  numberOfHouseholdMembers: string | null;
  numberOfMattresses: string | null;
  numberOfMobilePhones: string | null;
  numberOfRooms: string | null;
  numberOfTVs: string | null;
  numberOfVehicles: string | null;
  occupationalGroup: string | null;
  occupation: string | null;
  primaryCookingImplement: string | null;
  relationshipToHouseholdHead: string | null;
  typeOfRoof: string | null;
  typeOfToilet: string | null;
  /**
   * Score in percentage
   */
  questionnaireScore: number | null;
}

export interface GetUserWithDetailsAndHospital_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface GetUserWithDetailsAndHospital_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  coverageType: string | null;
  companyName: string | null;
  companyAddress: string | null;
  familyName: string | null;
  familyAddress: string | null;
  name: string | null;
  contactAddress: string | null;
  memberNumber: string | null;
  memberPlan: string | null;
  employeeNumber: string | null;
  capturedDate: any | null;
  capturedBy: string | null;
  enrolledBy: string | null;
  enrollmentDateTime: any | null;
  paymentDateTime: any | null;
  paymentFrequency: string | null;
  memberStartDate: any | null;
  memberDueDate: any | null;
  memberStatus: string | null;
  memberUniqueId: string | null;
  parentMemberUniqueId: string | null;
  primaryProviderId: string | null;
  primaryProviderName: string | null;
  secondaryProviderName: string | null;
  tertiaryProviderName: string | null;
  primaryProviderAddress: string | null;
  secondaryProviderAddress: string | null;
  tertiaryProviderAddress: string | null;
  capitatedMember: string | null;
  capitatedAmount: string | null;
  employeeType: string | null;
  employeeDivision: string | null;
  occupation: string | null;
  retired: boolean | null;
  memberPlanGroup: string | null;
  memberPlanSubGroup: string | null;
  terminationDate: any | null;
  premiumCollected: string | null;
  premiumOutstanding: string | null;
  registrationSource: string | null;
  administrationAgency: string | null;
  commissionRate: string | null;
  commissionPayable: string | null;
  tpaNonTpaCommissionRate: string | null;
  tpaNonTpaCommissionPayable: string | null;
  enrollmentAgent: string | null;
  enrollmentAgency: string | null;
  planCategory: string | null;
  salesWing: string | null;
  sponsorType: string | null;
  sponsorName: string | null;
  referrer: string | null;
  referrerCode: string | null;
  referrerNumber: string | null;
  referrerCommissionRate: string | null;
  referrerCommissionPayable: string | null;
  employerCode: string | null;
  activationDatetime: any | null;
  totalPremiumAmountPaid: string | null;
  questionnaireData: GetUserWithDetailsAndHospital_profile_coverageDetails_questionnaireData | null;
  provider: GetUserWithDetailsAndHospital_profile_coverageDetails_provider | null;
}

export interface GetUserWithDetailsAndHospital_profile_fileNumbers {
  __typename: "FileNumberInputType";
  coverageRef: string | null;
  fileNumber: string | null;
  existingFamily: boolean | null;
}

export interface GetUserWithDetailsAndHospital_profile_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
}

export interface GetUserWithDetailsAndHospital_profile_personalInformation_coverageDetails_questionnaireData {
  __typename: "QuestionnaireData";
  employmentStatus: string | null;
  gender: string | null;
  highestFormalEducationalLevel: string | null;
  maritalStatus: string | null;
  numberOfHouseholdMembers: string | null;
  numberOfMattresses: string | null;
  numberOfMobilePhones: string | null;
  numberOfRooms: string | null;
  numberOfTVs: string | null;
  numberOfVehicles: string | null;
  occupationalGroup: string | null;
  occupation: string | null;
  primaryCookingImplement: string | null;
  relationshipToHouseholdHead: string | null;
  typeOfRoof: string | null;
  typeOfToilet: string | null;
  /**
   * Score in percentage
   */
  questionnaireScore: number | null;
}

export interface GetUserWithDetailsAndHospital_profile_personalInformation_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface GetUserWithDetailsAndHospital_profile_personalInformation_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  coverageType: string | null;
  companyName: string | null;
  companyAddress: string | null;
  familyName: string | null;
  familyAddress: string | null;
  name: string | null;
  contactAddress: string | null;
  memberNumber: string | null;
  memberPlan: string | null;
  employeeNumber: string | null;
  capturedDate: any | null;
  capturedBy: string | null;
  enrolledBy: string | null;
  enrollmentDateTime: any | null;
  paymentDateTime: any | null;
  paymentFrequency: string | null;
  memberStartDate: any | null;
  memberDueDate: any | null;
  memberStatus: string | null;
  memberUniqueId: string | null;
  parentMemberUniqueId: string | null;
  primaryProviderId: string | null;
  primaryProviderName: string | null;
  secondaryProviderName: string | null;
  tertiaryProviderName: string | null;
  primaryProviderAddress: string | null;
  secondaryProviderAddress: string | null;
  tertiaryProviderAddress: string | null;
  capitatedMember: string | null;
  capitatedAmount: string | null;
  employeeType: string | null;
  employeeDivision: string | null;
  occupation: string | null;
  retired: boolean | null;
  memberPlanGroup: string | null;
  memberPlanSubGroup: string | null;
  terminationDate: any | null;
  premiumCollected: string | null;
  premiumOutstanding: string | null;
  registrationSource: string | null;
  administrationAgency: string | null;
  commissionRate: string | null;
  commissionPayable: string | null;
  tpaNonTpaCommissionRate: string | null;
  tpaNonTpaCommissionPayable: string | null;
  enrollmentAgent: string | null;
  enrollmentAgency: string | null;
  planCategory: string | null;
  salesWing: string | null;
  sponsorType: string | null;
  sponsorName: string | null;
  referrer: string | null;
  referrerCode: string | null;
  referrerNumber: string | null;
  referrerCommissionRate: string | null;
  referrerCommissionPayable: string | null;
  employerCode: string | null;
  activationDatetime: any | null;
  totalPremiumAmountPaid: string | null;
  questionnaireData: GetUserWithDetailsAndHospital_profile_personalInformation_coverageDetails_questionnaireData | null;
  provider: GetUserWithDetailsAndHospital_profile_personalInformation_coverageDetails_provider | null;
}

export interface GetUserWithDetailsAndHospital_profile_personalInformation_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  value: string | null;
  countryCode: string | null;
  countryName: string | null;
}

export interface GetUserWithDetailsAndHospital_profile_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  rank: string | null;
  title: string | null;
  department: string | null;
  speciality: string | null;
  displayPictureUrl: string | null;
  firstName: string | null;
  lastName: string | null;
  middleName: string | null;
  dateOfBirth: any | null;
  bloodGroup: string | null;
  genoType: string | null;
  weight: string | null;
  weightUnit: string | null;
  height: string | null;
  heightUnit: string | null;
  address: string | null;
  gender: Gender | null;
  secondaryEmail: string | null;
  folioNumber: string | null;
  clinicalTrials: string | null;
  patientFileOrCardNo: string | null;
  coverageDetails: GetUserWithDetailsAndHospital_profile_personalInformation_coverageDetails[] | null;
  secondaryPhoneNumber: GetUserWithDetailsAndHospital_profile_personalInformation_secondaryPhoneNumber | null;
  nin: string | null;
  votersId: string | null;
  passportNumber: string | null;
  bvn: string | null;
  registrationNote: string | null;
  lga: string | null;
  ward: string | null;
  buildingName: string | null;
  buildingLevel: string | null;
  countryOfResidence: string | null;
  stateOfResidence: string | null;
  patientCategory: string[] | null;
  nationality: string | null;
  state: string | null;
  originLga: string | null;
  city: string | null;
  placeOfBirth: string | null;
  userRole: string | null;
}

export interface GetUserWithDetailsAndHospital_profile_backgroundInformation {
  __typename: "backgroundInformation";
  id: string | null;
  maritalStatus: string | null;
  numberOfChildren: number | null;
  education: string | null;
  state: string | null;
  religion: string | null;
  nationality: string | null;
  organDonor: string | null;
  occupation: string | null;
  salaryRange: string | null;
  bloodDonor: string | null;
  preferredLanguage: string | null;
  modeOfCommunication: string | null;
  tissueDonor: string | null;
  boneMarrowDonor: string | null;
  originLga: string | null;
  ethnicity: string | null;
}

export interface GetUserWithDetailsAndHospital_profile_hospital_hmoHospitals_planVisibility {
  __typename: "PlanVisibilityDto";
  id: string;
  name: string;
}

export interface GetUserWithDetailsAndHospital_profile_hospital_hmoHospitals_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
  documentUrl: string[] | null;
}

export interface GetUserWithDetailsAndHospital_profile_hospital_hmoHospitals {
  __typename: "HmoHospitalModel";
  hmoProviderId: string;
  planVisibility: GetUserWithDetailsAndHospital_profile_hospital_hmoHospitals_planVisibility[] | null;
  provider: GetUserWithDetailsAndHospital_profile_hospital_hmoHospitals_provider | null;
  id: string;
}

export interface GetUserWithDetailsAndHospital_profile_hospital_billingInformation {
  __typename: "FacilityBillingInformationModel";
  id: string;
  accountName: string;
  accountNumber: string;
  bankName: string;
  bvn: string | null;
  branchName: string | null;
  isPreferredPayoutAccount: boolean | null;
}

export interface GetUserWithDetailsAndHospital_profile_hospital_sponsorBillingInformation {
  __typename: "SponsorBillingInformationModel";
  id: string;
  accountName: string;
  accountNumber: string;
  bankName: string;
  bvn: string | null;
  branchName: string | null;
  sponsorName: string | null;
}

export interface GetUserWithDetailsAndHospital_profile_hospital_phoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface GetUserWithDetailsAndHospital_profile_hospital_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface GetUserWithDetailsAndHospital_profile_hospital_hospitalSupportPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface GetUserWithDetailsAndHospital_profile_hospital_hmo {
  __typename: "HmoProviderModel";
  id: string;
  providerCode: string;
}

export interface GetUserWithDetailsAndHospital_profile_hospital {
  __typename: "HospitalModel";
  name: string | null;
  id: string;
  address: string | null;
  clinifyId: string | null;
  plan: HospitalPlan;
  website: string | null;
  facilityLogo: string | null;
  specialties: string[] | null;
  hmoHospitals: GetUserWithDetailsAndHospital_profile_hospital_hmoHospitals[] | null;
  state: string | null;
  lga: string | null;
  politicalWard: string | null;
  city: string | null;
  ownership: string | null;
  level: string | null;
  country: string | null;
  licenseNumber: string | null;
  documentUrl: string[] | null;
  billingInformation: GetUserWithDetailsAndHospital_profile_hospital_billingInformation[] | null;
  sponsorBillingInformation: GetUserWithDetailsAndHospital_profile_hospital_sponsorBillingInformation[] | null;
  phoneNumber: GetUserWithDetailsAndHospital_profile_hospital_phoneNumber | null;
  facebook: string | null;
  twitter: string | null;
  instagram: string | null;
  secondaryPhoneNumber: GetUserWithDetailsAndHospital_profile_hospital_secondaryPhoneNumber | null;
  hospitalSupportPhoneNumber: GetUserWithDetailsAndHospital_profile_hospital_hospitalSupportPhoneNumber | null;
  supportMail: string | null;
  preferenceId: string | null;
  hqFacilityId: string | null;
  hmoId: string | null;
  lastPaymentDateTime: any | null;
  nextPaymentDateTime: any | null;
  hmo: GetUserWithDetailsAndHospital_profile_hospital_hmo | null;
}

export interface GetUserWithDetailsAndHospital_profile_user {
  __typename: "UserModel";
  id: string;
  email: string | null;
  nonCorporateEmail: string | null;
  corporatePhoneNumber: string | null;
  phoneNumber: string | null;
  country: string | null;
  hasPin: boolean | null;
  isPinMandatory: boolean | null;
  isMfaEnabled: boolean | null;
  forcePasswordChange: boolean | null;
}

export interface GetUserWithDetailsAndHospital_profile_permissions_rules_conditions {
  __typename: "ConditionModel";
  organizationId: string | null;
}

export interface GetUserWithDetailsAndHospital_profile_permissions_rules {
  __typename: "PermissionModelInput";
  action: Action;
  subject: Subject;
  conditions: GetUserWithDetailsAndHospital_profile_permissions_rules_conditions | null;
}

export interface GetUserWithDetailsAndHospital_profile_permissions {
  __typename: "PermissionModel";
  id: string;
  rules: GetUserWithDetailsAndHospital_profile_permissions_rules[] | null;
}

export interface GetUserWithDetailsAndHospital_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  active: boolean;
  isDefault: boolean;
  createdDate: any | null;
  updatedDate: any | null;
  creatorName: string | null;
  lastModifierName: string | null;
  patientProfileType: PatientProfileType | null;
  enrollmentTpaNonTpa: GetUserWithDetailsAndHospital_profile_enrollmentTpaNonTpa | null;
  enrollmentAgency: GetUserWithDetailsAndHospital_profile_enrollmentAgency | null;
  coverageDetails: GetUserWithDetailsAndHospital_profile_coverageDetails[] | null;
  fileNumbers: GetUserWithDetailsAndHospital_profile_fileNumbers[] | null;
  fullName: string;
  type: string;
  typeAlias: string | null;
  title: string | null;
  gender: Gender | null;
  patientStatus: string | null;
  deathDateTime: any | null;
  deathLocation: string | null;
  causeOfDeath: string | null;
  dataAccessType: string | null;
  serviceDetails: GetUserWithDetailsAndHospital_profile_serviceDetails[] | null;
  personalInformation: GetUserWithDetailsAndHospital_profile_personalInformation | null;
  backgroundInformation: GetUserWithDetailsAndHospital_profile_backgroundInformation | null;
  billStatus: string | null;
  createdFromHmo: boolean | null;
  shareData: boolean | null;
  hospital: GetUserWithDetailsAndHospital_profile_hospital | null;
  user: GetUserWithDetailsAndHospital_profile_user;
  permissions: GetUserWithDetailsAndHospital_profile_permissions;
}

export interface GetUserWithDetailsAndHospital {
  profile: GetUserWithDetailsAndHospital_profile;
}

export interface GetUserWithDetailsAndHospitalVariables {
  id: string;
}
