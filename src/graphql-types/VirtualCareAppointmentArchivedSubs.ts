/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Gender } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: VirtualCareAppointmentArchivedSubs
// ====================================================

export interface VirtualCareAppointmentArchivedSubs_VirtualCareAppointmentArchived_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface VirtualCareAppointmentArchivedSubs_VirtualCareAppointmentArchived_specialists_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  speciality: string | null;
}

export interface VirtualCareAppointmentArchivedSubs_VirtualCareAppointmentArchived_specialists {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
  personalInformation: VirtualCareAppointmentArchivedSubs_VirtualCareAppointmentArchived_specialists_personalInformation | null;
}

export interface VirtualCareAppointmentArchivedSubs_VirtualCareAppointmentArchived_patientProfile_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
  dateOfBirth: any | null;
  displayPictureUrl: string | null;
  bloodGroup: string | null;
}

export interface VirtualCareAppointmentArchivedSubs_VirtualCareAppointmentArchived_patientProfile_user {
  __typename: "UserModel";
  id: string;
  nonCorporateEmail: string | null;
  phoneNumber: string | null;
  country: string | null;
}

export interface VirtualCareAppointmentArchivedSubs_VirtualCareAppointmentArchived_patientProfile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  gender: Gender | null;
  clinifyId: string;
  personalInformation: VirtualCareAppointmentArchivedSubs_VirtualCareAppointmentArchived_patientProfile_personalInformation | null;
  user: VirtualCareAppointmentArchivedSubs_VirtualCareAppointmentArchived_patientProfile_user;
}

export interface VirtualCareAppointmentArchivedSubs_VirtualCareAppointmentArchived {
  __typename: "VirtualCareAppointmentModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  liveSessionUrl: string | null;
  category: string | null;
  appointmentDate: any;
  startDateTime: any | null;
  endDateTime: any | null;
  priority: string | null;
  duration: string | null;
  patientType: string | null;
  paymentType: string | null;
  hospital: VirtualCareAppointmentArchivedSubs_VirtualCareAppointmentArchived_hospital | null;
  specialists: VirtualCareAppointmentArchivedSubs_VirtualCareAppointmentArchived_specialists[] | null;
  patientProfile: VirtualCareAppointmentArchivedSubs_VirtualCareAppointmentArchived_patientProfile | null;
}

export interface VirtualCareAppointmentArchivedSubs {
  VirtualCareAppointmentArchived: VirtualCareAppointmentArchivedSubs_VirtualCareAppointmentArchived;
}

export interface VirtualCareAppointmentArchivedSubsVariables {
  hospitalId?: string | null;
  profileId: string;
}
