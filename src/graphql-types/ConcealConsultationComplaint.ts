/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealConsultationComplaint
// ====================================================

export interface ConcealConsultationComplaint_concealConsultationComplaint {
  __typename: "ConsultationModel";
  id: string;
  concealComplaint: boolean | null;
  complaint: string | null;
}

export interface ConcealConsultationComplaint {
  concealConsultationComplaint: ConcealConsultationComplaint_concealConsultationComplaint;
}

export interface ConcealConsultationComplaintVariables {
  id: string;
  concealStatus: boolean;
}
