/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { OxygenTherapyInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateMedicationOxygenTherapy
// ====================================================

export interface UpdateMedicationOxygenTherapy_updateMedicationOxygenTherapy_details {
  __typename: "OxygenTherapyDetails";
  administeredBy: string | null;
  administrationDateTime: any | null;
  administrationHour: string | null;
  saturation: string | null;
  therapyType: string | null;
}

export interface UpdateMedicationOxygenTherapy_updateMedicationOxygenTherapy {
  __typename: "OxygenTherapyModel";
  id: string;
  createdDate: any;
  creator: string | null;
  medicationDetailId: string | null;
  updatedDate: any;
  updater: string | null;
  details: UpdateMedicationOxygenTherapy_updateMedicationOxygenTherapy_details[] | null;
}

export interface UpdateMedicationOxygenTherapy {
  updateMedicationOxygenTherapy: UpdateMedicationOxygenTherapy_updateMedicationOxygenTherapy;
}

export interface UpdateMedicationOxygenTherapyVariables {
  id: string;
  input: OxygenTherapyInput;
  pin?: string | null;
}
