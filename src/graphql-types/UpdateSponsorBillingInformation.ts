/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { SponsorBillingInformationInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateSponsorBillingInformation
// ====================================================

export interface UpdateSponsorBillingInformation_updateSponsorBillingInformation {
  __typename: "SponsorBillingInformationModel";
  id: string;
  accountName: string;
  accountNumber: string;
  bankName: string;
  branchName: string | null;
  bvn: string | null;
  sponsorName: string | null;
  hospitalId: string;
  createdDate: any;
  updatedDate: any;
  creatorName: string | null;
  lastModifierName: string | null;
}

export interface UpdateSponsorBillingInformation {
  updateSponsorBillingInformation: UpdateSponsorBillingInformation_updateSponsorBillingInformation;
}

export interface UpdateSponsorBillingInformationVariables {
  billingInformationId: string;
  input: SponsorBillingInformationInput;
}
