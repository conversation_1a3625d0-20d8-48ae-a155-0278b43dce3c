/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Gender, BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: PatientLookupResultUpdatedSubs
// ====================================================

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_fileNumbers {
  __typename: "FileNumberInputType";
  coverageRef: string | null;
  fileNumber: string | null;
  existingFamily: boolean | null;
}

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_coverageDetails_questionnaireData {
  __typename: "QuestionnaireData";
  employmentStatus: string | null;
  gender: string | null;
  highestFormalEducationalLevel: string | null;
  maritalStatus: string | null;
  numberOfHouseholdMembers: string | null;
  numberOfMattresses: string | null;
  numberOfMobilePhones: string | null;
  numberOfRooms: string | null;
  numberOfTVs: string | null;
  numberOfVehicles: string | null;
  occupationalGroup: string | null;
  occupation: string | null;
  primaryCookingImplement: string | null;
  relationshipToHouseholdHead: string | null;
  typeOfRoof: string | null;
  typeOfToilet: string | null;
  /**
   * Score in percentage
   */
  questionnaireScore: number | null;
}

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  coverageType: string | null;
  companyName: string | null;
  companyAddress: string | null;
  familyName: string | null;
  familyAddress: string | null;
  name: string | null;
  contactAddress: string | null;
  memberNumber: string | null;
  memberPlan: string | null;
  employeeNumber: string | null;
  capturedDate: any | null;
  capturedBy: string | null;
  enrolledBy: string | null;
  enrollmentDateTime: any | null;
  paymentDateTime: any | null;
  paymentFrequency: string | null;
  memberStartDate: any | null;
  memberDueDate: any | null;
  memberStatus: string | null;
  memberUniqueId: string | null;
  parentMemberUniqueId: string | null;
  primaryProviderId: string | null;
  primaryProviderName: string | null;
  secondaryProviderName: string | null;
  tertiaryProviderName: string | null;
  primaryProviderAddress: string | null;
  secondaryProviderAddress: string | null;
  tertiaryProviderAddress: string | null;
  capitatedMember: string | null;
  capitatedAmount: string | null;
  employeeType: string | null;
  employeeDivision: string | null;
  occupation: string | null;
  retired: boolean | null;
  memberPlanGroup: string | null;
  memberPlanSubGroup: string | null;
  terminationDate: any | null;
  premiumCollected: string | null;
  premiumOutstanding: string | null;
  registrationSource: string | null;
  administrationAgency: string | null;
  commissionRate: string | null;
  commissionPayable: string | null;
  tpaNonTpaCommissionRate: string | null;
  tpaNonTpaCommissionPayable: string | null;
  enrollmentAgent: string | null;
  enrollmentAgency: string | null;
  planCategory: string | null;
  salesWing: string | null;
  sponsorType: string | null;
  sponsorName: string | null;
  referrer: string | null;
  referrerCode: string | null;
  referrerNumber: string | null;
  referrerCommissionRate: string | null;
  referrerCommissionPayable: string | null;
  employerCode: string | null;
  activationDatetime: any | null;
  totalPremiumAmountPaid: string | null;
  questionnaireData: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_coverageDetails_questionnaireData | null;
  provider: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_coverageDetails_provider | null;
}

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_hmoProfiles_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_hmoProfiles_lastCheckIn {
  __typename: "LastCheckInData";
  /**
   * Date Of Check-In
   */
  checkInDate: any;
}

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_hmoProfiles {
  __typename: "HmoProfileModel";
  id: string;
  checkedIn: boolean | null;
  verificationCode: string | null;
  visitationId: string | null;
  memberPlan: string | null;
  memberNumber: string | null;
  memberStatus: string | null;
  memberPlanId: string | null;
  provider: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_hmoProfiles_provider;
  memberStartDate: any | null;
  memberDueDate: any | null;
  planEligibilityText: string | null;
  planEligibility: string | null;
  planEnrolleeDob: string | null;
  employer: string | null;
  lastCheckIn: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_hmoProfiles_lastCheckIn | null;
}

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_bill {
  __typename: "BillModel";
  id: string;
  createdDate: any;
}

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_personalInformation_coverageDetails_questionnaireData {
  __typename: "QuestionnaireData";
  employmentStatus: string | null;
  gender: string | null;
  highestFormalEducationalLevel: string | null;
  maritalStatus: string | null;
  numberOfHouseholdMembers: string | null;
  numberOfMattresses: string | null;
  numberOfMobilePhones: string | null;
  numberOfRooms: string | null;
  numberOfTVs: string | null;
  numberOfVehicles: string | null;
  occupationalGroup: string | null;
  occupation: string | null;
  primaryCookingImplement: string | null;
  relationshipToHouseholdHead: string | null;
  typeOfRoof: string | null;
  typeOfToilet: string | null;
  /**
   * Score in percentage
   */
  questionnaireScore: number | null;
}

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_personalInformation_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_personalInformation_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  coverageType: string | null;
  companyName: string | null;
  companyAddress: string | null;
  familyName: string | null;
  familyAddress: string | null;
  name: string | null;
  contactAddress: string | null;
  memberNumber: string | null;
  memberPlan: string | null;
  employeeNumber: string | null;
  capturedDate: any | null;
  capturedBy: string | null;
  enrolledBy: string | null;
  enrollmentDateTime: any | null;
  paymentDateTime: any | null;
  paymentFrequency: string | null;
  memberStartDate: any | null;
  memberDueDate: any | null;
  memberStatus: string | null;
  memberUniqueId: string | null;
  parentMemberUniqueId: string | null;
  primaryProviderId: string | null;
  primaryProviderName: string | null;
  secondaryProviderName: string | null;
  tertiaryProviderName: string | null;
  primaryProviderAddress: string | null;
  secondaryProviderAddress: string | null;
  tertiaryProviderAddress: string | null;
  capitatedMember: string | null;
  capitatedAmount: string | null;
  employeeType: string | null;
  employeeDivision: string | null;
  occupation: string | null;
  retired: boolean | null;
  memberPlanGroup: string | null;
  memberPlanSubGroup: string | null;
  terminationDate: any | null;
  premiumCollected: string | null;
  premiumOutstanding: string | null;
  registrationSource: string | null;
  administrationAgency: string | null;
  commissionRate: string | null;
  commissionPayable: string | null;
  tpaNonTpaCommissionRate: string | null;
  tpaNonTpaCommissionPayable: string | null;
  enrollmentAgent: string | null;
  enrollmentAgency: string | null;
  planCategory: string | null;
  salesWing: string | null;
  sponsorType: string | null;
  sponsorName: string | null;
  referrer: string | null;
  referrerCode: string | null;
  referrerNumber: string | null;
  referrerCommissionRate: string | null;
  referrerCommissionPayable: string | null;
  employerCode: string | null;
  activationDatetime: any | null;
  totalPremiumAmountPaid: string | null;
  questionnaireData: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_personalInformation_coverageDetails_questionnaireData | null;
  provider: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_personalInformation_coverageDetails_provider | null;
}

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_personalInformation_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  value: string | null;
  countryCode: string | null;
  countryName: string | null;
}

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  rank: string | null;
  title: string | null;
  department: string | null;
  speciality: string | null;
  displayPictureUrl: string | null;
  firstName: string | null;
  lastName: string | null;
  middleName: string | null;
  dateOfBirth: any | null;
  bloodGroup: string | null;
  genoType: string | null;
  weight: string | null;
  weightUnit: string | null;
  height: string | null;
  heightUnit: string | null;
  address: string | null;
  gender: Gender | null;
  secondaryEmail: string | null;
  folioNumber: string | null;
  clinicalTrials: string | null;
  patientFileOrCardNo: string | null;
  coverageDetails: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_personalInformation_coverageDetails[] | null;
  secondaryPhoneNumber: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_personalInformation_secondaryPhoneNumber | null;
  nin: string | null;
  votersId: string | null;
  passportNumber: string | null;
  bvn: string | null;
  registrationNote: string | null;
  lga: string | null;
  ward: string | null;
  buildingName: string | null;
  buildingLevel: string | null;
  countryOfResidence: string | null;
  stateOfResidence: string | null;
  patientCategory: string[] | null;
  nationality: string | null;
  state: string | null;
  originLga: string | null;
  city: string | null;
  placeOfBirth: string | null;
  userRole: string | null;
}

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_user {
  __typename: "UserModel";
  id: string;
  email: string | null;
  nonCorporateEmail: string | null;
  phoneNumber: string | null;
  country: string | null;
}

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_registeredWith {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
}

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_isProfileMerged {
  __typename: "IsProfileMergedResponse";
  isMerged: boolean;
}

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_preauthorizationDetails_provider | null;
}

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  fullName: string;
  type: string;
  fileNumbers: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_fileNumbers[] | null;
  patientStatus: string | null;
  deathDateTime: any | null;
  deathLocation: string | null;
  causeOfDeath: string | null;
  lastCheckinDate: any | null;
  coverageDetails: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_coverageDetails[] | null;
  hmoProfiles: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_hmoProfiles[] | null;
  createdDate: any | null;
  updatedDate: any | null;
  gender: Gender | null;
  bill: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_bill | null;
  billStatus: string | null;
  personalInformation: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_personalInformation | null;
  user: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_user;
  registeredWith: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_registeredWith | null;
  isProfileMerged: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_isProfileMerged;
  preauthorizationDetails: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list_preauthorizationDetails | null;
}

export interface PatientLookupResultUpdatedSubs_PatientLookupResultUpdated {
  __typename: "ProfilesResponse";
  totalCount: number;
  list: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated_list[];
}

export interface PatientLookupResultUpdatedSubs {
  PatientLookupResultUpdated: PatientLookupResultUpdatedSubs_PatientLookupResultUpdated;
}

export interface PatientLookupResultUpdatedSubsVariables {
  requestId: string;
}
