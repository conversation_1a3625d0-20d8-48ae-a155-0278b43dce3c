/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetCapitatedEnrolleeSummary
// ====================================================

export interface GetCapitatedEnrolleeSummary_getCapitatedEnrolleeSummary {
  __typename: "CapitatedEnrolleeSummary";
  totalEnrolleeCount: number | null;
  totalActiveEnrolleeCount: number | null;
  totalInactiveEnrolleeCount: number | null;
  totalExpiredEnrolleeCount: number | null;
  totalCapitatedEnrolleeCount: number | null;
  totalCapitatedAmount: number | null;
}

export interface GetCapitatedEnrolleeSummary {
  getCapitatedEnrolleeSummary: GetCapitatedEnrolleeSummary_getCapitatedEnrolleeSummary;
}

export interface GetCapitatedEnrolleeSummaryVariables {
  startDate?: string | null;
  endDate?: string | null;
  hmoProviderId?: string | null;
  registeredWith?: string | null;
  planId?: string | null;
}
