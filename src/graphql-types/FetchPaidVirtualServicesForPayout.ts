/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { DateRangeInput, PayoutStatus, CommissionPayer } from "./globalTypes";

// ====================================================
// GraphQL query operation: FetchPaidVirtualServicesForPayout
// ====================================================

export interface FetchPaidVirtualServicesForPayout_fetchPaidVirtualServicesForPayout_virtualServicesPayments_bill_receiverProfile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface FetchPaidVirtualServicesForPayout_fetchPaidVirtualServicesForPayout_virtualServicesPayments_bill {
  __typename: "BillModel";
  id: string;
  billingDateTime: any | null;
  raisedBy: string | null;
  receiverProfile: FetchPaidVirtualServicesForPayout_fetchPaidVirtualServicesForPayout_virtualServicesPayments_bill_receiverProfile | null;
}

export interface FetchPaidVirtualServicesForPayout_fetchPaidVirtualServicesForPayout_virtualServicesPayments {
  __typename: "VirtualServicesPaymentModel";
  id: string;
  paymentStatus: string;
  payoutStatus: PayoutStatus;
  amountDue: number;
  amountPaid: number;
  commissionFeeAmount: number | null;
  commissionPayer: CommissionPayer | null;
  commissionFeePercentage: number | null;
  paymentMethod: string;
  billId: string | null;
  bill: FetchPaidVirtualServicesForPayout_fetchPaidVirtualServicesForPayout_virtualServicesPayments_bill | null;
}

export interface FetchPaidVirtualServicesForPayout_fetchPaidVirtualServicesForPayout {
  __typename: "PaidVirtualServicesPaymentOnPayoutResponse";
  totalCommissionFeeAmount: number | null;
  totalVirtualServicesAmountPaid: number | null;
  virtualServicesPayments: FetchPaidVirtualServicesForPayout_fetchPaidVirtualServicesForPayout_virtualServicesPayments[] | null;
}

export interface FetchPaidVirtualServicesForPayout {
  fetchPaidVirtualServicesForPayout: FetchPaidVirtualServicesForPayout_fetchPaidVirtualServicesForPayout;
}

export interface FetchPaidVirtualServicesForPayoutVariables {
  hospitalId: string;
  dateRange: DateRangeInput;
}
