/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: ArchiveRequestProcedureSubs
// ====================================================

export interface ArchiveRequestProcedureSubs_RequestProcedureArchived_procedureType {
  __typename: "ProcedureTypeInputType";
  ref: string | null;
  type: string;
  priority: string | null;
  provider: string | null;
  itemId: string | null;
  preauthorizationDetailsId: string | null;
}

export interface ArchiveRequestProcedureSubs_RequestProcedureArchived_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface ArchiveRequestProcedureSubs_RequestProcedureArchived_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface ArchiveRequestProcedureSubs_RequestProcedureArchived_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: ArchiveRequestProcedureSubs_RequestProcedureArchived_profile_coverageDetails_provider | null;
}

export interface ArchiveRequestProcedureSubs_RequestProcedureArchived_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: ArchiveRequestProcedureSubs_RequestProcedureArchived_profile_coverageDetails[] | null;
}

export interface ArchiveRequestProcedureSubs_RequestProcedureArchived_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface ArchiveRequestProcedureSubs_RequestProcedureArchived_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: ArchiveRequestProcedureSubs_RequestProcedureArchived_preauthorizationDetails_provider | null;
}

export interface ArchiveRequestProcedureSubs_RequestProcedureArchived {
  __typename: "RequestProcedureModel";
  id: string;
  surgeryDate: any | null;
  duration: string | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
  procedureType: ArchiveRequestProcedureSubs_RequestProcedureArchived_procedureType[];
  rank: string | null;
  reason: string | null;
  requestedBy: string | null;
  specialty: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  operatedBy: string | null;
  patientConsent: string | null;
  serviceDetails: ArchiveRequestProcedureSubs_RequestProcedureArchived_serviceDetails[] | null;
  isPackage: boolean;
  documentUrl: string[] | null;
  surgeryStartDate: any | null;
  surgeryEndDate: any | null;
  appointmentId: string | null;
  createdDate: any;
  updatedDate: any;
  department: string | null;
  billStatus: string | null;
  hospitalId: string | null;
  profileId: string | null;
  profile: ArchiveRequestProcedureSubs_RequestProcedureArchived_profile | null;
  preauthorizationDetails: ArchiveRequestProcedureSubs_RequestProcedureArchived_preauthorizationDetails[] | null;
}

export interface ArchiveRequestProcedureSubs {
  RequestProcedureArchived: ArchiveRequestProcedureSubs_RequestProcedureArchived[];
}

export interface ArchiveRequestProcedureSubsVariables {
  profileId: string;
}
