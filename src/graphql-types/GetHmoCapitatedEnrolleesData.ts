/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetHmoCapitatedEnrolleesData
// ====================================================

export interface GetHmoCapitatedEnrolleesData_getHmoCapitatedEnrolleesData_list {
  __typename: "CapitatedEnrolleesData";
  profileId: string | null;
  fullName: string | null;
  memberStatus: string | null;
  dateOfBirth: string | null;
  gender: string | null;
  phoneNumber: string | null;
  memberNumber: string | null;
  emailAddress: string | null;
  planStartDate: string | null;
  planEndDate: string | null;
  planName: string | null;
  primaryProviderId: string | null;
  enrolleeCount: string | null;
  amount: string | null;
  serviceChargeAmount: string | null;
  primaryProviderName: string | null;
  primaryProviderAddress: string | null;
  narration: string | null;
}

export interface GetHmoCapitatedEnrolleesData_getHmoCapitatedEnrolleesData {
  __typename: "CapitatedEnrolleesListData";
  primaryProviderId: string | null;
  list: GetHmoCapitatedEnrolleesData_getHmoCapitatedEnrolleesData_list[] | null;
}

export interface GetHmoCapitatedEnrolleesData {
  getHmoCapitatedEnrolleesData: GetHmoCapitatedEnrolleesData_getHmoCapitatedEnrolleesData[];
}

export interface GetHmoCapitatedEnrolleesDataVariables {
  hmoPlanTypeId?: string | null;
  hospitalId?: string | null;
  startDate: any;
  endDate: any;
}
