/**
 * Side Navigation Items with Patient Selected
 *
 * This file defines and manages the navigation items that appear in the side navigation
 * when a patient is selected in the Clinify application. It provides:
 *
 * 1. A base set of navigation items organized by functional groups
 * 2. Filtering logic based on user type (doctor, nurse, admin, etc.)
 * 3. Filtering logic based on hospital plan type
 * 4. Core EMR feature exclusion options
 *
 * The navigation items are structured as nested arrays, where each inner array
 * represents a group of related navigation items.
 */

import { nanoid } from 'nanoid';
import { TPatientSelectedIconNames, TPatientSelectedSideNavItems } from './types';
import {
  BillingIcon,
  AntenatalIcon,
  PreauthorisationIcon,
  SubmitClaimsIcon,
  AttachmentIcon,
  FundWalletIcon,
  EnrolleeListIcon,
} from '../../../../assets';
import { ReactComponent as AdmissionIcon } from '../../../../assets/icons/navs/admission.svg';
import { ReactComponent as AllergyIcon } from '../../../../assets/icons/navs/allergy.svg';
import { ReactComponent as ConsultationIcon } from '../../../../assets/icons/navs/consultation.svg';
import { ReactComponent as BundleIcon } from '../../../../assets/icons/navs/medication-bundle.svg';
import { ReactComponent as CoverageIcon } from '../../../../assets/icons/navs/coverage.svg';
import { ReactComponent as ImmunizationIcon } from '../../../../assets/icons/navs/immunization.svg';
import { ReactComponent as LabResultIcon } from '../../../../assets/icons/navs/lab-result.svg';
import { ReactComponent as LookupIcon } from '../../../../assets/icons/navs/lookup.svg';
import { ReactComponent as MedicationIcon } from '../../../../assets/icons/navs/medication.svg';
import { ReactComponent as DetailsIcon } from '../../../../assets/icons/navs/my-detail.svg';
import { ReactComponent as MyHealthIcon } from '../../../../assets/icons/navs/my-health.svg';
import { ReactComponent as RadiologyIcon } from '../../../../assets/icons/navs/radiology.svg';
import { ReactComponent as RequestInvestigationIcon } from '../../../../assets/icons/navs/request-investigation.svg';
import { ReactComponent as SurgeryIcon } from '../../../../assets/icons/navs/surgery.svg';
import { ReactComponent as VitalSignsIcon } from '../../../../assets/icons/navs/vital-sign.svg';
import { ReactComponent as WaitingListIcon } from '../../../../assets/icons/navs/waiting-list.svg';
import { ReactComponent as ProcessInvestigationIcon } from '../../../../assets/icons/navs/process-investigation.svg';
import { ReactComponent as NursingServicesIcon } from '../../../../assets/icons/navs/nursing-services.svg';
import { ReactComponent as RequestReferralIcon } from '../../../../assets/icons/navs/request-referral.svg';
import { ReactComponent as AppointmentIcon } from '../../../../assets/icons/navs/appointment.svg';
import { HospitalPlan, UserType } from '../../../../graphql-types/globalTypes';
import { pickAndOmitPatientSelectedNavItems } from './helpers';
/**
 * Base navigation items for patient-selected view
 *
 * This constant defines all possible navigation items that can appear when a patient is selected.
 * The items are organized in nested arrays where each inner array represents a logical grouping:
 * - Group 1: Patient lookup and waiting list
 * - Group 2: Vital signs monitoring
 * - Group 3: Request-related actions (investigation, referral, preauthorization)
 * - Group 4: Claim submission
 * - Group 5: Investigation processing
 * - Group 6: Patient information (coverage, details, health, packages)
 * - Group 7: Nursing services
 * - Group 8: Medical records (pregnancy, admission, allergy, etc.)
 * - Group 9: Financial (wallet)
 *
 * Each item contains a URL, icon component, title, and unique ID.
 */
export const PatientSelectedNavItems: TPatientSelectedSideNavItems[][] = [
  [
    {
      url: '/enrollee-list',
      icon: EnrolleeListIcon,
      title: 'Enrollee List',
      id: nanoid(4),
    },
    {
      url: '/lookup',
      icon: LookupIcon,
      title: 'Patient Lookup',
      id: nanoid(4),
    },
    {
      url: '/lookup',
      icon: LookupIcon,
      title: 'Enrollee Lookup',
      id: nanoid(4),
    },
    {
      url: '/waiting-list',
      icon: WaitingListIcon,
      title: 'Patient Waiting List',
      id: nanoid(4),
    },
    {
      url: '/waiting-list',
      icon: WaitingListIcon,
      title: 'Enrollee Waiting List',
      id: nanoid(4),
    },
  ],
  [
    {
      url: '/vital-signs',
      icon: VitalSignsIcon,
      title: 'Vital Signs',
      id: nanoid(4),
    },
  ],
  [
    {
      url: '/appointment',
      icon: AppointmentIcon,
      title: 'Appointment',
      id: nanoid(4),
    },
  ],
  [
    {
      url: '/request-investigation',
      icon: RequestInvestigationIcon,
      title: 'Request Investigation',
      id: nanoid(4),
    },
    {
      url: '/request-referral',
      icon: RequestReferralIcon,
      title: 'Request Referral',
      id: nanoid(4),
    },
    {
      url: '/request-preauthorizations',
      icon: PreauthorisationIcon,
      title: 'Request Preauthorization',
      id: nanoid(4),
    },
  ],
  [
    {
      url: '/submit-claim',
      icon: SubmitClaimsIcon,
      title: 'Submit Claim',
      id: nanoid(4),
    },
  ],
  [
    {
      url: '/process-investigation',
      icon: ProcessInvestigationIcon,
      title: 'Process Investigation',
      id: nanoid(4),
    },
  ],
  [
    {
      url: '/coverage',
      icon: CoverageIcon,
      title: 'My Coverage',
      id: nanoid(4),
    },
    {
      url: '/details',
      icon: DetailsIcon,
      title: 'My Details',
      id: nanoid(4),
    },
    {
      url: '/health',
      icon: MyHealthIcon,
      title: 'My Health',
      id: nanoid(4),
    },
    {
      url: '/packages',
      icon: BundleIcon,
      title: 'Packages',
      id: nanoid(4),
    },
  ],
  [
    {
      url: '/nursing-services',
      icon: NursingServicesIcon,
      title: 'Nursing Services',
      id: nanoid(4),
    },
  ],
  [
    {
      url: '/pregnancy-care',
      icon: AntenatalIcon,
      title: 'Pregnancy Care',
      id: nanoid(4),
    },
    {
      url: '/admission',
      icon: AdmissionIcon,
      title: 'Admission',
      id: nanoid(4),
    },
    {
      url: '/allergy',
      icon: AllergyIcon,
      title: 'Allergy',
      id: nanoid(4),
    },
    {
      url: '/consultation',
      icon: ConsultationIcon,
      title: 'Consultation',
      id: nanoid(4),
    },
    {
      url: '/immunization',
      icon: ImmunizationIcon,
      title: 'Immunization',
      id: nanoid(4),
    },
    {
      url: '/laboratory',
      icon: LabResultIcon,
      title: 'Laboratory',
      id: nanoid(4),
    },
    {
      url: '/medication',
      icon: MedicationIcon,
      title: 'Medication',
      id: nanoid(4),
    },
    {
      url: '/radiology',
      icon: RadiologyIcon,
      title: 'Radiology',
      id: nanoid(4),
    },
    {
      url: '/procedure',
      icon: SurgeryIcon,
      title: 'Procedure',
      id: nanoid(4),
    },
    {
      url: '/billing',
      icon: BillingIcon,
      title: 'Billing',
      id: nanoid(4),
    },
    {
      url: '/attachments',
      icon: AttachmentIcon,
      title: 'Attachments',
      id: nanoid(4),
    },
  ],
  [
    {
      url: '/patient-wallet',
      icon: FundWalletIcon,
      title: 'Wallet',
      id: nanoid(4),
    },
  ],
];

/**
 * Filters navigation items based on user type/role
 *
 * This function returns a subset of navigation items that are relevant to the specified user type.
 * Different user roles (doctor, nurse, admin, etc.) have access to different navigation options
 * based on their responsibilities and permissions in the system.
 *
 * The function uses the helper function `pickAndOmitPatientSelectedNavItems` to include only
 * the navigation items that are relevant to the specified user type.
 *
 * @param userType - The type/role of the user (e.g., OrganizationDoctor, OrganizationNurse)
 * @param navItems - The base navigation items to filter (defaults to PatientSelectedNavItems)
 * @returns Filtered navigation items appropriate for the specified user type
 */
function filterByUserType(
  userType: UserType,
  navItems: TPatientSelectedSideNavItems[][] = PatientSelectedNavItems,
  isClinifyHmoHospital?: boolean,
  isHmo?: boolean,
): TPatientSelectedSideNavItems[][] {
  // For non-HMO and non-Clinify HMO hospitals, remove HMO-specific items
  if (!isHmo && !isClinifyHmoHospital) {
    navItems = pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
      navItems,
      [],
      ['Enrollee List'],
    );
  }

  switch (userType) {
    case UserType.OrganizationDoctor:
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [
          'Patient Lookup',
          'Enrollee Lookup',
          'Patient Waiting List',
          'Enrollee Waiting List',
          'Request Investigation',
          'Request Referral',
          'Request Preauthorization',
          'Submit Claim',
          'My Coverage',
          'My Details',
          'My Health',
          'Packages',
          'Vital Signs',
          'Nursing Services',
          'Pregnancy Care',
          'Admission',
          'Allergy',
          'Consultation',
          'Immunization',
          'Laboratory',
          'Medication',
          'Radiology',
          'Procedure',
          'Billing',
          'Attachments',
          ...(isClinifyHmoHospital
            ? (['Enrollee List', 'Appointment'] as TPatientSelectedIconNames[])
            : []),
        ],
        [],
      );
    case UserType.OrganizationNurse:
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [
          'Patient Lookup',
          'Enrollee Lookup',
          'Patient Waiting List',
          'Enrollee Waiting List',
          'Request Investigation',
          'Request Referral',
          'Request Preauthorization',
          'Submit Claim',
          'My Coverage',
          'My Details',
          'My Health',
          'Packages',
          'Vital Signs',
          'Nursing Services',
          'Pregnancy Care',
          'Admission',
          'Allergy',
          'Consultation',
          'Immunization',
          'Laboratory',
          'Medication',
          'Radiology',
          'Procedure',
          'Billing',
          'Attachments',
          ...(isClinifyHmoHospital
            ? (['Enrollee List', 'Appointment'] as TPatientSelectedIconNames[])
            : []),
        ],
        [],
      );
    case UserType.OrganizationRecordOfficer:
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [
          'Patient Lookup',
          'Enrollee Lookup',
          'Patient Waiting List',
          'Enrollee Waiting List',
          'Request Investigation',
          'Request Referral',
          'Request Preauthorization',
          'Submit Claim',
          'Process Investigation',
          'My Coverage',
          'My Details',
          'My Health',
          'Packages',
          'Vital Signs',
          'Nursing Services',
          'Pregnancy Care',
          'Admission',
          'Allergy',
          'Consultation',
          'Immunization',
          'Laboratory',
          'Medication',
          'Radiology',
          'Procedure',
          'Billing',
          'Wallet',
          'Attachments',
          ...(isClinifyHmoHospital ? (['Enrollee List'] as TPatientSelectedIconNames[]) : []),
        ],
        [],
      );
    case UserType.OrganizationFrontDeskOfficer:
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [
          'My Coverage',
          'My Details',
          'Billing',
          'Request Referral',
          'Request Preauthorization',
          'Submit Claim',
          'Wallet',
          ...(isClinifyHmoHospital ? (['Enrollee List'] as TPatientSelectedIconNames[]) : []),
        ],
        [],
      );
    case UserType.Admin:
    case UserType.Patient:
    case UserType.SuperAdmin:
      return [];
    case UserType.OrganizationStaffAdmin:
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [
          'Patient Lookup',
          'Enrollee Lookup',
          'Patient Waiting List',
          'Enrollee Waiting List',
          'Request Investigation',
          'Request Preauthorization',
          'Submit Claim',
          'Process Investigation',
          'My Coverage',
          'My Details',
          'My Health',
          'Vital Signs',
          'Nursing Services',
          'Pregnancy Care',
          'Admission',
          'Allergy',
          'Consultation',
          'Immunization',
          'Laboratory',
          'Medication',
          'Radiology',
          'Procedure',
          'Billing',
          ...(isClinifyHmoHospital ? (['Enrollee List'] as TPatientSelectedIconNames[]) : []),
        ],
        [],
      );
    case UserType.OrganizationAdmin:
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [
          'Patient Lookup',
          'Enrollee Lookup',
          'Patient Waiting List',
          'Enrollee Waiting List',
          'Request Investigation',
          'Request Referral',
          'Request Preauthorization',
          'Submit Claim',
          'Process Investigation',
          'My Coverage',
          'My Details',
          'My Health',
          'Packages',
          'Vital Signs',
          'Nursing Services',
          'Pregnancy Care',
          'Admission',
          'Allergy',
          'Consultation',
          'Immunization',
          'Laboratory',
          'Medication',
          'Radiology',
          'Procedure',
          'Billing',
          'Attachments',
          'Wallet',
          ...(isClinifyHmoHospital || isHmo
            ? (['Enrollee List', 'Appointment'] as TPatientSelectedIconNames[])
            : []),
        ],
        [],
      );
    case UserType.OrganizationStoreClerk:
      return [];
    case UserType.OrganizationCashier:
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [
          'My Coverage',
          'My Details',
          'Billing',
          'Request Referral',
          'Request Preauthorization',
          'Submit Claim',
          'Wallet',
          ...(isClinifyHmoHospital ? (['Enrollee List'] as TPatientSelectedIconNames[]) : []),
        ],
        [],
      );
    case UserType.OrganizationBillingOfficer:
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [
          'Patient Lookup',
          'Enrollee Lookup',
          'Patient Waiting List',
          'Enrollee Waiting List',
          'Request Investigation',
          'Request Referral',
          'Request Preauthorization',
          'Submit Claim',
          'My Coverage',
          'My Details',
          'My Health',
          'Packages',
          'Vital Signs',
          'Nursing Services',
          'Pregnancy Care',
          'Admission',
          'Allergy',
          'Consultation',
          'Immunization',
          'Laboratory',
          'Medication',
          'Radiology',
          'Procedure',
          'Billing',
          'Attachments',
          'Wallet',
          ...(isClinifyHmoHospital ? (['Enrollee List'] as TPatientSelectedIconNames[]) : []),
        ],
        [],
      );
    case UserType.OrganizationLabTechnician:
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [
          'Request Investigation',
          'Request Referral',
          'Request Preauthorization',
          'Submit Claim',
          'Process Investigation',
          'My Coverage',
          'My Details',
          'Packages',
          'Vital Signs',
          'Nursing Services',
          'Pregnancy Care',
          'Admission',
          'Allergy',
          'Consultation',
          'Immunization',
          'Medication',
          'Laboratory',
          'Billing',
          'Procedure',
          'Attachments',
          ...(isClinifyHmoHospital ? (['Enrollee List'] as TPatientSelectedIconNames[]) : []),
        ],
        [],
      );
    case UserType.OrganizationRadiographer:
    case UserType.OrganizationRadiologist:
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [
          'Request Investigation',
          'Request Referral',
          'Request Preauthorization',
          'Submit Claim',
          'Process Investigation',
          'My Coverage',
          'My Details',
          'Packages',
          'Vital Signs',
          'Nursing Services',
          'Pregnancy Care',
          'Admission',
          'Allergy',
          'Consultation',
          'Immunization',
          'Billing',
          'Radiology',
          'Medication',
          'Procedure',
          'Attachments',
          ...(isClinifyHmoHospital ? (['Enrollee List'] as TPatientSelectedIconNames[]) : []),
        ],
        [],
      );
    case UserType.Pharmacist:
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [
          'Patient Lookup',
          'Enrollee Lookup',
          'Patient Waiting List',
          'Enrollee Waiting List',
          'My Coverage',
          'My Details',
          'My Health',
          'Request Investigation',
          'Request Referral',
          'Request Preauthorization',
          'Submit Claim',
          'Process Investigation',
          'Vital Signs',
          'Pregnancy Care',
          'Allergy',
          'Consultation',
          'Immunization',
          'Laboratory',
          'Medication',
          'Radiology',
          'Billing',
          'Admission',
          ...(isClinifyHmoHospital ? (['Enrollee List'] as TPatientSelectedIconNames[]) : []),
        ],
        [],
      );
    case UserType.ClaimAdmin:
    case UserType.ClaimOfficer:
    case UserType.ClaimOfficerHOD:
    case UserType.ClaimAccount:
    case UserType.ClaimReviewer:
    case UserType.ClaimReviewerHOD:
    case UserType.ClaimAudit:
    case UserType.ClaimAuditHOD:
    case UserType.ClaimFinance:
    case UserType.ClaimConfirmation:
    case UserType.ClaimAgent:
    case UserType.ClaimAgentHOD:
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [
          'Enrollee List',
          'Enrollee Lookup',
          'My Coverage',
          'My Details',
          'Request Investigation',
          'Request Referral',
          'Request Preauthorization',
          'Submit Claim',
          'Process Investigation',
          'Vital Signs',
          'Consultation',
          'Laboratory',
          'Medication',
          'Radiology',
        ],
        [],
      );
    case UserType.EnrollmentAgent:
    case UserType.EnrollmentTpaNonTpa:
    case UserType.EnrollmentAgency:
    case UserType.FieldOfficer:
    case UserType.FieldManager:
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        ['Enrollee Lookup', 'My Coverage', 'My Details'],
        [],
      );
    case UserType.EnrollmentAdmin:
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        ['Enrollee List', 'Enrollee Lookup', 'My Coverage', 'My Details'],
        [],
      );
    default:
      return [];
  }
}

/**
 * Filters navigation items based on hospital plan type
 *
 * This function returns a subset of navigation items that are available for the specified
 * hospital plan. Different hospital plans (Clinify, Investigation, Laboratory, etc.) have
 * access to different features and navigation options based on their subscription level.
 *
 * For example:
 * - Clinify plans have access to all features
 * - Investigation plans don't have access to Medication
 * - Laboratory plans don't have access to Radiology or Medication
 * - Pharmacy plans don't have access to investigation-related features
 *
 * @param plan - The hospital plan type (e.g., ClinifyPlusInventory, LaboratoryMinusInventory)
 * @param navItems - The navigation items to filter
 * @param replacePatientWithEnrollee
 * @returns Filtered navigation items appropriate for the specified hospital plan
 */
function filterByHospitalPlan(
  plan: HospitalPlan,
  navItems: TPatientSelectedSideNavItems[][],
  replacePatientWithEnrollee?: boolean,
): TPatientSelectedSideNavItems[][] {
  const excludedItemsBasedOnPatientReplacement: TPatientSelectedIconNames[] = replacePatientWithEnrollee
    ? ['Patient Lookup', 'Patient Waiting List']
    : ['Enrollee Lookup', 'Enrollee Waiting List'];
  switch (plan) {
    case HospitalPlan.ClinifyPlusInventory:
    case HospitalPlan.ClinifyMinusInventory: {
      excludedItemsBasedOnPatientReplacement.push('Appointment');
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [],
        excludedItemsBasedOnPatientReplacement,
      );
    }
    case HospitalPlan.InvestigationPlusInventory:
    case HospitalPlan.InvestigationMinusInventory: {
      excludedItemsBasedOnPatientReplacement.push('Appointment');
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [],
        ['Medication', ...excludedItemsBasedOnPatientReplacement],
      );
    }
    case HospitalPlan.LaboratoryPlusInventory:
    case HospitalPlan.LaboratoryMinusInventory: {
      excludedItemsBasedOnPatientReplacement.push('Appointment');
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [],
        ['Radiology', 'Medication', ...excludedItemsBasedOnPatientReplacement],
      );
    }
    case HospitalPlan.RadiologyPlusInventory:
    case HospitalPlan.RadiologyMinusInventory: {
      excludedItemsBasedOnPatientReplacement.push('Appointment');
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [],
        ['Laboratory', 'Medication', ...excludedItemsBasedOnPatientReplacement],
      );
    }
    case HospitalPlan.PharmacyPlusInventory:
    case HospitalPlan.PharmacyMinusInventory: {
      excludedItemsBasedOnPatientReplacement.push('Appointment');
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [],
        [
          'Request Investigation',
          'Process Investigation',
          'Laboratory',
          'Radiology',
          ...excludedItemsBasedOnPatientReplacement,
          ...(replacePatientWithEnrollee
            ? ([
                'Request Preauthorization',
                'Request Referral',
                'Enrollee List',
              ] as TPatientSelectedIconNames[])
            : []),
        ],
      );
    }
    case HospitalPlan.TelemedicineMinusInventory:
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [],
        ['Radiology', 'Laboratory', ...excludedItemsBasedOnPatientReplacement],
      );
    case HospitalPlan.HMOPlusInventory:
    case HospitalPlan.HMOMinusInventory: {
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [],
        ['Patient Lookup', 'Patient Waiting List', 'Appointment'],
      );
    }
    case HospitalPlan.AmbulanceMinusInventory:
    case HospitalPlan.AmbulancePlusInventory: {
      excludedItemsBasedOnPatientReplacement.push('Appointment');
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [],
        [
          'Request Investigation',
          'Process Investigation',
          'Medication',
          'Consultation',
          'Procedure',
          'Immunization',
          'Laboratory',
          'Radiology',
          ...excludedItemsBasedOnPatientReplacement,
        ],
      );
    }
    case HospitalPlan.BloodBankMinusInventory:
    case HospitalPlan.BloodBankPlusInventory: {
      excludedItemsBasedOnPatientReplacement.push('Appointment');
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [],
        [
          'Request Investigation',
          'Process Investigation',
          'Medication',
          'Consultation',
          'Procedure',
          'Immunization',
          'Laboratory',
          'Radiology',
          ...excludedItemsBasedOnPatientReplacement,
        ],
      );
    }
    case HospitalPlan.DentalMinusInventory:
    case HospitalPlan.DentalPlusInventory: {
      excludedItemsBasedOnPatientReplacement.push('Appointment');
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [],
        [
          'Request Investigation',
          'Process Investigation',
          'Medication',
          'Consultation',
          'Procedure',
          'Immunization',
          'Laboratory',
          'Radiology',
          ...excludedItemsBasedOnPatientReplacement,
        ],
      );
    }
    case HospitalPlan.EyeMinusInventory:
    case HospitalPlan.EyePlusInventory: {
      excludedItemsBasedOnPatientReplacement.push('Appointment');
      return pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
        navItems,
        [],
        [
          'Request Investigation',
          'Process Investigation',
          'Medication',
          'Consultation',
          'Procedure',
          'Immunization',
          'Laboratory',
          'Radiology',
          ...excludedItemsBasedOnPatientReplacement,
        ],
      );
    }
    default:
      return navItems;
  }
}

/**
 * Core EMR features that can be excluded from navigation
 *
 * This constant defines a list of navigation items that are considered core Electronic Medical Record
 * (EMR) features. These features can be optionally excluded from the navigation when the
 * `excludeCoreEMRFeatures` parameter is set to true in the `displayMainNavItemsWithPatientSelected` function.
 *
 * Core EMR features typically include advanced clinical and administrative functionalities that
 * might not be relevant or accessible to all users or in all contexts.
 */
const CORE_EMR_TO_EXCLUDE: TPatientSelectedIconNames[] = [
  'Admission',
  'Procedure',
  'Immunization',
  'Billing',
  'Pregnancy Care',
  'Nursing Services',
  'Attachments',
  'Packages',
  'Wallet',
  'Allergy',
  'Request Investigation',
  'Process Investigation',
  'My Health',
  'Consultation',
  'Radiology',
  'Laboratory',
];

/**
 * Main function to determine which navigation items to display when a patient is selected
 *
 * This function combines multiple filtering strategies to determine the final set of navigation
 * items that should be displayed in the side navigation when a patient is selected. It:
 *
 * 1. Filters items based on the user's role/type
 * 2. Further filters items based on the hospital's plan type
 * 3. Optionally excludes core EMR features if specified
 *
 * The resulting navigation items are tailored to the specific user, hospital plan, and context.
 *
 * @param isHmoAgencyUser - Whether the user belongs to an HMO agency
 * @param hospitalPlan - The type of hospital plan (determines available features)
 * @param userType - The role/type of the user (determines permissions)
 * @param excludeCoreEMRFeatures - Whether to exclude core EMR features from navigation
 * @param replacePatientWithEnrollee
 * @returns The filtered navigation items to display
 */
export function displayMainNavItemsWithPatientSelected(
  isHmoAgencyUser: boolean,
  hospitalPlan: HospitalPlan,
  userType: UserType,
  excludeCoreEMRFeatures?: boolean,
  replacePatientWithEnrollee?: boolean,
  isClinifyHmo?: boolean,
): TPatientSelectedSideNavItems[][] {
  // Step 1: Filter navigation items based on user type/role
  let navs: TPatientSelectedSideNavItems[][] = filterByUserType(
    userType,
    undefined,
    isClinifyHmo && !isHmoAgencyUser, // Pass isClinifyHmoHospital flag
    isHmoAgencyUser, // Pass isHmo flag
  );

  // Step 2: Further filter based on hospital plan type
  navs = filterByHospitalPlan(hospitalPlan, navs, replacePatientWithEnrollee);

  // Step 3: Optionally exclude core EMR features
  if (excludeCoreEMRFeatures) {
    let excludedNavs = [...CORE_EMR_TO_EXCLUDE];
    if (hospitalPlan === HospitalPlan.TelemedicineMinusInventory) {
      excludedNavs = CORE_EMR_TO_EXCLUDE.filter((i) => i !== 'Consultation');
    }
    navs = pickAndOmitPatientSelectedNavItems<TPatientSelectedIconNames>(
      navs,
      [], // No additional items to include
      excludedNavs, // Core EMR features to exclude
    );
  }

  return navs;
}
