/* eslint-disable react-hooks/rules-of-hooks */
import {
  B<PERSON><PERSON><PERSON>GROUP_OPTIONS,
  COUNTRIES_OPTIONS,
  GENDER_OPTIONS,
  GENOTYPE_OPTIONS,
  HEIGHT_OPTIONS,
  LAGOS_POLITICAL_WARD_BY_LGA_MAP,
  LGA_BY_STATE_OPTIONS,
  PATIENT_ACCESS_TYPE,
  PATIENT_STATUS,
  PAYMENT_OPTIONS,
  QUESTION_OPTIONS,
  TITLE_GENDER_MAP,
  TITLE_OPTIONS,
  WEIGHT_OPTIONS,
} from '@constant/options';
import MultiGroupAction from 'app/shared/components/buttons/MultiGroupActions';
import Dropdown from 'app/shared/components/dropdown/Dropdown';
import AddressInput from 'app/shared/components/inputs/Address/AddressInput';
import { FlaggedAuthenticationInput } from 'app/shared/components/inputs/AuthenticationInput';
import TextInput from 'app/shared/components/inputs/TextInput';
import DatePicker from 'app/shared/components/pickers/DatePicker';
import { InputRow, SelectWrapper } from 'app/shared/components/wrappers/Wrapper';
import { useServiceOption } from 'app/shared/hooks/fetch/useServiceOption';
import {
  allowPhoneNumberChange,
  getPhoneExtensionCode,
  getUserPayload,
} from 'app/shared/utils/authentication';
import { userType } from 'app/shared/utils/authTracker';
import { BillableRecords, PatientProfileType, UserType } from 'graphql-types/globalTypes';
import React, { FC, useEffect, useRef, useState } from 'react';
import useHospitalProviders from 'app/shared/hooks/forms/useHospitalProviders';
import CoverageForm from 'app/pages/dashboard/components/forms/CoverageForm';
import { defaultServiceDetails, IServiceDetails } from 'app/shared/constant';
import { ExcludeProviders } from 'app/shared/hooks/fetch/usePaymentOption';
import PreauthorizationDetailsInput from 'app/shared/components/inputs/PreauthorizationDetailsInput';
import { Accordion, AccordionItem } from 'app/shared/components/accordion/Accordion';
import { useLazyQuery } from '@apollo/client';
import { useFacilityPreference } from 'app/pages/dashboard/views/settings/hooks/useFacilityPreference';
import omit from 'lodash.omit';
import errorHandler from 'app/shared/utils/errorHandler';
import { useToasts } from 'react-toast-notifications';
import cloneDeep from 'lodash.clonedeep';
import { LOOKUP_BY_HMO } from 'apollo-queries/mains/user';
import { LookupByHmo, LookupByHmoVariables } from 'graphql-types/LookupByHmo';
import { loadFacilityNumber } from 'app/shared/utils/loadFacilityNumber';
import { nanoid } from 'nanoid';
import TextArea from 'app/shared/components/inputs/TextArea';
import { getDTime } from 'app/shared/utils/common';
import { stateList } from 'app/shared/utils/hospitalVariables';
import useHospitalData from 'app/shared/hooks/client/useHospitalData';
import RadioStatus from 'app/shared/components/inputs/Radio';
import { calculateAge } from 'app/shared/components/pickers/helper';
import useEnrollmentAgencyUtil from 'app/pages/dashboard/views/patient-registration/hooks/useEnrollmentAgencyUtil';
import useDrafts from 'app/shared/hooks/forms/useDrafts';
import { ProfileImageContainer } from '../../views/settings/patient/profileImage';
import SingleBillServiceDetail from './SingleBillServiceDetail';
import ProviderOptions from '../../views/provider-network/components/ProviderOptions';
import { getFileNumbers, shouldUpdateFileNumber } from '../../views/patient-coverage/constants';
import { loadHmoProviderAsync } from '../../../../shared/utils/loadHmoProviderAsync';
import { isHmoClaimsManagedByClinify } from '../../../../shared/utils/is-clinify-hmo';
import EnrollmentPaymentSection from '../../views/patient-registration/components/EnrollmentPaymentSection';

interface PersonalInformationFormProps {
  readOnly?: boolean;
  isEditForm?: boolean;
  data?: any;
  inputs: any;
  email?: string;
  phoneNumber?: string;
  country?: string;
  directOnChange: (value: any) => void;
  handleInputChange: (field: string, value: any) => void;
  isRegistering?: boolean;
  myDetails?: boolean;
  handleServiceDetailUpdate?: (param: IServiceDetails[]) => void;
  setInputs: React.Dispatch<any>;
  preauthorizationDetails?: Record<string, any>;
  patientProfileType?: PatientProfileType;
  showProviderDropdown?: boolean;
  registeredWithId?: string;
  clinifyId?: string | null;
  showPremiumOutstanding?: boolean;
  dependentCountOvershoot?: number | null;
  dependentsCount?: number | null;
}

const canRegisterPatient = [
  UserType.OrganizationAdmin,
  UserType.OrganizationNurse,
  UserType.OrganizationStaffAdmin,
  UserType.OrganizationRecordOfficer,
  UserType.OrganizationFrontDeskOfficer,
  UserType.OrganizationBillingOfficer,
  UserType.ClaimAdmin,
  UserType.ClaimOfficer,
  UserType.ClaimOfficerHOD,
  UserType.ClaimReviewer,
  UserType.ClaimAccount,
  UserType.ClaimReviewerHOD,
  UserType.ClaimAudit,
  UserType.ClaimAuditHOD,
  UserType.ClaimFinance,
  UserType.ClaimConfirmation,
  UserType.ClaimAgent,
  UserType.ClaimAgentHOD,
  UserType.EnrollmentAgent,
  UserType.EnrollmentAgency,
  UserType.EnrollmentAdmin,
  UserType.FieldOfficer,
  UserType.FieldManager,
];

export const FILE_NUMBER_MODE_OPTIONS = [
  { value: 'New', label: 'New' },
  { value: 'Existing', label: 'Existing' },
];

export interface IPrimaryProviderDetails {
  id: string;
  name: string;
  address: string;
}

const PersonalInformationForm: FC<PersonalInformationFormProps> = ({
  readOnly,
  handleInputChange,
  inputs,
  data,
  isEditForm = true,
  phoneNumber,
  email,
  country,
  directOnChange,
  isRegistering = false,
  myDetails = false,
  handleServiceDetailUpdate,
  setInputs,
  preauthorizationDetails,
  patientProfileType,
  showProviderDropdown,
  registeredWithId,
  clinifyId,
  showPremiumOutstanding,
  dependentCountOvershoot,
  dependentsCount,
}) => {
  const loggedInUserType = userType();

  const fileNumberRef = useRef<string>('');
  const { activeDraftId } = useDrafts('PATIENT_REGISTRATION');
  const [
    primaryProviderDetails,
    setPrimaryProviderDetails,
  ] = useState<null | IPrimaryProviderDetails>(null);

  const { providers, providersMap, providerOptions } = useHospitalProviders();
  const { showServiceDetails, inputs: facilityPref } = useFacilityPreference();
  const { hospitalShortName, generateFileNumber, fieldOfficers } = facilityPref;

  const {
    hospitalName,
    hospitalAddress,
    hospitalId,
    fullNameWithTitle,
    isLashmaAgency,
    isLashmaProvider,
    isOnlyClinifyManagedProvider,
    profileId,
    isSubscriptionValid,
    isProviderFromHmoWithoutSubscription,
  } = useHospitalData();

  const { enrollmentAgencyDetails } = useEnrollmentAgencyUtil(inputs?.enrollmentTpaNonTpaId);

  const isFacilityAdmin = [UserType.OrganizationAdmin, UserType.OrganizationStaffAdmin].includes(
    loggedInUserType,
  );

  const {
    loadServiceNameOption,
    setServiceType,
    fetchServiceTypeOptions,
    getPriceListFromProvider,
    loadServiceTypeOptions,
  } = useServiceOption();
  const { addToast } = useToasts();
  const newPaymentOptions = [...PAYMENT_OPTIONS];
  [...(providers || [])].forEach((tarrif) => {
    if (ExcludeProviders.includes(tarrif)) return;
    newPaymentOptions.push({ value: tarrif, label: tarrif });
  });
  const patientCoverageDetails =
    inputs?.coverageDetails?.length && Array.isArray(inputs.coverageDetails)
      ? inputs.coverageDetails
      : [{ coverageType: null }];

  const patientServiceDetails =
    inputs?.serviceDetails?.length && Array.isArray(inputs.serviceDetails)
      ? inputs.serviceDetails
      : [{ type: null, name: null, priceId: '', quantity: '', pricePerUnit: 0 }];

  const patientFileNumbers =
    inputs?.fileNumbers?.length && Array.isArray(inputs.fileNumbers)
      ? inputs.fileNumbers
      : [{ coverageRef: '', fileNumber: '' }];

  const handleChangePicture = async (event) => {
    handleInputChange('displayPictureUrl', event.target.files);
  };

  const [extractCoverageInformation, { loading }] = useLazyQuery<LookupByHmo, LookupByHmoVariables>(
    LOOKUP_BY_HMO,
    {
      onError: (err) => {
        addToast(errorHandler(err), { appearance: 'error' });
      },
      fetchPolicy: 'network-only',
    },
  );

  const setMultipleFields = (fields: { [key: string]: any }) => {
    if (isEditForm) {
      setInputs((prevInput) => ({
        ...prevInput,
        ...fields,
      }));

      return;
    }

    directOnChange({
      ...data,
      personalInformation: {
        ...inputs,
        ...fields,
      },
    });
  };

  const handlePopulateCoverageInformation = (index, path) => async () => {
    const { memberNumber, providerId, provider } = inputs?.[path]?.[index];
    const providerIdOrProviderCode = providerId || provider?.id;
    const { data } = await extractCoverageInformation({
      variables: {
        input: {
          enrolleeId: memberNumber,
          providerId: providerIdOrProviderCode,
        },
      },
    });
    const payload = data?.verifyEnrollee?.list?.[0];
    if (payload) {
      const newData = [...patientCoverageDetails];
      const { personalInformation, user } = cloneDeep<any>(payload);
      newData[index] = {
        ...newData[index],
        ...omit(payload.hmos?.list?.[0], [
          'visitationId',
          'verificationCode',
          'checkedIn',
          'employer',
        ]),
      };
      const fields = ['firstName', 'lastName', 'middleName', 'dateOfBirth', 'gender', 'title'];
      if (user?.phoneNumber) {
        const countryCode = getPhoneExtensionCode(user?.country || country || 'Nigeria');
        if (allowPhoneNumberChange(countryCode, user?.phoneNumber)) {
          directOnChange({ phoneNumber: user?.phoneNumber });
        }
      }
      fields.forEach((element) => {
        handleInputChange(element, personalInformation[element]);
      });
      handleInputChange('coverageDetails', newData);
    }
  };

  const handleCoverageChange = (
    idx: number,
    field: string | string[],
    value: { key: string; value: any },
  ) => {
    const newData = [...patientCoverageDetails];
    if (Array.isArray(field)) {
      field.forEach((item, index) => {
        newData[idx] = {
          ...newData[idx],
          [item]: value[index],
        };
      });
    } else {
      newData[idx] = {
        ...newData[idx],
        [field]: value,
      };
    }

    const updateFileNumber = shouldUpdateFileNumber(
      { ...patientCoverageDetails[idx] },
      { ...newData[idx] },
    );

    if (!updateFileNumber || !generateFileNumber) {
      return handleInputChange('coverageDetails', newData);
    }

    let coverageRef = nanoid(8);

    if (newData[idx]?.id) {
      coverageRef = newData[idx]?.id;
    }

    newData[idx] = {
      ...newData[idx],
      id: coverageRef,
    };

    const fileNumbers = cloneDeep([...patientFileNumbers]);

    let index = idx;
    index = fileNumbers.findIndex(
      ({ coverageRef: _coverageRef }) => _coverageRef && _coverageRef === coverageRef,
    );

    if (index === -1 || index > fileNumbers?.length) {
      if (!fileNumbers[idx]?.coverageRef) {
        fileNumbers[idx] = { coverageRef, fileNumber: '' };

        index = idx;
      } else {
        index = fileNumbers?.length;
        fileNumbers[index] = { coverageRef, fileNumber: '' };
      }
    }

    if (!fileNumbers[index]?.coverageRef) {
      fileNumbers[index] = { coverageRef, fileNumber: '' };
    }

    const { tempFileNumber } = getFileNumbers(hospitalShortName, { ...newData[idx] });
    if (
      newData[idx]?.coverageType !== 'Family' ||
      (newData[idx]?.coverageType === 'Family' && !fileNumbers[index]?.existingFamily)
    ) {
      fileNumbers[index].fileNumber = tempFileNumber;
    }

    setMultipleFields({ coverageDetails: newData, fileNumbers });
  };

  const handleDeleteCoverage = (idx: number, coverageData: Record<string, any>[]) => {
    const itemToDelete = patientCoverageDetails.find((_, i) => i === idx + 1);

    if (!itemToDelete.id) return handleInputChange('coverageDetails', coverageData);

    const fileNumbers = cloneDeep([...patientFileNumbers]);
    const filteredFileNumbers = fileNumbers.filter(
      ({ coverageRef }) => coverageRef !== itemToDelete.id,
    );
    let coverageIdsToDelete = [itemToDelete.id];

    if (inputs?.coverageIdsToDelete?.length) {
      coverageIdsToDelete = [...inputs?.coverageIdsToDelete, itemToDelete.id];
    }

    setMultipleFields({
      coverageDetails: coverageData,
      fileNumbers: filteredFileNumbers,
      coverageIdsToDelete,
    });
  };

  const secondaryPhoneNumberCountryCode = inputs?.secondaryPhoneNumber?.countryCode
    ? inputs?.secondaryPhoneNumber?.countryCode.replace(/^\+/, '')
    : '234';
  const secondaryPhoneNumberValue = inputs?.secondaryPhoneNumber?.value || '';

  async function populateHmoDetails() {
    const res = await loadHmoProviderAsync(getUserPayload().orgName);
    const _hmo = res.options?.find(({ label }) => label === getUserPayload().orgName);
    if (!_hmo || !isHmoClaimsManagedByClinify(_hmo?.label)) return;
    const fieldOfficerInfo = (fieldOfficers || []).find((_item) => _item?.profileId === profileId);

    handleCoverageChange(
      0,
      [
        'coverageType',
        'primaryProviderName',
        'primaryProviderId',
        'primaryProviderAddress',
        'provider',
        'providerId',
        'memberStatus',
        'enrollmentDateTime',
        'enrolledBy',
        'registrationSource',
        'planCategory',
        'administrationAgency',
        'enrollmentAgency',
        'enrollmentTpaNonTpa',
        'enrollmentAgencyId',
        'enrollmentTpaNonTpaId',
      ],
      [
        'HMO',
        primaryProviderDetails?.name,
        primaryProviderDetails?.id,
        primaryProviderDetails?.address,
        { id: _hmo.providerCode, name: _hmo.label },
        _hmo.providerCode,
        'Pending',
        getDTime(),
        fullNameWithTitle,
        'Core',
        'Individual',
        fieldOfficerInfo?.administrationAgency || 'LASHMA',
        enrollmentAgencyDetails?.name || fieldOfficerInfo?.enrollmentAgency || null,
        enrollmentAgencyDetails?.tpaNonTpa?.name,
        enrollmentAgencyDetails?.id || null,
        enrollmentAgencyDetails?.tpaNonTpa?.id || null,
      ] as any,
    );
  }

  useEffect(() => {
    if (!primaryProviderDetails?.id) return;

    if (patientCoverageDetails?.[0]?.primaryProviderId === primaryProviderDetails?.id) {
      return;
    }

    const isEnrollment = [UserType.EnrollmentAgency, UserType.EnrollmentAgent].includes(
      loggedInUserType,
    );
    if (
      isLashmaAgency &&
      isEnrollment &&
      getUserPayload()?.enrollmentAgencyId &&
      !enrollmentAgencyDetails?.id
    ) {
      return;
    }

    if (isRegistering && !!getUserPayload()?.orgHmoId && !!fullNameWithTitle && !activeDraftId) {
      populateHmoDetails();
    }
  }, [
    primaryProviderDetails?.name,
    primaryProviderDetails?.id,
    fullNameWithTitle,
    enrollmentAgencyDetails?.id,
    activeDraftId,
  ]);

  useEffect(() => {
    if (isLashmaAgency) {
      if (!inputs?.coverageDetails?.[0]?.primaryProviderName) {
        setPrimaryProviderDetails({
          id: hospitalId,
          name: hospitalName,
          address: hospitalAddress || '',
        });
      }
    }
  }, [hospitalName, hospitalId, hospitalAddress, isLashmaAgency]);

  const getPatientCategories = (value) => {
    if (value === 'None') {
      return [value];
    }

    let tempCategory = (inputs.patientCategory || []).filter((_item) => _item !== 'None');

    if (['Underage', 'Elderly'].includes(value)) {
      if (value === 'Underage') {
        tempCategory = tempCategory.filter((_item) => _item !== 'Elderly');
      } else {
        tempCategory = tempCategory.filter((_item) => _item !== 'Underage');
      }
    }

    if (tempCategory.includes(value)) {
      tempCategory = tempCategory.filter((_item) => _item !== value);
    } else {
      tempCategory = [...tempCategory, value];
    }

    return tempCategory;
  };

  const isClinifyManaged = patientCoverageDetails?.some((_item) =>
    isHmoClaimsManagedByClinify(_item?.provider?.name),
  );

  const disableProviderForm =
    isEditForm && isClinifyManaged ? readOnly || !getUserPayload()?.orgHmoId : readOnly;
  return (
    <>
      {canRegisterPatient.includes(loggedInUserType) ? (
        <div className="flex-row-center">
          <ProfileImageContainer
            url={
              typeof inputs.displayPictureUrl === 'object' && inputs?.displayPictureUrl?.[0]
                ? URL.createObjectURL(inputs.displayPictureUrl[0])
                : inputs.displayPictureUrl
            }
            changePicture={handleChangePicture}
            disable={readOnly}
          />
        </div>
      ) : null}

      {showProviderDropdown && !(isLashmaAgency || isLashmaProvider) ? (
        <InputRow>
          <SelectWrapper fullWidth>
            <ProviderOptions
              onChange={(hospitalId, hospitalName, hospitalAddress) => {
                setPrimaryProviderDetails({
                  id: hospitalId,
                  name: hospitalName || '',
                  address: hospitalAddress || '',
                });
                directOnChange({ registeredWithId: hospitalId });
              }}
              value={registeredWithId || null}
              title="Facility Name"
              useDropdownComponent
              readOnly={isEditForm}
              isRequired
            />
          </SelectWrapper>
        </InputRow>
      ) : (
        <></>
      )}

      {isLashmaAgency ? (
        <>
          {patientCoverageDetails.map((val, idx) => (
            <CoverageForm
              handleInputChange={(field, value) => handleCoverageChange(idx, field, value)}
              readOnly={readOnly}
              inputs={val}
              handlePopulateCoverageInformation={handlePopulateCoverageInformation(
                idx,
                'coverageDetails',
              )}
              loadingCoverageInformation={loading}
              isRegistering={isRegistering}
              isEditForm={isEditForm}
              isOnProfile
              primaryProviderDetails={primaryProviderDetails}
              hideCoverageType={isLashmaAgency}
              showPremiumOutstanding={showPremiumOutstanding}
              dependentCountOvershoot={dependentCountOvershoot}
              dependentsCount={dependentsCount}
            />
          ))}
        </>
      ) : null}

      <InputRow>
        <SelectWrapper>
          <Dropdown
            onChange={({ value }) => {
              if (readOnly || !value) return () => {};

              const gender = TITLE_GENDER_MAP[value];
              if (gender) {
                setMultipleFields({ title: value, gender });
              } else {
                handleInputChange('title', value);
              }
            }}
            readOnly={readOnly || disableProviderForm}
            options={TITLE_OPTIONS}
            value={inputs?.title || data?.title || null}
            placeholder="Select One"
            title="Title"
            creatable
            noPadding
          />
        </SelectWrapper>
        <TextInput
          name="firstName"
          value={inputs?.firstName}
          title="First Name"
          onChange={({ target: { value } }) => handleInputChange('firstName', value)}
          readOnly={readOnly || disableProviderForm}
          placeholder="Enter First Name"
          isRequired
          skipTranslate
        />
      </InputRow>

      <InputRow>
        <TextInput
          name="middleName"
          value={inputs?.middleName}
          title="Middle Name"
          onChange={({ target: { value } }) => handleInputChange('middleName', value)}
          readOnly={readOnly || disableProviderForm}
          placeholder="Enter Middle Name"
          skipTranslate
        />

        <TextInput
          name="lastName"
          value={inputs?.lastName}
          title="Last Name"
          onChange={({ target: { value } }) => handleInputChange('lastName', value)}
          readOnly={readOnly || disableProviderForm}
          placeholder="Enter Last Name"
          isRequired
          skipTranslate
        />
      </InputRow>
      <InputRow>
        <SelectWrapper>
          <Dropdown
            onChange={({ value }) => (!readOnly ? handleInputChange('gender', value) : null)}
            readOnly={readOnly}
            options={GENDER_OPTIONS}
            value={inputs?.gender || null}
            placeholder="Select One"
            title="Gender"
            creatable
            isRequired
            noPadding
          />
        </SelectWrapper>

        <SelectWrapper noPadding>
          <DatePicker
            label="Date of Birth"
            withBorderRadius
            onChange={(date) => {
              const age = calculateAge(date);
              if (age < 18 || age >= 60) {
                setMultipleFields({
                  dateOfBirth: date,
                  patientCategory: getPatientCategories(age < 18 ? 'Underage' : 'Elderly'),
                });
              } else {
                handleInputChange('dateOfBirth', date);
              }
            }}
            readOnly={readOnly}
            value={inputs?.dateOfBirth}
            placeholderText="Select Date"
            type="DateOnly"
            isRequired
            disableTextInput
            withAge
          />
        </SelectWrapper>
      </InputRow>

      {isLashmaAgency && !isFacilityAdmin ? null : (
        <>
          <InputRow>
            <SelectWrapper>
              <Dropdown
                onChange={({ value }) =>
                  !readOnly ? handleInputChange('bloodGroup', value) : null
                }
                options={BLOODGROUP_OPTIONS}
                readOnly={readOnly || disableProviderForm}
                value={inputs?.bloodGroup || null}
                placeholder="Select One"
                title="Blood Group"
                noPadding
              />
            </SelectWrapper>

            <SelectWrapper>
              <Dropdown
                onChange={({ value }) => (!readOnly ? handleInputChange('genoType', value) : null)}
                options={GENOTYPE_OPTIONS}
                readOnly={readOnly || disableProviderForm}
                value={inputs?.genoType || null}
                placeholder="Select One"
                title="Genotype"
                noPadding
              />
            </SelectWrapper>
          </InputRow>
          <InputRow>
            <SelectWrapper>
              <TextInput
                name="weight"
                value={inputs?.weight}
                onChange={({ target: { value } }) => handleInputChange('weight', value)}
                readOnly={readOnly || disableProviderForm}
                title="Weight"
                fullWidth
                withTag
                noPadding
                skipTranslate
                tag={
                  <Dropdown
                    onChange={({ value }) =>
                      !readOnly ? handleInputChange('weightUnit', value) : null
                    }
                    options={WEIGHT_OPTIONS}
                    readOnly={readOnly || disableProviderForm}
                    value={inputs?.weightUnit || null}
                    placeholder="Unit"
                    withTag
                    noPadding
                  />
                }
                placeholder="Enter Weight"
              />
            </SelectWrapper>
            <SelectWrapper>
              <TextInput
                name="height"
                value={inputs?.height}
                onChange={({ target: { value } }) => handleInputChange('height', value)}
                readOnly={readOnly || disableProviderForm}
                title="Height"
                fullWidth
                withTag
                noPadding
                skipTranslate
                tag={
                  <Dropdown
                    onChange={({ value }) =>
                      !readOnly ? handleInputChange('heightUnit', value) : null
                    }
                    readOnly={readOnly || disableProviderForm}
                    options={HEIGHT_OPTIONS}
                    value={inputs?.heightUnit || null}
                    placeholder="Unit"
                    withTag
                    noPadding
                  />
                }
                placeholder="Enter Height"
              />
            </SelectWrapper>
          </InputRow>
        </>
      )}
      <InputRow>
        <FlaggedAuthenticationInput
          value={data?.user?.phoneNumber || phoneNumber || '+234'}
          label="Primary Phone Number"
          name="phoneNumber"
          placeholder="Enter Phone Number"
          countryName={data?.user?.country || country || 'Nigeria'}
          readOnly={isEditForm || disableProviderForm}
          onChange={({ target: { value } }) => {
            const countryCode = getPhoneExtensionCode(data?.user?.country || country || 'Nigeria');
            allowPhoneNumberChange(countryCode, value) && directOnChange({ phoneNumber: value });
          }}
          changeCountryName={(countryName, countryCode) =>
            directOnChange({ phoneNumber: countryCode, country: countryName })
          }
          smallHeight
          withBorderRadius
          noMargin
          fullWidth
          padded
          halfWidth
          disabled={isEditForm}
          allowFlagSwitch
          fullBorder
          isRequired={isRegistering && patientProfileType !== PatientProfileType.Others}
        />

        <TextInput
          name="email"
          value={email || data?.user?.nonCorporateEmail}
          onChange={(event) => directOnChange({ email: event.target.value })}
          title="Primary Email Address"
          readOnly={isEditForm || readOnly || myDetails}
          placeholder="Enter Email Address"
          skipTranslate
        />
      </InputRow>

      <InputRow>
        <FlaggedAuthenticationInput
          name="secondaryPhoneNumber"
          value={`+${secondaryPhoneNumberCountryCode}${secondaryPhoneNumberValue}`}
          onChange={({ target: { value } }) => {
            const countryCode = `+${secondaryPhoneNumberCountryCode}`;
            const countryCodeLength = countryCode.length;
            if (
              value?.length < countryCodeLength ||
              value.slice(0, countryCodeLength) !== countryCode
            ) {
              return;
            }
            handleInputChange('secondaryPhoneNumber', {
              ...inputs.secondaryPhoneNumber,
              value: value.slice(countryCodeLength),
            });
          }}
          changeCountryName={(countryName, countryCode) =>
            handleInputChange('secondaryPhoneNumber', {
              value: secondaryPhoneNumberValue,
              countryName,
              countryCode: countryCode.replace(/^\+/, ''),
            })
          }
          label="Secondary Phone Number"
          placeholder="Enter Secondary Phone Number"
          countryName={inputs?.secondaryPhoneNumber?.countryName || 'Nigeria'}
          smallHeight
          readOnly={readOnly || disableProviderForm}
          withBorderRadius
          noMargin
          fullWidth
          padded
          halfWidth
          allowFlagSwitch
          fullBorder
        />
        {isLashmaAgency || isLashmaProvider ? (
          <SelectWrapper>
            <Dropdown
              title="LGA Of Residence"
              options={LGA_BY_STATE_OPTIONS[inputs?.stateOfResidence || ''] || []}
              value={inputs?.lga}
              placeholder="Select One"
              creatable
              noPadding
              readOnly={readOnly || disableProviderForm}
              onChange={({ value }) => handleInputChange('lga', value)}
              isRequired={
                inputs?.patientProfileType !== PatientProfileType.Others &&
                !!getUserPayload()?.orgHmoId
              }
            />
          </SelectWrapper>
        ) : (
          <TextInput
            name="email"
            value={inputs?.secondaryEmail}
            onChange={({ target: { value } }) => handleInputChange('secondaryEmail', value)}
            title="Secondary Email Address"
            readOnly={readOnly || disableProviderForm}
            placeholder="Enter Secondary Email Address"
            skipTranslate
          />
        )}

        {isLashmaAgency || isLashmaProvider ? (
          <>
            <SelectWrapper>
              <Dropdown
                onChange={({ value }) =>
                  !readOnly ? handleInputChange('nationality', value) : null
                }
                options={COUNTRIES_OPTIONS}
                readOnly={readOnly || disableProviderForm}
                value={inputs.nationality || null}
                placeholder="Select One"
                title="Country Of Origin"
                creatable
                noPadding
              />
            </SelectWrapper>
            <SelectWrapper>
              <Dropdown
                onChange={({ value }) => (!readOnly ? handleInputChange('state', value) : null)}
                options={(stateList[inputs?.nationality?.toLowerCase() || ''] || []).map(
                  ({ name }) => ({
                    label: name,
                    value: name,
                  }),
                )}
                readOnly={readOnly || disableProviderForm}
                value={inputs.state || null}
                placeholder="Select One"
                title="State Of Origin"
                creatable
                noPadding
                skipTranslate
              />
            </SelectWrapper>
            {/* <SelectWrapper>
              <Dropdown
                title="City"
                options={[]}
                value={inputs?.city || null}
                placeholder="Select One"
                creatable
                noPadding
                readOnly={readOnly}
                onChange={({ value }) => handleInputChange('city', value)}
              />
            </SelectWrapper>
            <SelectWrapper>
              <Dropdown
                title="Place Of Birth"
                options={[]}
                value={inputs?.placeOfBirth || null}
                placeholder="Select One"
                creatable
                noPadding
                readOnly={readOnly}
                onChange={({ value }) => handleInputChange('placeOfBirth', value)}
              />
            </SelectWrapper> */}
          </>
        ) : (
          <>
            <SelectWrapper>
              <Dropdown
                title="Country Of Residence"
                options={COUNTRIES_OPTIONS}
                value={inputs?.countryOfResidence || null}
                placeholder="Select One"
                creatable
                noPadding
                readOnly={readOnly || disableProviderForm}
                onChange={({ value }) => handleInputChange('countryOfResidence', value)}
              />
            </SelectWrapper>
            <SelectWrapper>
              <Dropdown
                title="State Of Residence"
                options={(stateList[inputs?.countryOfResidence?.toLowerCase() || ''] || []).map(
                  ({ name }) => ({
                    label: name,
                    value: name,
                  }),
                )}
                value={inputs?.stateOfResidence || null}
                placeholder="Select One"
                creatable
                noPadding
                readOnly={readOnly || disableProviderForm}
                onChange={({ value }) => handleInputChange('stateOfResidence', value)}
              />
            </SelectWrapper>
          </>
        )}
      </InputRow>

      {isLashmaAgency || isLashmaProvider ? null : (
        <InputRow>
          <SelectWrapper>
            <Dropdown
              title="LGA Of Residence"
              options={LGA_BY_STATE_OPTIONS[inputs?.stateOfResidence || ''] || []}
              value={inputs?.lga}
              placeholder="Select One"
              creatable
              noPadding
              readOnly={readOnly || disableProviderForm}
              onChange={({ value }) => handleInputChange('lga', value)}
              isRequired={
                inputs?.patientProfileType !== PatientProfileType.Others &&
                !!getUserPayload()?.orgHmoId
              }
            />
          </SelectWrapper>
          {inputs?.stateOfResidence === 'Lagos State' ? (
            <SelectWrapper>
              <Dropdown
                title="Ward"
                options={LAGOS_POLITICAL_WARD_BY_LGA_MAP[inputs?.lga || ''] || []}
                value={inputs?.ward}
                placeholder="Select One"
                creatable
                noPadding
                readOnly={readOnly || disableProviderForm}
                onChange={({ value }) => handleInputChange('ward', value)}
              />
            </SelectWrapper>
          ) : (
            <TextInput
              name="ward"
              value={inputs?.ward}
              onChange={({ target: { value } }) => handleInputChange('ward', value)}
              title="Ward"
              readOnly={readOnly || disableProviderForm}
              placeholder="Enter Ward"
              skipTranslate
            />
          )}
        </InputRow>
      )}

      <SelectWrapper fullWidth noPadding>
        <div className="google-address-wrapper-row full-width">
          <AddressInput
            label="Contact Address"
            placeholder="Enter Contact Address"
            value={inputs.address}
            handleInputChange={handleInputChange}
            fieldPath="address"
            readOnly={readOnly || disableProviderForm}
            isRequired
          />
        </div>
      </SelectWrapper>

      {showProviderDropdown && (isLashmaAgency || isLashmaProvider) ? (
        <InputRow>
          <SelectWrapper fullWidth>
            <ProviderOptions
              onChange={(hospitalId, hospitalName, hospitalAddress) => {
                setPrimaryProviderDetails({
                  id: hospitalId,
                  name: hospitalName || '',
                  address: hospitalAddress || '',
                });
                directOnChange({ registeredWithId: hospitalId });
              }}
              value={registeredWithId || null}
              title="Facility Name"
              useDropdownComponent
              readOnly={isEditForm}
              isRequired
              filterEnrolleeLimit
              filterActiveFacility
            />
          </SelectWrapper>
        </InputRow>
      ) : (
        <></>
      )}

      <RadioStatus
        text="Category"
        options={['Underage', 'Elderly', 'Pregnant', 'Disabled', 'None']}
        checked={inputs.patientCategory || []}
        onChange={(value) => handleInputChange('patientCategory', getPatientCategories(value))}
        disabled={readOnly || disableProviderForm}
        inputType="checkbox"
      />

      {!!getUserPayload()?.orgHmoId || isOnlyClinifyManagedProvider ? null : (
        <>
          {isRegistering || canRegisterPatient.includes(loggedInUserType) ? (
            <>
              <Dropdown
                title="Tariff"
                options={providerOptions}
                value={inputs.provider}
                onChange={({ value }) => {
                  directOnChange({
                    ...data,
                    personalInformation: {
                      ...inputs,
                      provider: value,
                      providerServiceName: null,
                      serviceDetails: [
                        { ...defaultServiceDetails, paymentType: value },
                        ...(patientServiceDetails?.length > 1
                          ? patientServiceDetails.splice(1)
                          : []),
                      ],
                    },
                  });
                  fetchServiceTypeOptions(value);
                  const providerId = providersMap?.[value]?.providerId;
                  if (providerId) {
                    getPriceListFromProvider(providerId);
                  }
                }}
                creatable={false}
                readOnly
              />

              {showServiceDetails && (
                <Accordion>
                  {(setExpand, state) => (
                    <AccordionItem
                      title="Service Details"
                      id={0}
                      state={state}
                      key={0}
                      colorHeader
                      onHederClick={setExpand}
                    >
                      <SingleBillServiceDetail
                        inputs={inputs}
                        readOnly={!!readOnly}
                        paymentOption={newPaymentOptions}
                        setServiceType={setServiceType}
                        handleInputChange={handleInputChange}
                        loadServiceNameOption={loadServiceNameOption}
                        loadServiceTypeOptions={loadServiceTypeOptions}
                        setInputs={(param) =>
                          directOnChange({
                            ...data,
                            personalInformation: {
                              ...inputs,
                              ...param,
                            },
                          })
                        }
                        updateRecordPath="providerServiceName"
                        fetchServiceTypeOptions={fetchServiceTypeOptions}
                        updateServiceDetail={(params) =>
                          handleServiceDetailUpdate &&
                          handleServiceDetailUpdate(params?.serviceDetails)
                        }
                      />
                    </AccordionItem>
                  )}
                </Accordion>
              )}

              {setInputs && (
                <PreauthorizationDetailsInput
                  record={BillableRecords.Registration}
                  setInputs={setInputs}
                  inputs={preauthorizationDetails as Record<string, any>}
                  parentRecordId={data?.id}
                />
              )}
            </>
          ) : null}
        </>
      )}

      {isLashmaAgency ? null : (
        <>
          {patientCoverageDetails.map((val, idx) => (
            <>
              <CoverageForm
                handleInputChange={(field, value) => handleCoverageChange(idx, field, value)}
                readOnly={readOnly}
                inputs={val}
                handlePopulateCoverageInformation={handlePopulateCoverageInformation(
                  idx,
                  'coverageDetails',
                )}
                loadingCoverageInformation={loading}
                isRegistering={isRegistering}
                isEditForm={isEditForm}
                isOnProfile
                primaryProviderDetails={primaryProviderDetails}
                hideCoverageType={isLashmaAgency}
              />
              <InputRow key={idx}>
                {!readOnly && !isProviderFromHmoWithoutSubscription && (
                  <MultiGroupAction
                    items={patientCoverageDetails}
                    index={idx}
                    initialItem={{ coverageType: null }}
                    onClick={(value, action) => {
                      if (action === 'Delete') {
                        handleDeleteCoverage(idx, value);
                      } else {
                        handleInputChange('coverageDetails', value);
                      }
                    }}
                  />
                )}
              </InputRow>
            </>
          ))}
        </>
      )}

      {isLashmaAgency || (isLashmaProvider && !isSubscriptionValid) ? null : (
        <>
          <InputRow>
            <SelectWrapper extraBottomPadding>
              <Dropdown
                title="Enrol In Clinical Trials"
                options={QUESTION_OPTIONS}
                onChange={({ value }) => handleInputChange('clinicalTrials', value)}
                placeholder="Select One"
                value={inputs.clinicalTrials || null}
                readOnly={readOnly}
                noPadding
              />
            </SelectWrapper>

            {patientFileNumbers?.map(({ coverageRef, fileNumber, existingFamily }, idx) => {
              const coverage = patientCoverageDetails.find(
                ({ id: coverageId }) => coverageId === coverageRef,
              );
              const coverageType = coverage?.coverageType;

              return (
                <>
                  {coverageType === 'Family' ? (
                    <SelectWrapper>
                      <Dropdown
                        title="File Number"
                        options={FILE_NUMBER_MODE_OPTIONS}
                        onChange={({ value }) => {
                          const fileNumbers = [...patientFileNumbers];
                          const fileNumberToUse = value === 'Existing' ? '' : fileNumberRef.current;
                          fileNumbers[idx] = {
                            coverageRef,
                            fileNumber: fileNumberToUse,
                            existingFamily: value === 'Existing',
                          };
                          fileNumberRef.current = fileNumber;
                          handleInputChange('fileNumbers', fileNumbers);
                        }}
                        placeholder="Select One"
                        value={existingFamily ? 'Existing' : 'New'}
                        readOnly={readOnly}
                        noPadding
                        creatable={false}
                      />
                    </SelectWrapper>
                  ) : (
                    <></>
                  )}

                  <div className="muliple-half" key={idx}>
                    {existingFamily ? (
                      <Dropdown
                        title="Patient Card/File Number"
                        options={[]}
                        onChange={({ value }) => {
                          const fileNumbers = [...patientFileNumbers];
                          fileNumbers[idx] = {
                            ...fileNumbers[idx],
                            coverageRef,
                            fileNumber: value,
                          };
                          handleInputChange('fileNumbers', fileNumbers);
                        }}
                        placeholder="Select One"
                        value={fileNumber || null}
                        readOnly={readOnly}
                        noPadding
                        isAsync
                        creatable={false}
                        loadOptions={loadFacilityNumber}
                        additional={{ faciltyRef: `${hospitalShortName}/F` }}
                      />
                    ) : (
                      <TextInput
                        name="fileNumbers"
                        title="Patient Card/File Number"
                        onChange={({ target: { value } }) => {
                          const fileNumbers = [...patientFileNumbers];
                          fileNumbers[idx] = { coverageRef, fileNumber: value };
                          handleInputChange('fileNumbers', fileNumbers);
                        }}
                        value={fileNumber}
                        readOnly={readOnly || generateFileNumber}
                        placeholder="Enter Patient Card/File Number"
                        fullWidth
                        skipTranslate
                      />
                    )}
                  </div>
                  {!readOnly && !generateFileNumber && (
                    <SelectWrapper fullWidth>
                      <MultiGroupAction
                        items={patientFileNumbers}
                        index={idx}
                        initialItem=""
                        onClick={(value) => handleInputChange('fileNumbers', value)}
                      />
                    </SelectWrapper>
                  )}
                </>
              );
            })}
          </InputRow>

          <InputRow>
            <TextInput
              name="nin"
              title="NIN"
              value={inputs?.nin}
              onChange={({ target: { value } }) => {
                handleInputChange('nin', value);
              }}
              readOnly={readOnly}
              placeholder="Enter NIN"
              skipTranslate
            />
            <TextInput
              name="votersId"
              title="Voter's ID"
              value={inputs?.votersId}
              readOnly={readOnly}
              onChange={({ target: { value } }) => handleInputChange('votersId', value)}
              placeholder="Enter Voter's ID"
              skipTranslate
            />
          </InputRow>

          <InputRow>
            <TextInput
              name="passportNumber"
              title="Passport Number"
              value={inputs?.passportNumber}
              onChange={({ target: { value } }) => handleInputChange('passportNumber', value)}
              readOnly={readOnly}
              placeholder="Enter Passport Number"
              skipTranslate
            />
            <TextInput
              name="bvn"
              title="BVN"
              value={inputs?.bvn}
              onChange={({ target: { value } }) => handleInputChange('bvn', value)}
              readOnly={readOnly}
              placeholder="Enter BVN"
              skipTranslate
            />
          </InputRow>
        </>
      )}

      {isLashmaAgency ? null : (
        <>
          {isRegistering || canRegisterPatient.includes(loggedInUserType) ? (
            <InputRow>
              <SelectWrapper>
                <Dropdown
                  title="Patient Status"
                  options={PATIENT_STATUS}
                  onChange={({ value }) => handleInputChange('patientStatus', value)}
                  placeholder="Select One"
                  value={inputs.patientStatus || null}
                  readOnly={readOnly}
                  noPadding
                />
              </SelectWrapper>
              <SelectWrapper>
                <Dropdown
                  title="Access Type"
                  options={PATIENT_ACCESS_TYPE}
                  onChange={({ value }) => handleInputChange('dataAccessType', value)}
                  placeholder="Select One"
                  value={inputs.dataAccessType || null}
                  readOnly
                  // readOnly={readOnly || inputs.dataAccessType === 'Custom'}
                  noPadding
                />
              </SelectWrapper>

              {inputs?.patientStatus?.toLowerCase() === 'dead' ? (
                <>
                  <SelectWrapper>
                    <DatePicker
                      label="Death Date and Time"
                      withBorderRadius
                      readOnly={readOnly}
                      onChange={(date) => handleInputChange('deathDateTime', date)}
                      value={inputs.deathDateTime}
                      minDate={new Date(new Date().getFullYear() - 100, new Date().getMonth())}
                      maxDate={new Date()}
                    />
                  </SelectWrapper>
                  <SelectWrapper>
                    <TextInput
                      name="deathLocation"
                      value={inputs?.deathLocation}
                      title="Death Location"
                      onChange={({ target: { value } }) =>
                        handleInputChange('deathLocation', value)
                      }
                      readOnly={readOnly}
                      placeholder="Enter Death Location"
                      fullWidth
                    />
                  </SelectWrapper>
                  <SelectWrapper fullWidth>
                    <TextInput
                      name="causeOfDeath"
                      value={inputs?.causeOfDeath}
                      title="Cause Of Death"
                      onChange={({ target: { value } }) => handleInputChange('causeOfDeath', value)}
                      readOnly={readOnly}
                      placeholder="Enter Cause Of Death"
                      fullWidth
                    />
                  </SelectWrapper>
                </>
              ) : (
                <></>
              )}
              <SelectWrapper fullWidth noPadding>
                <TextArea
                  name="registrationNote"
                  label="Additional Note"
                  fullWidth
                  onChange={({ target: { value } }) => handleInputChange('registrationNote', value)}
                  value={inputs.registrationNote}
                  readOnly={readOnly}
                />
              </SelectWrapper>
            </InputRow>
          ) : (
            <></>
          )}
        </>
      )}

      <InputRow>
        <div style={{ display: 'flex', gap: 8, padding: 15 }}>
          <input
            type="checkbox"
            checked={inputs?.shareData}
            onChange={({ target: { checked } }) => handleInputChange('shareData', checked)}
            disabled={readOnly}
          />
          <span>
            I agree to have my data shared with institutions for clinical trials, research purposes
            and studies.
          </span>
        </div>
      </InputRow>

      <EnrollmentPaymentSection clinifyId={clinifyId} />
    </>
  );
};

export default PersonalInformationForm;
