import React, { Fragment, useState } from 'react';
import { InputRow, SelectWrapper } from 'app/shared/components/wrappers/Wrapper';
import DatePicker from 'app/shared/components/pickers/DatePicker';
import Dropdown from 'app/shared/components/dropdown/Dropdown';
import { loadDiagnosisOptions } from 'app/shared/utils/loadIcdAsync';
import { DropdownCheckBox } from 'app/shared/components/dropdown/DropdownCheckBox';
import MultiGroupAction from 'app/shared/components/buttons/MultiGroupActions';
import { Tooltip as ReactTooltip } from 'react-tooltip';
import TextArea from 'app/shared/components/inputs/TextArea';
import TextInput from 'app/shared/components/inputs/TextInput';
import AddressInput from 'app/shared/components/inputs/Address/AddressInput';
import DocumentAttacher from 'app/shared/components/file-uploads/DocumentAttacher';
import ActionSectionSectionWithAuditDetails from 'app/shared/components/record-forms/ActionSectionWithAuditDetails';
import fieldsAreValid from 'app/shared/utils/fieldsAreValid';
import Button from 'app/shared/components/buttons/Button';
import { PreauthUtilisations } from 'graphql-types/PreauthUtilisations';
import { AgreedTariffVariables } from 'graphql-types/AgreedTariff';
import { ProcessPreauthUtilizationStatusVariables } from 'graphql-types/ProcessPreauthUtilizationStatus';
import { FlaggedAuthenticationInput } from 'app/shared/components/inputs/AuthenticationInput';
import StatusButtons from 'app/shared/components/buttons/StatusButton';
import AuditDetails from 'app/shared/components/record-forms/AuditDetails';
import { calculateAgeWithMonth } from 'app/shared/utils/calculateAge';
import { userType } from 'app/shared/utils/authTracker';
import { UserType } from 'graphql-types/globalTypes';
import SingleDurationInput from 'app/shared/components/inputs/SingleDurationInput';
import useHospitalData from 'app/shared/hooks/client/useHospitalData';
import calculateTotalUtilization from 'app/shared/calculate-total-utilization';
import usePatientData from 'app/shared/hooks/client/usePatientData';
import MultiDurationInput from 'app/shared/components/inputs/MultipleDurationInput';
import { OtpInputIcon } from '../../../../../assets';
import { defaultInputProps, withComma } from '../billings/constants';
import { getDiagnosisValue, listTypeMapper } from '../admission/AddAdmission';
import {
  BIRTH_COUNT_OPTIONS,
  CLASS_CONSULTATION_OPTIONS,
  DOSAGE_OPTIONS,
  HMO_MEDICATION_FREQUENCY,
  HOSPITAL_UNIT_OPTIONS,
  LASHMA_HMO_DOSAGE_OPTIONS,
  OGSHIA_DIAGNOSIS_LIST,
  PATIENT_OPTIONS,
  PRIORITY_SURGERY_OPTIONS,
  SPECIALTY_OPTIONS,
  UTILIZATION_SPECIALTY_LIST_2,
} from '../../../../../@constant/options';
import useProcessClaimForm from './hooks/useProcessClaimForm';
import ApprovalGroupForm from './components/ApprovalGroupForm';
import ApprovalGroupSummary from './components/ApprovalGroupSummary';
import ReferralComponent from '../request-preauthorizations/components/ReferralComponent';
import ClaimPreAuthUtilization from './components/ClaimPreAuthUtilization';
import HmoFlagSection from '../../components/forms/HmoFlagSection/HmoFlagSection';
import LazyClaimSummaryTable from './components/LazyClaimSummaryTable';
import { OGSHIA_FLAG_DESCRIPTION } from '../../../../shared/utils/ogshia-flag-description';
import UtilizationsHistoryWrapper from '../request-preauthorizations/components/UtilizationsHistoryWrapper';

type Props = {
  isOnModal?: boolean;
  defaultId?: string;
  clearAction?: (id?: string) => void;
  filterOptions: Record<string, unknown>;
  refetchList?: () => void;
};

// Percentage covered then comment
export default function ProcessClaimForm({ filterOptions, defaultId }: Props) {
  const [listType, setListType] = useState({ 0: 'ICD-10' });
  const currentUserRole = userType();

  const {
    inputs,
    isEdit,
    startEdit,
    handleInputChange,
    handleMultipleFieldsChange,
    readOnly,
    authDiagnosis,
    toggle,
    disableActionButton,
    setStartEdit,
    action,
    actionText,
    processUtilizationStatus,
    processClaimStatus,
    processingClaimStatus,
    setInputs,
    onQuantityChange,
    flagClaim,
    processBulkUtilizationsStatus,
    onPercentageCoveredChange,
    onAmountCoveredChange,
    updatingClaimFlag,
    processingUtilizationStatus,
    processingBulkUtilizationsStatus,
  } = useProcessClaimForm({ filterOptions, defaultId });
  const [showHistory, setShowHistory] = useState(false);

  const { isOgshiaAgency, isPoliceAgency } = useHospitalData();

  const { enrolleeDetails } = usePatientData(inputs?.profile?.clinifyId);

  const disableReturn =
    (inputs.revertAction?.action === 'Returned' &&
      inputs.revertAction?.mutatorUserType === currentUserRole) ||
    inputs?.payoutStatus?.includes('paid');
  const disableSendBack =
    inputs.revertAction?.action === 'Sent Back' &&
    inputs.revertAction?.mutatorUserType === currentUserRole;

  return (
    <>
      <div className="flex flex-row gap-8 justify-content-end" style={{ marginRight: 15 }}>
        <input
          type="checkbox"
          checked={showHistory}
          onChange={({ target: { checked } }) => setShowHistory(checked)}
          className="cursor-pointer"
        />
        <small>Claim History</small>
      </div>
      {showHistory && (
        <UtilizationsHistoryWrapper
          type="Claim"
          profileId={inputs.profile?.id}
          date={inputs.claimDate}
        />
      )}
      <div className="flex flex-row justify-content-end">
        <HmoFlagSection
          flags={inputs.flags?.map(({ flag }) => flag)}
          action={flagClaim}
          tooltipDescription={isOgshiaAgency ? OGSHIA_FLAG_DESCRIPTION : undefined}
          variant={isOgshiaAgency ? 'Green-Red' : undefined}
          disabled={updatingClaimFlag}
        />
      </div>
      <div className="header">
        <div className="force-full-width title">CLAIMS SUMMARY</div>
      </div>
      <LazyClaimSummaryTable
        utilizations={inputs.utilizations}
        providerInformation={{
          name: inputs.facilityName,
          address: inputs.facilityAddress,
        }}
        enrolleeInformation={{
          fullName: inputs.profile?.fullName,
          gender: inputs.profile?.gender,
          phoneNumber: inputs.profile?.user?.phoneNumber,
          secondaryPhoneNumber: inputs.profile?.secondaryPhoneNumber?.value,
          diagnosis: inputs.diagnosis,
          enrolleeNumber: inputs.enrolleeNumber,
          age: calculateAgeWithMonth(inputs.profile?.personalInformation?.dateOfBirth, true),
          enrolleePlanType: (enrolleeDetails?.hmoProfile as any)?.memberPlan || '',
          enrolleePlanCategory: enrolleeDetails?.hmoProfile?.planCategory || '',
          profileId: inputs.profile?.id,
        }}
        referringProvider={{
          from: inputs.referredFrom,
          to: inputs.referredTo,
        }}
        authorizationCode={inputs.referralCode}
        onProcessUtilization={processUtilizationStatus}
        onProcessBulkUtilizations={processBulkUtilizationsStatus}
        treatmentDateTime={inputs.claimDate}
        hmoClaimId={inputs.id}
        showConfirmationControl
        showComments
        showPercentageCovered
        onPercentageCoveredChange={onPercentageCoveredChange}
        onQuantityChange={onQuantityChange}
        onAmountCoveredChange={onAmountCoveredChange}
        showFlagControl
        utilizationOrigin="CLAIM"
        processingUtilizationStatus={processingUtilizationStatus}
        processingBulkUtilizationsStatus={processingBulkUtilizationsStatus}
        showVitalsSummary
      />
      <div className="header">
        <div className="force-full-width title">CLAIMS DETAILED</div>
      </div>
      <InputRow>
        <SelectWrapper noPadding>
          <DatePicker
            label="Treatment Date and Time"
            withBorderRadius
            onChange={(date) => handleInputChange('claimDate', date)}
            value={inputs.claimDate}
            placeholderText="Select Date and Time"
            readOnly={false}
            maxDate={new Date()}
            disabled={readOnly}
          />
        </SelectWrapper>
        <SelectWrapper>
          <Dropdown
            title="Coverage Name"
            onChange={() => {}}
            placeholder="Select One"
            value={inputs.provider?.name || null}
            readOnly
            noPadding
            skipTranslate
          />
        </SelectWrapper>
      </InputRow>
      <InputRow>
        <SelectWrapper noPadding>
          <DatePicker
            label="Treatment Start Date and Time"
            withBorderRadius
            onChange={(date) => handleInputChange('treatmentStartDate', date)}
            value={inputs.treatmentStartDate}
            placeholderText="Select Date and Time"
            readOnly={false}
            minDate={new Date(new Date().getFullYear() - 100, new Date().getMonth())}
            maxDate={new Date(new Date().getFullYear() + 100, new Date().getMonth())}
            disabled={readOnly}
          />
        </SelectWrapper>
        <SelectWrapper noPadding>
          <DatePicker
            label="Treatment End Date and Time"
            withBorderRadius
            onChange={(date) => handleInputChange('treatmentEndDate', date)}
            value={inputs.treatmentEndDate}
            placeholderText="Select Date and Time"
            readOnly={false}
            minDate={new Date(new Date().getFullYear() - 100, new Date().getMonth())}
            maxDate={new Date(new Date().getFullYear() + 100, new Date().getMonth())}
            disabled={readOnly}
          />
        </SelectWrapper>
      </InputRow>

      {inputs.provider?.id ? (
        <>
          <InputRow>
            <SelectWrapper noFlex>
              <Dropdown
                title="Visitation Type"
                options={[]}
                onChange={() => {}}
                placeholder="Select One"
                isRequired
                value={inputs.serviceType || null}
                readOnly={readOnly}
                additional={{
                  action: 'visitation',
                  providerId: inputs.providerId,
                }}
                noPadding
              />
            </SelectWrapper>
            <SelectWrapper>
              <Dropdown
                title="Priority"
                options={PRIORITY_SURGERY_OPTIONS}
                onChange={({ value }) => handleInputChange('priority', value)}
                placeholder="Select One"
                value={inputs.priority || null}
                readOnly={readOnly}
                creatable
                noPadding
              />
            </SelectWrapper>
          </InputRow>
          {authDiagnosis.map((diagnose, idx) => {
            const { diagnosisICD10 = '', diagnosisICD11 = '', diagnosisSNOMED = '' } = {
              ...diagnose,
            };
            return (
              <Fragment key={idx}>
                <SelectWrapper fullWidth>
                  {isOgshiaAgency ? (
                    <Dropdown
                      title="Clinical Diagnosis"
                      options={OGSHIA_DIAGNOSIS_LIST}
                      onChange={({ value }) => {
                        const newData = [...(inputs.diagnosis || [])];
                        newData[idx] = {
                          ...newData[idx],
                          diagnosisICD10: value,
                        };
                        handleInputChange('diagnosis', newData);
                      }}
                      placeholder="Select One"
                      value={diagnosisICD10 || null}
                      readOnly={readOnly}
                      creatable
                      noPadding
                    />
                  ) : (
                    <Dropdown
                      title="Clinical Diagnosis"
                      options={[]}
                      onChange={({ value }) => {
                        const newData = [...(inputs.diagnosis || [])];
                        newData[idx] = {
                          ...newData[idx],
                          [listTypeMapper[listType[`${idx}`] || 'ICD-10']]: value,
                        };
                        handleInputChange('diagnosis', newData);
                      }}
                      placeholder="Select One"
                      value={
                        getDiagnosisValue(
                          diagnosisICD10,
                          diagnosisICD11,
                          diagnosisSNOMED,
                          listType[`${idx}`] || 'ICD-10',
                        ) || null
                      }
                      loadOptions={loadDiagnosisOptions}
                      isAsync
                      creatable
                      noPadding
                      readOnly={readOnly}
                      isRequired
                      additional={{ dropdownType: listType[`${idx}`] || 'ICD-10' }}
                      topRightComponent={
                        <DropdownCheckBox
                          value={listType[`${idx}`] || 'ICD-10'}
                          onChange={(value) => {
                            setListType({
                              ...listType,
                              [idx]: value,
                            });
                          }}
                        />
                      }
                      cacheUniqs={[listType[`${idx}`] || 'ICD-10']}
                    />
                  )}
                </SelectWrapper>
                <SelectWrapper fullWidth noPadding>
                  {!readOnly && (
                    <MultiGroupAction
                      items={authDiagnosis}
                      index={idx}
                      initialItem={{
                        diagnosisICD10: null,
                        diagnosisICD11: null,
                        diagnosisSNOMED: null,
                      }}
                      onClick={(value) => {
                        handleInputChange('diagnosis', value);
                      }}
                    />
                  )}
                </SelectWrapper>
              </Fragment>
            );
          })}

          <SelectWrapper fullWidth noPadding>
            <TextArea
              name="presentingComplain"
              label="Presenting Complaints"
              fullWidth
              onChange={({ target: { value } }) => handleInputChange('presentingComplain', value)}
              value={inputs.presentingComplain}
              readOnly={readOnly}
              fieldId={inputs?.id}
              isRequired
              createdBy={inputs?.creatorId}
            />
          </SelectWrapper>

          {inputs.utilizations.map((utilization, idx) => (
            <Fragment key={idx}>
              <Utilizations
                visitationType={inputs.serviceTypeCode}
                utilization={utilization}
                handleInputChange={(name, value) =>
                  handleMultipleFieldsChange('utilizations', name, value, idx)
                }
                getTariff={() => {}}
                index={idx}
                loadingPrice={false}
                readOnly={readOnly || !inputs.providerId}
                isEdit={isEdit}
                providerId={inputs.providerId}
                enrolleeId={inputs.enrolleeId}
                treatmentDate={inputs.requestDateTime}
                processUtilization={processUtilizationStatus}
                onQuantityChange={onQuantityChange}
                providerName={inputs.provider?.name}
              />
            </Fragment>
          ))}

          <InputRow>
            <TextInput
              name="totalQuantity"
              title="Total Quantity"
              value={calculateTotalUtilization(inputs.utilizations).totalQuantity}
              readOnly
            />
            <FlaggedAuthenticationInput
              {...defaultInputProps}
              name="grandTotal"
              label="Grand Total"
              value={withComma(calculateTotalUtilization(inputs.utilizations).grandTotal)}
              readOnly
            />

            <ReferralComponent inputs={inputs} readOnly={readOnly} setInputs={setInputs} />
          </InputRow>

          <InputRow>
            <SelectWrapper>
              <Dropdown
                creatable
                title="Patient Type"
                options={PATIENT_OPTIONS}
                onChange={({ value }) => handleInputChange('patientType', value)}
                placeholder="Select One"
                value={inputs.patientType || null}
                readOnly={readOnly}
                noPadding
              />
            </SelectWrapper>
            <SelectWrapper>
              <Dropdown
                creatable
                title="Payment Type"
                options={[]}
                onChange={({ value }) => handleInputChange('paymentType', value)}
                placeholder="Select One"
                value={inputs.paymentType || null}
                readOnly={readOnly}
                noPadding
              />
            </SelectWrapper>
          </InputRow>

          <InputRow>
            <TextInput
              name="doctor"
              title="Submitted By"
              readOnly
              onChange={() => {}}
              value={inputs.submittedBy}
              placeholder="Enter Doctor's Name"
              skipTranslate
            />
            <SelectWrapper>
              <Dropdown
                creatable
                title="Specialty"
                options={SPECIALTY_OPTIONS}
                onChange={() => {}}
                placeholder="Select One"
                value={inputs.specialty || null}
                readOnly
                noPadding
              />
            </SelectWrapper>
          </InputRow>

          <InputRow>
            <SelectWrapper>
              <Dropdown
                title="Rank"
                options={CLASS_CONSULTATION_OPTIONS}
                onChange={() => {}}
                placeholder="Select One"
                value={inputs.rank || null}
                readOnly
                creatable
                noPadding
              />
            </SelectWrapper>
            <SelectWrapper>
              <Dropdown
                title="Department"
                options={HOSPITAL_UNIT_OPTIONS}
                placeholder="Select One"
                value={inputs?.department || null}
                onChange={() => {}}
                readOnly
                creatable
                noPadding
              />
            </SelectWrapper>
          </InputRow>

          <TextInput
            fullWidth
            name="facilityName"
            title="Facility Name"
            onChange={() => {}}
            readOnly
            value={inputs.facilityName}
            placeholder="Enter Facility Name"
            skipTranslate
          />
          <AddressInput
            label="Facility Address"
            fieldPath="facilityAddress"
            handleInputChange={() => {}}
            readOnly
            value={inputs.facilityAddress}
            placeholder="Enter Facility Address"
          />
          <InputRow>
            <FlaggedAuthenticationInput
              {...defaultInputProps}
              label="Enrollee Phone Number"
              placeholder="Enter Enrollee Phone Number"
              value={`${inputs?.enrolleePhoneNumber?.countryCode || '234'} ${
                inputs?.enrolleePhoneNumber?.value || ''
              }`}
              onChange={() => {}}
              name="enrolleePhoneNumber"
              countryName={inputs?.enrolleePhoneNumber?.countryName || 'Nigeria'}
              changeCountryName={() => {}}
            />
            <TextInput
              name="enrolleeEmail"
              title="Enrollee Email"
              onChange={() => {}}
              readOnly
              value={inputs.enrolleeEmail}
              placeholder="Enter Enrollee Email"
            />
          </InputRow>
          <InputRow>
            <SelectWrapper fullWidth noPadding>
              <TextArea
                name="additionalNote"
                label="Clinical Findings"
                fullWidth
                onChange={({ target: { value } }) => handleInputChange('additionalNote', value)}
                value={inputs.additionalNote}
                readOnly={readOnly}
                fieldId={inputs?.id}
                createdBy={inputs?.creatorId}
              />
            </SelectWrapper>
          </InputRow>

          <InputRow>
            <DocumentAttacher
              documents={inputs.documentUrl}
              handleInputChange={handleInputChange}
              readOnly={readOnly}
              type="claims"
            />
          </InputRow>

          {isEdit ? (
            <div>
              <ActionSectionSectionWithAuditDetails
                isEdit={isEdit}
                isEditActive={startEdit}
                toggle={toggle}
                cancelAction={() => setStartEdit(false)}
                disableActionButton
                disableDeleteButton
                onAction={() => action()}
                actionText={actionText}
                inputs={inputs}
              />
              {currentUserRole === UserType.ClaimAccount ? (
                <StatusButtons
                  status={[['Return'], ['Paid']]}
                  onChange={processClaimStatus}
                  currentStatus={inputs.status}
                  loading={processingClaimStatus}
                  buttonClassName="full-width"
                  statusMapper={{
                    Return: {
                      className: 'bg-danger-btn',
                      isDisabled: isPoliceAgency ? disableReturn : true,
                    },
                    Paid: { isDisabled: currentUserRole !== UserType.ClaimAccount },
                  }}
                />
              ) : (
                <StatusButtons
                  status={[['Return'], ['Send Back']]}
                  onChange={processClaimStatus}
                  currentStatus={inputs.status}
                  loading={processingClaimStatus}
                  buttonClassName="full-width"
                  statusMapper={{
                    Return: {
                      className: 'bg-danger-btn',
                      isDisabled: isPoliceAgency
                        ? currentUserRole === UserType.ClaimConfirmation || disableReturn
                        : true,
                    },
                    'Send Back': {
                      isDisabled: isPoliceAgency ? disableSendBack : true,
                    },
                  }}
                />
              )}
            </div>
          ) : (
            <>
              <div className="save-btn-group_appointment">
                <SelectWrapper>
                  <Button text="Save and Submit" onClick={() => {}} disabled />
                </SelectWrapper>

                <ActionSectionSectionWithAuditDetails
                  isEdit={isEdit}
                  isEditActive={startEdit}
                  toggle={toggle}
                  cancelAction={() => setStartEdit(false)}
                  disableActionButton={
                    disableActionButton ||
                    isEdit ||
                    !fieldsAreValid('authutilizationsfields', inputs.utilizations, true)
                  }
                  onAction={() => action()}
                  actionText={actionText}
                  inputs={inputs}
                />
              </div>
            </>
          )}
        </>
      ) : null}
      <ReactTooltip id="otpToolTip" place="top" className="otpTip" variant="light" />
    </>
  );
}

type UtilisationsProps = {
  utilization: Partial<PreauthUtilisations>;
  handleInputChange: (field: string, value: string) => void;
  getTariff: (input: AgreedTariffVariables['input']) => void;
  index: number;
  readOnly?: boolean;
  loadingPrice?: boolean;
  loadingDetails?: boolean;
  isEdit?: boolean;
  providerId: string;
  visitationType: any;
  enrolleeId: string;
  treatmentDate: string;
  isClinifyHmo?: boolean;
  providerName: string;
  processUtilization?: (
    input: ProcessPreauthUtilizationStatusVariables['input'],
    onDone?: () => void,
  ) => void;
  onQuantityChange: (quantity: string, index?: string) => void;
};

function Utilizations({
  utilization,
  handleInputChange,
  getTariff,
  index,
  readOnly,
  loadingPrice,
  loadingDetails,
  isEdit,
  providerId,
  visitationType,
  enrolleeId,
  treatmentDate,
  isClinifyHmo,
  processUtilization,
  onQuantityChange,
  providerName,
}: UtilisationsProps) {
  const { isLashmaAgency } = useHospitalData();

  const showDrugFields = ['drug', 'medicine', 'injection'].some(
    (keyword) =>
      `${utilization.category}${utilization.type}`.toLowerCase().includes(keyword) &&
      !utilization.category?.toLowerCase()?.includes('fees') &&
      !utilization.category?.toLowerCase()?.includes('admission'),
  );
  const showDeliveryFields = ['delivery', 'caesarean'].some((keyword) =>
    `${utilization.category}${utilization.type}`.toLowerCase().includes(keyword),
  );
  const showSpecialty = ['consult'].some((keyword) =>
    `${utilization.category}${utilization.type}`.toLowerCase().includes(keyword),
  );

  const HMO_DOSAGE_OPTIONS = isLashmaAgency ? LASHMA_HMO_DOSAGE_OPTIONS : DOSAGE_OPTIONS;

  return (
    <>
      <InputRow>
        <SelectWrapper fullWidth>
          <Dropdown
            creatable={false}
            title="Utilization Type"
            options={[]}
            onChange={() => {}}
            placeholder="Select One"
            isRequired
            value={utilization.type || null}
            readOnly={readOnly}
            additional={{
              utilizationId: utilization?.utilizationId,
              action: 'utilizationProcedure',
              providerId,
              visitationType,
            }}
            noPadding
            cacheUniqs={[utilization?.utilizationId]}
          />
        </SelectWrapper>
      </InputRow>
      <InputRow>
        <SelectWrapper>
          <Dropdown
            creatable={false}
            title="Utilization Category"
            options={[]}
            onChange={() => {}}
            placeholder="Select One"
            isRequired
            value={utilization.category || null}
            readOnly={readOnly}
            additional={{
              action: 'utilizationCategory',
              providerId,
              visitationType,
            }}
            noPadding
            cacheUniqs={[visitationType]}
          />
        </SelectWrapper>
        <TextInput
          name="utilizationCode"
          title="Utilization Code"
          onChange={({ target: { value } }) => {
            const valueToUse = (value || '').replaceAll(' ', '');
            getTariff({
              providerId,
              position: index,
              id: valueToUse,
              utilizationId: isClinifyHmo ? undefined : utilization.utilizationId,
              enrolleeId,
              treatmentDate,
              visitTypeId: visitationType,
            });
          }}
          value={utilization.utilizationCode}
          placeholder="Enter Utilization Code"
          readOnly
        />
      </InputRow>
      {showDrugFields ? (
        <InputRow>
          <SelectWrapper noPadding>
            <TextInput
              name="dosage"
              title="Dosage"
              placeholder="Enter Dosage"
              onChange={({ target: { value } }) => {
                if (Number(value) > 10) return;
                handleInputChange('dosage', value);
              }}
              value={utilization.dosage}
              readOnly
              fullWidth
              withTag
              equalTagWith
              isRequired
              type="number"
              tag={
                <Dropdown
                  options={HMO_DOSAGE_OPTIONS}
                  value={utilization.dosageUnit || null}
                  onChange={({ value }) => handleInputChange('dosageUnit', value)}
                  placeholder="Select One"
                  readOnly
                  withTag
                  noPadding
                  creatable
                />
              }
            />
          </SelectWrapper>
          <SelectWrapper padded>
            <SingleDurationInput
              title="Duration (Month:Week:Day)"
              onChange={() => {}}
              readOnly
              value={utilization.duration}
              isRequired
            />
          </SelectWrapper>
          <SelectWrapper>
            <Dropdown
              noPadding
              title="Frequency"
              options={HMO_MEDICATION_FREQUENCY}
              onChange={() => {}}
              readOnly
              value={utilization.frequency || null}
              placeholder="Enter Frequency"
              creatable
              isRequired
            />
          </SelectWrapper>
          <TextInput
            name="category"
            title="Medication Category"
            readOnly
            onChange={({ target: { value } }) => handleInputChange('medicationCategory', value)}
            value={utilization.medicationCategory}
            placeholder="Enter Medication Category"
            isRequired
          />
        </InputRow>
      ) : null}
      <InputRow>
        <TextInput
          name="quantity"
          title="Quantity"
          onChange={({ target: { value } }) => {
            handleInputChange('quantity', value);
            onQuantityChange(value, utilization.id);
          }}
          value={utilization.quantity}
          placeholder="Enter Quantity"
          readOnly={false}
          isRequired
        />
        <FlaggedAuthenticationInput
          {...defaultInputProps}
          label="Unit Price"
          name="unitPrice"
          onChange={({ target: { value } }) => handleInputChange('price', value)}
          value={utilization.price ? `${withComma(utilization.price)}` : undefined}
          readOnly
          loading={loadingPrice}
        />
      </InputRow>
      {showSpecialty ? (
        <InputRow>
          <SelectWrapper>
            <Dropdown
              noPadding
              title="Specialty"
              options={UTILIZATION_SPECIALTY_LIST_2}
              onChange={({ value }) => handleInputChange('specialty', value)}
              readOnly
              value={utilization.specialty || null}
              isRequired
            />
          </SelectWrapper>
        </InputRow>
      ) : null}
      {showDeliveryFields ? (
        <InputRow>
          <SelectWrapper>
            <Dropdown
              noPadding
              title="Birth Count"
              options={BIRTH_COUNT_OPTIONS}
              onChange={({ value }) => handleInputChange('birthCount', value)}
              readOnly
              value={utilization.birthCount || null}
              isRequired
            />
          </SelectWrapper>
          <SelectWrapper noPadding>
            <DatePicker
              label="Delivery Date and Time"
              withBorderRadius
              onChange={(date) => handleInputChange('deliveryDateTime', date)}
              value={utilization.deliveryDateTime}
              placeholderText="Select Date and Time"
              readOnly
              minDate={new Date(new Date().getFullYear() - 100, new Date().getMonth())}
              maxDate={new Date()}
              disabled
              isRequired
            />
          </SelectWrapper>
          <SelectWrapper padded>
            <MultiDurationInput
              title="Gestational Age (Month:Week:Day)"
              onChange={(duration) => handleInputChange('gestationalAge', duration)}
              value={utilization?.gestationalAge}
              readOnly
              disableBox={[2]}
              minValue={{ 0: 0, 1: 1 }}
              maxValue={{ 0: 30, 1: 42 }}
              isRequired
            />
          </SelectWrapper>
        </InputRow>
      ) : null}
      <InputRow>
        <FlaggedAuthenticationInput
          {...defaultInputProps}
          label="Total Price"
          name="totalPrice"
          onChange={() => {}}
          value={
            utilization.price
              ? `${withComma(Number(utilization.price) * Number(utilization.quantity))}`
              : undefined
          }
          readOnly
        />
        <TextInput
          readOnly
          name="PACode"
          value={utilization.paCode}
          title={`PA Code - ${utilization.status || 'Processing'}`}
          onChange={({ target: { value } }) => {
            handleInputChange('paCode', value);
          }}
          placeholder="Enter PA Code"
          withIcon
          iconWithBrownLeftBorder
          loading={loadingDetails}
          skipTranslate
          icon={
            <div
              data-tooltip-content="Click to get PA Code"
              data-tooltip-id="otpToolTip"
              className="otp-button"
              data-tooltip-hidden={!isEdit}
            >
              <OtpInputIcon />
            </div>
          }
        />
      </InputRow>
      <div className="medication-dispense-action-row parent">
        <div className="audit-section">
          {!!utilization.id && <AuditDetails inputs={utilization} />}
        </div>
      </div>
      <ApprovalGroupForm
        providerName={providerName}
        utilization={utilization}
        processUtilization={processUtilization}
      />
      <ApprovalGroupSummary
        providerName={providerName}
        title="Claim Approval Group"
        utilization={utilization}
      />
      <ClaimPreAuthUtilization providerName={providerName} utilization={utilization} />
    </>
  );
}
