import React, { useMemo } from 'react';
import Content from 'app/shared/components/wrappers/Wrapper';
import useAppData from 'app/shared/hooks/client/useAppData';
import SearchTab from 'app/shared/components/table/SearchTab';
import SearchInput from 'app/shared/components/inputs/SearchInput';
import { ReactComponent as SearchIcon } from 'assets/icons/search.svg';
import { ReactComponent as HmoCheckInIcon } from 'assets/icons/patient-checkin-icon.svg';
import classNames from 'classnames';
import OutlineButton from 'app/shared/components/buttons/OutlineButton';
import OutlineIconButton from 'app/shared/components/buttons/OutlineIconButton';
import Button from 'app/shared/components/buttons/Button';
import Table from 'app/shared/components/table/Table';
import { nanoid } from 'nanoid';
import GroupAddIcon from '@material-ui/icons/GroupAdd';
import CheckBox from 'app/shared/components/inputs/CheckBox';
import ProfileImageAvatar from 'app/shared/components/avatars/ProfileImageAvatar';
import {
  formatTableData,
  formatTableDate,
  formatTableDateTime,
} from 'app/shared/utils/formatTable';
import { calculateAgeWithMonth } from 'app/shared/utils/calculateAge';
import { Tooltip as ReactTooltip } from 'react-tooltip';
import Dropdown from 'app/shared/components/dropdown/Dropdown';
import { getUserPayload } from 'app/shared/utils/authentication';
import { MEMBER_STATUS_OPTIONS } from '@constant/options';
import InfoBlock from 'app/shared/components/texts/InfoBlock';
import { formatMoney } from 'app/shared/utils/common';
import colors from '../../../../../@constant/colors';
import useEnrolleeList from './hooks/useEnrolleeList';
import defaultAvatar from '../../../../../assets/images/avatar.png';
import { SingleFilterIcon, ExcelIcon } from '../../../../../assets';
import { formatNumWithComma } from '../billings/constants';
import ProviderOptions from '../provider-network/components/ProviderOptions';
import { OptionType } from '../../../../shared/components/table/ListView';
import useHospitalData from '../../../../shared/hooks/client/useHospitalData';
import { convertToCurrencyString } from '../invoice/utils';
import StatusTag from '../../../../shared/components/visual-tags/StatusTag';
import {
  COVERAGE_STATUS_COLOR_MAP,
  COVERAGE_STATUS_TEXT_MAP,
} from '../patient-registration/constants';
import '../lookup/styles/lookup.scss';

export default function EnrolleeList({ providerId }) {
  const {
    items,
    loading,
    refetching,
    filterOptions,
    onRowClick,
    clearFilter,
    handleFieldSearch,
    triggerSearch,
    lastItemElementRef,
    totalCount,
    hmoOptions,
    handleSpreadsheetExport,
    fetchingAllEnrollees,
    highlightAll,
    allHighlighted,
    highlightRecord,
    highlightedRecords,
    handleBulkActivate,
    agencyPlanTypes,
    enrolleeSummary,
    checkEnrolleesIn,
    allowEnrolleeCheckIn,
  } = useEnrolleeList(providerId);
  const userPayload = getUserPayload();
  const isHmoOfficial = !!userPayload?.orgHmoId;

  const enableCheckInIcon = highlightedRecords?.length > 0;

  const COLUMNS = useMemo(() => {
    return [
      {
        Header: (
          <CheckBox
            id="checkbox"
            name="select all"
            onChange={highlightAll}
            checked={allHighlighted}
          />
        ),
        accessor: 'id',
        Cell: ({ value }) => (
          <CheckBox
            id={value}
            name="record"
            addMarginLeft
            onChange={() => highlightRecord(value)}
            checked={highlightedRecords.includes(value)}
          />
        ),
      },
      {
        Header: 'S/N',
        accessor: 'number',
        Cell: (arg) => {
          return <span>{arg.cell.row.index + 1}</span>;
        },
      },
      {
        Header: 'Photo',
        accessor: 'personalInformation.displayPictureUrl',
        Cell: ({ value }) => {
          return <ProfileImageAvatar profileUrl={value || defaultAvatar} />;
        },
      },
      {
        Header: (
          <div className="flex flex-column">
            <span>Registration Date and Time</span>
            <span>Enrollment Date and Time</span>
          </div>
        ),
        accessor: (row) => row,
        id: nanoid(4),
        Cell: ({ value }) => {
          return (
            <div className="flex flex-column">
              <span>
                {formatTableDateTime({ value: value?.hmoProfiles[0]?.enrollmentDateTime })}
              </span>
              <span>{formatTableDateTime({ value: value?.hmoProfiles[0]?.paymentDateTime })}</span>
            </div>
          );
        },
      },
      {
        Header: () => (
          <div className="flex-column">
            <span style={{ whiteSpace: 'nowrap' }}>Enrollee ID</span>
            <span style={{ whiteSpace: 'nowrap' }}>Enrollee Status</span>
            <span style={{ whiteSpace: 'nowrap' }}>Enrollee Plan Type</span>
            <span>Enrollee Plan Category</span>
          </div>
        ),
        accessor: (row) => row,
        id: nanoid(4),
        Cell: ({ value }) => {
          const multipleValues = value?.hmoProfiles?.length > 1;
          const hmoProfile = value?.hmoProfiles?.[0] || {};
          const coverageStatus = hmoProfile?.memberStatus?.toLowerCase();
          return (
            <div className="flex-column">
              <span>{formatTableData({ value: hmoProfile.memberNumber, multipleValues })}</span>
              <span>
                <StatusTag
                  status={COVERAGE_STATUS_TEXT_MAP?.[coverageStatus]}
                  color={COVERAGE_STATUS_COLOR_MAP[coverageStatus]}
                  className="mb-1"
                />
              </span>
              <span>{formatTableData({ value: hmoProfile.memberPlan, multipleValues })}</span>
              <span>{formatTableData({ value: hmoProfile.planCategory, multipleValues })}</span>
            </div>
          );
        },
      },
      {
        Header: () => (
          <div className="flex-column">
            <span>Full Name</span>
            <span style={{ whiteSpace: 'nowrap' }}>Date Of Birth</span>
            <span>Phone Number</span>
            <span>Email Address</span>
          </div>
        ),
        accessor: (row) => row,
        id: nanoid(4),
        Cell: ({ value }) => {
          return (
            <div className="flex-column notranslate">
              <span>{formatTableData({ value: value?.fullName })}</span>
              <span>{formatTableDate({ value: value?.personalInformation?.dateOfBirth })}</span>
              <span>{formatTableData({ value: value?.user?.phoneNumber })}</span>
              <span>{formatTableData({ value: value?.user?.nonCorporateEmail })}</span>
            </div>
          );
        },
      },
      {
        Header: () => (
          <div className="flex-column">
            <span>Age</span>
            <span>Sex</span>
            <span>Plan Start Date</span>
            <span>Plan Due Date</span>
          </div>
        ),
        accessor: (row) => row,
        id: nanoid(4),
        Cell: ({ value }) => {
          return (
            <div className="flex-column">
              <span>{calculateAgeWithMonth(value?.personalInformation?.dateOfBirth)}</span>
              <span>{value?.gender}</span>
              <span>{formatTableDate({ value: value.hmoProfiles[0]?.memberStartDate })}</span>
              <span>{formatTableDate({ value: value.hmoProfiles[0]?.memberDueDate })}</span>
            </div>
          );
        },
      },
      {
        Header: (
          <div className="flex-column">
            <span>Coverage Name</span>
            <span style={{ whiteSpace: 'nowrap' }}>Primary Care Provider</span>
            <span>Payment Frequency</span>
            {isHmoOfficial ? <span>Premium Amount</span> : null}
          </div>
        ),
        id: nanoid(7),
        accessor: (row) => row,
        Cell: ({ value }) => {
          const multipleValues = value?.hmoProfiles?.length > 1;
          const hmoProfile = value?.hmoProfiles?.[0] || {};
          return (
            <div className="flex flex-column">
              <span>{value?.hmoProfiles[0]?.provider?.name || '--'}</span>
              <span>{hmoProfile?.primaryProviderName || '--'}</span>
              <span>{formatTableData({ value: hmoProfile.paymentFrequency, multipleValues })}</span>
              {isHmoOfficial ? (
                <span>N{convertToCurrencyString(hmoProfile?.premiumCollected || 0)}</span>
              ) : null}
            </div>
          );
        },
      },
    ];
  }, [items, allHighlighted, highlightedRecords]);

  return (
    <Content detailsPage noPadding>
      <TableSearchTab
        clearFilter={clearFilter}
        handleFieldSearch={handleFieldSearch}
        filterOptions={filterOptions}
        triggerSearch={triggerSearch}
        totalCount={totalCount || 0}
        itemsCount={items.length}
        hmoOptions={hmoOptions}
        handleSpreadsheetExport={handleSpreadsheetExport}
        fetchingAllEnrollees={fetchingAllEnrollees}
        handleBulkActivate={handleBulkActivate}
        noRecordsSelected={highlightedRecords.length === 0}
        agencyPlanTypes={agencyPlanTypes}
        enrolleeSummary={enrolleeSummary as any}
        checkEnrolleesIn={checkEnrolleesIn}
        enableCheckInIcon={enableCheckInIcon}
        allowEnrolleeCheckIn={allowEnrolleeCheckIn}
      />
      {items?.length === 0 && !loading ? (
        <div className="body-wrap">
          <div className="lookup-wrapper">
            <div>
              <SearchIcon />
              <span className="lookup">Look Up</span>
              <p>Results will show up here</p>
            </div>
          </div>
        </div>
      ) : (
        <Table
          columns={COLUMNS}
          data={items}
          onRowClick={onRowClick}
          pageCount={1}
          showPagination={false}
          defaultGoToPath="/facility/enrollee-list"
          attachId={false}
          loading={loading}
          lastRef={lastItemElementRef}
          refetching={refetching}
        />
      )}
    </Content>
  );
}

interface IEnrolleeSummary {
  totalEnrolleeCount: number;
  totalActiveEnrolleeCount: number;
  totalInactiveEnrolleeCount: number;
  totalExpiredEnrolleeCount: number;
  totalCapitatedEnrolleeCount: number;
  totalCapitatedAmount: number;
}

type TableSearchTabProps = {
  filterOptions: Record<string, string>;
  handleFieldSearch: (field: string, value: string) => void;
  triggerSearch: () => void;
  clearFilter: () => void;
  totalCount: number;
  itemsCount: number;
  hmoOptions?: OptionType[];
  handleSpreadsheetExport?: () => void;
  fetchingAllEnrollees?: boolean;
  handleBulkActivate?: () => void;
  noRecordsSelected?: boolean;
  agencyPlanTypes: OptionType[];
  enrolleeSummary?: IEnrolleeSummary;
  checkEnrolleesIn?: () => void;
  enableCheckInIcon?: boolean;
  allowEnrolleeCheckIn?: boolean;
};

function TableSearchTab({
  handleFieldSearch,
  filterOptions,
  triggerSearch,
  clearFilter,
  totalCount,
  itemsCount,
  hmoOptions,
  handleSpreadsheetExport,
  fetchingAllEnrollees,
  handleBulkActivate,
  noRecordsSelected,
  agencyPlanTypes,
  enrolleeSummary,
  checkEnrolleesIn,
  enableCheckInIcon,
  allowEnrolleeCheckIn,
}: TableSearchTabProps) {
  const { orgHmoId } = getUserPayload();
  const { isMobile } = useAppData();
  const { isSubscriptionValid, isProviderHmoClaimsManagedByClinify } = useHospitalData();
  const isHmoHealthProvider = isProviderHmoClaimsManagedByClinify && !isSubscriptionValid;

  return (
    <div className="heading-table">
      <PaginationInfo
        loaded={itemsCount}
        totalCount={totalCount}
        enrolleeSummary={enrolleeSummary}
      />
      <div className="patient-record-list-tab">
        <div className="search-tab-section">
          <SearchTab isVitals>
            {checkEnrolleesIn && (
              <>
                {isMobile ? (
                  <OutlineButton
                    withBorderRadius
                    withIcon
                    text="Check In"
                    onClick={checkEnrolleesIn}
                    mainColor={colors.secondaryColor}
                    icon={
                      <HmoCheckInIcon
                        color={!enableCheckInIcon ? colors.tintGrey : colors.secondaryColor}
                      />
                    }
                    disabled={!enableCheckInIcon || !allowEnrolleeCheckIn}
                    fullWidth
                  />
                ) : (
                  <div data-tooltip-content="Check In" data-tooltip-id="enrolleeListTip">
                    <OutlineIconButton
                      onClick={checkEnrolleesIn}
                      noHover
                      icon={
                        <HmoCheckInIcon
                          color={!enableCheckInIcon ? colors.tintGrey : colors.secondaryColor}
                        />
                      }
                      disabled={!enableCheckInIcon || !allowEnrolleeCheckIn}
                    />
                  </div>
                )}
              </>
            )}
            {orgHmoId ? (
              <ProviderOptions
                onChange={(value) => handleFieldSearch('registeredWith', value)}
                value={filterOptions.registeredWith || null}
                readOnly={!orgHmoId}
                placeholder="All Providers"
                showAll
              />
            ) : (
              <div style={isMobile ? { width: '100%', margin: '0 0 8px 0' } : { width: 250 }}>
                <Dropdown
                  value={filterOptions.hmoProviderId}
                  placeholder="Select HMO"
                  onChange={({ value }) => handleFieldSearch('hmoProviderId', value)}
                  options={hmoOptions}
                  noPadding
                  fullWidth
                />
              </div>
            )}
            {orgHmoId ? (
              <div style={isMobile ? { width: '100%', margin: '8px 0 8px 0' } : { width: 250 }}>
                <Dropdown
                  value={filterOptions.planId || null}
                  placeholder="Select Enrollee Plan"
                  onChange={({ value }) => handleFieldSearch('planId', value)}
                  options={[{ label: 'All', value: undefined }, ...agencyPlanTypes]}
                  noPadding
                  fullWidth
                />
              </div>
            ) : null}
            <div style={isMobile ? { width: '100%', margin: '0 0 8px 0' } : { width: 250 }}>
              <Dropdown
                value={filterOptions.enrolleeStatus || null}
                placeholder="Select Enrollee Status"
                onChange={({ value }) => handleFieldSearch('enrolleeStatus', value)}
                options={[{ label: 'All', value: undefined }, ...MEMBER_STATUS_OPTIONS]}
                noPadding
                fullWidth
              />
            </div>
            <SearchInput
              value={filterOptions.enrolleeId}
              onChange={({ target: { value } }) => handleFieldSearch('enrolleeId', value)}
              placeholder="Enter Enrollee ID"
              outline
              triggerSearch={triggerSearch}
            />
            <SearchInput
              value={filterOptions.fullName}
              onChange={({ target: { value } }) => handleFieldSearch('fullName', value)}
              placeholder="Enter Enrollee Name"
              outline
              triggerSearch={triggerSearch}
            />
            <SearchInput
              value={filterOptions.phoneNumber}
              onChange={({ target: { value } }) => handleFieldSearch('phoneNumber', value)}
              placeholder="Enter Phone Number"
              outline
              triggerSearch={triggerSearch}
            />
            <SearchInput
              value={filterOptions.email}
              onChange={({ target: { value } }) => handleFieldSearch('email', value)}
              placeholder="Enter Email Address"
              outline
              triggerSearch={triggerSearch}
            />
            {!!orgHmoId || isHmoHealthProvider ? null : (
              <SearchInput
                value={filterOptions.clinifyId}
                onChange={({ target: { value } }) => handleFieldSearch('clinifyId', value)}
                placeholder="Enter Clinify ID"
                outline
                triggerSearch={triggerSearch}
              />
            )}
            <Button
              text="Search"
              marginRight
              onClick={triggerSearch}
              className={classNames({ 'full-width': isMobile })}
            />
            <div className="checkout-icons">
              {isMobile ? (
                <OutlineButton
                  withBorderRadius
                  withIcon
                  text="Clear Filter"
                  onClick={() => clearFilter()}
                  mainColor={colors.secondaryColor}
                  icon={<SingleFilterIcon className="filter-icon" />}
                  fullWidth
                />
              ) : (
                <>
                  <div data-tooltip-content="Clear" data-tooltip-id="enrolleeListTip">
                    <OutlineIconButton
                      withBorderRadius
                      onClick={() => clearFilter()}
                      withIcon
                      icon={<SingleFilterIcon className="filter-icon" />}
                    />
                  </div>
                  <div data-tooltip-content="Download List" data-tooltip-id="enrolleeListTip">
                    <OutlineIconButton
                      icon={
                        fetchingAllEnrollees ? (
                          <div className="small-spinner" />
                        ) : (
                          <ExcelIcon width={24} height={24} />
                        )
                      }
                      onClick={() => handleSpreadsheetExport?.()}
                      disabled={fetchingAllEnrollees}
                      className={classNames({ 'not-allowed': fetchingAllEnrollees })}
                    />
                  </div>
                  {orgHmoId ? (
                    <div data-tooltip-content="Bulk Activate" data-tooltip-id="enrolleeListTip">
                      <OutlineIconButton
                        onClick={handleBulkActivate}
                        icon={<GroupAddIcon />}
                        disabled={noRecordsSelected}
                      />
                    </div>
                  ) : null}
                </>
              )}
            </div>
          </SearchTab>
        </div>
      </div>
      <ReactTooltip id="enrolleeListTip" place="top" className="button-tooltip" variant="light" />
    </div>
  );
}

type PaginationInfoProps = {
  totalCount: number;
  loaded: number;
  enrolleeSummary?: IEnrolleeSummary;
};
function PaginationInfo({ loaded, totalCount, enrolleeSummary }: PaginationInfoProps) {
  return (
    <>
      <div className="flex flex-row flex-wrap gap-8">
        <InfoBlock title="Total Enrollees" value={enrolleeSummary?.totalEnrolleeCount || '0'} />
        <InfoBlock
          title="Total Active Enrollees"
          value={enrolleeSummary?.totalActiveEnrolleeCount || '0'}
        />
        <InfoBlock
          title="Total Inactive Enrollees"
          value={enrolleeSummary?.totalInactiveEnrolleeCount || '0'}
        />
        <InfoBlock
          title="Total Expired Enrollees"
          value={enrolleeSummary?.totalExpiredEnrolleeCount || '0'}
        />
        <InfoBlock
          title="Total Capitation Enrollees"
          value={enrolleeSummary?.totalCapitatedEnrolleeCount || '0'}
        />
        <InfoBlock
          title="Total Capitation Amount"
          value={formatMoney(
            Number(
              enrolleeSummary?.totalCapitatedAmount
                ? enrolleeSummary?.totalCapitatedAmount / 100
                : 0,
            ),
          )}
        />

        <div className="flex flex-row align-items-center">
          <div className="text-around">
            <p className="no-margin">Showing</p>
          </div>
          <div>
            <Dropdown
              options={[]}
              value={loaded}
              onChange={() => {}}
              smallWidth
              noPadding
              readOnly
            />
          </div>
          <div className="text-around">
            <p className="no-margin">of {formatNumWithComma(totalCount)}</p>
          </div>
        </div>
      </div>
    </>
  );
}
