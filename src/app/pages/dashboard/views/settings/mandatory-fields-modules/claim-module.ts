import { ReactComponent as ClaimsIcon } from 'assets/icons/navs/claims.svg';
import { IFormFieldProps, IMandatoryFieldsModule } from '.';

const diagnosisNotEmptyCheck = (value: Record<string, any>[]): boolean =>
  value.every((_item) => _item.diagnosisICD10 || _item.diagnosisICD11 || _item.diagnosisSNOMED);

export type ClaimFormFields =
  | 'claim-date'
  | 'provider-id'
  | 'treatment-start-date'
  | 'treatment-end-date'
  | 'service-type'
  | 'priority'
  | 'diagnosis'
  | 'presenting-complain'
  | 'utilization-category'
  | 'utilization-type'
  | 'utilization-quantity'
  | 'utilization-price'
  | 'utilization-pa-code'
  | 'utilization-specialty'
  | 'payment-type'
  | 'submitted-by'
  | 'specialty'
  | 'rank'
  | 'department'
  | 'facility-name'
  | 'facility-address'
  | 'additional-note'
  | 'document-url'
  | 'patient-type';

const claimFields: Record<ClaimFormFields, IFormFieldProps> = {
  'patient-type': {
    k: 'patient-type',
    label: 'Patient Type',
    path: 'patientType',
  },
  'document-url': {
    k: 'document-url',
    label: 'Document URL',
    path: 'documentUrl',
  },
  'additional-note': {
    k: 'additional-note',
    label: 'Additional Note',
    path: 'additionalNote',
  },
  'facility-address': {
    k: 'facility-address',
    label: 'Facility Address',
    path: 'facilityAddress',
  },
  'facility-name': {
    k: 'facility-name',
    label: 'Facility Name',
    path: 'facilityName',
  },
  department: {
    k: 'department',
    label: 'Department',
    path: 'department',
  },
  rank: {
    k: 'rank',
    label: 'Rank',
    path: 'rank',
  },
  specialty: {
    k: 'specialty',
    label: 'Specialty',
    path: 'specialty',
  },
  'payment-type': {
    k: 'payment-type',
    label: 'Payment Type',
    path: 'paymentType',
  },
  'submitted-by': {
    k: 'submitted-by',
    label: 'Submitted By',
    path: 'submittedBy',
  },
  'claim-date': {
    k: 'claim-date',
    label: 'Treatment Date and Time',
    path: 'claimDate',
  },
  'provider-id': {
    k: 'provider-id',
    label: 'Coverage Name',
    path: 'providerId',
  },
  'treatment-start-date': {
    k: 'treatment-start-date',
    label: 'Treatment Start Date and Time',
    path: 'treatmentStartDate',
  },
  'treatment-end-date': {
    k: 'treatment-end-date',
    label: 'Treatment End Date and Time',
    path: 'treatmentEndDate',
  },
  'service-type': {
    k: 'service-type',
    label: 'Visitation Type',
    path: 'serviceType',
  },
  priority: {
    k: 'priority',
    label: 'Priority',
    path: 'priority',
  },
  diagnosis: {
    k: 'diagnosis',
    label: 'Clinical Diagnosis',
    path: 'diagnosis',
    customEmptyCheck: diagnosisNotEmptyCheck,
  },
  'presenting-complain': {
    k: 'presentingComplain',
    label: 'Presenting Complaints',
    path: 'presentingComplain',
  },
  'utilization-category': {
    k: 'utilization-category',
    label: 'Utilization > Utilization Category',
    path: 'utilizations',
    childPath: 'category',
    isMultiple: true,
  },
  'utilization-type': {
    k: 'utilization-type',
    label: 'Utilization > Utilization Type',
    path: 'utilizations',
    childPath: 'type',
    isMultiple: true,
  },
  'utilization-quantity': {
    k: 'utilization-quantity',
    label: 'Utilization > Quantity',
    path: 'utilizations',
    childPath: 'quantity',
    isMultiple: true,
  },
  'utilization-price': {
    k: 'utilization-price',
    label: 'Utilization > Price',
    path: 'utilizations',
    childPath: 'price',
    isMultiple: true,
  },
  'utilization-pa-code': {
    k: 'utilization-pa-code',
    label: 'Utilization > PA Code',
    path: 'utilizations',
    childPath: 'paCode',
    isMultiple: true,
  },
  'utilization-specialty': {
    k: 'utilization-specialty',
    label: 'Utilization > Specialty',
    path: 'utilizations',
    childPath: 'specialty',
    isMultiple: true,
    customEmptyCheck: (_, u) => {
      return `${u.category}${u.type}`.toLowerCase().includes('consult') ? !!u.specialty : true;
    },
  },
};
export const DEFAULT_CLAIMS_FORM_MANDATORY_FIELDS: ClaimFormFields[] = [
  'provider-id',
  'claim-date',
  'diagnosis',
  'utilization-type',
  'utilization-category',
  'service-type',
  'utilization-specialty',
];
const CRITICAL_CLAIMS_MANDATORY_FIELDS: ClaimFormFields[] = [
  'provider-id',
  'claim-date',
  'diagnosis',
  'utilization-type',
  'utilization-category',
  'service-type',
];

export const ClaimMandatoryFieldsModule: IMandatoryFieldsModule = {
  fields: claimFields,
  icon: ClaimsIcon,
  name: 'Claims',
  criticalFields: CRITICAL_CLAIMS_MANDATORY_FIELDS,
  path: 'hmoClaims',
  defaultMandatoryFields: DEFAULT_CLAIMS_FORM_MANDATORY_FIELDS,
  isNotDisplayed: true,
};
