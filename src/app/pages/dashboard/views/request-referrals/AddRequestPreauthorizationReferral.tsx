import React, { Fragment, useEffect, useMemo, useRef, useState } from 'react';
import {
  isHmoClaimsManagedByClinify,
  LASHMA_AGENCY,
  LASHMA_AGENCY_2,
  OGSHIA_AGENCY,
} from 'app/shared/utils/is-clinify-hmo';
import useClinifyId from 'app/shared/hooks/client/useClinifyId';
import useHospitalData from 'app/shared/hooks/client/useHospitalData';
import StatusButtons from 'app/shared/components/buttons/StatusButton';
import { getUserPayload } from 'app/shared/utils/authentication';
import ActionSectionSectionWithAuditDetails from 'app/shared/components/record-forms/ActionSectionWithAuditDetails';
import usePatientProfile from 'app/shared/hooks/client/usePatientProfile';
import usePatientData from 'app/shared/hooks/client/usePatientData';
import { HospitalPlan, PaCodeStatus, UserType } from 'graphql-types/globalTypes';
import { userType } from 'app/shared/utils/authTracker';
import { useMutation } from '@apollo/client';
import { UPDATE_REFERRAL_UTILIZATION_STATUS } from 'apollo-queries/mains/preauthorization-referral';
import { useToasts } from 'react-toast-notifications';
import errorHandler from 'app/shared/utils/errorHandler';
import classNames from 'classnames';
import { FlaggedRuleItem } from 'app/shared/types/business-rule';
import { hasRuleCategory } from 'app/shared/utils/common';
import useBusinessRulesFlaggedItems from 'app/pages/dashboard/components/forms/HmoFlagSection/hooks/useBusinessRulesFlaggedItems';
import { AgreedTariffVariables } from '../../../../../graphql-types/AgreedTariff';
import { InputRow, SelectWrapper } from '../../../../shared/components/wrappers/Wrapper';
import Dropdown from '../../../../shared/components/dropdown/Dropdown';
import TextInput from '../../../../shared/components/inputs/TextInput';
import { FlaggedAuthenticationInput } from '../../../../shared/components/inputs/AuthenticationInput';
import { defaultInputProps, withComma } from '../billings/constants';
import LoaderOrError from '../../../../shared/components/loaders/LoaderOrError';
import { FormContent, RecordForm } from '../../../../shared/components/record-forms/FormWrapper';
import DatePicker from '../../../../shared/components/pickers/DatePicker';
import {
  BLOODGROUP_OPTIONS,
  CLASS_CONSULTATION_OPTIONS,
  HOSPITAL_UNIT_OPTIONS,
  OGSHIA_DIAGNOSIS_LIST,
  PRIORITY_SURGERY_OPTIONS,
  RHESUS_FACTOR,
  SPECIALTY_OPTIONS,
  UTILIZATION_SPECIALTY_LIST_2,
} from '../../../../../@constant/options';
import { getDiagnosisValue, listTypeMapper } from '../admission/AddAdmission';
import { loadDiagnosisOptions } from '../../../../shared/utils/loadIcdAsync';
import { DropdownCheckBox } from '../../../../shared/components/dropdown/DropdownCheckBox';
import MultiGroupAction from '../../../../shared/components/buttons/MultiGroupActions';
import TextArea from '../../../../shared/components/inputs/TextArea';
import AddressInput from '../../../../shared/components/inputs/Address/AddressInput';
import DocumentAttacher from '../../../../shared/components/file-uploads/DocumentAttacher';
import fieldsAreValid from '../../../../shared/utils/fieldsAreValid';
import Prompter from '../../../../shared/components/prompter/Prompter';
import Modal from '../../../../shared/components/modal/Modal';
import {
  ANNUAL_SCREENING_DIAGNOSIS,
  preAuthInitialState,
  SINGLE_COUNT_UTILIZATIONS,
  utilisationInitialState,
} from '../request-preauthorizations/constants';
import useHmoProviderHospitalOptions from '../request-preauthorizations/hooks/useHmoProviderHospitalOptions';
import ReferralActionsWithAuditDetails from './components/ReferralActionsWithAuditDetails';
import useRequestPreauthorizationReferralForm from './hooks/useRequestPreauthorizationReferralForm';
import useRequestPreauthorizationAPILoader from '../request-preauthorizations/hooks/useRequestPreauthorizationAPILoader';
import {
  LASHMA_REFERRAL_PROVIDER_TYPES_2,
  EXTERNAL_PROVIDER_TYPES,
  LASHMA_REFERRAL_PROVIDER_TYPES,
  PROVIDER_TYPES,
} from '../submit-claims/constants';
import useMappedUtilizationToDiagnosis from '../request-preauthorizations/hooks/useMappedUtilizationToDiagnosis';
import utilizationDiagnosisMap from '../request-preauthorizations/utilization-diagnosis.map.json';
import CheckBoxWithLabel from '../../../../shared/components/inputs/CheckBoxWithLabel';
import useFilterUtilizationOptions from '../request-preauthorizations/hooks/useFilterUtilizationOptions';
import ApprovalGroupForm from '../cliams/components/ApprovalGroupForm';
import UtilizationsSummaryTable from '../submit-claims/components/UtilizationsSummary';
import ProcessReferralUtilizationsTable from './components/ProcessReferralUtilizationsTable';

const FORM_TAG = 'PreauthorizationReferral';

interface UtilisationInput {
  category: string;
  type: string;
  quantity: string;
  price: string;
  utilizationId: string;
  paCode: string;
  status: string;
  utilizationCode?: string;
  rhesusFactor?: string;
  specialty?: string;
  [key: string]: any;
}

interface IBloodProfile {
  bloodGroup: string;
  genoType: string;
}

interface UtilisationsProp {
  utilisation: UtilisationInput;
  handleInputChange: (field: string, value: string) => void;
  getTariff: (input: AgreedTariffVariables['input']) => void;
  setItems: (input: Record<any, any>) => void;
  index: number;
  unitPriceText: string;
  readOnly?: boolean;
  loadingPrice?: boolean;
  handleGetDetail?: () => void;
  loadingDetails?: boolean;
  isEdit?: boolean;
  getProviderId: (input: string) => string;
  providerId: string;
  loadVisitationTypes: any;
  visitationType: any;
  enrolleeId: string;
  treatmentDate: string;
  shouldShowPrice?: boolean;
  isClinifyHmo?: boolean;
  facilityId?: string;
  externalPlanTypeId?: string;
  setInputs: React.Dispatch<any>;
  isExternalPlanType: boolean;
  providerName: string;
  providerType: string;
  bloodProfile: IBloodProfile;
  setBloodProfile: React.Dispatch<React.SetStateAction<IBloodProfile>>;
  referredProvider: Record<string, any>;
  flaggedItems?: FlaggedRuleItem[];
}

export const Utilisations: React.FC<UtilisationsProp> = ({
  utilisation,
  handleInputChange,
  getTariff,
  index,
  unitPriceText,
  isEdit,
  readOnly,
  setItems,
  loadingPrice,
  loadingDetails,
  providerId,
  loadVisitationTypes,
  visitationType,
  enrolleeId,
  treatmentDate,
  shouldShowPrice,
  isClinifyHmo,
  facilityId,
  externalPlanTypeId,
  setInputs,
  isExternalPlanType,
  providerName,
  providerType,
  bloodProfile,
  setBloodProfile,
  referredProvider,
  flaggedItems = [],
}) => {
  const { addToast } = useToasts();
  const { isLashmaAgency } = useHospitalData();
  const { isLashmaEnrollee } = usePatientData();

  const [updateUtilizationReferralStatus] = useMutation(UPDATE_REFERRAL_UTILIZATION_STATUS, {
    onCompleted: () => {
      addToast('Status Updated Successfully', {
        appearance: 'success',
      });
    },
    onError: (error) => {
      addToast(errorHandler(error), {
        appearance: 'error',
      });
    },
  });

  const {
    loadAllUtilizationTypes,
    toggleFetchAllTypes,
    fetchAllTypes,
    rejectedOptions,
  } = useFilterUtilizationOptions('REFERRAL', providerName, providerId, visitationType);
  const { isProviderHmoClaimsManagedByClinify } = useHospitalData();
  const { onUtilizationTypeChange } = useMappedUtilizationToDiagnosis({
    setInputs,
    diagnosisFieldName: 'diagnosis',
    utilizationFieldName: 'utilizations',
    map: utilizationDiagnosisMap,
    utilIdx: index,
    loadAllUtilizationTypes,
    enrolleeId,
    providerId,
    isExternalPlanType,
  });

  const processReferralUtilizationStatus = async (_input: any, onDone?: () => unknown) => {
    await updateUtilizationReferralStatus({
      variables: {
        input: {
          id: _input?.id,
          status: _input?.status,
          rejectionReason: _input?.rejectionReason,
          specifyReasonForRejection: _input?.specifyReasonForRejection,
        },
      },
    });
    onDone?.();
  };

  const isBloodBank =
    providerType === 'BloodBank' ||
    (referredProvider?.plan || '')?.toLowerCase().includes('bloodbank');
  const showDrugFields = ['drug', 'medicine', 'injection'].some(
    (keyword) =>
      `${utilisation.category}${utilisation.type}`.toLowerCase().includes(keyword) &&
      !utilisation.category?.toLowerCase()?.includes('fees') &&
      !utilisation.category?.toLowerCase()?.includes('admission'),
  );
  const showSpecialty = ['consult'].some((keyword) =>
    `${utilisation.category}${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isToothExtraction = ['tooth', 'extraction'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isBloodTransfusion = ['blood', 'transfusion'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isLimbXray = ['limb', 'xray'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isDentalRadiograph = ['dental', 'radiograph'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isCrossMatching = ['cross', 'matching'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isWoundDressing = ['wound', 'dressing'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isInjectionService = utilisation.category?.toLowerCase()?.includes('injection');
  const isInfusionService = utilisation.category?.toLowerCase()?.includes('infusion');

  const medicationIsPack = useMemo(() => {
    if (!showDrugFields || !utilisation.type) return false;
    return SINGLE_COUNT_UTILIZATIONS.includes(utilisation.type);
  }, [showDrugFields, utilisation.type]);

  const disableQuantity =
    isLashmaEnrollee &&
    !(
      isBloodBank ||
      (showDrugFields && !medicationIsPack) ||
      isInjectionService ||
      isInfusionService ||
      isToothExtraction ||
      isBloodTransfusion ||
      isLimbXray ||
      isDentalRadiograph ||
      isCrossMatching ||
      isWoundDressing
    );

  const hidePaCode = useMemo(
    () => isProviderHmoClaimsManagedByClinify && utilisation.status?.toLowerCase() === 'pending',
    [isProviderHmoClaimsManagedByClinify, utilisation.status],
  );

  const cannotEdit = utilisation.status && utilisation.status !== 'Pending';
  const categoryFlaggedItems = useMemo(() => {
    return (
      utilisation.utilizationCode &&
      hasRuleCategory(utilisation.utilizationCode, 'utilizationCategory', flaggedItems)
    );
  }, [flaggedItems, utilisation.category]);

  const typeFlaggedItems = useMemo(() => {
    return (
      utilisation.utilizationCode &&
      hasRuleCategory(utilisation.utilizationCode, 'utilizationType', flaggedItems)
    );
  }, [flaggedItems, utilisation.type]);

  const quantityFlaggedItems = useMemo(() => {
    return (
      utilisation.utilizationCode &&
      hasRuleCategory(utilisation.utilizationCode, 'quantity', flaggedItems)
    );
  }, [flaggedItems, utilisation.quantity]);

  const rejectionReasons =
    utilisation?.rejectionReason?.length && Array.isArray(utilisation.rejectionReason)
      ? utilisation.rejectionReason
      : [''];

  return (
    <>
      <InputRow>
        {fetchAllTypes ? (
          <Dropdown
            creatable={false}
            title="Utilization Type"
            options={[]}
            onChange={({ label, code, hmoPlanBenefitId, utilizationCategory }) => {
              handleInputChange('type', code);
              setItems({
                type: label,
                utilizationCode: (code || '').replaceAll(' ', ''),
                price: '0',
                quantity: '1',
                category: utilizationCategory,
                utilizationId: hmoPlanBenefitId,
              });
              getTariff({
                providerId,
                position: index,
                id: (code || '').replaceAll(' ', ''),
                enrolleeId,
                treatmentDate,
                visitTypeId: visitationType,
                facilityId,
                externalPlanId: externalPlanTypeId,
              });
            }}
            onValueChange={(selection) => {
              const label = selection?.label;
              onUtilizationTypeChange(label);
            }}
            placeholder="Select One"
            isRequired
            overrideIsRequired
            value={utilisation.type || null}
            readOnly={readOnly || cannotEdit}
            isAsync
            loadOptions={loadAllUtilizationTypes}
            noPadding
            cacheUniqs={[`${utilisation?.utilizationId}-${externalPlanTypeId}`]}
            fieldName={`${FORM_TAG}_utilization-type`}
            withError={typeFlaggedItems}
          />
        ) : (
          <Dropdown
            creatable={false}
            title="Utilization Type"
            options={[]}
            onChange={({ value, label }) => {
              handleInputChange('type', value);
              setItems({
                type: label,
                utilizationCode: value,
                price: '0',
              });
              getTariff({
                providerId,
                position: index,
                id: value,
                utilizationId: utilisation.utilizationId,
                enrolleeId,
                treatmentDate,
                visitTypeId: visitationType,
                facilityId,
                externalPlanId: externalPlanTypeId,
              });
            }}
            onValueChange={(selection) => {
              const label = selection?.label;
              onUtilizationTypeChange(label);
            }}
            placeholder="Select One"
            isRequired
            overrideIsRequired
            value={utilisation.type || null}
            readOnly={readOnly || cannotEdit}
            isAsync
            loadOptions={loadVisitationTypes}
            additional={{
              utilizationId: utilisation?.utilizationId,
              action: 'utilizationProcedure',
              providerId,
              visitationType,
              hospitalId: facilityId || '',
            }}
            noPadding
            cacheUniqs={[`${utilisation?.utilizationId}-${externalPlanTypeId}`]}
            onFocus={() => {
              if (!utilisation.category) {
                toggleFetchAllTypes(true);
              }
            }}
            fieldName={`${FORM_TAG}_utilization-type`}
            withError={typeFlaggedItems}
          />
        )}
      </InputRow>
      <SelectWrapper>
        <Dropdown
          creatable={false}
          title="Utilization Category"
          options={[]}
          onChange={({ value, label }) => {
            setItems({
              category: label,
              utilizationId: value,
              utilizationCode: null,
              type: null,
              quantity: '1',
              price: '0',
            });
          }}
          placeholder="Select One"
          isRequired
          overrideIsRequired
          value={utilisation.category || null}
          readOnly={readOnly || cannotEdit}
          isAsync
          loadOptions={loadVisitationTypes}
          additional={{
            action: 'utilizationCategory',
            providerId,
            visitationType,
            hospitalId: facilityId || '',
            rejectedOptions,
          }}
          noPadding
          cacheUniqs={[visitationType]}
          fieldName={`${FORM_TAG}_utilization-category`}
          withError={categoryFlaggedItems}
        />
      </SelectWrapper>
      <TextInput
        name="utilizationCode"
        title="Utilization Code"
        onChange={({ target: { value } }) => {
          setItems({
            utilizationCode: value,
            price: '0',
            type: null,
          });
          getTariff({
            providerId,
            position: index,
            id: value,
            utilizationId: isClinifyHmo ? undefined : utilisation.utilizationId,
            enrolleeId,
            treatmentDate,
            visitTypeId: visitationType,
            facilityId,
            externalPlanId: externalPlanTypeId,
          });
        }}
        value={utilisation.utilizationCode}
        placeholder="Enter Utilization Code"
        readOnly
        fieldName={`${FORM_TAG}_utilization-code`}
      />
      {isBloodBank ? (
        <InputRow>
          <SelectWrapper>
            <Dropdown
              onChange={({ value }) => {
                setBloodProfile({ ...bloodProfile, bloodGroup: value });
              }}
              options={BLOODGROUP_OPTIONS}
              readOnly={readOnly}
              value={bloodProfile?.bloodGroup || null}
              placeholder="Select One"
              title="Blood Group"
              noPadding
              fieldName={`${FORM_TAG}_utilization-blood-group`}
            />
          </SelectWrapper>

          {/* <SelectWrapper>
            <Dropdown
              onChange={({ value }) => {
                setBloodProfile({ ...bloodProfile, genoType: value });
              }}
              options={GENOTYPE_OPTIONS}
              readOnly={readOnly}
              value={bloodProfile?.genoType || null}
              placeholder="Select One"
              title="Genotype"
              noPadding
            />
          </SelectWrapper> */}

          <SelectWrapper>
            <Dropdown
              onChange={({ value }) => handleInputChange('rhesusFactor', value)}
              options={RHESUS_FACTOR}
              readOnly={readOnly}
              value={utilisation?.rhesusFactor || null}
              placeholder="Select One"
              title="Rhesus (Rh) Factor"
              noPadding
              fieldName={`${FORM_TAG}_utilization-rhesus-factor`}
            />
          </SelectWrapper>
        </InputRow>
      ) : null}
      <TextInput
        name="quantity"
        title={isBloodBank ? 'Quantity (Blood Pint)' : 'Quantity'}
        onChange={({ target: { value } }) => handleInputChange('quantity', value)}
        value={utilisation.quantity}
        placeholder="Enter Quantity"
        readOnly={readOnly || cannotEdit || (isExternalPlanType && !isBloodBank) || disableQuantity}
        type="number"
        isRequired
        overrideIsRequired
        fieldName={`${FORM_TAG}_utilization-quantity`}
        withError={quantityFlaggedItems}
      />
      <FlaggedAuthenticationInput
        {...defaultInputProps}
        label="Unit Price"
        name="unitPrice"
        onChange={({ target: { value } }) => handleInputChange('price', value)}
        value={utilisation.price ? `${withComma(shouldShowPrice ? utilisation.price : 0)}` : '0.00'}
        placeholder={unitPriceText}
        readOnly
        loading={loadingPrice}
      />

      {showSpecialty ? (
        <InputRow>
          <SelectWrapper>
            <Dropdown
              noPadding
              title="Specialty"
              options={UTILIZATION_SPECIALTY_LIST_2}
              onChange={({ value }) => handleInputChange('specialty', value)}
              readOnly={readOnly || cannotEdit}
              value={utilisation.specialty || null}
              isRequired
              overrideIsRequired
              fieldName={`${FORM_TAG}_utilization-specialty`}
            />
          </SelectWrapper>
        </InputRow>
      ) : null}
      <FlaggedAuthenticationInput
        {...defaultInputProps}
        label="Total Price"
        name="totalPrice"
        onChange={() => {}}
        value={
          utilisation.price
            ? `${withComma(
                Number(shouldShowPrice ? utilisation.price : 0) * Number(utilisation.quantity),
              )}`
            : '0.00'
        }
        placeholder={unitPriceText}
        readOnly
      />
      <TextInput
        readOnly
        name="RefCode"
        value={hidePaCode ? '' : utilisation.paCode}
        title="Referral Code"
        onChange={() => {}}
        placeholder="Enter Referral Code"
        loading={loadingDetails}
        skipTranslate
      />

      {utilisation.status === PaCodeStatus.Rejected && (
        <InputRow>
          {[LASHMA_AGENCY, LASHMA_AGENCY_2].includes(providerName) ? (
            <>
              {rejectionReasons.map((_rejectionReason, _idx) => (
                <InputRow key={_idx}>
                  <SelectWrapper fullWidth>
                    <Dropdown
                      title="Rejection Reason"
                      options={[]}
                      placeholder="Select One"
                      value={_rejectionReason || null}
                      onChange={() => {}}
                      readOnly
                      creatable
                      noPadding
                    />
                  </SelectWrapper>
                  {_rejectionReason === 'Others' && (
                    <TextArea
                      name="reasonForRejection"
                      label="Specify Rejection Reason"
                      fullWidth
                      onChange={() => {}}
                      value={utilisation?.specifyReasonForRejection}
                      readOnly
                    />
                  )}
                </InputRow>
              ))}
            </>
          ) : (
            <TextArea
              name="reasonForRejection"
              label="Reason for Rejection"
              fullWidth
              onChange={() => {}}
              value={utilisation.statusDescription}
              readOnly
              fieldName={`${FORM_TAG}_utilization-reason-for-rejection`}
            />
          )}
        </InputRow>
      )}

      {isClinifyHmo && isLashmaAgency && isEdit ? (
        <>
          <ApprovalGroupForm
            providerName={providerName}
            utilization={utilisation}
            processUtilization={processReferralUtilizationStatus}
            isReferral
          />
        </>
      ) : null}
    </>
  );
};

type Props = {
  filterOptions: Record<string, any>;
  isOnModal?: boolean;
  defaultId?: string;
  clearAction?: (id?: string) => void;
};
export default function AddRequestPreauthorizationReferral(props: Props) {
  const { filterOptions, isOnModal, defaultId, clearAction } = props;
  const [listType, setListType] = useState({ 0: 'ICD-10' });
  const utilizationsRef = useRef<Record<string, any>[]>([]);

  const _userType = userType();

  const {
    isEdit,
    startEdit,
    setStartEdit,
    handleInputChange,
    handleMultipleFieldsChange,
    inputs,
    actionText,
    action: _action,
    readOnly,
    disableActionButton,
    fetchingData,
    errorFetching,
    unitPriceText,
    toggle,
    getTariff,
    setListInputs,
    loading,
    authDiagnosis,
    setInputs,
    hmoDropdownOptions,
    getProviderId,
    processReferralStatus,
    processingReferralStatus,
    externalPlanType,
  } = useRequestPreauthorizationReferralForm({
    filterOptions,
    defaultId,
    isOnModal,
    clearAction,
  });
  const { loadVisitationTypes } = useRequestPreauthorizationAPILoader();
  const { hmoId } = useClinifyId();
  const { orgHmoId } = getUserPayload();
  const {
    hospitalId,
    hospitalName,
    isLashmaAgency,
    hospitalLGA,
    isLashmaProvider,
  } = useHospitalData();
  const { loadHmoHospitals } = useHmoProviderHospitalOptions(inputs.providerId);
  const { patientData, updatePersonalInformation } = usePatientProfile();
  const { isLashmaEnrollee } = usePatientData();

  const [providerType, setProviderType] = useState('');
  const [providerLga, setProviderLGA] = useState('');

  const [bloodProfile, setBloodProfile] = useState<IBloodProfile>({
    bloodGroup: '',
    genoType: '',
  });

  const action = () => {
    if (
      (bloodProfile?.bloodGroup || bloodProfile?.genoType) &&
      (bloodProfile?.bloodGroup !== patientData?.personalInformation?.bloodGroup ||
        bloodProfile?.genoType !== patientData?.personalInformation?.genoType)
    ) {
      updatePersonalInformation(bloodProfile);
    }
    _action();
  };

  const isClinifyHmoWithoutLashma = useMemo(() => {
    return isHmoClaimsManagedByClinify(
      hmoDropdownOptions.find(({ value }) => value === inputs?.providerId)?.label,
      [LASHMA_AGENCY, LASHMA_AGENCY_2],
    );
  }, [inputs.providerId, hmoDropdownOptions]);
  const isClinifyHmo = useMemo(() => {
    return isHmoClaimsManagedByClinify(
      hmoDropdownOptions.find(({ value }) => value === inputs?.providerId)?.label,
    );
  }, [inputs.providerId, hmoDropdownOptions]);

  const provider = hmoDropdownOptions?.find(({ value }) => value === inputs?.providerId);
  const isLashma = [LASHMA_AGENCY, LASHMA_AGENCY_2].includes(provider?.label as string);
  const isOgshia = provider?.label === OGSHIA_AGENCY;

  const diagnosisIsValid = authDiagnosis.every((_diag) => Object.values(_diag).some(Boolean));

  const { loadAllUtilizationTypes, toggleFetchAllTypes } = useFilterUtilizationOptions(
    'REFERRAL',
    provider?.label || '',
    inputs.providerId,
    inputs.serviceTypeCode,
  );

  const isExternalPlanType = inputs?.isExternalPlanType;
  const { onDiagnosisChange } = useMappedUtilizationToDiagnosis({
    setInputs,
    map: utilizationDiagnosisMap,
    diagnosisFieldName: 'diagnosis',
    utilizationFieldName: 'utilizations',
    utilIdx: 0,
    loadAllUtilizationTypes,
    providerId: inputs.providerId,
    enrolleeId: inputs.enrolleeId || inputs.enrolleeNumber,
    facilityId: inputs.facilityId,
    getTariff,
    visitationType: inputs.serviceTypeCode,
    treatmentDate: inputs.requestDateTime,
    isExternalPlanType,
    externalPlanTypeId: inputs.externalPlanTypeId,
  });

  useEffect(() => {
    if (!inputs.providerId && inputs.provider?.id) {
      const currentInput = { ...inputs };
      const providerId = currentInput.provider.id;
      delete currentInput.provider;
      setInputs({
        ...currentInput,
        providerId,
      });
    }
  }, [inputs]);

  useEffect(() => {
    if (isClinifyHmoWithoutLashma) {
      setInputs((v) => {
        if (v?.utilizations?.length) {
          utilizationsRef.current = v.utilizations;
        }
        return {
          ...v,
          utilizations: [],
        };
      });
    }
  }, [isClinifyHmoWithoutLashma, inputs.utilizations?.length]);
  useEffect(() => {
    if (hmoId) {
      setInputs((v) => {
        if (v?.utilizations?.length) {
          utilizationsRef.current = v.utilizations;
        }
        return {
          ...v,
          utilizations: isClinifyHmoWithoutLashma ? [] : v.utilizations,
          providerId: hmoId,
        };
      });
    } else if (!isEdit) {
      setInputs((v) => ({
        ...v,
        referringProviderId: hospitalId,
        referringProviderName: hospitalName,
      }));
    }
  }, [hmoId, hospitalId, inputs.hospital?.id, inputs.enrolleeId]);

  useEffect(() => {
    if (
      !!provider?.memberPlanId &&
      !!externalPlanType?.id &&
      provider.memberPlanId === externalPlanType.id
    ) {
      setInputs({
        ...inputs,
        isExternalPlanType: true,
        externalPlanTypeId: externalPlanType?.id,
        priority: 'Emergency',
        serviceType: '',
        serviceTypeCode: '',
        utilizations: [
          {
            category: '',
            utilizationId: '',
            utilizationCode: null,
            type: null,
            quantity: '1',
            price: '0',
          },
        ],
      });
      setProviderType(EXTERNAL_PROVIDER_TYPES[1].value || '');
    }
  }, [provider?.memberPlanId, externalPlanType?.id]);

  useEffect(() => {
    if (
      providerType === 'BloodBank' ||
      (inputs?.referredProvider?.plan || '')?.toLowerCase().includes('bloodbank')
    ) {
      setBloodProfile({
        bloodGroup: patientData?.personalInformation?.bloodGroup || '',
        genoType: patientData?.personalInformation?.genoType || '',
      });
    } else {
      setBloodProfile({ bloodGroup: '', genoType: '' });
    }
  }, [patientData?.id, providerType]);

  const shouldShowPrice =
    isLashma && !isLashmaAgency
      ? (inputs.referredProviderId || inputs.referredProvider?.id) === hospitalId
      : true;

  let PROVIDER_TYPE_LIST = [...PROVIDER_TYPES];
  if (isLashma) {
    PROVIDER_TYPE_LIST = orgHmoId
      ? [...LASHMA_REFERRAL_PROVIDER_TYPES]
      : [...LASHMA_REFERRAL_PROVIDER_TYPES_2];
  }

  const agencyName = useMemo(() => {
    return hmoDropdownOptions.find(({ value }) => value === inputs.providerId)?.label;
  }, [hmoDropdownOptions, inputs.providerId]);
  const providerTypeList = useMemo(() => {
    if (inputs.isExternalPlanType) {
      return EXTERNAL_PROVIDER_TYPES;
    }
    return PROVIDER_TYPE_LIST;
  }, [providerType, inputs.isExternalPlanType, isLashma]);

  const canMutate = [UserType.OrganizationAdmin, UserType.OrganizationBillingOfficer].includes(
    _userType,
  );
  const { flaggedItems, setFlaggedItems } = useBusinessRulesFlaggedItems();

  const isAnnualScreening = useMemo(() => {
    if (!isLashmaEnrollee) return false;
    if ([...(inputs.diagnosis || [])]?.[0]?.diagnosisICD10 === ANNUAL_SCREENING_DIAGNOSIS) {
      return true;
    }
    if (inputs?.utilizations?.[0]?.type?.toLowerCase().includes('annual screening')) {
      return true;
    }
    return false;
  }, [inputs?.diagnosis, inputs?.utilizations]);

  const handleDiagnosisChange = (idx: number, value: string) => {
    if (isLashmaEnrollee && value === ANNUAL_SCREENING_DIAGNOSIS) {
      handleInputChange('diagnosis', [
        { diagnosisICD10: ANNUAL_SCREENING_DIAGNOSIS, diagnosisICD11: '', diagnosisSNOMED: '' },
      ]);
      onDiagnosisChange(value, 0);

      const annualScreeningUtil = (inputs?.utilizations || []).find((_item) =>
        _item?.type?.toLowerCase().includes('annual screening'),
      );

      if (!annualScreeningUtil) {
        handleInputChange('utilizations', [
          {
            category: '',
            utilizationId: '',
            utilizationCode: null,
            type: null,
            quantity: '1',
            price: '0',
          },
        ]);
      } else {
        handleInputChange('utilizations', [annualScreeningUtil]);
      }
      return;
    }

    const newData = [...(inputs.diagnosis || [])];
    newData[idx] = {
      ...newData[idx],
      [listTypeMapper[listType[`${idx}`] || 'ICD-10']]: value,
    };
    handleInputChange('diagnosis', newData);
    onDiagnosisChange(value, idx);
  };

  if (fetchingData || errorFetching)
    return <LoaderOrError loading={fetchingData} error={errorFetching} />;

  return (
    <>
      {orgHmoId ? (
        <ProcessReferralUtilizationsTable
          utilizations={inputs?.utilizations}
          enrolleeId={inputs?.enrolleeNumber || inputs?.enrolleeId}
          referringFrom={{
            name: inputs.referringProviderName || inputs.referringProvider?.name,
            address: inputs?.facilityAddress,
          }}
          referringTo={{
            name: inputs?.referredProviderName || inputs?.referredProvider?.name,
          }}
          referralCode={inputs?.utilizations?.[0]?.paCode}
          onProcessUtilization={() => {}}
          referralId={inputs?.id}
          diagnosisList={authDiagnosis}
          treatmentDateAndTime={inputs?.requestDateTime}
          profileId={inputs?.profileId}
          showVitalsSummary
        />
      ) : (
        <UtilizationsSummaryTable
          data={inputs}
          providerName={provider?.label || ''}
          className={classNames({ hide: !!hmoId })}
          type="Referral"
          setInputs={setInputs}
          setItems={(value, i) => {
            setListInputs('utilizations', value, i);
          }}
          getTariff={getTariff}
          action={action}
          readOnly={readOnly}
          disableEdit={
            disableActionButton ||
            (isEdit && !startEdit ? false : isClinifyHmo && !diagnosisIsValid) ||
            (isEdit && isClinifyHmo && (hmoId ? inputs?.providerId !== hmoId : !canMutate)) ||
            (!isClinifyHmoWithoutLashma &&
              !fieldsAreValid('authutilizationsfields', inputs.utilizations, true))
          }
          isEdit={isEdit}
          showPrice={shouldShowPrice}
          onAddNewItem={
            isAnnualScreening
              ? undefined
              : () =>
                  setInputs((prev) => ({
                    ...prev,
                    utilizations: [...prev.utilizations, utilisationInitialState],
                  }))
          }
          flaggedItems={flaggedItems}
        />
      )}
      <RecordForm clear>
        <FormContent>
          <InputRow>
            <SelectWrapper noPadding>
              <DatePicker
                label="Treatment Date and Time"
                isRequired
                overrideIsRequired
                withBorderRadius
                onChange={(date) => handleInputChange('requestDateTime', date)}
                value={inputs.requestDateTime}
                placeholderText="Select Date and Time"
                readOnly={false}
                minDate={new Date(new Date().getFullYear() - 100, new Date().getMonth())}
                maxDate={new Date()}
                disabled={readOnly}
                fieldName={`${FORM_TAG}_treatment-date-and-time`}
              />
            </SelectWrapper>
            <SelectWrapper>
              <Dropdown
                title="Coverage Name"
                options={hmoDropdownOptions}
                onChange={({ value, enrolleeId, memberUniqueId }) =>
                  setInputs({ ...inputs, providerId: value, enrolleeId, memberUniqueId })
                }
                placeholder="Select One"
                isRequired
                overrideIsRequired
                value={inputs.providerId ? inputs.providerId : null}
                readOnly={readOnly}
                noPadding
                skipTranslate
                topRightComponent={
                  (externalPlanType?.id || inputs.isExternalPlanType) &&
                  (isLashmaProvider || isLashmaAgency) ? (
                    <CheckBoxWithLabel
                      className="checkbox-container d-flex align-items-center gap-x-2"
                      label="LASHMA-AID"
                      onChange={() => {
                        setInputs({
                          ...inputs,
                          isExternalPlanType: !inputs.isExternalPlanType,
                          priority: !inputs.isExternalPlanType ? 'Emergency' : null,
                          externalPlanTypeId: !inputs.isExternalPlanType
                            ? externalPlanType?.id
                            : undefined,
                          serviceType: '',
                          serviceTypeCode: '',
                          utilizations: [
                            {
                              category: '',
                              utilizationId: '',
                              utilizationCode: null,
                              type: null,
                              quantity: '1',
                              price: '0',
                            },
                          ],
                        });
                      }}
                      checked={inputs.isExternalPlanType}
                      disable
                      // disable={
                      //   isEdit || readOnly || externalPlanType?.id === provider?.memberPlanId
                      // }
                    />
                  ) : null
                }
              />
            </SelectWrapper>
          </InputRow>
          {isLashma && !inputs.id ? (
            <InputRow>
              <SelectWrapper>
                <Dropdown
                  title="Provider Types"
                  options={providerTypeList}
                  onChange={({ value }) => {
                    setProviderType(value);
                    setProviderLGA('');
                    setInputs({
                      ...inputs,
                      referredProviderId: '',
                      referredProviderName: '',
                    });
                  }}
                  placeholder="Select One"
                  value={providerType || null}
                  readOnly={readOnly}
                  creatable
                  noPadding
                />
              </SelectWrapper>
              <SelectWrapper>
                <TextInput
                  name="providerLga"
                  title="LGA Of Provider"
                  readOnly
                  onChange={() => {}}
                  value={providerLga}
                  placeholder="LGA Of Referral Provider (To)"
                  skipTranslate
                  noPadding
                  fullWidth
                />
              </SelectWrapper>
            </InputRow>
          ) : null}
          <InputRow>
            <SelectWrapper>
              <Dropdown
                title="Referring Provider (From)"
                options={[]}
                onChange={({ value, hospitalId, address }) => {
                  setInputs({
                    ...inputs,
                    referringProviderId: hospitalId,
                    referringProviderName: value,
                    facilityName: value,
                    facilityAddress: address,
                  });
                }}
                placeholder="Select One"
                readOnly={readOnly || !hmoId}
                value={inputs.referringProviderName || inputs.referringProvider?.name || null}
                loadOptions={loadHmoHospitals}
                isAsync
                creatable={false}
                noPadding
                isRequired
                overrideIsRequired
              />
            </SelectWrapper>
            <SelectWrapper>
              <Dropdown
                title="Referral Provider (To)"
                options={[]}
                onChange={({ value, hospitalId, lga }) => {
                  setProviderLGA(lga || '');
                  setInputs({
                    ...inputs,
                    referredProviderId: hospitalId,
                    referredProviderName: value,
                  });
                }}
                placeholder="Select One"
                readOnly={readOnly}
                value={inputs.referredProviderName || inputs.referredProvider?.name || null}
                loadOptions={loadHmoHospitals}
                isAsync
                creatable={false}
                noPadding
                isRequired
                overrideIsRequired
                additional={{
                  removeInactive: true,
                  agencyName,
                  ...(isLashma
                    ? {
                        providerType,
                        ...(providerType === 'Investigation' ? { lga: hospitalLGA } : {}),
                        isExternalPlanType: inputs.isExternalPlanType,
                        ignorePlans: orgHmoId
                          ? []
                          : [
                              ...(providerType === 'Pharmacy'
                                ? []
                                : [
                                    HospitalPlan.PharmacyPlusInventory,
                                    HospitalPlan.PharmacyMinusInventory,
                                  ]),
                              ...(providerType === 'Investigation'
                                ? []
                                : [
                                    HospitalPlan.InvestigationPlusInventory,
                                    HospitalPlan.InvestigationMinusInventory,
                                    HospitalPlan.LaboratoryPlusInventory,
                                    HospitalPlan.LaboratoryMinusInventory,
                                    HospitalPlan.RadiologyPlusInventory,
                                    HospitalPlan.RadiologyMinusInventory,
                                  ]),
                            ],
                      }
                    : {}),
                }}
                cacheUniqs={[providerType, hospitalLGA, inputs.isExternalPlanType, agencyName]}
              />
            </SelectWrapper>

            <TextInput
              name="referredBy"
              title="Referred By"
              readOnly
              onChange={() => {}}
              value={inputs.referredBy}
              placeholder="Enter Referral's Name"
              skipTranslate
              fieldName={`${FORM_TAG}_referred-by`}
            />
            <TextInput
              name="status"
              title="Referral Status"
              readOnly
              onChange={() => {}}
              value={utilizationsRef.current?.[0]?.status || 'Pending'}
              placeholder="Referral Status"
              skipTranslate
            />
          </InputRow>

          {inputs.providerId ? (
            <>
              <InputRow>
                <SelectWrapper noFlex>
                  <Dropdown
                    title="Visitation Type"
                    options={[]}
                    onChange={({ label, value }) => {
                      setInputs({
                        ...inputs,
                        serviceType: label,
                        serviceTypeCode: value,
                        ...(isEdit
                          ? {}
                          : {
                              utilizations: preAuthInitialState.utilizations,
                            }),
                      });
                    }}
                    placeholder="Select One"
                    isRequired
                    overrideIsRequired
                    value={inputs.serviceType || null}
                    readOnly={readOnly}
                    isAsync
                    loadOptions={loadVisitationTypes}
                    additional={{
                      action: 'visitation',
                      providerId: inputs.providerId,
                      ...(hmoId ? { hospitalId: inputs.referringProviderId } : {}),
                      externalPlanTypeId: inputs.externalPlanTypeId,
                    }}
                    noPadding
                    creatable={false}
                    cacheUniqs={[
                      `${inputs.providerId}_${inputs.facilityId}_${inputs.serviceTypeCode}_${inputs.externalPlanTypeId}`,
                    ]}
                    fieldName={`${FORM_TAG}_visitation-type`}
                  />
                </SelectWrapper>
                <SelectWrapper>
                  <Dropdown
                    title="Priority"
                    options={PRIORITY_SURGERY_OPTIONS}
                    onChange={({ value }) => handleInputChange('priority', value)}
                    placeholder="Select One"
                    value={inputs.priority || null}
                    readOnly={readOnly}
                    creatable
                    noPadding
                    fieldName={`${FORM_TAG}_priority`}
                  />
                </SelectWrapper>
              </InputRow>
              {authDiagnosis.map((diagnose, idx) => {
                const { diagnosisICD10 = '', diagnosisICD11 = '', diagnosisSNOMED = '' } = {
                  ...diagnose,
                };
                return (
                  <Fragment key={idx}>
                    <SelectWrapper fullWidth>
                      {isOgshia ? (
                        <Dropdown
                          title="Clinical Diagnosis"
                          options={OGSHIA_DIAGNOSIS_LIST}
                          onChange={({ value }) => {
                            const newData = [...(inputs.diagnosis || [])];
                            newData[idx] = {
                              ...newData[idx],
                              diagnosisICD10: value,
                            };
                            handleInputChange('diagnosis', newData);
                          }}
                          placeholder="Select One"
                          value={diagnosisICD10 || null}
                          readOnly={readOnly}
                          noPadding
                          isRequired
                          overrideIsRequired
                          fieldName={`${FORM_TAG}_clinical-diagnosis`}
                        />
                      ) : (
                        <Dropdown
                          title="Clinical Diagnosis"
                          options={
                            isExternalPlanType
                              ? Object.values(utilizationDiagnosisMap)
                                  .flat()
                                  .map((diagnosis) => ({ value: diagnosis, label: diagnosis }))
                              : []
                          }
                          onChange={({ value }) => handleDiagnosisChange(idx, value)}
                          placeholder="Select One"
                          value={
                            getDiagnosisValue(
                              diagnosisICD10,
                              diagnosisICD11,
                              diagnosisSNOMED,
                              listType[`${idx}`] || 'ICD-10',
                            ) || null
                          }
                          loadOptions={isExternalPlanType ? undefined : loadDiagnosisOptions}
                          isAsync={!isExternalPlanType}
                          creatable={false}
                          noPadding
                          readOnly={readOnly}
                          isRequired
                          overrideIsRequired
                          additional={{ dropdownType: listType[`${idx}`] || 'ICD-10' }}
                          onFocus={
                            isExternalPlanType
                              ? () => {
                                  toggleFetchAllTypes(true);
                                }
                              : undefined
                          }
                          topRightComponent={
                            <DropdownCheckBox
                              value={listType[`${idx}`] || 'ICD-10'}
                              onChange={
                                isExternalPlanType
                                  ? undefined
                                  : (value) => {
                                      setListType({
                                        ...listType,
                                        [idx]: value,
                                      });
                                    }
                              }
                            />
                          }
                          cacheUniqs={[listType[`${idx}`] || 'ICD-10']}
                          fieldName={`${FORM_TAG}_clinical-diagnosis`}
                        />
                      )}
                    </SelectWrapper>
                    <SelectWrapper fullWidth>
                      {!readOnly && !inputs.isExternalPlanType && (
                        <MultiGroupAction
                          items={authDiagnosis}
                          index={idx}
                          initialItem={{
                            diagnosisICD10: null,
                            diagnosisICD11: null,
                            diagnosisSNOMED: null,
                          }}
                          onClick={(value) => {
                            handleInputChange('diagnosis', value);
                          }}
                          disableAdd={
                            (isLashmaEnrollee && authDiagnosis.length > 2) || isAnnualScreening
                          }
                          buttonAddText="Add More Diagnosis"
                        />
                      )}
                    </SelectWrapper>
                  </Fragment>
                );
              })}

              <InputRow>
                <SelectWrapper fullWidth noPadding>
                  <TextArea
                    name="referralRemarks"
                    label="Brief History and Treatment Given So Far"
                    fullWidth
                    onChange={({ target: { value } }) =>
                      handleInputChange('referralProviderRemark', value)
                    }
                    value={inputs.referralProviderRemark}
                    readOnly={readOnly}
                    fieldId={inputs?.id}
                    createdBy={inputs?.creatorId}
                    isRequired
                    overrideIsRequired
                    fieldName={`${FORM_TAG}_brief-history-treatment`}
                  />
                </SelectWrapper>
              </InputRow>

              {!isClinifyHmoWithoutLashma ? (
                <InputRow>
                  {inputs?.utilizations.map((utilisation, idx) => (
                    <Fragment key={idx}>
                      <Utilisations
                        visitationType={inputs.serviceTypeCode}
                        loadVisitationTypes={loadVisitationTypes}
                        utilisation={utilisation}
                        handleInputChange={(name, value) => {
                          setFlaggedItems([]);
                          handleMultipleFieldsChange('utilizations', name, value, idx);
                        }}
                        getTariff={getTariff}
                        index={idx}
                        loadingPrice={loading}
                        unitPriceText={unitPriceText}
                        readOnly={readOnly || !inputs.providerId}
                        setItems={(value) => setListInputs('utilizations', value, idx)}
                        isEdit={isEdit}
                        providerId={inputs.providerId}
                        getProviderId={getProviderId}
                        enrolleeId={inputs.enrolleeId || inputs.enrolleeNumber}
                        treatmentDate={inputs.requestDateTime}
                        shouldShowPrice={shouldShowPrice}
                        isClinifyHmo={isClinifyHmo}
                        facilityId={inputs.referredProviderId || inputs.referredProvider?.id}
                        externalPlanTypeId={inputs.externalPlanTypeId}
                        setInputs={setInputs}
                        isExternalPlanType={inputs.isExternalPlanType}
                        providerName={provider?.label || ''}
                        providerType={providerType}
                        bloodProfile={bloodProfile}
                        setBloodProfile={setBloodProfile}
                        referredProvider={inputs?.referredProvider}
                        flaggedItems={flaggedItems}
                      />
                      {(!isEdit || (isEdit && startEdit && isClinifyHmo)) &&
                        !isExternalPlanType &&
                        !isAnnualScreening && (
                          <MultiGroupAction
                            items={inputs.utilizations}
                            index={idx}
                            initialItem={utilisationInitialState}
                            onClick={(value) => {
                              handleInputChange('utilizations', value);
                            }}
                            buttonAddText="Add More Services"
                          />
                        )}
                    </Fragment>
                  ))}
                </InputRow>
              ) : null}
              <InputRow>
                <TextInput
                  name="totalQuantity"
                  title="Total Quantity"
                  value={inputs.totalQuantity}
                  readOnly
                />
                <FlaggedAuthenticationInput
                  {...defaultInputProps}
                  name="grandTotal"
                  label="Grand Total"
                  value={
                    inputs.grandTotal
                      ? `${withComma(shouldShowPrice ? inputs.grandTotal : 0)}`
                      : undefined
                  }
                  readOnly
                />
              </InputRow>
              <InputRow>
                <TextInput
                  name="doctor"
                  title="Requested By"
                  readOnly={readOnly}
                  onChange={({ target: { value } }) => handleInputChange('requestedBy', value)}
                  value={inputs.requestedBy}
                  placeholder="Enter Doctor's Name"
                  skipTranslate
                  fieldName={`${FORM_TAG}_requested-by`}
                />
                {isLashma ? null : (
                  <>
                    <SelectWrapper>
                      <Dropdown
                        creatable
                        title="Specialty"
                        options={SPECIALTY_OPTIONS}
                        onChange={() => {}}
                        placeholder="Select One"
                        value={inputs.specialty || null}
                        readOnly
                        noPadding
                        fieldName={`${FORM_TAG}_specialty`}
                      />
                    </SelectWrapper>
                    <SelectWrapper>
                      <Dropdown
                        title="Rank"
                        options={CLASS_CONSULTATION_OPTIONS}
                        onChange={() => {}}
                        placeholder="Select One"
                        value={inputs.rank || null}
                        readOnly
                        creatable
                        noPadding
                        fieldName={`${FORM_TAG}_rank`}
                      />
                    </SelectWrapper>
                  </>
                )}

                <SelectWrapper>
                  <Dropdown
                    title="Department"
                    options={HOSPITAL_UNIT_OPTIONS}
                    placeholder="Select One"
                    value={inputs?.department || null}
                    onChange={() => {}}
                    readOnly
                    creatable
                    noPadding
                    fieldName={`${FORM_TAG}_department`}
                  />
                </SelectWrapper>
              </InputRow>
              <TextInput
                fullWidth
                name="facilityName"
                title="Facility Name"
                onChange={() => {}}
                readOnly
                value={inputs.facilityName}
                placeholder="Enter Facility Name"
                skipTranslate
              />
              <AddressInput
                label="Facility Address"
                fieldPath="facilityAddress"
                handleInputChange={() => {}}
                readOnly
                value={inputs.facilityAddress}
                placeholder="Enter Facility Address"
                fieldName={`${FORM_TAG}_facility-address`}
              />
              <SelectWrapper fullWidth noPadding>
                <TextArea
                  name="additionalNote"
                  label="Additional Note"
                  fullWidth
                  onChange={({ target: { value } }) => handleInputChange('additionalNote', value)}
                  value={inputs.additionalNote}
                  readOnly={readOnly}
                  fieldId={inputs?.id}
                  createdBy={inputs?.creatorId}
                  fieldName={`${FORM_TAG}_additional-note`}
                />
              </SelectWrapper>
              <InputRow>
                <DocumentAttacher
                  documents={inputs.documentUrl}
                  handleInputChange={handleInputChange}
                  readOnly={readOnly}
                  type="preauthorization"
                />
              </InputRow>
              {isClinifyHmoWithoutLashma && orgHmoId ? (
                <ReferralActionsWithAuditDetails
                  isEdit={isEdit}
                  isEditActive={startEdit}
                  toggle={toggle}
                  cancelAction={() => setStartEdit(false)}
                  disableActionButton={
                    disableActionButton ||
                    (isEdit &&
                      isClinifyHmoWithoutLashma &&
                      (hmoId ? inputs?.providerId !== hmoId : !canMutate)) ||
                    (!isClinifyHmoWithoutLashma &&
                      !fieldsAreValid('authutilizationsfields', inputs.utilizations, true))
                  }
                  disableDeleteButton={!startEdit}
                  onAction={action}
                  actionText={actionText}
                  inputs={inputs}
                  utilizationStatusHistory={utilizationsRef.current?.[0]?.statusHistory}
                />
              ) : (
                <ActionSectionSectionWithAuditDetails
                  isEdit={isEdit}
                  isEditActive={startEdit}
                  toggle={toggle}
                  cancelAction={() => setStartEdit(false)}
                  disableActionButton={
                    disableActionButton ||
                    (isEdit && !startEdit ? false : isClinifyHmo && !diagnosisIsValid) ||
                    (isEdit &&
                      isClinifyHmo &&
                      (hmoId ? inputs?.providerId !== hmoId : !canMutate)) ||
                    (!isClinifyHmoWithoutLashma &&
                      !fieldsAreValid('authutilizationsfields', inputs.utilizations, true))
                  }
                  disableDeleteButton={!startEdit}
                  onAction={action}
                  actionText={actionText}
                  inputs={inputs}
                />
              )}

              {isClinifyHmo && orgHmoId && isEdit ? (
                <>
                  <StatusButtons
                    status={[['Rejected'], ['Approved']]}
                    onChange={(status) => {
                      processReferralStatus(utilizationsRef.current?.[0]?.paCode, status);
                    }}
                    currentStatus={utilizationsRef.current?.[0]?.status}
                    loading={processingReferralStatus}
                    buttonClassName="full-width"
                  />
                </>
              ) : (
                <></>
              )}
            </>
          ) : null}
        </FormContent>

        <Modal
          modalContent={
            <Prompter
              text="Are you sure you want to delete this record?"
              actionText="Delete"
              deleteAction={() => {}}
              cancelAction={() => {}}
              disabled
            />
          }
          isShown={false}
          hide={() => {}}
          handleDone={() => {}}
          isAuthentication
        />
      </RecordForm>
    </>
  );
}
