import React, { useMemo } from 'react';
import { Tooltip as ReactTooltip } from 'react-tooltip';
import { AgreedTariffVariables } from 'graphql-types/AgreedTariff';
import { PreauthUtilisations } from 'graphql-types/PreauthUtilisations';
import { PaCodeStatus } from 'graphql-types/globalTypes';
import TextArea from 'app/shared/components/inputs/TextArea';
import Dropdown from 'app/shared/components/dropdown/Dropdown';
import { FlaggedAuthenticationInput } from 'app/shared/components/inputs/AuthenticationInput';
import TextInput from 'app/shared/components/inputs/TextInput';
import { InputRow, SelectWrapper } from 'app/shared/components/wrappers/Wrapper';
import {
  BIRTH_COUNT_OPTIONS,
  DOSAGE_OPTIONS,
  HMO_MEDICATION_FREQUENCY,
  LASHMA_HMO_DOSAGE_OPTIONS,
  UTILIZATION_SPECIALTY_LIST_2,
} from '@constant/options';
import SingleDurationInput from 'app/shared/components/inputs/SingleDurationInput';
import { LASHMA_AGENCY, LASHMA_AGENCY_2 } from 'app/shared/utils/is-clinify-hmo';
import DatePicker from 'app/shared/components/pickers/DatePicker';
import usePatientData from 'app/shared/hooks/client/usePatientData';
import cloneDeep from 'lodash.clonedeep';
import { FlaggedRuleItem } from 'app/shared/types/business-rule';
import { hasRuleCategory } from 'app/shared/utils/common';
import MultiDurationInput from 'app/shared/components/inputs/MultipleDurationInput';
import { OtpInputIcon } from '../../../../../assets';
import { defaultInputProps, withComma } from '../billings/constants';
import useRequestPreauthorizationAPILoader from '../request-preauthorizations/hooks/useRequestPreauthorizationAPILoader';
import useMappedUtilizationToDiagnosis from '../request-preauthorizations/hooks/useMappedUtilizationToDiagnosis';
import utilizationDiagnosisMap from '../request-preauthorizations/utilization-diagnosis.map.json';
import useFilterUtilizationOptions from '../request-preauthorizations/hooks/useFilterUtilizationOptions';
import {
  calculateMedicationQuantity,
  SINGLE_COUNT_UTILIZATIONS,
} from '../request-preauthorizations/constants';
import { formatUtilizationPaymentModelText } from './constants';

interface UtilisationsProp {
  utilisation: PreauthUtilisations;
  handleInputChange: (field: string, value: string) => void;
  getTariff: (input: AgreedTariffVariables['input']) => void;
  setItems: (input: Record<any, any>) => void;
  index: number;
  unitPriceText: string;
  readOnly?: boolean;
  loadingPrice?: boolean;
  handleGetDetail: () => void;
  loadingDetails?: boolean;
  isEdit?: boolean;
  providerId: string;
  visitationType: any;
  allowPaCodeEdit?: boolean;
  enrolleeId: string;
  treatmentDate: string;
  isClinifyHmo?: boolean;
  facilityId?: string;
  providerName: string;
  externalPlanTypeId: string | null;
  setInputs: React.Dispatch<any>;
  isExternalPlanType: boolean;
  flaggedItems?: FlaggedRuleItem[];
}

const AddUtilisations: React.FC<UtilisationsProp> = ({
  utilisation,
  handleInputChange,
  getTariff,
  index,
  unitPriceText,
  readOnly,
  setItems,
  loadingPrice,
  handleGetDetail,
  loadingDetails,
  isEdit,
  providerId,
  visitationType,
  allowPaCodeEdit = false,
  enrolleeId,
  treatmentDate,
  isClinifyHmo,
  facilityId,
  providerName,
  externalPlanTypeId,
  setInputs,
  isExternalPlanType,
  flaggedItems = [],
}) => {
  const { isLashmaEnrollee } = usePatientData();

  const {
    rejectedOptions,
    acceptedUtilizationTypeOptions,
    loadAllUtilizationTypes,
    toggleFetchAllTypes,
    fetchAllTypes,
    LABORATORY,
    RADIOLOGY,
    DRUG,
    utilizationResetKey,
    rejectedUtilizationTypeOptions,
  } = useFilterUtilizationOptions(
    'CLAIMS',
    providerName,
    providerId,
    visitationType,
    utilisation?.category || '',
  );

  const { loadVisitationTypes } = useRequestPreauthorizationAPILoader();

  const { onUtilizationTypeChange } = useMappedUtilizationToDiagnosis({
    setInputs,
    diagnosisFieldName: 'diagnosis',
    utilizationFieldName: 'utilizations',
    map: utilizationDiagnosisMap,
    utilIdx: index,
    loadAllUtilizationTypes,
    enrolleeId,
    providerId,
    isExternalPlanType,
  });

  const handleQuantityUpdate = (field: string, value: string | Record<string, any>) => {
    const resetQuantityToOne = SINGLE_COUNT_UTILIZATIONS.includes(utilisation.type || '');
    const _update = calculateMedicationQuantity(field, value, {
      dosage: utilisation.dosage,
      duration: utilisation.duration,
      frequency: utilisation.frequency,
    });
    if (resetQuantityToOne && isLashmaEnrollee) {
      _update.quantity = '1';
    }
    setInputs((_prev) => {
      const tempUtilizations = cloneDeep(_prev.utilizations || []);
      tempUtilizations[index] = {
        ...tempUtilizations[index],
        ..._update,
      };
      return { ..._prev, utilizations: tempUtilizations };
    });
  };

  const showDrugFields = ['drug', 'medicine', 'injection'].some(
    (keyword) =>
      `${utilisation.category}${utilisation.type}`.toLowerCase().includes(keyword) &&
      !utilisation.category?.toLowerCase()?.includes('fees') &&
      !utilisation.category?.toLowerCase()?.includes('admission'),
  );
  const showDeliveryFields = ['delivery', 'caesarean'].some((keyword) =>
    `${utilisation.category}${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const showSpecialty = ['consult'].some((keyword) =>
    `${utilisation.category}${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isToothExtraction = ['tooth', 'extraction'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isBloodTransfusion = ['blood', 'transfusion'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isLimbXray = ['limb', 'xray'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isDentalRadiograph = ['dental', 'radiograph'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isCrossMatching = ['cross', 'matching'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isWoundDressing = ['wound', 'dressing'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isInjectionService = utilisation.category?.toLowerCase()?.includes('injection');
  const isInfusionService = utilisation.category?.toLowerCase()?.includes('infusion');

  const disableDispensedMedication =
    [LASHMA_AGENCY, LASHMA_AGENCY_2].includes(providerName) &&
    (utilisation.serviceName || '')?.endsWith('- Medication');

  const rejectionReasons =
    utilisation?.rejectionReason?.length && Array.isArray(utilisation.rejectionReason)
      ? utilisation.rejectionReason
      : [''];

  const medicationIsPack = useMemo(() => {
    if (!showDrugFields || !utilisation.type) return false;
    return SINGLE_COUNT_UTILIZATIONS.includes(utilisation.type);
  }, [showDrugFields, utilisation.type]);

  const disableQuantity =
    isLashmaEnrollee &&
    !(
      (showDrugFields && !medicationIsPack) ||
      isInjectionService ||
      isInfusionService ||
      isToothExtraction ||
      isBloodTransfusion ||
      isLimbXray ||
      isDentalRadiograph ||
      isCrossMatching ||
      isWoundDressing
    );

  const HMO_DOSAGE_OPTIONS = isLashmaEnrollee ? LASHMA_HMO_DOSAGE_OPTIONS : DOSAGE_OPTIONS;
  const categoryFlaggedItems = useMemo(() => {
    return (
      utilisation.utilizationCode &&
      hasRuleCategory(utilisation.utilizationCode, 'utilizationCategory', flaggedItems)
    );
  }, [flaggedItems, utilisation.category]);

  const typeFlaggedItems = useMemo(() => {
    return (
      utilisation.utilizationCode &&
      hasRuleCategory(utilisation.utilizationCode, 'utilizationType', flaggedItems)
    );
  }, [flaggedItems, utilisation.type]);

  const quantityFlaggedItems = useMemo(() => {
    return (
      utilisation.utilizationCode &&
      hasRuleCategory(utilisation.utilizationCode, 'quantity', flaggedItems)
    );
  }, [flaggedItems, utilisation.quantity]);

  return (
    <>
      <SelectWrapper fullWidth>
        {fetchAllTypes ? (
          <Dropdown
            title="Utilization Type"
            creatable={false}
            onChange={({ label, code, hmoPlanBenefitId, utilizationCategory }) => {
              handleInputChange('type', code);
              setItems({
                type: label,
                utilizationCode: code,
                price: '0',
                category: utilizationCategory,
                utilizationId: hmoPlanBenefitId,
              });
              getTariff({
                providerId,
                position: index,
                id: code,
                enrolleeId,
                treatmentDate,
                visitTypeId: visitationType,
                facilityId,
                externalPlanId: externalPlanTypeId,
              });
            }}
            onValueChange={(selection) => {
              const label = selection?.label;
              onUtilizationTypeChange(label);
            }}
            placeholder="Select One"
            isRequired
            value={utilisation.type || null}
            readOnly={readOnly || disableDispensedMedication}
            isAsync
            loadOptions={loadAllUtilizationTypes}
            noPadding
            cacheUniqs={[
              `${utilisation?.utilizationId}-${externalPlanTypeId}-${utilizationResetKey}`,
            ]}
            criticalInfo={utilisation.serviceName}
            withError={typeFlaggedItems}
          />
        ) : (
          <Dropdown
            title="Utilization Type"
            creatable={false}
            options={[]}
            onChange={({ value, label }) => {
              handleInputChange('type', value);
              setItems({
                type: label,
                utilizationCode: value,
                price: '0',
              });

              getTariff({
                providerId,
                position: index,
                id: value,
                utilizationId: utilisation.utilizationId,
                enrolleeId,
                treatmentDate,
                visitTypeId: visitationType,
                facilityId,
                externalPlanId: externalPlanTypeId,
              });
            }}
            onValueChange={(selection) => {
              const label = selection?.label;
              onUtilizationTypeChange(label);
            }}
            criticalInfo={utilisation.serviceName}
            placeholder="Select One"
            isRequired
            value={utilisation.type || null}
            readOnly={readOnly || disableDispensedMedication}
            isAsync
            loadOptions={loadVisitationTypes}
            additional={{
              utilizationId: utilisation?.utilizationId,
              action: 'utilizationProcedure',
              providerId,
              visitationType,
              hospitalId: facilityId || '',
              externalPlanTypeId,
              acceptedOptions: [LABORATORY, RADIOLOGY].includes(utilisation.category || '')
                ? acceptedUtilizationTypeOptions
                : [],
              rejectedOptions: [DRUG].includes(utilisation.category || '')
                ? rejectedUtilizationTypeOptions
                : [],
            }}
            noPadding
            cacheUniqs={[
              `${utilisation?.utilizationId}-${externalPlanTypeId}-${utilizationResetKey}`,
            ]}
            onFocus={() => {
              if (!utilisation.category) {
                toggleFetchAllTypes(true);
              }
            }}
            withError={typeFlaggedItems}
          />
        )}
      </SelectWrapper>
      <SelectWrapper>
        <Dropdown
          creatable={false}
          title="Utilization Category"
          options={[]}
          onChange={({ value, label }) => {
            setItems({
              category: label,
              utilizationId: value,
              utilizationCode: null,
              type: null,
              quantity: '1',
              price: '0',
            });
          }}
          placeholder="Select One"
          isRequired
          value={utilisation.category || null}
          readOnly={readOnly || disableDispensedMedication}
          isAsync
          loadOptions={loadVisitationTypes}
          additional={{
            action: 'utilizationCategory',
            providerId,
            visitationType,
            hospitalId: facilityId || '',
            rejectedOptions,
          }}
          noPadding
          cacheUniqs={[visitationType]}
          withError={categoryFlaggedItems}
        />
      </SelectWrapper>
      <TextInput
        name="utilizationCode"
        title="Utilization Code"
        onChange={({ target: { value } }) => {
          const valueToUse = (value || '').replaceAll(' ', '');
          setItems({
            utilizationCode: valueToUse,
            price: '0',
            type: null,
          });
          getTariff({
            providerId,
            position: index,
            id: valueToUse,
            utilizationId: isClinifyHmo ? undefined : utilisation.utilizationId,
            enrolleeId,
            treatmentDate,
            visitTypeId: visitationType,
            facilityId,
            externalPlanId: externalPlanTypeId,
          });
        }}
        value={utilisation.utilizationCode}
        placeholder="Enter Utilization Code"
        readOnly
      />
      {showDrugFields ? (
        <>
          <SelectWrapper noPadding>
            <TextInput
              name="dosage"
              title="Dosage"
              placeholder="Enter Dosage"
              onChange={({ target: { value } }) => {
                if (Number(value) > 10) return;
                handleQuantityUpdate('dosage', value);
              }}
              value={utilisation.dosage}
              readOnly={readOnly}
              fullWidth
              withTag
              equalTagWith
              isRequired
              type="number"
              tag={
                <Dropdown
                  options={HMO_DOSAGE_OPTIONS}
                  value={utilisation.dosageUnit || null}
                  onChange={({ value }) => handleInputChange('dosageUnit', value)}
                  placeholder="Select One"
                  readOnly={readOnly || isLashmaEnrollee}
                  withTag
                  noPadding
                  creatable
                />
              }
            />
          </SelectWrapper>
          <SelectWrapper padded>
            <SingleDurationInput
              title="Duration (Month:Week:Day)"
              onChange={(duration) => handleQuantityUpdate('duration', duration)}
              readOnly={readOnly}
              value={utilisation.duration}
              isRequired
            />
          </SelectWrapper>
          <SelectWrapper>
            <Dropdown
              noPadding
              title="Frequency"
              options={HMO_MEDICATION_FREQUENCY}
              onChange={(selection) => handleQuantityUpdate('frequency', selection)}
              readOnly={readOnly}
              value={utilisation.frequency || null}
              placeholder="Enter Frequency"
              creatable
              isRequired
            />
          </SelectWrapper>
          <TextInput
            name="category"
            title="Medication Category"
            readOnly={readOnly || isLashmaEnrollee}
            onChange={({ target: { value } }) => handleInputChange('medicationCategory', value)}
            value={utilisation.medicationCategory}
            placeholder="Enter Medication Category"
            isRequired
          />
        </>
      ) : null}
      <TextInput
        name="quantity"
        title="Quantity"
        onChange={({ target: { value } }) => handleInputChange('quantity', value)}
        value={utilisation.quantity}
        placeholder="Enter Quantity"
        readOnly={readOnly || isExternalPlanType || disableQuantity}
        type="number"
        isRequired
        withError={quantityFlaggedItems}
      />
      <FlaggedAuthenticationInput
        {...defaultInputProps}
        label={`Unit Price${
          utilisation?.paymentModel
            ? ` - ${formatUtilizationPaymentModelText(utilisation.paymentModel)}`
            : ''
        }`}
        name="unitPrice"
        onChange={({ target: { value } }) => handleInputChange('price', String(value))}
        value={utilisation.price ? `${withComma(utilisation.price)}` : undefined}
        placeholder={unitPriceText}
        readOnly
        loading={loadingPrice}
      />

      {showSpecialty ? (
        <InputRow>
          <SelectWrapper>
            <Dropdown
              noPadding
              title="Specialty"
              options={UTILIZATION_SPECIALTY_LIST_2}
              onChange={({ value }) => handleInputChange('specialty', value)}
              readOnly={readOnly}
              value={utilisation.specialty || null}
              isRequired
            />
          </SelectWrapper>
        </InputRow>
      ) : null}
      {showDeliveryFields ? (
        <InputRow>
          <SelectWrapper>
            <Dropdown
              noPadding
              title="Birth Count"
              options={BIRTH_COUNT_OPTIONS}
              onChange={({ value }) => handleInputChange('birthCount', value)}
              readOnly={readOnly}
              value={utilisation.birthCount || null}
              isRequired
            />
          </SelectWrapper>
          <SelectWrapper noPadding>
            <DatePicker
              label="Delivery Date and Time"
              withBorderRadius
              onChange={(date) => handleInputChange('deliveryDateTime', date)}
              value={utilisation.deliveryDateTime}
              placeholderText="Select Date and Time"
              readOnly={readOnly}
              minDate={new Date(new Date().getFullYear() - 100, new Date().getMonth())}
              maxDate={new Date()}
              disabled={readOnly}
              isRequired
            />
          </SelectWrapper>
          <SelectWrapper padded>
            <MultiDurationInput
              title="Gestational Age (Month:Week:Day)"
              onChange={(duration) => handleInputChange('gestationalAge', duration)}
              value={utilisation?.gestationalAge}
              readOnly={readOnly}
              disableBox={[2]}
              minValue={{ 0: 0, 1: 1 }}
              maxValue={{ 0: 30, 1: 42 }}
              isRequired
            />
          </SelectWrapper>
        </InputRow>
      ) : null}

      <FlaggedAuthenticationInput
        {...defaultInputProps}
        label="Total Price"
        name="totalPrice"
        onChange={() => {}}
        value={
          utilisation.price
            ? `${withComma(Number(utilisation.price) * Number(utilisation.quantity))}`
            : undefined
        }
        placeholder={unitPriceText}
        readOnly
      />
      <TextInput
        readOnly={readOnly || isClinifyHmo}
        name="PACode"
        value={utilisation.paCode}
        title={`PA Code - ${utilisation.status || 'Processing'}`}
        onChange={({ target: { value } }) => allowPaCodeEdit && handleInputChange('paCode', value)}
        placeholder="Enter PA Code"
        withIcon
        iconWithBrownLeftBorder
        loading={loadingDetails}
        skipTranslate
        icon={
          <div
            data-tooltip-content="Click to get PA Code"
            data-tooltip-id="addUtilisationsToolTip"
            className="otp-button"
            onClick={isEdit ? handleGetDetail : undefined}
            data-tooltip-hidden={!isEdit}
          >
            <OtpInputIcon />
          </div>
        }
      />
      {utilisation.status === PaCodeStatus.Rejected && (
        <InputRow>
          {[LASHMA_AGENCY, LASHMA_AGENCY_2].includes(providerName) ? (
            <>
              {rejectionReasons.map((_rejectionReason, _idx) => (
                <InputRow key={_idx}>
                  <SelectWrapper fullWidth>
                    <Dropdown
                      title="Rejection Reason"
                      options={[{ value: 'Others', label: 'Others' }]}
                      placeholder="Select One"
                      value={_rejectionReason || null}
                      onChange={() => {}}
                      readOnly
                      creatable
                      noPadding
                    />
                  </SelectWrapper>
                  {_rejectionReason === 'Others' && (
                    <TextArea
                      name="reasonForRejection"
                      label="Specify Rejection Reason"
                      fullWidth
                      onChange={() => {}}
                      value={utilisation?.specifyReasonForRejection}
                      readOnly
                    />
                  )}
                </InputRow>
              ))}
            </>
          ) : (
            <TextArea
              name="reasonForRejection"
              label="Reason for Rejection"
              fullWidth
              onChange={() => {}}
              value={utilisation.statusDescription}
              readOnly
            />
          )}
        </InputRow>
      )}
      <ReactTooltip id="addUtilisationsToolTip" place="top" className="otpTip" variant="light" />
    </>
  );
};

export default AddUtilisations;
