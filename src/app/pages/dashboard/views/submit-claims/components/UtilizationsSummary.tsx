import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import classNames from 'classnames';
import DeleteIcon from '@material-ui/icons/Delete';
import InfoIcon from '@material-ui/icons/Info';
import EditIcon from '@material-ui/icons/Edit';
import SaveIcon from '@material-ui/icons/Save';
import AddIcon from '@material-ui/icons/Add';
import { Popover } from '@mui/material';
import cloneDeep from 'lodash.clonedeep';
import {
  HMO_MEDICATION_FREQUENCY,
  LASHMA_HMO_DOSAGE_OPTIONS,
  DOSAGE_OPTIONS,
} from '@constant/options';
import PlainTable, { PlainTableColumns } from 'app/shared/components/table/PlainTable';
import { Modal } from 'app/shared/components/modal/Modal';
import Prompter from 'app/shared/components/prompter/Prompter';
import { formatTableDateTime } from 'app/shared/utils/formatTable';
import utilizationDiagnosisMap from 'app/pages/dashboard/views/request-preauthorizations/utilization-diagnosis.map.json';
import { FlaggedRuleItem } from 'app/shared/types/business-rule';
import { listAllDiagnosis } from '../../pre-authorisations/helpers/list-all-diagnosis';
import { convertToCurrencyString, convertToNumber } from '../../invoice/utils';
import { ApproveIcon, RejectIcon } from '../../../../../../assets';
import { useModal } from '../../../../../shared/hooks/ui/useModal';
import useMappedUtilizationToDiagnosis from '../../request-preauthorizations/hooks/useMappedUtilizationToDiagnosis';
import Dropdown from '../../../../../shared/components/dropdown/Dropdown';
import TextInput, { BareTextInput } from '../../../../../shared/components/inputs/TextInput';
import SingleDurationInput from '../../../../../shared/components/inputs/SingleDurationInput';
import useFilterUtilizationOptions from '../../request-preauthorizations/hooks/useFilterUtilizationOptions';
import useRequestPreauthorizationAPILoader from '../../request-preauthorizations/hooks/useRequestPreauthorizationAPILoader';
import usePatientData from '../../../../../shared/hooks/client/usePatientData';
import {
  calculateMedicationQuantity,
  SINGLE_COUNT_UTILIZATIONS,
} from '../../request-preauthorizations/constants';
import { SelectWrapper } from '../../../../../shared/components/wrappers/Wrapper';
import { hasRuleCategory } from '../../../../../shared/utils/common';

type ProviderClaimSummaryTableProps = {
  className?: string;
  data: Record<string, any>;
  type: 'HmoClaim' | 'Preauthorization' | 'Referral';
  setInputs: React.Dispatch<React.SetStateAction<any>>;
  setItems: any;
  getTariff: any;
  action: any;
  disableEdit: boolean;
  isEdit: boolean;
  onAddNewItem?: () => void;
  readOnly: boolean;
  showPrice?: boolean;
  providerName: string;
  flaggedItems: FlaggedRuleItem[];
  utilizationOnlyServices?: null | string[];
};

function getColumns(
  toggleModal,
  utilIdxRef: React.MutableRefObject<number | null>,
  length?: number,
  readOnly?: boolean,
  handleUtilizationTypeChange?: any,
  setInputs?: any,
  onUtilizationCategoryChange?: any,
  visitationType?: any,
  providerId?: string,
  facilityId?: string,
  acceptedOptions?: any[],
  rejectedOptions?: any[],
  providerName?: string,
  enrolleeId?: string,
  isExternalPlanType?: boolean,
  externalPlanTypeId?: string,
  setItems?: any,
  isLashmaEnrollee?: boolean,
  recordType?: string,
  showPrice?: boolean,
  flaggedItems: FlaggedRuleItem[] = [],
  utilizationOnlyServices: null | string[] = null,
): PlainTableColumns {
  const showPACodeOnlyIfApproved = ['Referral'].includes(recordType as string);
  const showPACodeOnlyIfApprovedOrRejected = ['Preauthorization'].includes(recordType as string);
  return [
    {
      key: 'category',
      title: 'Utilization Category',
      CustomElement: ({ data, row }) => (
        <UtilizationCategoryCell
          value={data}
          readOnly={readOnly}
          index={row._idx}
          onUtilizationCategoryChange={(arg0) => onUtilizationCategoryChange(arg0, row._idx)}
          visitationType={visitationType}
          providerId={providerId}
          facilityId={facilityId}
          acceptedOptions={acceptedOptions}
          rejectedOptions={rejectedOptions}
          utilizationStatus={row.status}
          flaggedItems={flaggedItems}
          utilizationCode={row.utilizationCode}
        />
      ),
    },
    {
      key: 'type',
      title: 'Utilization Type',
      CustomElement: ({ data, row }) => (
        <UtilizationTypeCell
          value={data}
          readOnly={readOnly}
          index={row._idx}
          handleUtilizationTypeChange={handleUtilizationTypeChange}
          setInputs={setInputs}
          utilizationCategory={row.category}
          visitationType={visitationType}
          providerId={providerId}
          providerName={providerName}
          enrolleeId={enrolleeId}
          isExternalPlanType={isExternalPlanType}
          utilizationId={row.utilizationId}
          externalPlanTypeId={externalPlanTypeId}
          recordType={recordType}
          utilizationStatus={row.status}
          utilization={row}
          flaggedItems={flaggedItems}
          utilizationOnlyServices={utilizationOnlyServices}
        />
      ),
    },
    {
      key: 'quantity',
      title: 'Qty',
      showSum: true,
      CustomElement({ data, row }) {
        return (
          <UtilizationQuantityCell
            value={data}
            readOnly={readOnly}
            index={row._idx}
            isExternalPlanType={isExternalPlanType}
            isLashmaEnrollee={isLashmaEnrollee}
            utilizationCategory={row.category}
            utilizationType={row.type}
            utilizationStatus={row.status}
            setInputs={setInputs}
            utilizationCode={row.utilizationCode}
            flaggedItems={flaggedItems}
          />
        );
      },
    },
    {
      key: 'price',
      title: 'Unit Price',
      showSum: true,
      CustomElement({ data }) {
        return <span>{`₦${convertToCurrencyString(showPrice ? data : 0)}`}</span>;
      },
      sumFormatter: (data: any) => `₦${convertToCurrencyString(showPrice ? data : 0)}`,
    },
    {
      key: 'totalPrice',
      title: 'Total Price',
      CustomElement({ row }) {
        if (!showPrice) return <span>{`₦${convertToCurrencyString(0)}`}</span>;
        return (
          <span>
            {`₦${
              row.percentageCovered || row.amountCovered
                ? row.amountCovered
                  ? convertToCurrencyString(row.amountCovered)
                  : convertToCurrencyString(
                      (row.percentageCovered / 100) *
                        (convertToNumber(row.price) * convertToNumber(row.quantity)),
                    )
                : convertToCurrencyString(
                    convertToNumber(row.price) * convertToNumber(row.quantity),
                  )
            }`}
          </span>
        );
      },
      customValue(row) {
        return convertToNumber(showPrice ? row.price : 0) * convertToNumber(row.quantity);
      },
      showSum: true,
      sumFormatter: (data: any) => `₦${convertToCurrencyString(showPrice ? data : 0)}`,
    },
    {
      key: 'status',
      title: 'Status',
      CustomElement({ data }) {
        return (
          <>
            <div className={classNames({ hide: data !== 'Approved' })}>
              <ApproveIcon style={{ display: 'block', margin: '0 auto' }} />
            </div>
            <div className={classNames({ hide: data !== 'Rejected' })}>
              <RejectIcon style={{ display: 'block', margin: '0 auto' }} />
            </div>
          </>
        );
      },
    },
    {
      key: '_idx',
      title: 'Remove',
      CustomElement({ data, row }) {
        const disableDelete = readOnly || (row.status && row.status !== 'Pending') || length === 1;
        return (
          <DeleteIcon
            className="cursor-pointer"
            onClick={
              disableDelete
                ? undefined
                : () => {
                    utilIdxRef.current = data;
                    toggleModal();
                  }
            }
            color={disableDelete ? 'disabled' : undefined}
          />
        );
      },
    },
    {
      key: 'paCode',
      title: recordType === 'Referral' ? 'Referral Code' : 'PA Code',
      CustomElement: ({ data, row }) => {
        const paCode = () => {
          if (showPACodeOnlyIfApproved) {
            return row.status === 'Approved' ? data : '';
          }
          if (showPACodeOnlyIfApprovedOrRejected) {
            return row.status === 'Approved' || row.status === 'Rejected' ? data : '';
          }
          return data;
        };
        return <span>{paCode()}</span>;
      },
    },
  ];
}

interface DrugFieldsPopoverProps {
  utilizationDosage: any;
  utilizationDosageUnit: string;
  utilizationDuration: string;
  utilizationFrequency: string;
  utilizationMedicationCategory: string;
  utilizationQuantity: any;
  utilizationType: string;
  index: number;
  setInputs: React.Dispatch<React.SetStateAction<any>>;
  readOnly?: boolean;
  autoOpen?: boolean;
}

function DrugFieldsPopover({
  utilizationDosage,
  utilizationDosageUnit,
  utilizationDuration,
  utilizationFrequency,
  utilizationMedicationCategory,
  utilizationQuantity,
  utilizationType,
  index,
  setInputs,
  readOnly = false,
  autoOpen = false,
}: DrugFieldsPopoverProps) {
  const { isLashmaEnrollee } = usePatientData();
  const [popoverAnchor, setPopoverAnchor] = useState<HTMLElement | null>(null);
  const infoIconRef = useRef<HTMLDivElement>(null);
  const [localDosage, setLocalDosage] = useState(utilizationDosage);
  const [localDosageUnit, setLocalDosageUnit] = useState(utilizationDosageUnit);
  const [localDuration, setLocalDuration] = useState(utilizationDuration);
  const [localFrequency, setLocalFrequency] = useState(utilizationFrequency);
  const [localMedicationCategory, setLocalMedicationCategory] = useState(
    utilizationMedicationCategory,
  );
  const [localQuantity, setLocalQuantity] = useState(utilizationQuantity);

  const HMO_DOSAGE_OPTIONS = isLashmaEnrollee ? LASHMA_HMO_DOSAGE_OPTIONS : DOSAGE_OPTIONS;

  // Update local state when utilization changes
  useEffect(() => {
    setLocalDosageUnit(utilizationDosageUnit);
    setLocalMedicationCategory(utilizationMedicationCategory);
    if (popoverAnchor && !readOnly) return;
    setLocalDosage(utilizationDosage);
    setLocalDuration(utilizationDuration);
    setLocalFrequency(utilizationFrequency);
    setLocalQuantity(utilizationQuantity);
  }, [
    utilizationDosage,
    utilizationDosageUnit,
    utilizationDuration,
    utilizationFrequency,
    utilizationMedicationCategory,
    utilizationQuantity,
  ]);

  // Auto-open popover when autoOpen is true
  useEffect(() => {
    if (autoOpen && infoIconRef.current && !popoverAnchor) {
      setPopoverAnchor(infoIconRef.current);
    }
  }, [autoOpen, popoverAnchor, infoIconRef]);

  // Handle opening popover
  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const anchorElement = event.currentTarget;
    setPopoverAnchor(anchorElement);
  };

  // Handle closing popover
  const handleClosePopover = () => {
    setPopoverAnchor(null);
  };

  // Handle saving drug fields
  const handleSaveDrugFields = () => {
    setInputs((prev) => {
      const tempUtilizations = cloneDeep(prev.utilizations || []);
      tempUtilizations[index] = {
        ...tempUtilizations[index],
        dosage: localDosage,
        dosageUnit: localDosageUnit,
        duration: localDuration,
        frequency: localFrequency,
        medicationCategory: localMedicationCategory,
        quantity: localQuantity,
      };
      return { ...prev, utilizations: tempUtilizations };
    });
    handleClosePopover();
  };

  const handleQuantityUpdate = (field: string, value: string | Record<string, any>) => {
    const resetQuantityToOne = SINGLE_COUNT_UTILIZATIONS.includes(utilizationType || '');
    const _update = calculateMedicationQuantity(field, value, {
      dosage: localDosage,
      duration: localDuration,
      frequency: localFrequency,
    });
    if (resetQuantityToOne && isLashmaEnrollee) {
      _update.quantity = '1';
    }
    setLocalDosage(_update.dosage);
    setLocalDuration(_update.duration);
    setLocalFrequency(_update.frequency);
    setLocalQuantity(_update.quantity);
  };

  return (
    <>
      <div
        ref={infoIconRef}
        onClick={handleClick}
        className="cursor-pointer"
        style={{
          display: 'inline-block',
          marginLeft: '8px',
        }}
      >
        <InfoIcon fontSize="small" color="primary" />
      </div>

      <Popover
        open={Boolean(popoverAnchor)}
        anchorEl={popoverAnchor}
        onClose={handleSaveDrugFields}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        container={popoverAnchor?.ownerDocument?.body}
        disablePortal={false}
      >
        <div className="flex flex-column" style={{ width: '450px' }}>
          <SelectWrapper noPadding fullWidth>
            <TextInput
              name="dosage"
              title="Dosage"
              placeholder="Enter Dosage"
              onChange={({ target: { value } }) => {
                if (Number(value) > 10) return;
                handleQuantityUpdate('dosage', value);
              }}
              value={localDosage}
              readOnly={readOnly}
              fullWidth
              withTag
              equalTagWith
              isRequired
              type="number"
              tag={
                <Dropdown
                  options={HMO_DOSAGE_OPTIONS}
                  value={localDosageUnit || null}
                  onChange={({ value }) => setLocalDosageUnit(value)}
                  placeholder="Select One"
                  readOnly={readOnly || isLashmaEnrollee}
                  withTag
                  noPadding
                  creatable
                />
              }
            />
          </SelectWrapper>

          <SelectWrapper fullWidth padded>
            <SingleDurationInput
              title="Duration (Month:Week:Day)"
              onChange={(duration) => handleQuantityUpdate('duration', duration)}
              readOnly={readOnly}
              value={localDuration}
              isRequired
            />
          </SelectWrapper>

          <Dropdown
            noPadding
            title="Frequency"
            options={HMO_MEDICATION_FREQUENCY}
            onChange={(selection) => handleQuantityUpdate('frequency', selection)}
            readOnly={readOnly}
            value={localFrequency || null}
            placeholder="Enter Frequency"
            creatable={false}
            isRequired
          />

          <TextInput
            name="medicationCategory"
            title="Medication Category"
            readOnly={readOnly || isLashmaEnrollee}
            onChange={({ target: { value } }) => setLocalMedicationCategory(value)}
            value={localMedicationCategory}
            placeholder="Enter Medication Category"
            isRequired
            overrideIsRequired
            fullWidth
          />
        </div>
      </Popover>
    </>
  );
}

function UtilizationsSummaryTable({
  className,
  data,
  type,
  setInputs,
  setItems,
  getTariff,
  action,
  disableEdit,
  onAddNewItem,
  readOnly,
  showPrice = true,
  providerName,
  flaggedItems,
  utilizationOnlyServices,
}: ProviderClaimSummaryTableProps) {
  const { isShown: showModalPrompt, toggle } = useModal();
  const { isLashmaEnrollee } = usePatientData();
  const selectedUtilIdxRef = useRef<number | null>(null);
  const { handleUtilizationTypeChange, onUtilizationCategoryChange } = useUtilizationsSummaryTable({
    setInputs,
    setItems,
    getTariff,
    recordType: type,
  });

  // Get filter options for utilization categories
  const { acceptedOptions, rejectedOptions } = useFilterUtilizationOptions(
    type === 'Preauthorization' ? 'PREAUTH' : type === 'Referral' ? 'REFERRAL' : 'CLAIMS',
    providerName || '',
    data?.providerId || '',
    data?.serviceTypeCode || '',
    '',
  );

  const deleteUtilization = () => {
    const idx = selectedUtilIdxRef.current;
    setInputs((prev) => {
      const updatedData = cloneDeep(prev);
      updatedData.utilizations = updatedData.utilizations.filter((_, i) => i !== idx);
      toggle();
      selectedUtilIdxRef.current = null;
      return updatedData;
    });
  };

  const utilizations = useMemo(
    () =>
      data?.utilizations?.map((util, idx) => ({
        ...util,
        _idx: idx,
      })),
    [data?.utilizations],
  );

  const columns = useMemo(
    () =>
      getColumns(
        toggle,
        selectedUtilIdxRef,
        utilizations?.length,
        readOnly,
        handleUtilizationTypeChange,
        setInputs,
        onUtilizationCategoryChange,
        data?.serviceTypeCode,
        data?.providerId,
        data?.facilityId,
        acceptedOptions,
        rejectedOptions,
        providerName,
        data?.enrolleeId || data?.enrolleeNumber,
        data?.isExternalPlanType,
        data?.externalPlanTypeId,
        setItems,
        isLashmaEnrollee,
        type,
        showPrice,
        flaggedItems,
        utilizationOnlyServices,
      ),
    [
      selectedUtilIdxRef,
      utilizations?.length,
      readOnly,
      data?.serviceTypeCode,
      data?.facilityId,
      data?.providerName,
      data?.enrolleeId,
      data?.enrolleeNumber,
      data?.isExternalPlanType,
      data?.externalPlanTypeId,
      isLashmaEnrollee,
      type,
      showPrice,
      setInputs,
      flaggedItems,
      utilizationOnlyServices?.length,
    ],
  );

  const treatmentDate = data?.[type === 'HmoClaim' ? 'claimDate' : 'requestDateTime'];
  return (
    <div className={classNames('bg-white m-auto m-3 mt-5 pt-3 pb-3 max-width-800', className)}>
      <div className="flex justify-content-end">
        {readOnly ? (
          <EditIcon
            className="p-1 mr-3 cursor-pointer"
            style={{ border: '1px solid #ccc' }}
            fontSize="large"
            onClick={
              disableEdit
                ? undefined
                : () => {
                    action?.();
                  }
            }
            color={disableEdit ? 'disabled' : undefined}
          />
        ) : (
          <>
            {!!onAddNewItem && (
              <AddIcon
                className="p-1 mr-0 cursor-pointer"
                onClick={onAddNewItem}
                style={{ border: '1px solid #ccc' }}
                fontSize="large"
              />
            )}
            <SaveIcon
              className="p-1 mr-3 cursor-pointer"
              style={{ border: '1px solid #ccc' }}
              fontSize="large"
              color={disableEdit ? 'disabled' : undefined}
              onClick={
                disableEdit
                  ? undefined
                  : () => {
                      action?.();
                    }
              }
            />
          </>
        )}
      </div>
      <div className="flex flex-column overflow-auto">
        <div style={{ minWidth: 800 }}>
          <PlainTable
            columns={[
              { key: '_', className: 'min-width-200', title: '' },
              { key: '__', className: 'full-width min-width-200', title: '' },
            ]}
            data={[
              { _: 'Clinical Diagnosis', __: listAllDiagnosis(data?.diagnosis || []) },
              { _: 'Treatment Date and Time', __: formatTableDateTime({ value: treatmentDate }) },
            ]}
            showHeader={false}
            showSerial={false}
            noColoredEvenRow
          />
          <div className="ml-3 mr-3" style={{ overflowX: 'auto' }}>
            <PlainTable
              showSerial
              columns={columns}
              className="ml-0 mr-0 absolute-full-width"
              data={utilizations || []}
              noColoredHeader
              fullWidth
              noColoredEvenRow
            />
          </div>
        </div>
      </div>
      <Modal
        modalContent={
          <Prompter
            text="Are you sure you want to delete this record?"
            actionText="Delete"
            deleteAction={() => deleteUtilization()}
            cancelAction={toggle}
            disabled={false}
          />
        }
        isShown={showModalPrompt}
        hide={() => {}}
        handleDone={() => {}}
        isAuthentication
      />
    </div>
  );
}

function UtilizationTypeCell({
  readOnly,
  value,
  index,
  handleUtilizationTypeChange,
  setInputs,
  utilizationCategory,
  visitationType,
  providerId,
  providerName,
  enrolleeId,
  isExternalPlanType,
  utilizationId,
  externalPlanTypeId,
  recordType,
  utilizationStatus,
  utilization,
  flaggedItems,
  utilizationOnlyServices,
}) {
  const [shouldAutoOpenPopover, setShouldAutoOpenPopover] = useState(false);
  const {
    loadAllUtilizationTypes,
    utilizationResetKey,
    toggleFetchAllTypes,
  } = useFilterUtilizationOptions(
    recordType === 'Preauthorization'
      ? 'PREAUTH'
      : recordType === 'Referral'
      ? 'REFERRAL'
      : 'CLAIMS',
    providerName,
    providerId,
    visitationType,
    utilizationCategory,
    utilizationOnlyServices,
  );
  const { onUtilizationTypeChange } = useMappedUtilizationToDiagnosis({
    setInputs,
    diagnosisFieldName: 'diagnosis',
    utilizationFieldName: 'utilizations',
    map: utilizationDiagnosisMap,
    utilIdx: index,
    loadAllUtilizationTypes,
    enrolleeId,
    providerId,
    isExternalPlanType,
  });
  useEffect(() => {
    toggleFetchAllTypes(true);
  }, []);

  // Reset auto-open state after it's been used
  useEffect(() => {
    if (shouldAutoOpenPopover) {
      // Reset after a short delay to allow the popover to open
      const timer = setTimeout(() => {
        setShouldAutoOpenPopover(false);
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [shouldAutoOpenPopover]);

  // Drug detection logic (same as in AddRequestPreauthorization.tsx)
  const showDrugFields = ['drug', 'medicine', 'injection'].some(
    (keyword) =>
      `${utilizationCategory}${value}`.toLowerCase().includes(keyword) &&
      !utilizationCategory?.toLowerCase()?.includes('fees') &&
      !utilization.category?.toLowerCase()?.includes('admission'),
  );
  const typeFlaggedItems = useMemo(() => {
    return (
      utilization.utilizationCode &&
      hasRuleCategory(utilization.utilizationCode, 'utilizationType', flaggedItems)
    );
  }, [flaggedItems, utilization.type]);

  if (readOnly) {
    return (
      <div style={{ minWidth: 210, display: 'flex', alignItems: 'center' }}>
        <span>{value}</span>
        {showDrugFields && (
          <DrugFieldsPopover
            utilizationDosage={utilization.dosage}
            utilizationDosageUnit={utilization.dosageUnit}
            utilizationDuration={utilization.duration}
            utilizationFrequency={utilization.frequency}
            utilizationMedicationCategory={utilization.medicationCategory}
            utilizationQuantity={utilization.quantity}
            utilizationType={value}
            index={index}
            setInputs={setInputs}
            readOnly={readOnly}
            autoOpen={false}
          />
        )}
      </div>
    );
  }

  const cannotEdit = utilizationStatus && utilizationStatus !== 'Pending';
  return (
    <div style={{ minWidth: 210, display: 'flex', alignItems: 'center' }}>
      <Dropdown
        creatable={false}
        options={[]}
        onChange={(arg0) => {
          handleUtilizationTypeChange(arg0, index);
          // Check if the selected type is a drug and trigger auto-open
          const isDrug = ['drug', 'medicine', 'injection'].some(
            (keyword) =>
              `${arg0.utilizationCategory}${arg0?.label || ''}`.toLowerCase().includes(keyword) &&
              !arg0.utilizationCategory?.toLowerCase()?.includes('fees') &&
              !arg0.utilizationCategory?.toLowerCase()?.includes('admission'),
          );
          if (isDrug) {
            setShouldAutoOpenPopover(true);
          }
        }}
        onValueChange={(selection) => {
          const label = selection?.label;
          onUtilizationTypeChange(label);
        }}
        placeholder="Select One"
        isRequired
        value={value || null}
        readOnly={readOnly || cannotEdit}
        isAsync
        loadOptions={loadAllUtilizationTypes}
        noPadding
        noPaddingAll
        cacheUniqs={[`${utilizationId}-${externalPlanTypeId}-${utilizationResetKey}`]}
        withError={typeFlaggedItems}
      />
      {showDrugFields && (
        <DrugFieldsPopover
          utilizationDosage={utilization.dosage}
          utilizationDosageUnit={utilization.dosageUnit}
          utilizationDuration={utilization.duration}
          utilizationFrequency={utilization.frequency}
          utilizationMedicationCategory={utilization.medicationCategory}
          utilizationQuantity={utilization.quantity}
          utilizationType={value}
          index={index}
          setInputs={setInputs}
          readOnly={readOnly || cannotEdit}
          autoOpen={shouldAutoOpenPopover}
        />
      )}
    </div>
  );
}

function UtilizationCategoryCell({
  readOnly,
  value,
  onUtilizationCategoryChange,
  visitationType,
  providerId,
  facilityId,
  acceptedOptions,
  rejectedOptions,
  index,
  utilizationStatus,
  utilizationCode,
  flaggedItems,
}) {
  const { loadVisitationTypes } = useRequestPreauthorizationAPILoader();
  const cannotEdit = utilizationStatus && utilizationStatus !== 'Pending';
  const categoryFlaggedItems = useMemo(() => {
    return utilizationCode && hasRuleCategory(utilizationCode, 'utilizationCategory', flaggedItems);
  }, [flaggedItems, value]);

  if (readOnly) return <span>{value}</span>;

  return (
    <div style={{ minWidth: 100 }}>
      <Dropdown
        creatable={false}
        options={[]}
        onChange={({ value, label }) => {
          onUtilizationCategoryChange({ value, label }, index);
        }}
        placeholder="Select One"
        isRequired
        value={value || null}
        readOnly={readOnly || cannotEdit}
        isAsync
        loadOptions={loadVisitationTypes}
        additional={{
          action: 'utilizationCategory',
          providerId,
          visitationType,
          hospitalId: facilityId || '',
          acceptedOptions,
          rejectedOptions,
        }}
        noPadding
        noPaddingAll
        cacheUniqs={[visitationType]}
        withError={categoryFlaggedItems}
      />
    </div>
  );
}

function UtilizationQuantityCell({
  value,
  readOnly,
  index,
  setInputs,
  isExternalPlanType,
  isLashmaEnrollee,
  utilizationCategory,
  utilizationType,
  utilizationStatus,
  utilizationCode,
  flaggedItems,
}) {
  const cannotEdit = utilizationStatus && utilizationStatus !== 'Pending';
  const showDrugFields = ['drug', 'medicine', 'injection'].some(
    (keyword) =>
      `${utilizationCategory}${utilizationType}`.toLowerCase().includes(keyword) &&
      !utilizationCategory?.toLowerCase()?.includes('fees') &&
      !utilizationCategory?.toLowerCase()?.includes('admission'),
  );
  const isToothExtraction = ['tooth', 'extraction'].every((keyword) =>
    `${utilizationType}`.toLowerCase().includes(keyword),
  );
  const isBloodTransfusion = ['blood', 'transfusion'].every((keyword) =>
    `${utilizationType}`.toLowerCase().includes(keyword),
  );
  const isLimbXray = ['limb', 'xray'].every((keyword) =>
    `${utilizationType}`.toLowerCase().includes(keyword),
  );
  const isDentalRadiograph = ['dental', 'radiograph'].every((keyword) =>
    `${utilizationType}`.toLowerCase().includes(keyword),
  );
  const isCrossMatching = ['cross', 'matching'].every((keyword) =>
    `${utilizationType}`.toLowerCase().includes(keyword),
  );
  const isWoundDressing = ['wound', 'dressing'].every((keyword) =>
    `${utilizationType}`.toLowerCase().includes(keyword),
  );
  const isInjectionService = utilizationCategory?.toLowerCase()?.includes('injection');
  const isInfusionService = utilizationCategory?.toLowerCase()?.includes('infusion');

  const medicationIsPack = useMemo(() => {
    if (!showDrugFields || !utilizationType) return false;
    return SINGLE_COUNT_UTILIZATIONS.includes(utilizationType);
  }, [showDrugFields, utilizationType]);

  const quantityFlaggedItems = useMemo(() => {
    return utilizationCode && hasRuleCategory(utilizationCode, 'quantity', flaggedItems);
  }, [value, flaggedItems]);

  const disableQuantity =
    isLashmaEnrollee &&
    !(
      (showDrugFields && !medicationIsPack) ||
      isInjectionService ||
      isInfusionService ||
      isToothExtraction ||
      isBloodTransfusion ||
      isLimbXray ||
      isDentalRadiograph ||
      isCrossMatching ||
      isWoundDressing
    );

  if (readOnly || disableQuantity || isExternalPlanType) return <span>{value}</span>;

  return (
    <div style={{ width: 50 }}>
      <BareTextInput
        name="quantity"
        title=""
        onChange={({ target: { value } }) => {
          setInputs((prev) => {
            const tempUtilizations = cloneDeep(prev.utilizations || []);
            tempUtilizations[index] = {
              ...tempUtilizations[index],
              quantity: value,
            };
            return { ...prev, utilizations: tempUtilizations };
          });
        }}
        value={value}
        placeholder="Enter Quantity"
        readOnly={readOnly || cannotEdit || isExternalPlanType || disableQuantity}
        isRequired
        type="number"
        fullWidth
        noPadding
        styledInputWrapperClassName="zero-padding"
        withError={quantityFlaggedItems}
      />
    </div>
  );
}

function useUtilizationsSummaryTable({ setInputs, setItems, getTariff, recordType }) {
  const handleUtilizationTypeChange = useCallback(
    ({ label, code, hmoPlanBenefitId, utilizationCategory }, index: number) => {
      setInputs((_prev) => {
        const newInputs = cloneDeep(_prev);
        const selUtil = newInputs.utilizations[index];
        selUtil.type = code;
        setItems(
          {
            type: label,
            utilizationCode: (code || '').replaceAll(' ', ''),
            price: '0',
            category: utilizationCategory,
            utilizationId: hmoPlanBenefitId,
            dosage: null,
            dosageUnit: null,
            duration: null,
            frequency: null,
            medicationCategory: null,
            quantity: '1',
          },
          index,
        );
        getTariff({
          providerId: newInputs.providerId,
          position: index,
          id: (code || '').replaceAll(' ', ''),
          enrolleeId: newInputs.enrolleeId || newInputs.enrolleeNumber,
          treatmentDate:
            recordType === 'HmoClaim' ? newInputs.claimDate : newInputs.requestDateTime,
          visitTypeId: newInputs.serviceTypeCode,
          facilityId: newInputs.facilityId,
          externalPlanId: newInputs.externalPlanTypeId,
        });
      });
    },
    [setInputs, setItems, getTariff, recordType],
  );

  const onUtilizationCategoryChange = useCallback(
    ({ value, label }, index) => {
      setItems(
        {
          category: label,
          utilizationId: value,
          utilizationCode: null,
          type: null,
          quantity: '1',
          price: '0',
        },
        index,
      );
    },
    [setItems],
  );

  return {
    handleUtilizationTypeChange,
    onUtilizationCategoryChange,
  };
}

export default memo(UtilizationsSummaryTable);
