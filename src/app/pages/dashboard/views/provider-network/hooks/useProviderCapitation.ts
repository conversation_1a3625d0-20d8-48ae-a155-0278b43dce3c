import dayjs from 'dayjs';
import { ApolloCache, useLazyQuery, useMutation, useQuery } from '@apollo/client';
import { ChangeEvent, useCallback, useContext, useMemo, useState } from 'react';
import {
  GET_CAPITATED_PROVIDER_SUMMARY,
  GET_HOSPITALS_WITH_ADMIN,
  GET_TOTAL_PROVIDERS_COUNT,
} from 'apollo-queries/mains/hospital';
import {
  GetHospitalsWithAdmin,
  GetHospitalsWithAdminVariables,
} from 'graphql-types/GetHospitalsWithAdmin';
import errorHandler from 'app/shared/utils/errorHandler';
import { useToasts } from 'react-toast-notifications';
import { getUserPayload } from 'app/shared/utils/authentication';
import {
  FundTransactionStatus,
  HospitalFilterInput,
  PerPlanPayoutDecreasePercentage,
} from 'graphql-types/globalTypes';
import { useHistory, useLocation } from 'react-router-dom';
import updateUrl from 'app/shared/utils/updateUrl';
import debounce from 'lodash.debounce';
import cloneDeep from 'lodash.clonedeep';
import { PayoutCapitation, PayoutCapitationVariables } from 'graphql-types/PayoutCapitation';
import {
  CapitationPayoutApproval,
  CapitationPayoutApprovalVariables,
} from 'graphql-types/CapitationPayoutApproval';
import {
  UpdateCapitatedEnrolleeCount,
  UpdateCapitatedEnrolleeCountVariables,
} from 'graphql-types/UpdateCapitatedEnrolleeCount';
import {
  CAPITATION_PAYOUT_APPROVAL,
  PAYOUT_CAPITATION,
  SEND_TRANSACTION_RECEIPT_TO_PROVIDER,
  UPDATE_CAPITATED_ENROLLEE_COUNT,
} from 'apollo-queries/mains/payout';
import { LOOKUP_PATIENT } from 'apollo-queries/mains/user';
import { LookupPatient, LookupPatientVariables } from 'graphql-types/LookupPatient';
import { printPaymentAdviceDocRef } from 'app/pages/dashboard/views/provider-network/components/printPaymentAdviceDoc';
import ThemeContext from '@constant/themes';
import useHospitalData from 'app/shared/hooks/client/useHospitalData';
import { useGetCacheProfile } from 'app/shared/hooks/fetch/useCacheProfile';
import { usePrintPdf } from 'app/shared/hooks/forms/usePrintPdf';
import { GetHmoPlanList, GetHmoPlanListVariables } from 'graphql-types/GetHmoPlanList';
import { GET_HMO_PLANS } from 'apollo-queries/mains/hmo-plans';
import { OptionType } from 'app/shared/components/table/ListView';
import {
  GetHmoCapitatedEnrolleesData,
  GetHmoCapitatedEnrolleesData_getHmoCapitatedEnrolleesData_list,
  GetHmoCapitatedEnrolleesDataVariables,
} from 'graphql-types/GetHmoCapitatedEnrolleesData';
import {
  GET_CAPITATED_ENROLLEES_DATA,
  GET_CAPITATED_ENROLLEES_SUMMARY,
} from 'apollo-queries/mains/hmo-providers';
import { convertToNumber } from '../../invoice/utils';
import generateCapitationPayoutAdvice from '../components/docs/generate-bulk-print-payment-advice';
import bulkPrintCapitationPaymentAdvice, {
  BulkPrintCapitationPaymentAdvice_items,
} from '../pdf-docs/bulk-print-capitation-payment-advice';
import {
  GetHmoCapitatedPaymentSummary,
  GetHmoCapitatedPaymentSummaryVariables,
} from '../../../../../../graphql-types/GetHmoCapitatedPaymentSummary';
import { getBase64FromURL } from '../../../../../shared/utils/getBase64FromURL';
import { useFacilityPreference } from '../../settings/hooks/useFacilityPreference';
import useSheetExport from '../../analytics/tabs/hooks/useSheetExport';
import { toNaira, withComma } from '../../billings/constants';
import {
  BANK_TO_SORT_CODE_MAP,
  BENEFICIARY_ACCOUNT_NUMBER_TEXT,
  BENEFICIARY_BANK_SORT_CODE_TEXT,
  BENEFICIARY_CODE_TEXT,
  BENEFICIARY_NAME_TEXT,
  DEBIT_ACCOUNT_NUMBER_TEXT,
  PAYMENT_AMOUNT_TEXT,
  PAYMENT_DUE_DATE_TEXT,
  PROVIDER_CLASSIFICATION_MAP,
  TRANSACTION_REFERENCE_TEXT,
} from '../constants';
import useFundWallet from '../../wallet/hooks/useFundWallet';

interface IProviderCapFilterInput {
  filterOptions: Record<string, any>;
  startDate: string;
  endDate: string;
  planId?: string;
}

const useProviderCapitation = (skipFetch: boolean) => {
  const { isPartnerProfile, partnerId, profileId } = getUserPayload();
  const defaultFilterInput = {
    ...(isPartnerProfile && partnerId
      ? { partnerProviderLookup: true }
      : { hmoProviderLookup: true }),
    skip: 0,
    take: 50,
  };

  const [providerCapFilterInput, setProviderCapFilterInput] = useState<IProviderCapFilterInput>({
    filterOptions: { ...defaultFilterInput },
    startDate: dayjs().startOf('month').toDate() as any,
    endDate: dayjs().endOf('month').toDate() as any,
  });
  const dateRangeValues = useMemo(
    () => ({
      startDate: providerCapFilterInput?.startDate
        ? dayjs(providerCapFilterInput.startDate).startOf('month').format('YYYY-MM-DD')
        : dayjs().startOf('month').format('YYYY-MM-DD'),
      endDate: providerCapFilterInput?.endDate
        ? dayjs(providerCapFilterInput.endDate).endOf('month').format('YYYY-MM-DD')
        : dayjs().endOf('month').format('YYYY-MM-DD'),
    }),
    [providerCapFilterInput?.startDate?.toString(), providerCapFilterInput?.endDate?.toString()],
  );

  const [transactionStatusOption, setTransactionStatusOption] = useState<string | null>(null);

  const { addToast } = useToasts();
  const hospitalData = useHospitalData();
  const { inputs: facilityPref } = useFacilityPreference();
  const { exportSheet } = useSheetExport();
  const { virtualAccount } = useFundWallet();

  const { pathname, search } = useLocation();
  const history = useHistory();
  const params = new URLSearchParams(search);

  const currentPageNumber = params.get('page') || 1;

  const { data: planTypeData } = useQuery<GetHmoPlanList, GetHmoPlanListVariables>(GET_HMO_PLANS, {
    variables: { filterOptions: { skip: 0, take: 1000 } },
    onError: (error) => {
      addToast(errorHandler(error), { appearance: 'error' });
    },
  });

  const agencyPlanTypes: OptionType[] = (planTypeData?.hospital?.hmoPlanTypes?.list || []).map(
    (_item) =>
      ({
        label: _item.name,
        value: _item.id,
      } as OptionType),
  );

  const _currentPageNumber = Number(currentPageNumber);

  const queryVariables = useMemo(
    () => ({
      ...providerCapFilterInput,
      ...dateRangeValues,
      filterOptions: {
        ...providerCapFilterInput.filterOptions,
        skip:
          (!_currentPageNumber ? 1 : _currentPageNumber - 1) *
          providerCapFilterInput.filterOptions.take,
      },
    }),
    [providerCapFilterInput, dateRangeValues, _currentPageNumber],
  );

  const { data, loading, refetch, networkStatus } = useQuery<
    GetHospitalsWithAdmin,
    GetHospitalsWithAdminVariables
  >(GET_HOSPITALS_WITH_ADMIN, {
    fetchPolicy: 'network-only',
    notifyOnNetworkStatusChange: true,
    skip: skipFetch,
    variables: queryVariables,
    onError: (err) => {
      addToast(errorHandler(err), { appearance: 'error' });
    },
  });

  const [fetchAllProviders, { data: fetchAllProvidersData }] = useLazyQuery<
    GetHospitalsWithAdmin,
    GetHospitalsWithAdminVariables
  >(GET_HOSPITALS_WITH_ADMIN, {
    variables: {
      ...providerCapFilterInput,
      filterOptions: {
        ...providerCapFilterInput.filterOptions,
        skip: 0,
        take: 2_000,
      },
    },
  });

  const { data: getHmoCapitatedPaymentSummaryData } = useQuery<
    GetHmoCapitatedPaymentSummary,
    GetHmoCapitatedPaymentSummaryVariables
  >(GET_CAPITATED_ENROLLEES_SUMMARY, {
    variables: {
      ...dateRangeValues,
      hmoPlanTypeId: providerCapFilterInput?.planId,
    },
    onError: () => {
      // no-op
    },
  });

  const [
    fetchHmoCapitatedEnrolleesQuery,
    { refetch: refetchHmoCapitatedEnrollees, called: calledFetchHmoCapitatedEnrollees },
  ] = useLazyQuery<GetHmoCapitatedEnrolleesData, GetHmoCapitatedEnrolleesDataVariables>(
    GET_CAPITATED_ENROLLEES_DATA,
    {
      variables: {
        ...dateRangeValues,
        hmoPlanTypeId: providerCapFilterInput.planId,
        hospitalId: providerCapFilterInput?.filterOptions?.hospitalId,
      },
    },
  );

  const { data: totalProviderCount } = useQuery(GET_TOTAL_PROVIDERS_COUNT, {
    onError: (error) => {
      addToast(errorHandler(error), { appearance: 'error' });
    },
  });
  const { data: capitatedProviderSummary, refetch: refetchCapitationSummary } = useQuery(
    GET_CAPITATED_PROVIDER_SUMMARY,
    {
      skip: skipFetch,
      variables: {
        ...dateRangeValues,
        planId: providerCapFilterInput?.planId,
        hospitalId: providerCapFilterInput?.filterOptions?.hospitalId,
      },
      onError: (error) => {
        addToast(errorHandler(error), { appearance: 'error' });
      },
    },
  );

  const _refetchHmoProviderCapitation = (param?: any) => {
    if (param) {
      refetch({
        ...param,
        startDate: param?.startDate ? dayjs(param.startDate).format('YYYY-MM-DD') : undefined,
        endDate: param?.endDate ? dayjs(param.endDate).format('YYYY-MM-DD') : undefined,
      });
    } else refetch();
    if (param) {
      refetchCapitationSummary({
        startDate: param?.startDate ? dayjs(param.startDate).format('YYYY-MM-DD') : undefined,
        endDate: param?.endDate ? dayjs(param.endDate).format('YYYY-MM-DD') : undefined,
        planId: param?.planId,
        hospitalId: param?.filterOptions?.hospitalId,
      });
    } else {
      refetchCapitationSummary({
        ...dateRangeValues,
        planId: providerCapFilterInput?.planId,
        hospitalId: providerCapFilterInput?.filterOptions?.hospitalId,
      });
    }
  };

  const [
    payoutForCapitatedEnrolleesMutation,
    { loading: payingoutForCapitatedEnrollees },
  ] = useMutation<PayoutCapitation, PayoutCapitationVariables>(PAYOUT_CAPITATION, {
    onError(error) {
      addToast(errorHandler(error), { appearance: 'error' });
    },
    onCompleted() {
      addToast('Record Updated Successfully!', { appearance: 'success' });
    },
  });

  const [
    capitationPayoutApprovalMutation,
    { loading: updatingCapitationPayoutApproval },
  ] = useMutation<CapitationPayoutApproval, CapitationPayoutApprovalVariables>(
    CAPITATION_PAYOUT_APPROVAL,
    {
      onError(error) {
        addToast(errorHandler(error), { appearance: 'error' });
      },
      onCompleted() {
        addToast('Record Updated Successfully', { appearance: 'success' });
      },
      update(cache, { data }) {
        updateCapitationPayoutApprovalInCache(cache, data, queryVariables);
      },
    },
  );

  const [
    updateCapitatedEnrolleeCountMutation,
    { loading: updatingCapitedEnrolleeCount },
  ] = useMutation<UpdateCapitatedEnrolleeCount, UpdateCapitatedEnrolleeCountVariables>(
    UPDATE_CAPITATED_ENROLLEE_COUNT,
    {
      onError(error) {
        addToast(errorHandler(error), { appearance: 'error' });
      },
      onCompleted() {
        _refetchHmoProviderCapitation();
        addToast('Record Updated Successfully', { appearance: 'success' });
      },
    },
  );

  const [sendTransactionReceiptToProvider] = useLazyQuery(SEND_TRANSACTION_RECEIPT_TO_PROVIDER, {
    onCompleted: () => {
      addToast('Receipt Sent To Provider Successfully', { appearance: 'success' });
    },
    onError: () =>
      addToast('Error Sending Receipt To Provider, Please Try Again', { appearance: 'error' }),
  });

  const handleCapitationPayoutApproval = useCallback(
    (hospitalIds: string[], status: boolean) => {
      return capitationPayoutApprovalMutation({
        variables: {
          hospitalIds,
          status,
          ...dateRangeValues,
          hmoPlanTypeId: providerCapFilterInput.planId,
        },
      });
    },
    [
      capitationPayoutApprovalMutation,
      providerCapFilterInput.startDate,
      providerCapFilterInput.endDate,
      providerCapFilterInput.planId,
    ],
  );

  const handlePayoutForCapitatedEnrollees = useCallback(
    (
      hospitalIds: string[],
      transferFundId: string,
      totalCapitationAmount: number,
      enrolleeCount: number,
      totalAmountForPayout: number,
      payoutDecreasePercentage: number | null | undefined,
      planPercentageDecrease: Record<string, number | null | undefined> | null | undefined,
    ) => {
      const perPlanPayoutDecreasePercentage:
        | PerPlanPayoutDecreasePercentage[]
        | null = Object.entries(planPercentageDecrease || {})?.map(([key, value]) => ({
        hmoPlanTypeId: key.split('___')[1],
        payoutDecreasePercentage: value,
      }));
      return payoutForCapitatedEnrolleesMutation({
        variables: {
          origin: '',
          inputs: hospitalIds.map((hid) => ({
            hospitalId: hid,
            transferFundId,
            totalCapitationAmount,
            enrolleeCount,
            totalAmountForPayout,
            payoutDecreasePercentage,
            perPlanPayoutDecreasePercentage,
          })),
        },
      });
    },
    [payoutForCapitatedEnrolleesMutation],
  );

  const handleEnrolleeCountUpdate = useCallback(
    (hospitalId: string, enrolleeCount: number, hmoPlanTypeId?: string) => {
      return updateCapitatedEnrolleeCountMutation({
        variables: {
          hospitalId,
          ...dateRangeValues,
          enrolleeCount,
          hmoPlanTypeId: hmoPlanTypeId || providerCapFilterInput.planId,
        },
      });
    },
    [
      updateCapitatedEnrolleeCountMutation,
      providerCapFilterInput.startDate,
      providerCapFilterInput.endDate,
      providerCapFilterInput.planId,
    ],
  );

  const hospitalIds = data?.hospitals?.list?.map((_item) => _item?.id);
  const items = useMemo(() => {
    let sortedList = (data?.hospitals?.list || []).sort((a, b) => {
      const aLatest = (a.capitationDetails || [])?.[0];
      const _aLatest = aLatest?.transferFund?.enrolleeCount || aLatest?.enrolleeCount || 0;
      const bLatest = (b.capitationDetails || [])?.[0];
      const _bLatest = bLatest?.transferFund?.enrolleeCount || bLatest?.enrolleeCount || 0;
      return _bLatest - _aLatest;
    });

    if (transactionStatusOption) {
      sortedList = sortedList.filter((item) => {
        return item.capitationDetails?.some((i) => {
          if (i.transferFund?.transferStatus === transactionStatusOption) {
            return true;
          }
          if (
            transactionStatusOption === 'None' &&
            (['Processing', 'Pending'].includes(i.transferFund?.transferStatus as string) ||
              i.transferFund?.transferStatus === null)
          ) {
            return true;
          }
          return false;
        });
      });
    }

    return sortedList;
  }, [hospitalIds]);

  const getPageCount = () => {
    return data?.hospitals?.totalCount && providerCapFilterInput?.filterOptions?.take
      ? Math.ceil(
          (data.hospitals.totalCount as number) / providerCapFilterInput.filterOptions.take,
        ) || 1
      : 1;
  };

  const pageCount = getPageCount();

  const delayedSetFilterOptions = useCallback(
    debounce((params) => {
      handleSetMultiFilterOptions(params);
    }, 1000),
    [],
  );

  const clearFilterInput = () => {
    const newFilterInput = {
      filterOptions: { ...defaultFilterInput } as HospitalFilterInput,
      startDate: dayjs().startOf('month').toDate() as any,
      endDate: dayjs().endOf('month').toDate() as any,
    };
    setProviderCapFilterInput({ ...newFilterInput });
  };

  const handleSetFilterOptions = (objKey: string, value: any) => {
    const newFilterInput = { ...providerCapFilterInput };

    if (objKey === 'planId') {
      newFilterInput[objKey] = value;
    } else if (['from', 'to'].includes(objKey)) {
      if (objKey === 'from') {
        // When startDate is selected, set to start of month and update endDate to end of same month
        newFilterInput.startDate = value ? (dayjs(value).startOf('month').toDate() as any) : value;
        newFilterInput.endDate = value ? (dayjs(value).endOf('month').toDate() as any) : value;
      } else {
        // When endDate is selected, set to end of month and update startDate to beginning of same month
        newFilterInput.endDate = value ? (dayjs(value).endOf('month').toDate() as any) : value;
        newFilterInput.startDate = value ? (dayjs(value).startOf('month').toDate() as any) : value;
      }
    } else {
      newFilterInput.filterOptions = {
        ...newFilterInput.filterOptions,
        [objKey]: value,
      };
    }

    if (objKey === 'hospitalId' && _currentPageNumber !== 1) {
      const pathToUse = `${pathname}${updateUrl('page', 1, '')}`;
      history.push(pathToUse);
      newFilterInput.filterOptions = {
        ...newFilterInput.filterOptions,
        skip: 0,
        take: 50,
      };
    }

    setProviderCapFilterInput({ ...newFilterInput });
    _refetchHmoProviderCapitation({ ...newFilterInput });
  };

  const handleSetMultiFilterOptions = (param: Record<string, any>) => {
    const newFilterInput = { ...providerCapFilterInput };

    newFilterInput.filterOptions = {
      ...newFilterInput.filterOptions,
      ...param,
    };

    setProviderCapFilterInput({ ...newFilterInput });
    _refetchHmoProviderCapitation({ ...newFilterInput });
  };

  const changeLimit = (take: number) => {
    const pathToUse = `${pathname}${updateUrl('page', '1', '')}`;
    handleSetMultiFilterOptions({ take, skip: 0 });
    history.push(pathToUse);
  };

  const toPage = (page: number) => {
    const pathToUse = `${pathname}${updateUrl('page', page, '')}`;
    const skip = (page - 1) * providerCapFilterInput.filterOptions.take;
    handleSetFilterOptions('skip', skip);
    history.push(pathToUse);
  };

  const handleSearch = (event: ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    if (_currentPageNumber !== 1) {
      const pathToUse = `${pathname}${updateUrl('page', 1, '')}`;
      history.push(pathToUse);
    }
    delayedSetFilterOptions({ keyword: value, skip: 0, take: 50 });
  };

  const setDateRange = (objKey: 'from' | 'to') => (value: Date | null) => {
    const { endDate, startDate } = providerCapFilterInput;
    if (objKey === 'from' && value && endDate && startDate && value > new Date(endDate)) {
      addToast('Invalid Date Range', { appearance: 'error' });
      return;
    }

    handleSetFilterOptions(objKey, value ? new Date(value).toISOString() : null);
  };

  const isFetchingMore = loading;
  const theme = useContext(ThemeContext);
  const { auditFields } = useGetCacheProfile();
  const { openPdf } = usePrintPdf();
  const handlePrint = async (
    data: LookupPatient['profiles']['list'],
    amountPaid,
    enrolleeCount,
  ) => {
    const docDef = await printPaymentAdviceDocRef(
      data,
      {
        name: hospitalData?.hospitalName,
        address: hospitalData?.hospitalAddress,
        logo: hospitalData?.facilityLogo || '',
        website: hospitalData?.website || '',
        userName: auditFields?.fullName || '',
      },
      amountPaid,
      enrolleeCount,
      theme,
    );
    openPdf(docDef);
  };
  const [
    fetchProviderEnrollees,
    { called: calledFetchProviderEnrollees, refetch: refetchProviderEnrollees },
  ] = useLazyQuery<LookupPatient, LookupPatientVariables>(LOOKUP_PATIENT, {
    fetchPolicy: 'network-only',
  });
  const handlePrintPaymentAdvice = useCallback(
    async (registeredWith: string) => {
      const selectedFacility = items.find(({ id }) => id === registeredWith);
      if (!selectedFacility) return;
      const { amountPaid, totalEnrolleeCount } = (selectedFacility.capitationDetails || []).reduce(
        (acc, curr) => {
          acc.amountPaid +=
            convertToNumber(curr.transferFund?.amount) *
            convertToNumber(curr.transferFund?.enrolleeCount);
          if (curr.transferFund?.amount) {
            acc.totalEnrolleeCount += curr.transferFund?.enrolleeCount || 0;
          }
          return acc;
        },
        { amountPaid: 0, totalEnrolleeCount: 0 },
      );
      let d: LookupPatient | undefined;
      if (calledFetchProviderEnrollees) {
        const { data: refetchData } = await refetchProviderEnrollees({
          filterOptions: {
            archive: false,
            skip: 0,
            take: 5000,
            registeredWith,
            usersRegisteredUnder: 'ALL',
          } as any,
        });
        d = refetchData;
      } else {
        const res = await fetchProviderEnrollees({
          variables: {
            filterOptions: {
              archive: false,
              skip: 0,
              take: 5000,
              registeredWith,
              usersRegisteredUnder: 'ALL',
            } as any,
          },
        });
        d = res.data;
      }
      handlePrint(d?.profiles?.list || [], amountPaid, totalEnrolleeCount);
    },
    [
      items,
      calledFetchProviderEnrollees,
      refetchProviderEnrollees,
      fetchProviderEnrollees,
      handlePrint,
    ],
  );

  const allPayoutApprovedBySelf = items.every(({ capitationDetails }) =>
    capitationDetails?.every(
      ({ transferFund }) =>
        transferFund?.auditApproval?.length &&
        transferFund?.auditApproval?.some(({ creatorId }) => creatorId === profileId),
    ),
  );

  const handleBulkCapitationPayoutApproval = useCallback(
    (checked: boolean) => {
      if (checked) {
        if (allPayoutApprovedBySelf) return;
        const rowIds = items.reduce((acc, curr) => {
          if (
            !curr?.capitationDetails?.some(
              ({ transferFund }) =>
                transferFund?.auditApproval?.some(({ creatorId }) => creatorId === profileId) &&
                transferFund?.transferStatus !== FundTransactionStatus.Success,
            )
          ) {
            acc.push(curr.id);
          }
          return acc;
        }, [] as string[]);

        if (!rowIds.length) return;

        handleCapitationPayoutApproval(rowIds, checked);
      } else {
        const rowIds = items.reduce((acc, curr) => {
          if (
            curr.capitationDetails?.some(
              ({ transferFund }) => transferFund?.transferStatus === FundTransactionStatus.Success,
            )
          ) {
            return acc;
          }
          acc.push(curr.id);
          return acc;
        }, [] as string[]);
        if (!rowIds?.length) return;
        handleCapitationPayoutApproval(rowIds, checked);
      }
    },
    [allPayoutApprovedBySelf, handleCapitationPayoutApproval, items, profileId],
  );

  const handlePrintProviderCapitationList = useCallback(async () => {
    const providersData = fetchAllProvidersData || (await fetchAllProviders()).data;
    const docDef = await generateCapitationPayoutAdvice(
      providersData?.hospitals?.list || [],
      {
        name: hospitalData.hospitalName,
        address: hospitalData.hospitalAddress,
        logo: hospitalData.facilityLogo,
        website: hospitalData.website,
      },
      auditFields?.fullName,
      theme,
      facilityPref?.enrolleeCapitationAmountPerPlan,
    );

    openPdf(docDef);
  }, [data, fetchAllProviders, hospitalData, auditFields?.fullName, theme, openPdf]);

  const handlePrintProviderCapitationListExcel = async () => {
    const providersData = fetchAllProvidersData || (await fetchAllProviders()).data;
    const payoutMonth = dayjs(providerCapFilterInput.startDate).format('MMMM YYYY');
    const _payoutMonth = (payoutMonth || '').toLocaleUpperCase();
    const paymentDueDate = dayjs(providerCapFilterInput.startDate)
      .startOf('month')
      .add(3, 'week')
      .startOf('week')
      .add(1, 'day')
      .format('DD/MM/YYYY');

    const spreadsheetData = [
      {
        title: `${payoutMonth} Capitation Payout`,
        columns: [
          TRANSACTION_REFERENCE_TEXT,
          'PAYOUT STATUS',
          'PROVIDER NAME',
          BENEFICIARY_NAME_TEXT,
          PAYMENT_AMOUNT_TEXT,
          'PAYMENT DECREASE AMOUNT',
          PAYMENT_DUE_DATE_TEXT,
          BENEFICIARY_CODE_TEXT,
          BENEFICIARY_ACCOUNT_NUMBER_TEXT,
          BENEFICIARY_BANK_SORT_CODE_TEXT,
          DEBIT_ACCOUNT_NUMBER_TEXT,
        ],
        rows: (providersData?.hospitals?.list || [])
          ?.filter(
            (_provD) =>
              _provD?.capitationDetails?.[0]?.transferFund &&
              _provD?.capitationDetails?.[0]?.transferFund?.transferStatus !== 'Pending',
          )
          .map((_item, index) => {
            const _capitationDetails = _item?.capitationDetails?.[0];
            const _transferFund = _capitationDetails?.transferFund;
            const benfCode = index + 1;
            const trnxRef = `${benfCode}`.padStart(2, '0');
            const bankName =
              _transferFund?.destinationBankName || _item?.preferredPayoutAccount?.bankName;
            let _detailsByPlanType = _capitationDetails?.detailsByPlanType;
            if (_transferFund?.detailsByPlanType?.length) {
              _detailsByPlanType = _transferFund?.detailsByPlanType;
            }

            let _totalFullAmount = 0;
            let _totalAmount = _transferFund?.amount;
            _totalFullAmount =
              _detailsByPlanType?.reduce((sum, item) => {
                return sum + (item?.enrolleeCount || 0) * (item?.totalCapitationAmount || 0);
              }, 0) || 0;
            if (!_totalAmount) {
              const decreaseRatio = _transferFund?.payoutDecreasePercentage
                ? (100 - _transferFund.payoutDecreasePercentage) / 100
                : 1;
              _totalAmount = _totalFullAmount * decreaseRatio;
            }

            let _transactionRef = `CAPITAT ${_payoutMonth}/${trnxRef}`;
            const _classification = PROVIDER_CLASSIFICATION_MAP[_item?.classification || ''];
            if (_item?.classification && _classification) {
              _transactionRef = `${_classification} ${_transactionRef}`;
            }

            return [
              _transactionRef,
              _transferFund?.transferStatus || '--',
              _item?.name,
              _transferFund?.destinationAccountName ||
                _item?.preferredPayoutAccount?.accountName ||
                '--',
              withComma(toNaira(_totalFullAmount || 0)),
              withComma(toNaira(_totalAmount || 0)),
              paymentDueDate,
              benfCode,
              _transferFund?.destinationAccountNumber ||
                _item?.preferredPayoutAccount?.accountNumber ||
                '--',
              bankName ? BANK_TO_SORT_CODE_MAP[bankName?.toLowerCase()] || '--' : '--',
              virtualAccount?.accountNumber || '--',
            ];
          }),
      },
    ];

    exportSheet(spreadsheetData, `${payoutMonth}-capitation-payout`);
  };

  const handleBulkPrintPaymentAdvice = useCallback(
    async (hid?: string) => {
      const hospitalId = hid || providerCapFilterInput.filterOptions?.hospitalId;
      const capitatedEnrolleeData = calledFetchHmoCapitatedEnrollees
        ? (
            await refetchHmoCapitatedEnrollees({
              hmoPlanTypeId: providerCapFilterInput.planId,
              hospitalId,
              startDate: providerCapFilterInput.startDate,
              endDate: providerCapFilterInput.endDate,
            })
          ).data
        : (
            await fetchHmoCapitatedEnrolleesQuery({
              variables: {
                hmoPlanTypeId: providerCapFilterInput.planId,
                hospitalId,
                startDate: providerCapFilterInput.startDate,
                endDate: providerCapFilterInput.endDate,
              },
            })
          ).data;
      if (!capitatedEnrolleeData?.getHmoCapitatedEnrolleesData) return;

      const bulkItems: BulkPrintCapitationPaymentAdvice_items[] = [];

      let transactionRemark =
        capitatedEnrolleeData?.getHmoCapitatedEnrolleesData?.[0].list?.[0]?.narration;

      // eslint-disable-next-line no-restricted-syntax
      for (const providerData of capitatedEnrolleeData.getHmoCapitatedEnrolleesData) {
        if (!transactionRemark) {
          transactionRemark = providerData?.[0].list?.[0]?.narration;
        }
        const amountPaid = convertToNumber(
          providerData?.list?.find(({ amount }) => !!amount)?.amount,
        );
        const serviceChargeAmount = convertToNumber(
          providerData?.list?.find(({ serviceChargeAmount }) => !!serviceChargeAmount)
            ?.serviceChargeAmount,
        );
        const totalEnrolleeCount =
          providerData?.list?.[0]?.enrolleeCount ?? convertToNumber(providerData?.list?.length);
        const facilityName = providerData?.list?.[0]?.primaryProviderName as string;
        const facilityId = providerData?.list?.[0]?.primaryProviderId as string;
        const facilityAddress = providerData?.list?.[0]?.primaryProviderAddress as string;

        // eslint-disable-next-line no-continue
        if (totalEnrolleeCount === 0 || amountPaid === 0) continue;

        let enrolleesData: GetHmoCapitatedEnrolleesData_getHmoCapitatedEnrolleesData_list[];
        try {
          enrolleesData = providerData?.list || [];

          bulkItems.push({
            enrollees: enrolleesData || [],
            amountPaid,
            enrolleeCount: convertToNumber(totalEnrolleeCount),
            facilityName,
            facilityId,
            facilityAddress,
            serviceChargeAmount,
          });
        } catch (error) {
          // no-op
        }
      }

      if (bulkItems.length > 0) {
        let _facilityLogo: string | undefined | null;
        if (hospitalData.facilityLogo) {
          try {
            _facilityLogo = await getBase64FromURL(hospitalData.facilityLogo as string);
          } catch {
            // no-op
          }
        }
        const docDef = bulkPrintCapitationPaymentAdvice(
          bulkItems,
          theme,
          {
            name: hospitalData.hospitalName,
            address: hospitalData.hospitalAddress,
            logo: _facilityLogo,
            website: hospitalData.website as string,
            userName: auditFields?.fullName as string,
          },
          transactionRemark || '',
        );

        openPdf(docDef);
      }
    },
    [
      fetchAllProvidersData,
      fetchAllProviders,
      fetchProviderEnrollees,
      hospitalData,
      auditFields?.fullName,
      theme,
      openPdf,
    ],
  );
  const handleEmailTransactionReceipt = (payload: { id: string; amount: number }[]) => {
    sendTransactionReceiptToProvider({
      variables: {
        input: [...payload],
        origin: window.location.origin,
      },
    });
  };

  const emailTransactionReceipt = (transactionId: string, amount: number) => {
    handleEmailTransactionReceipt([{ id: transactionId, amount }]);
  };

  const handleBulkEmailTransactionReceipt = useCallback(
    (checked: boolean) => {
      if (checked) {
        if (allPayoutApprovedBySelf) return;

        const emailInputs: any[] = [];

        items.forEach((_item) => {
          const tFund = _item.capitationDetails?.[0].transferFund;
          if (!tFund?.id) return;

          const transactionAmount =
            _item.capitationDetails?.reduce(
              (acc, curr) => acc + convertToNumber(curr.transferFund?.amount),
              0,
            ) || 0;

          emailInputs.push({ id: tFund.id, amount: transactionAmount });
        });

        if (!emailInputs) return;

        handleEmailTransactionReceipt(emailInputs);
      } else {
        const emailInputs: any[] = [];

        items.forEach((_item) => {
          const tFund = _item.capitationDetails?.[0].transferFund;
          if (!tFund?.id) return;

          const transactionAmount =
            _item.capitationDetails?.reduce(
              (acc, curr) => acc + convertToNumber(curr.transferFund?.amount),
              0,
            ) || 0;

          emailInputs.push({ id: tFund.id, amount: transactionAmount });
        });

        if (!emailInputs) return;

        handleEmailTransactionReceipt(emailInputs);
      }
    },
    [allPayoutApprovedBySelf, handleEmailTransactionReceipt, items, profileId],
  );

  const refetchingHmoProviderCapitation = networkStatus === 4;

  return {
    items,
    loading,
    filterOptions: { ...providerCapFilterInput, ...providerCapFilterInput.filterOptions },
    handleSearch,
    clearFilter: clearFilterInput,
    filterByStatus: handleSetFilterOptions,
    setDateRange,
    changeLimit,
    pageCount,
    toPage,
    currentPageNumber,
    payingoutForCapitatedEnrollees,
    handleCapitationPayoutApproval,
    handlePayoutForCapitatedEnrollees,
    refetchHmoProviderCapitation: _refetchHmoProviderCapitation,
    updatingCapitationPayoutApproval,
    handleEnrolleeCountUpdate,
    updatingCapitedEnrolleeCount,
    isFetchingMore,
    handlePrintPaymentAdvice,
    agencyPlanTypes,
    refetchingHmoProviderCapitation,
    handleBulkCapitationPayoutApproval,
    allPayoutApprovedBySelf,
    handlePrintProviderCapitationList,
    handleBulkPrintPaymentAdvice,
    transactionStatusOption,
    setTransactionStatusOption,
    payoutSummary: {
      totalProviders: totalProviderCount?.getTotalProvidersCount,
      ...(capitatedProviderSummary?.getCapitatedProviderSummary || {}),
    },
    emailTransactionReceipt,
    handleBulkEmailTransactionReceipt,
    getHmoCapitatedPaymentSummaryData,
    handlePrintProviderCapitationListExcel,
  };
};

function updateCapitationPayoutApprovalInCache(
  cache: ApolloCache<any>,
  data: CapitationPayoutApproval | null | undefined,
  filterOptions: IProviderCapFilterInput,
) {
  const cacheData: any = cache.readQuery({
    query: GET_HOSPITALS_WITH_ADMIN,
    variables: { ...filterOptions },
  });

  if (!cacheData?.hospitals?.list?.length || !data?.capitationAuditApproval?.length) {
    return;
  }

  const updatedCachedData = cacheData?.hospitals?.list.map((_item) => {
    const _capitationDetails = cloneDeep(_item?.capitationDetails);

    const mutatedTransferFund = data?.capitationAuditApproval.find(
      (auditApproval) => auditApproval.hospitalId === _item.id,
    );

    if (!mutatedTransferFund) return _item;

    const transferFundIdExist = _item?.capitationDetails?.find(
      (capDet) => capDet?.transferFund?.id === mutatedTransferFund.id,
    );

    if (transferFundIdExist) return _item;

    const transferFundIdMissingIndex = _capitationDetails.findIndex(
      (capDet) => !capDet?.transferFund?.id,
    );

    if (transferFundIdMissingIndex !== -1) {
      _capitationDetails[transferFundIdMissingIndex] = {
        ..._capitationDetails[transferFundIdMissingIndex],
        transferFund: { ...mutatedTransferFund },
      };
      return { ..._item, capitationDetails: _capitationDetails };
    }

    return _item;
  });

  cache.writeQuery({
    query: GET_HOSPITALS_WITH_ADMIN,
    variables: { ...filterOptions },
    data: {
      hospitals: {
        ...cacheData?.hospitals,
        list: [...updatedCachedData],
      },
    },
  });
}

export default useProviderCapitation;
