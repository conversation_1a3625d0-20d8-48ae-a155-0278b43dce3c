/* eslint-disable jsx-a11y/no-autofocus */

/**
 * NOTE: All tooltips in this component use the unified ID 'capitationPayoutTableTip'.
 * The parent component that uses this table should include:
 * <ReactTooltip id="capitationPayoutTableTip" place="top" className="button-tooltip" variant="light" />
 */
import './styles.scss';
import 'app/pages/dashboard/views/pre-authorisations/components/AuthorizationCodesList/style.scss';

import PlainTable, { PlainTableColumns } from 'app/shared/components/table/PlainTable';
import React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { formatMoney } from 'app/shared/utils/common';
import { formatTableDateTime } from 'app/shared/utils/formatTable';
import RefreshIcon from '@material-ui/icons/Refresh';
import DownloadIcon from '@material-ui/icons/GetApp';
import PrintIcon from '@material-ui/icons/Print';
import classNames from 'classnames';
import ArrowRightIcon from '@material-ui/icons/ArrowRight';
import ArrowLeftIcon from '@material-ui/icons/ArrowLeft';
import EditIcon from '@material-ui/icons/Edit';
import { ReactComponent as CompletePayment } from 'assets/icons/complete-payment.svg';
import { RecordForm } from 'app/shared/components/record-forms/FormWrapper';
import {
  GetHospitalsWithAdmin_hospitals_list,
  GetHospitalsWithAdmin_hospitals_list_capitationDetails,
  GetHospitalsWithAdmin_hospitals_list_capitationDetails_detailsByPlanType,
  GetHospitalsWithAdmin_hospitals_list_capitationDetails_transferFund,
} from 'graphql-types/GetHospitalsWithAdmin';
import SaveIcon from '@material-ui/icons/Save';
import moment from 'moment';
import ThemeContext from '@constant/themes';
import { useModal } from 'app/shared/hooks/ui/useModal';
import Prompter from 'app/shared/components/prompter/Prompter';
import Modal from 'app/shared/components/modal/Modal';
import EmailIcon from '@material-ui/icons/Email';
import PhoneIcon from '@material-ui/icons/Phone';
import { GetHmoCapitatedPaymentSummary } from 'graphql-types/GetHmoCapitatedPaymentSummary';
import { useMutation } from '@apollo/client';
import { SEND_CAPITATION_PAYMENT_WHATSAPP_NOTIFICATION } from 'apollo-queries/mains/hmo-providers';
import { useToasts } from 'react-toast-notifications';
import { convertToNumber } from '../../invoice/utils';
import { formatNumWithComma, toNaira } from '../../billings/constants';
import { FundTransactionStatus } from '../../../../../../graphql-types/globalTypes';
import { getUserPayload } from '../../../../../shared/utils/authentication';
import { usePrintPdf } from '../../../../../shared/hooks/forms/usePrintPdf';
import generateBankTransactionReceipt from '../../transactions/pdf-docs/bank-transaction-receipt';
import bulkBankTransactionReceipt, {
  BulkBankTransactionReceiptArg_items,
} from '../../transactions/pdf-docs/bulk-bank-transaction-receipt';
import { useFacilityPreference } from '../../settings/hooks/useFacilityPreference';
import {
  SendCapitationPaymentWhatsAppNotification,
  SendCapitationPaymentWhatsAppNotificationVariables,
} from '../../../../../../graphql-types/SendCapitationPaymentWhatsAppNotification';

interface IPayoutCapitationParam {
  hospitalIds: string[];
  transferFundId: string;
  totalCapitationAmount: number;
  enrolleeCount: number;
  totalAmountForPayout: number;
  payoutDecreasePercentage: number | null | undefined;
  planPercentageDecrease: Record<string, number | null | undefined> | null | undefined;
}

type Props = {
  items: GetHospitalsWithAdmin_hospitals_list[];
  onRefresh?: () => void;
  payoutCapitation?: (
    hospitalIds: string[],
    transferFundId: string,
    totalCapitationAmount: number,
    enrolleeCount: number,
    totalAmountForPayout: number,
    payoutDecreasePercentage: number | null | undefined,
    perPlanPayoutDecreasePercentage: Record<string, number | null | undefined> | null | undefined,
  ) => void;
  printPaymentAdvice?: (registeredWith: string) => void;
  handleBulkPrintPaymentAdvice?: (hid?: string) => void;
  handleTransactionReceiptPrint?: (id: string, transactionAmount: number) => unknown;
  handleBulkTransactionReceiptPrint?: () => unknown;
  loadingBankInformation?: boolean;
  payingOutCapitation?: boolean;
  handleCapitationPayoutApproval: (hospitalIds: string[], status: boolean) => void;
  handleBulkCapitationPayoutApproval: (status: boolean) => void;
  allPayoutApprovedBySelf: boolean;
  handleEnrolleeCountUpdate: (
    hospitalId: string,
    enrolleeCount: number,
    hmoPlanTypeId: string,
  ) => Promise<unknown>;
  currentPageNumber: number;
  maxRowCount: number;
  authorizeCapitationPayoutLoading: boolean;
  capitatedProvidersDataSummary?: {
    totalEnrolleeCapitationAmount: string;
    totalEnrolleeCount: string;
    totalCapitationAmount: string;
    totalAuthorizedAmount: string;
  };
  emailTransactionReceipt: (id: string, amount: number) => unknown;
  handleBulkEmailTransactionReceipt: () => unknown;
  getCapitatedPaymentSummaryData: GetHmoCapitatedPaymentSummary | null | undefined;
};

export default function CapitationPayoutTable({
  items,
  onRefresh = () => {},
  payoutCapitation = () => {},
  printPaymentAdvice = () => {},
  loadingBankInformation = false,
  payingOutCapitation = false,
  handleCapitationPayoutApproval,
  handleBulkCapitationPayoutApproval,
  allPayoutApprovedBySelf,
  handleEnrolleeCountUpdate,
  currentPageNumber,
  maxRowCount,
  handleBulkPrintPaymentAdvice,
  authorizeCapitationPayoutLoading,
  emailTransactionReceipt = () => {},
  handleBulkEmailTransactionReceipt = () => {},
  getCapitatedPaymentSummaryData,
}: Props) {
  const [isExpanded, setExpanded] = useState(true);
  const [sortColumn, setSortColumn] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc' | null>(null);
  const [sortedItems, setSortedItems] = useState(items);

  const { openPdf } = usePrintPdf();
  const theme = useContext(ThemeContext);
  const { facilityPreference } = useFacilityPreference();
  const isEnrolleeCapitationAmountPerPlan = !!facilityPreference?.enrolleeCapitationAmountPerPlan;

  const validItemsForBulkPrint = useMemo(
    () =>
      items.filter(
        (item) =>
          item.capitationDetails?.[0]?.transferFund &&
          item.capitationDetails?.[0]?.transferFund?.transferStatus ===
            FundTransactionStatus.Success,
      ),
    [items],
  );

  const { isShown: showConfirmationModal, toggle: toggleConfirmationModal } = useModal();
  const [payoutPayload, setPayoutPayload] = useState<IPayoutCapitationParam | null>(null);

  // Per-row percentage decrease state - key is row ID, value is percentage
  const [rowPercentageDecrease, setRowPercentageDecrease] = useState<
    Record<string, number | null | undefined>
  >({});

  // Per-plan percentage decrease state - key is "rowId___planType", value is percentage
  const [planPercentageDecrease, setPlanPercentageDecrease] = useState<
    Record<string, number | null | undefined>
  >({});

  const confirmationMessage =
    (payoutPayload?.hospitalIds?.length || 0) > 1
      ? 'Are you sure you want to bulk payment of $1?'
      : 'Are you sure you want to make payment of $1?';

  const resetPayoutPayload = () => {
    setPayoutPayload(null);
    toggleConfirmationModal();
  };

  // Function to apply sorting to data
  const applySortToData = useCallback(
    (data: typeof items, columnKey: string, direction: 'asc' | 'desc' | null) => {
      if (!direction) {
        setSortedItems(data);
        return;
      }

      if (columnKey === 'name') {
        const sorted = [...data].sort((a, b) => {
          const aName = a.name || '';
          const bName = b.name || '';

          return direction === 'asc' ? aName.localeCompare(bName) : bName.localeCompare(aName);
        });
        setSortedItems(sorted);
      } else {
        setSortedItems(data);
      }
    },
    [],
  );

  // Update sortedItems when items change, maintaining current sort
  useEffect(() => {
    applySortToData(items, sortColumn, sortDirection);
  }, [items, sortColumn, sortDirection, applySortToData]);

  // Handle sorting
  const handleSort = (columnKey: string, direction: 'asc' | 'desc' | null) => {
    setSortColumn(columnKey);
    setSortDirection(direction);
    applySortToData(items, columnKey, direction);
  };

  const handleTransactionReceiptPrint = useCallback(
    (d: GetHospitalsWithAdmin_hospitals_list) => {
      const tFund = d.capitationDetails?.[0].transferFund;
      if (!tFund) return;
      const { payoutCommissionPayer } = tFund;
      const enrolleeCount =
        tFund?.detailsByPlanType?.reduce((acc, curr) => acc + (curr?.enrolleeCount || 0), 0) ||
        tFund.enrolleeCount;
      const docDef = generateBankTransactionReceipt({
        transactionDate: moment(tFund.updatedDate).format('DD MMM, YYYY h:mm A'),
        transactionAmount:
          d.capitationDetails?.reduce(
            (acc, curr) => acc + convertToNumber(curr.transferFund?.amount),
            0,
          ) || 0,
        beneficiaryDetails: {
          bank: tFund?.destinationBankName as string,
          accountName: tFund?.destinationAccountName as string,
          accountNumber: tFund?.destinationAccountNumber as string,
        },
        senderDetails: {
          bank: 'WEMA BANK',
          accountName: tFund?.originatorName || 'CLINIFY',
        },
        referenceNumber: tFund?.transferReference as string,
        serviceChargeAmount: tFund?.serviceChargeAmount || 0,
        transactionRemark: tFund.narration as string,
        theme,
        scheme: tFund.hmoPlanType?.name,
        enrolleeCount,
        payoutCommissionPayer,
        enrolleeCommissionAmount: tFund.enrolleeCommissionAmount,
      });
      openPdf(docDef);
    },
    [openPdf, theme],
  );

  const handleBulkTransactionReceiptPrint = useCallback(() => {
    if (validItemsForBulkPrint.length === 0) return;

    const transactionItems: BulkBankTransactionReceiptArg_items[] = validItemsForBulkPrint
      .map((item) => {
        const tFund = item.capitationDetails?.[0].transferFund;
        if (!tFund) return null;

        return {
          transactionDate: moment(tFund.createdDate).format('DD MMM, YYYY h:mm A'),
          transactionAmount:
            item.capitationDetails?.reduce(
              (acc, curr) => acc + convertToNumber(curr.transferFund?.amount),
              0,
            ) || 0,
          beneficiaryDetails: {
            bank: tFund?.destinationBankName as string,
            accountName: tFund?.destinationAccountName as string,
            accountNumber: tFund?.destinationAccountNumber as string,
          },
          senderDetails: {
            bank: 'WEMA BANK',
            accountName: tFund?.originatorName || 'CLINIFY',
          },
          referenceNumber: tFund?.transferReference as string,
          serviceChargeAmount: tFund?.serviceChargeAmount || 0,
          transactionRemark: tFund.narration as string,
          scheme: tFund.hmoPlanType?.name,
          enrolleeCount: tFund.enrolleeCount,
        };
      })
      .filter(Boolean) as BulkBankTransactionReceiptArg_items[];

    if (transactionItems.length === 0) return;

    const docDef = bulkBankTransactionReceipt(transactionItems, theme);
    openPdf(docDef);
  }, [validItemsForBulkPrint, openPdf, theme]);

  const handlePayoutCapitation = useCallback(
    (params: IPayoutCapitationParam) => {
      setPayoutPayload(params);
      toggleConfirmationModal();
    },
    [toggleConfirmationModal],
  );

  const handleEmailTransactionReceipt = useCallback(
    (_item: GetHospitalsWithAdmin_hospitals_list) => {
      const tFund = _item.capitationDetails?.[0].transferFund;
      if (!tFund?.id) return;

      const transactionAmount =
        _item.capitationDetails?.reduce(
          (acc, curr) => acc + convertToNumber(curr.transferFund?.amount),
          0,
        ) || 0;

      emailTransactionReceipt(tFund.id, transactionAmount);
    },
    [openPdf, theme],
  );

  const columns = useMemo(() => {
    return getColumns(
      onRefresh,
      handlePayoutCapitation,
      printPaymentAdvice,
      loadingBankInformation,
      payingOutCapitation,
      handleCapitationPayoutApproval,
      handleBulkCapitationPayoutApproval,
      allPayoutApprovedBySelf,
      handleTransactionReceiptPrint,
      handleBulkTransactionReceiptPrint,
      undefined,
      handleEnrolleeCountUpdate,
      isExpanded,
      setExpanded,
      authorizeCapitationPayoutLoading,
      handleEmailTransactionReceipt,
      handleBulkEmailTransactionReceipt,
      handleBulkPrintPaymentAdvice,
      getCapitatedPaymentSummaryData,
      isEnrolleeCapitationAmountPerPlan,
      setPayoutPayload,
      payoutPayload,
      rowPercentageDecrease,
      setRowPercentageDecrease,
      planPercentageDecrease,
      setPlanPercentageDecrease,
    );
  }, [
    onRefresh,
    handlePayoutCapitation,
    printPaymentAdvice,
    loadingBankInformation,
    payingOutCapitation,
    handleCapitationPayoutApproval,
    handleBulkCapitationPayoutApproval,
    allPayoutApprovedBySelf,
    handleTransactionReceiptPrint,
    handleBulkTransactionReceiptPrint,
    handleEnrolleeCountUpdate,
    isExpanded,
    setExpanded,
    handleBulkPrintPaymentAdvice,
    handleEmailTransactionReceipt,
    handleBulkEmailTransactionReceipt,
    getCapitatedPaymentSummaryData?.getHmoCapitatedPaymentSummary,
    rowPercentageDecrease,
    setRowPercentageDecrease,
    planPercentageDecrease,
    setPlanPercentageDecrease,
  ]);
  return (
    <RecordForm contentClassName="fullwidth-record-form">
      <div style={{ overflowX: 'auto' }}>
        <div className="flex flex-column" style={{ minWidth: isExpanded ? 2600 : 1400 }}>
          <PlainTable
            showSerial
            columns={columns}
            data={sortedItems}
            noColoredHeader
            fullWidth
            startSerial={(!currentPageNumber ? 0 : currentPageNumber - 1) * maxRowCount}
            sortColumn={sortColumn}
            sortDirection={sortDirection}
            onSort={handleSort}
          />
          <Modal
            modalContent={
              <Prompter
                text={useMemo(() => {
                  const calculatedAmount = formatMoney(
                    toNaira(
                      convertToNumber(
                        (payoutPayload?.totalAmountForPayout || 0) *
                          (payoutPayload?.payoutDecreasePercentage
                            ? (100 - payoutPayload?.payoutDecreasePercentage) / 100
                            : 1),
                      ),
                    ),
                  );
                  return confirmationMessage?.replace('$1', calculatedAmount);
                }, [
                  confirmationMessage,
                  payoutPayload?.totalAmountForPayout,
                  payoutPayload?.payoutDecreasePercentage,
                ])}
                actionText="Yes"
                cancelText="No"
                deleteAction={() => {
                  if (payoutPayload?.hospitalIds?.length) {
                    payoutCapitation(
                      payoutPayload.hospitalIds,
                      payoutPayload.transferFundId,
                      payoutPayload.totalCapitationAmount,
                      payoutPayload.enrolleeCount,
                      payoutPayload.totalAmountForPayout *
                        (payoutPayload?.payoutDecreasePercentage
                          ? (100 - payoutPayload?.payoutDecreasePercentage) / 100
                          : 1),
                      payoutPayload?.payoutDecreasePercentage,
                      payoutPayload?.planPercentageDecrease,
                    );
                    resetPayoutPayload();
                  }
                }}
                cancelAction={() => {
                  resetPayoutPayload();
                }}
                disabled={false}
                cancelButtonClassName="min-width-unset with-no-extra-padding"
              />
            }
            isShown={showConfirmationModal}
            hide={resetPayoutPayload}
            handleDone={resetPayoutPayload}
            isAuthentication
          />
        </div>
      </div>
    </RecordForm>
  );
}

const EnrolleeCountCell = React.memo(
  ({
    singleData,
    row,
    updateEnrolleeCount,
  }: {
    singleData: any;
    row: any;
    updateEnrolleeCount: any;
  }) => {
    const [isEdit, setIsEdit] = useState(false);
    const [inputValue, setInputValue] = useState(
      singleData?.transferFund?.enrolleeCount || singleData?.enrolleeCount,
    );
    const hospitalId = row.id;
    const count = singleData.enrolleeCount;
    const isPaid = singleData?.transferFund?.transferStatus === FundTransactionStatus.Success;

    const startEditing = () => {
      if (isPaid) return;
      setInputValue(singleData?.transferFund?.enrolleeCount || count.toString());
      setIsEdit(true);
    };

    const handleInputChange = ({ target: { value } }) => {
      setInputValue(value);
    };

    const finishEditing = () => {
      updateEnrolleeCount(hospitalId, convertToNumber(inputValue));
      setIsEdit(false);
    };

    const exitEditing = () => {
      setInputValue(singleData?.transferFund?.enrolleeCount || count.toString());
      setIsEdit(false);
    };

    return !isEdit ? (
      <div className="flex flex-row justify-content-between">
        <span>{formatNumWithComma(inputValue)}</span>
        <EditIcon
          onClick={startEditing}
          className="cursor-pointer"
          color={isPaid ? 'disabled' : undefined}
        />
      </div>
    ) : (
      <div className="flex flex-row justify-content-between">
        <input
          onChange={handleInputChange}
          value={inputValue}
          onBlur={finishEditing}
          onKeyDown={(e) => {
            if (e.key === 'Escape') {
              exitEditing();
            }
            if (e.key === 'Enter') {
              finishEditing();
            }
          }}
          style={{ border: 0, width: '100%' }}
          autoFocus
          disabled={isPaid}
        />
        <span>
          <SaveIcon onClick={finishEditing} className="cursor-pointer" />
        </span>
      </div>
    );
  },
);

const EnrolleeCountCellForPerPlan = React.memo(
  ({
    singleData,
    row,
    updateEnrolleeCount,
    transferFund,
  }: {
    singleData: GetHospitalsWithAdmin_hospitals_list_capitationDetails_detailsByPlanType;
    row: GetHospitalsWithAdmin_hospitals_list;
    updateEnrolleeCount?: (
      hospitalId: string,
      enrolleeCount: number,
      hmoPlanTypeId: string,
    ) => Promise<unknown>;
    transferFund?: GetHospitalsWithAdmin_hospitals_list_capitationDetails_transferFund | null;
  }) => {
    const [isEdit, setIsEdit] = useState(false);
    const enrolleeCountFromTransferFund = useMemo(
      () =>
        transferFund?.detailsByPlanType?.find(
          ({ planTypeName }) => planTypeName === singleData.planTypeName,
        )?.enrolleeCount,
      [transferFund?.detailsByPlanType, singleData.planTypeName],
    );
    const [inputValue, setInputValue] = useState<string | number>(
      enrolleeCountFromTransferFund ?? singleData?.enrolleeCount,
    );
    const hospitalId = row.id;
    const count = singleData.enrolleeCount;
    const isPaid = transferFund?.transferStatus === FundTransactionStatus.Success;

    const startEditing = () => {
      if (isPaid) return;
      setInputValue(enrolleeCountFromTransferFund ?? count.toString());
      setIsEdit(true);
    };

    const handleInputChange = ({ target: { value } }) => {
      setInputValue(value);
    };

    const finishEditing = () => {
      updateEnrolleeCount?.(hospitalId, convertToNumber(inputValue), singleData.planType);
      setIsEdit(false);
    };

    const exitEditing = () => {
      setInputValue(enrolleeCountFromTransferFund ?? count.toString());
      setIsEdit(false);
    };

    return !isEdit ? (
      <div className="flex flex-row justify-content-between">
        <span>{formatNumWithComma(inputValue)}</span>
        <EditIcon
          onClick={startEditing}
          className="cursor-pointer"
          color={isPaid ? 'disabled' : undefined}
        />
      </div>
    ) : (
      <div className="flex flex-row justify-content-between">
        <input
          onChange={handleInputChange}
          value={inputValue}
          onBlur={finishEditing}
          onKeyDown={(e) => {
            if (e.key === 'Escape') {
              exitEditing();
            }
            if (e.key === 'Enter') {
              finishEditing();
            }
          }}
          style={{ border: 0, width: '100%' }}
          autoFocus
          disabled={isPaid}
        />
        <span>
          <SaveIcon onClick={finishEditing} className="cursor-pointer" />
        </span>
      </div>
    );
  },
);

function PlanPayoutDecreasePercentageCell({
  singleData,
  row,
  planPercentageDecrease,
  setPlanPercentageDecrease,
  transferFund,
}: {
  singleData: GetHospitalsWithAdmin_hospitals_list_capitationDetails_detailsByPlanType;
  row: GetHospitalsWithAdmin_hospitals_list;
  planPercentageDecrease: Record<string, number | null | undefined>;
  setPlanPercentageDecrease: React.Dispatch<
    React.SetStateAction<Record<string, number | null | undefined>>
  >;
  transferFund?: GetHospitalsWithAdmin_hospitals_list_capitationDetails_transferFund | null;
}) {
  const [isEdit, setIsEdit] = useState(false);
  const planKey = `${row.id}___${singleData.planType}`;

  // Get percentage from transfer fund or state
  const transferFundPercentage = useMemo(
    () =>
      transferFund?.detailsByPlanType?.find(
        ({ planTypeName }) => planTypeName === singleData.planTypeName,
      )?.payoutDecreasePercentage,
    [transferFund?.detailsByPlanType, singleData.planTypeName],
  );

  const currentPercentage =
    transferFundPercentage ??
    planPercentageDecrease[planKey] ??
    singleData.payoutDecreasePercentage;

  const [inputValue, setInputValue] = useState<string | number | null | undefined>(
    currentPercentage,
  );
  const isPaid = transferFund?.transferStatus === FundTransactionStatus.Success;

  const startEditing = () => {
    if (isPaid) return;
    setInputValue(currentPercentage?.toString());
    setIsEdit(true);
  };

  const handleInputChange = ({ target: { value } }) => {
    setInputValue(value);
  };

  const finishEditing = () => {
    const newPercentage = convertToNumber(inputValue);
    setPlanPercentageDecrease((prev) => ({
      ...prev,
      [planKey]: newPercentage,
    }));
    setIsEdit(false);
  };

  const exitEditing = () => {
    setInputValue(currentPercentage?.toString());
    setIsEdit(false);
  };

  return !isEdit ? (
    <div className="flex flex-row justify-content-between">
      <span>{inputValue ?? '--'}%</span>
      <EditIcon
        onClick={startEditing}
        className="cursor-pointer"
        color={isPaid ? 'disabled' : undefined}
      />
    </div>
  ) : (
    <div className="flex flex-row justify-content-between">
      <input
        onChange={handleInputChange}
        value={inputValue as any}
        onBlur={finishEditing}
        onKeyDown={(e) => {
          if (e.key === 'Escape') {
            exitEditing();
          }
          if (e.key === 'Enter') {
            finishEditing();
          }
        }}
        style={{ border: 0, width: '100%' }}
        autoFocus
        disabled={isPaid}
        type="number"
        min="0"
        max="100"
        step="0.01"
      />
      <span>
        <SaveIcon onClick={finishEditing} className="cursor-pointer" />
      </span>
    </div>
  );
}

function PayoutDecreasePercentageCell({
  data,
  row,
  rowPercentageDecrease,
  setRowPercentageDecrease,
}: {
  data: GetHospitalsWithAdmin_hospitals_list_capitationDetails[];
  row: GetHospitalsWithAdmin_hospitals_list;
  rowPercentageDecrease: Record<string, number | null | undefined>;
  setRowPercentageDecrease: React.Dispatch<
    React.SetStateAction<Record<string, number | null | undefined>>
  >;
}) {
  const [isEdit, setIsEdit] = useState(false);
  const transferFund = data?.[0]?.transferFund;

  // Determine current payout decrease percentage based on transfer fund status, row percentage, or default value
  const currentPercentage =
    transferFund?.payoutDecreasePercentage ??
    rowPercentageDecrease[row.id] ??
    (transferFund?.transferStatus !== 'Success' ? data?.[0]?.payoutDecreasePercentage : null);

  const [inputValue, setInputValue] = useState<string | number | null | undefined>(
    currentPercentage,
  );
  const isPaid = transferFund?.transferStatus === FundTransactionStatus.Success;

  const startEditing = () => {
    if (isPaid) return;
    setInputValue(currentPercentage?.toString());
    setIsEdit(true);
  };

  const handleInputChange = ({ target: { value } }) => {
    setInputValue(value);
  };

  const finishEditing = () => {
    const newPercentage = convertToNumber(inputValue);
    setRowPercentageDecrease((prev) => ({
      ...prev,
      [row.id]: newPercentage,
    }));
    setIsEdit(false);
  };

  const exitEditing = () => {
    setInputValue(currentPercentage?.toString());
    setIsEdit(false);
  };

  return !isEdit ? (
    <div className="flex flex-row justify-content-between">
      <span>{inputValue ?? '--'}%</span>
      <EditIcon
        onClick={startEditing}
        className="cursor-pointer"
        color={isPaid ? 'disabled' : undefined}
      />
    </div>
  ) : (
    <div className="flex flex-row justify-content-between">
      <input
        onChange={handleInputChange}
        value={inputValue as any}
        onBlur={finishEditing}
        onKeyDown={(e) => {
          if (e.key === 'Escape') {
            exitEditing();
          }
          if (e.key === 'Enter') {
            finishEditing();
          }
        }}
        style={{ border: 0, width: '100%' }}
        autoFocus
        disabled={isPaid}
        type="number"
        min="0"
        max="100"
        step="0.01"
      />
      <span>
        <SaveIcon onClick={finishEditing} className="cursor-pointer" />
      </span>
    </div>
  );
}

function getColumns(
  onRefresh: () => void,
  payoutCapitation: (params: IPayoutCapitationParam) => void,
  printPaymentAdvice: (registeredWith: string) => void,
  loadingBankInformation: boolean,
  payingOutCapitation: boolean,
  handleCapitationPayoutApproval: (hospitalIds: string[], status: boolean) => void,
  handleBulkCapitationPayoutApproval: (status: boolean) => void,
  allPayoutApprovedBySelf: boolean,
  handleTransactionReceiptPrint: (row: any) => unknown,
  handleBulkTransactionReceiptPrint: () => unknown,
  capitationFeeAmount: number | undefined,
  handleEnrolleeCountUpdate: (
    hospitalId: string,
    enrolleeCount: number,
    hmoPlanTypeId: string,
  ) => Promise<unknown>,
  expanded: boolean,
  setExpanded: (expanded: boolean) => void = () => {},
  authorizeCapitationPayoutLoading: boolean,
  handleEmailTransactionReceipt: (row: any) => unknown,
  handleBulkEmailTransactionReceipt: () => unknown,
  handleBulkPrintPaymentAdvice: (hid?: string) => void = () => {},
  getCapitatedPaymentSummaryData: GetHmoCapitatedPaymentSummary | null | undefined,
  isEnrolleeCapitationAmountPerPlan: boolean,
  setPayoutDetail: React.Dispatch<React.SetStateAction<IPayoutCapitationParam | null>>,
  payoutDetail: IPayoutCapitationParam | null,
  rowPercentageDecrease: Record<string, number | null | undefined>,
  setRowPercentageDecrease: React.Dispatch<
    React.SetStateAction<Record<string, number | null | undefined>>
  >,
  planPercentageDecrease: Record<string, number | null | undefined>,
  setPlanPercentageDecrease: React.Dispatch<
    React.SetStateAction<Record<string, number | null | undefined>>
  >,
): PlainTableColumns {
  const widths: string[] = expanded
    ? isEnrolleeCapitationAmountPerPlan
      ? [
          '10%',
          '14%',
          '5%',
          '6%',
          '6%',
          '3%',
          '8%',
          '3%',
          '13%',
          '3%',
          '7%',
          '7%',
          '7%',
          '2%',
          '2%',
          '2%',
          '2%',
        ]
      : [
          '13%',
          '5%',
          '8.5%',
          '8.5%',
          '8.5%',
          '4.5%',
          '12%',
          '3%',
          '7.5%',
          '7.5%',
          '7.5%',
          '1.5%',
          '1.5%',
          '1.5%',
          '1.5%',
        ]
    : ['25%', '12%', '18%', '18%', '23%'];
  const { profileId } = getUserPayload();
  return [
    {
      key: 'name',
      title: (
        <div className="flex flex-column">
          <span>Provider Name</span>
          <span>Provider Email</span>
          <span>Provider Contact</span>
          <span>Provider Address</span>
        </div>
      ),
      style: { width: widths[0] },
      sortable: true,
      CustomElement({ data, row }) {
        return (
          <div className="flex flex-column">
            <span>{data}</span>
            <span>{row?.supportMail || '--'}</span>
            <span>{row?.hospitalSupportPhoneNumber?.value || '--'}</span>
            <span>{row?.address || '--'}</span>
          </div>
        );
      },
    },
    ...(isEnrolleeCapitationAmountPerPlan
      ? [
          {
            key: 'capitationDetails',
            title: 'Plan Type',
            style: { width: widths[1] },
            CustomElement({
              data,
            }: {
              data: GetHospitalsWithAdmin_hospitals_list['capitationDetails'];
              row: GetHospitalsWithAdmin_hospitals_list;
            }) {
              return (
                <div className="flex flex-column">
                  {data?.[0]?.detailsByPlanType?.map(({ planTypeName }, idx) => (
                    <span
                      key={idx}
                      style={{
                        lineHeight: '1.5rem',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {planTypeName}
                    </span>
                  ))}
                  <span style={{ height: '1.5rem' }} />
                </div>
              );
            },
            showPrefixRow: true,
            customPrefixValue: () =>
              getCapitatedPaymentSummaryData?.getHmoCapitatedPaymentSummary?.map(
                ({ planTypeName }) => (
                  <>
                    <span className="red-text">{planTypeName}</span>
                    <br />
                  </>
                ),
              ),
          },
        ]
      : []),
    ...(isEnrolleeCapitationAmountPerPlan
      ? [
          {
            key: 'capitationDetails',
            title: 'Enrollee Count',
            style: { width: widths[2] },
            CustomElement({
              data,
              row,
            }: {
              data: GetHospitalsWithAdmin_hospitals_list['capitationDetails'];
              row: GetHospitalsWithAdmin_hospitals_list;
            }) {
              const transferFund = data?.[0]?.transferFund;
              let _detailsByPlanType = data?.[0]?.detailsByPlanType || [];

              if (
                Array.isArray(transferFund?.detailsByPlanType) &&
                transferFund?.detailsByPlanType?.length
              ) {
                _detailsByPlanType = transferFund.detailsByPlanType;
              }

              const totalCount =
                _detailsByPlanType?.reduce((acc, curr) => acc + curr.enrolleeCount || 0, 0) || 0;

              return (
                <>
                  {data?.[0]?.detailsByPlanType?.map((singleData, idx) => (
                    <EnrolleeCountCellForPerPlan
                      key={idx}
                      singleData={singleData}
                      row={row}
                      updateEnrolleeCount={handleEnrolleeCountUpdate}
                      transferFund={transferFund}
                    />
                  )) || <span>--</span>}

                  <span className="red-text">{totalCount || '--'}</span>
                </>
              );
            },
            showSum: true,
            customSumValue: (data: GetHospitalsWithAdmin_hospitals_list[]) => {
              const val = data?.reduce((acc, curr) => {
                return (
                  acc +
                  (curr?.capitationDetails?.[0]?.detailsByPlanType?.reduce((a, c) => {
                    const transferFundEnrolleeCount = curr?.capitationDetails?.[0]?.transferFund?.detailsByPlanType?.find(
                      ({ planTypeName }) => c.planTypeName === planTypeName,
                    )?.enrolleeCount;
                    return a + convertToNumber(transferFundEnrolleeCount || c.enrolleeCount);
                  }, 0) || 0)
                );
              }, 0);
              return <span className="red-text">{val}</span>;
            },
            showPrefixRow: true,
            customPrefixValue: () =>
              getCapitatedPaymentSummaryData?.getHmoCapitatedPaymentSummary?.map(
                ({ totalEnrollees }) => (
                  <>
                    <span className="red-text">{totalEnrollees || '0'}</span>
                    <br />
                  </>
                ),
              ),
          },
        ]
      : [
          {
            key: 'capitationDetails',
            title: 'Enrollee Count',
            style: { width: widths[1] },

            CustomElement({ data, row }) {
              return (
                data?.map((singleData, idx) => (
                  <EnrolleeCountCell
                    key={idx}
                    singleData={singleData}
                    row={row}
                    updateEnrolleeCount={handleEnrolleeCountUpdate}
                  />
                )) || <span>--</span>
              );
            },
            showSum: true,
            customSumValue: (data) => {
              const val = data?.reduce((acc, curr) => {
                const count =
                  curr.capitationDetails?.reduce(
                    (acc, curr) =>
                      acc +
                      convertToNumber(curr?.transferFund?.enrolleeCount || curr?.enrolleeCount),
                    0,
                  ) || curr?.capitationDetails?.enrolleeCount;
                return acc + convertToNumber(count);
              }, 0);
              return <span className="red-text">{val}</span>;
            },
            showPrefixRow: true,
            customPrefixValue: () =>
              getCapitatedPaymentSummaryData?.getHmoCapitatedPaymentSummary?.map(
                ({ totalEnrollees }) => (
                  <>
                    <span className="red-text">{totalEnrollees || '0'}</span>
                    <br />
                  </>
                ),
              ),
          },
        ]),
    ...(isEnrolleeCapitationAmountPerPlan
      ? [
          {
            key: 'capitationDetails',
            title: 'Enrollee Capitation Amount',
            style: { width: widths[3] },
            CustomElement({
              data,
            }: {
              data: GetHospitalsWithAdmin_hospitals_list_capitationDetails[];
            }) {
              const values =
                data?.[0]?.detailsByPlanType?.map?.(({ enrolleeCount, totalCapitationAmount }) => {
                  return (
                    totalCapitationAmount ||
                    convertToNumber(capitationFeeAmount) * convertToNumber(enrolleeCount)
                  );
                }) || [];
              const totalAmount = values?.reduce((acc, curr) => acc + convertToNumber(curr), 0);
              return (
                <div className="flex flex-column">
                  {values?.map?.((_totalCapitationAmount, idx) => {
                    return (
                      <span key={idx} style={{ lineHeight: '1.5rem' }}>
                        {formatMoney(toNaira(_totalCapitationAmount) || 0)}
                      </span>
                    );
                  })}
                  <span className="red-text">{formatMoney(toNaira(totalAmount) || 0)}</span>
                </div>
              );
            },
            showSum: true,
            customSumValue: (data: GetHospitalsWithAdmin_hospitals_list[]) => {
              const val = data?.reduce((acc, curr) => {
                return (
                  acc +
                  (curr?.capitationDetails?.[0]?.detailsByPlanType?.reduce((a, c) => {
                    const _totalCapitationAmount =
                      c.totalCapitationAmount ||
                      convertToNumber(capitationFeeAmount) * convertToNumber(c.enrolleeCount);
                    return a + convertToNumber(_totalCapitationAmount);
                  }, 0) || 0)
                );
              }, 0);

              return <span className="red-text">{formatMoney(toNaira(val))}</span>;
            },
            showPrefixRow: true,
            customPrefixValue: () =>
              getCapitatedPaymentSummaryData?.getHmoCapitatedPaymentSummary?.map(
                ({ enrolleeCapitationAmount }) => (
                  <>
                    <span className="red-text">
                      {formatMoney(toNaira(enrolleeCapitationAmount || 0))}
                    </span>
                    <br />
                  </>
                ),
              ),
          },
          {
            key: 'capitationDetails',
            title: 'Total Capitation Amount',
            style: { width: widths[4] },
            CustomElement({
              data,
              row,
            }: {
              data: GetHospitalsWithAdmin_hospitals_list_capitationDetails[];
              row: GetHospitalsWithAdmin_hospitals_list;
            }) {
              const transferFund = data?.[0]?.transferFund;
              const values =
                data?.[0]?.detailsByPlanType?.map(
                  ({
                    enrolleeCount,
                    totalCapitationAmount,
                    planTypeName,
                    planType,
                    payoutDecreasePercentage: _payoutDecreasePercentage,
                  }) => {
                    const transferFundEnrolleeCount = transferFund?.detailsByPlanType?.find(
                      ({ planTypeName: pt }) => pt === planTypeName,
                    )?.enrolleeCount;
                    const _enrolleeCount = transferFundEnrolleeCount ?? enrolleeCount ?? 0;

                    // Get plan-specific percentage decrease
                    const planKey = `${row.id}___${planType}`;
                    const agreedPayoutDecreasePercentage =
                      planPercentageDecrease[planKey] ?? _payoutDecreasePercentage;
                    const transferFundPercentage = transferFund?.detailsByPlanType?.find(
                      ({ planTypeName: pt }) => pt === planTypeName,
                    )?.payoutDecreasePercentage;
                    const planPercentage = transferFundPercentage ?? agreedPayoutDecreasePercentage;

                    const baseAmount =
                      convertToNumber(totalCapitationAmount) * convertToNumber(_enrolleeCount);
                    const _totalCapitationAmount = planPercentage
                      ? baseAmount * ((100 - planPercentage) / 100)
                      : baseAmount;
                    return _totalCapitationAmount;
                  },
                ) || [];
              const totalAmount = values?.reduce((acc, curr) => acc + convertToNumber(curr), 0);
              return (
                <div className="flex flex-column">
                  {values?.map((_totalCapitationAmount, idx) => {
                    return (
                      <span key={idx} style={{ lineHeight: '1.5rem' }}>
                        {formatMoney(toNaira(_totalCapitationAmount) || 0)}
                      </span>
                    );
                  })}
                  <span className="red-text">{formatMoney(toNaira(totalAmount) || 0)}</span>
                </div>
              );
            },
            showSum: true,
            customSumValue: (data: GetHospitalsWithAdmin_hospitals_list[]) => {
              const val = data?.reduce((acc, curr) => {
                const transferFund = curr?.capitationDetails?.[0]?.transferFund;
                return (
                  acc +
                  (curr?.capitationDetails?.[0]?.detailsByPlanType?.reduce((a, c) => {
                    const transferFundEnrolleeCount = transferFund?.detailsByPlanType?.find(
                      ({ planTypeName: pt }) => pt === c.planTypeName,
                    )?.enrolleeCount;
                    const _enrolleeCount = transferFundEnrolleeCount ?? c.enrolleeCount ?? 0;

                    // Get plan-specific percentage decrease
                    const planKey = `${curr.id}___${c.planType}`;
                    const agreedPayoutDecreasePercentage =
                      planPercentageDecrease[planKey] ?? c.payoutDecreasePercentage;
                    const transferFundPercentage = transferFund?.detailsByPlanType?.find(
                      ({ planTypeName: pt }) => pt === c.planTypeName,
                    )?.payoutDecreasePercentage;
                    const planPercentage = transferFundPercentage ?? agreedPayoutDecreasePercentage;

                    const baseAmount =
                      convertToNumber(c.totalCapitationAmount) * convertToNumber(_enrolleeCount);
                    const _totalCapitationAmount = planPercentage
                      ? baseAmount * ((100 - planPercentage) / 100)
                      : baseAmount;
                    return a + _totalCapitationAmount;
                  }, 0) || 0)
                );
              }, 0);
              return <span className="red-text">{formatMoney(toNaira(val))}</span>;
            },
            showPrefixRow: true,
            customPrefixValue: () =>
              getCapitatedPaymentSummaryData?.getHmoCapitatedPaymentSummary?.map(
                ({ totalCapitationAmount }) => (
                  <>
                    <span className="red-text">
                      {formatMoney(toNaira(totalCapitationAmount || 0))}
                    </span>
                    <br />
                  </>
                ),
              ),
          },
          {
            key: 'capitationDetails',
            title: 'Plan Percentage Decrease',
            style: { width: widths[5] },
            CustomElement({
              data,
              row,
            }: {
              data: GetHospitalsWithAdmin_hospitals_list['capitationDetails'];
              row: GetHospitalsWithAdmin_hospitals_list;
            }) {
              const transferFund = data?.[0]?.transferFund;
              return (
                <div className="flex flex-column">
                  {data?.[0]?.detailsByPlanType?.map((singleData, idx) => (
                    <PlanPayoutDecreasePercentageCell
                      key={idx}
                      singleData={singleData}
                      row={row}
                      planPercentageDecrease={planPercentageDecrease}
                      setPlanPercentageDecrease={setPlanPercentageDecrease}
                      transferFund={transferFund}
                    />
                  )) || <span>--</span>}
                  <span style={{ height: '1.5rem' }} />
                </div>
              );
            },
          },
        ]
      : [
          {
            key: 'capitationDetails',
            title: 'Enrollee Capitation Amount',
            style: { width: widths[2] },
            CustomElement({ data }) {
              return (
                <div className="flex flex-column">
                  {data?.map(({ enrolleeCount, totalCapitationAmount }, idx) => {
                    const _totalCapitationAmount =
                      totalCapitationAmount ||
                      convertToNumber(capitationFeeAmount) * convertToNumber(enrolleeCount);
                    return (
                      <span key={idx}>{formatMoney(toNaira(_totalCapitationAmount) || 0)}</span>
                    );
                  })}
                </div>
              );
            },
            showSum: true,
            customSumValue: (data) => {
              const val = data?.reduce((acc, curr) => {
                const amount = convertToNumber(curr?.capitationDetails?.[0]?.totalCapitationAmount);
                return acc + amount;
              }, 0);

              return <span className="red-text">{formatMoney(toNaira(val))}</span>;
            },
            showPrefixRow: true,
            customPrefixValue: () =>
              getCapitatedPaymentSummaryData?.getHmoCapitatedPaymentSummary?.map(
                ({ enrolleeCapitationAmount }) => (
                  <>
                    <span className="red-text">
                      {formatMoney(toNaira(enrolleeCapitationAmount || 0))}
                    </span>
                    <br />
                  </>
                ),
              ),
          },
          {
            key: 'capitationDetails',
            title: 'Total Capitation Amount',
            style: { width: widths[3] },
            CustomElement({ data }) {
              return (
                <div className="flex flex-column">
                  {data?.map(({ enrolleeCount, totalCapitationAmount, transferFund }, idx) => {
                    const _enrolleeCount = transferFund?.enrolleeCount || enrolleeCount || 0;
                    const _totalCapitationAmount =
                      convertToNumber(totalCapitationAmount) * convertToNumber(_enrolleeCount);
                    return (
                      <span key={idx}>{formatMoney(toNaira(_totalCapitationAmount) || 0)}</span>
                    );
                  })}
                </div>
              );
            },
            showSum: true,
            customSumValue: (data) => {
              const val = data?.reduce((acc, curr) => {
                const _enrolleeCount =
                  curr?.capitationDetails?.[0]?.transferFund?.enrolleeCount ||
                  curr?.capitationDetails?.[0]?.enrolleeCount ||
                  0;
                const amount =
                  convertToNumber(curr?.capitationDetails?.[0]?.totalCapitationAmount) *
                  convertToNumber(_enrolleeCount);
                return acc + amount;
              }, 0);
              return <span className="red-text">{formatMoney(toNaira(val))}</span>;
            },
            showPrefixRow: true,
            customPrefixValue: () =>
              getCapitatedPaymentSummaryData?.getHmoCapitatedPaymentSummary?.map(
                ({ totalCapitationAmount }) => (
                  <>
                    <span className="red-text">
                      {formatMoney(toNaira(totalCapitationAmount || 0))}
                    </span>
                    <br />
                  </>
                ),
              ),
          },
        ]),
    {
      key: 'capitationDetails',
      style: { width: isEnrolleeCapitationAmountPerPlan ? widths[6] : widths[4] },
      title: (
        <div className="flex flex-column">
          <div className="flex flex-column align-items-end">
            {expanded ? (
              <div onClick={() => setExpanded(false)} className="cursor-pointer">
                <ArrowLeftIcon />
              </div>
            ) : (
              <div onClick={() => setExpanded(true)}>
                <ArrowRightIcon />
              </div>
            )}
          </div>
          <div className="flex flex-row gap-8">
            <input
              type="checkbox"
              checked={allPayoutApprovedBySelf}
              onChange={({ target: { checked } }) => handleBulkCapitationPayoutApproval?.(checked)}
              disabled={authorizeCapitationPayoutLoading}
            />
            <span>Authorize Payout Amount</span>
          </div>
        </div>
      ),
      CustomElement({
        data,
        row,
      }: {
        data: GetHospitalsWithAdmin_hospitals_list_capitationDetails[];
        [key: string]: any;
      }) {
        const confirmed = data?.some(({ transferFund }) =>
          transferFund?.auditApproval?.some(({ creatorId }) => profileId === creatorId),
        );
        return (
          <div className="flex flex-row gap-8">
            <input
              type="checkbox"
              checked={confirmed}
              onChange={({ target: { checked } }) =>
                handleCapitationPayoutApproval?.([row.id], checked)
              }
              disabled={
                data?.every(({ transferFund }) => transferFund?.transferStatus === 'Success') ||
                authorizeCapitationPayoutLoading
              }
            />
            <div className="flex flex-column">
              {data?.map(
                (
                  {
                    transferFund,
                    detailsByPlanType,
                    totalCapitationAmount,
                    enrolleeCount,
                    payoutDecreasePercentage,
                  },
                  idx,
                ) => {
                  const planByTypes = (detailsByPlanType || []).map((v) => {
                    const overide = transferFund?.detailsByPlanType?.find(
                      ({ planType }) => planType === v.planType,
                    );
                    return {
                      ...v,
                      ...overide,
                    };
                  });
                  const rowDecreasePercentage =
                    rowPercentageDecrease[row.id] ?? payoutDecreasePercentage;
                  const _totalAmount =
                    (planByTypes?.reduce((acc, curr) => {
                      // Get plan-specific percentage decrease
                      const planKey = `${row.id}___${curr.planType}`;
                      const transferFundPercentage = transferFund?.detailsByPlanType?.find(
                        ({ planType }) => planType === curr.planType,
                      )?.payoutDecreasePercentage;
                      const agreedPayoutDecreasePercentage =
                        planPercentageDecrease[planKey] ?? curr.payoutDecreasePercentage;
                      const planPercentage =
                        transferFundPercentage ?? agreedPayoutDecreasePercentage;

                      const baseAmount = curr.enrolleeCount * curr.totalCapitationAmount;
                      const adjustedAmount = planPercentage
                        ? baseAmount * ((100 - planPercentage) / 100)
                        : baseAmount;
                      return acc + adjustedAmount;
                    }, 0) || 0) * (rowDecreasePercentage ? (100 - rowDecreasePercentage) / 100 : 1);
                  const _enrolleeCount = transferFund?.enrolleeCount || enrolleeCount || 0;
                  // Prevents it from using persisted totalCapitationAmount when payment is per plan
                  const _totalCapitationAmount = isEnrolleeCapitationAmountPerPlan
                    ? null
                    : convertToNumber(totalCapitationAmount) *
                      convertToNumber(_enrolleeCount) *
                      (rowDecreasePercentage ? (100 - rowDecreasePercentage) / 100 : 1);
                  return (
                    <span key={idx}>
                      {formatMoney(
                        toNaira(transferFund?.amount || _totalCapitationAmount || _totalAmount),
                      )}
                    </span>
                  );
                },
              )}
            </div>
          </div>
        );
      },
      showSum: true,
      customSumValue: (data: GetHospitalsWithAdmin_hospitals_list[]) => {
        const val = data?.reduce((acc, curr) => {
          const _enrolleeCount =
            curr?.capitationDetails?.[0]?.transferFund?.enrolleeCount ||
            curr?.capitationDetails?.[0]?.enrolleeCount ||
            0;
          const amount = isEnrolleeCapitationAmountPerPlan
            ? null
            : convertToNumber(curr?.capitationDetails?.[0]?.totalCapitationAmount) *
              convertToNumber(_enrolleeCount);

          const transferFundAmount = curr?.capitationDetails?.[0]?.transferFund?.amount;
          const planByTypes =
            curr?.capitationDetails?.[0]?.detailsByPlanType?.map((v) => {
              const overide = curr?.capitationDetails?.[0]?.transferFund?.detailsByPlanType?.find(
                ({ planType }) => planType === v.planType,
              );
              return {
                ...v,
                ...overide,
              };
            }) || [];

          // Get row-level percentage decrease
          const rowDecreasePercentage =
            rowPercentageDecrease[curr.id] ??
            curr?.capitationDetails?.[0]?.payoutDecreasePercentage;

          return (
            acc +
            (transferFundAmount ||
              (amount ?? 0) * (rowDecreasePercentage ? (100 - rowDecreasePercentage) / 100 : 1) ||
              (planByTypes?.reduce((a, c) => {
                // Get plan-specific percentage decrease
                const planKey = `${curr.id}___${c.planType}`;
                const agreedPayoutDecreasePercentage =
                  planPercentageDecrease[planKey] ?? c.payoutDecreasePercentage;
                const transferFundPercentage = curr?.capitationDetails?.[0]?.transferFund?.detailsByPlanType?.find(
                  ({ planType }) => planType === c.planType,
                )?.payoutDecreasePercentage;
                const planPercentage = transferFundPercentage ?? agreedPayoutDecreasePercentage;

                const baseAmount = c.enrolleeCount * c.totalCapitationAmount;
                const _totalAmount = planPercentage
                  ? baseAmount * ((100 - planPercentage) / 100)
                  : baseAmount;
                return a + _totalAmount;
              }, 0) || 0) * (rowDecreasePercentage ? (100 - rowDecreasePercentage) / 100 : 1))
          );
        }, 0);
        return <span className="red-text">{formatMoney(toNaira(val))}</span>;
      },
      showPrefixRow: true,
      customPrefixValue: () =>
        getCapitatedPaymentSummaryData?.getHmoCapitatedPaymentSummary?.map(
          ({ totalCapitationAmount }) => (
            <>
              <span className="red-text">{formatMoney(toNaira(totalCapitationAmount || 0))}</span>
              <br />
            </>
          ),
        ),
    },
    ...(expanded
      ? ([
          {
            key: 'capitationDetails',
            style: { width: isEnrolleeCapitationAmountPerPlan ? widths[7] : widths[5] },
            title: 'Percentage Decrease (%)',
            CustomElement: ({
              data,
              row,
            }: {
              data: GetHospitalsWithAdmin_hospitals_list_capitationDetails[];
              row: GetHospitalsWithAdmin_hospitals_list;
            }) => {
              return (
                <PayoutDecreasePercentageCell
                  data={data}
                  row={row}
                  rowPercentageDecrease={rowPercentageDecrease}
                  setRowPercentageDecrease={setRowPercentageDecrease}
                />
              );
            },
          },
          {
            key: 'preferredPayoutAccount',
            style: { width: isEnrolleeCapitationAmountPerPlan ? widths[8] : widths[6] },
            title: (
              <div className="flex flex-row align-items-center justify-content-between">
                <div className="flex flex-column">
                  <span>Payout Account Details</span>
                  <span>(Bank Name - Account Number - Account Name)</span>
                </div>
                <RefreshIcon
                  data-tooltip-content="Refetch Preferred Payout Bank Account"
                  data-tooltip-id="capitationPayoutTableTip"
                  onClick={onRefresh}
                  style={{ opacity: 0.5 }}
                  className={`cursor-pointer ${
                    loadingBankInformation ? 'refresh-icon-spinning' : ''
                  }`}
                />
              </div>
            ),
            CustomElement({ data, row }) {
              const disableAction = row.payoutStatus === 'paid';
              return (
                <div className="flex flex-row align-items-center justify-content-between">
                  <span>
                    {data?.bankName || ''} - {data?.accountNumber || ''} - {data?.accountName || ''}
                  </span>
                  <RefreshIcon
                    style={{ opacity: disableAction ? 0.5 : 1 }}
                    data-tooltip-content="Refetch Preferred Payout Bank Account"
                    data-tooltip-id="capitationPayoutTableTip"
                    onClick={disableAction ? undefined : () => onRefresh()}
                    className={`cursor-pointer ${
                      loadingBankInformation ? 'refresh-icon-spinning' : ''
                    }`}
                  />
                </div>
              );
            },
          },
          {
            key: 'capitationDetails',
            style: { width: isEnrolleeCapitationAmountPerPlan ? widths[9] : widths[7] },
            title: (
              <div>
                <CompletePayment
                  style={{
                    display: 'block',
                    margin: '0 auto',
                    opacity: 0.5,
                  }}
                  data-tooltip-content="Bulk Payment"
                  data-tooltip-id="capitationPayoutTableTip"
                  onClick={
                    payingOutCapitation
                      ? undefined
                      : () => {
                          // TODO
                        }
                  }
                  className="cursor-pointer"
                />
              </div>
            ),
            CustomElement({
              row,
              data,
            }: {
              data: GetHospitalsWithAdmin_hospitals_list_capitationDetails[];
              row: any;
            }) {
              let disableAction =
                data?.every(({ transferFund }) => transferFund?.transferStatus === 'Success') ||
                data?.every(({ transferFund }) => !transferFund?.id) ||
                payingOutCapitation ||
                (isEnrolleeCapitationAmountPerPlan
                  ? (data?.[0]?.detailsByPlanType?.reduce(
                      (acc, curr) => acc + curr.enrolleeCount,
                      0,
                    ) || 0) === 0
                  : false);
              return (
                <div>
                  {data?.map(
                    (
                      {
                        transferFund,
                        enrolleeCount,
                        totalCapitationAmount: capitationAmount,
                        detailsByPlanType,
                        payoutDecreasePercentage,
                      },
                      idx,
                    ) => {
                      const _enrolleeCount = transferFund?.enrolleeCount || enrolleeCount || 0;
                      const claimAdminApproval = transferFund?.auditApproval?.some(
                        ({ approvalGroup }) =>
                          ['OrganizationAdmin', 'ClaimAdmin'].includes(approvalGroup),
                      );
                      const claimAccountApproval = transferFund?.auditApproval?.some(
                        ({ approvalGroup }) => ['ClaimAccount'].includes(approvalGroup),
                      );
                      const planByTypes = (detailsByPlanType || []).map((v) => {
                        const overide = transferFund?.detailsByPlanType?.find(
                          ({ planType }) => planType === v.planType,
                        );
                        return {
                          ...v,
                          ...overide,
                        };
                      });
                      const _totalAmountForPerPlan =
                        planByTypes?.reduce((acc, curr) => {
                          // Get plan-specific percentage decrease
                          const planKey = `${row.id}___${curr.planType}`;
                          const transferFundPercentage = transferFund?.detailsByPlanType?.find(
                            ({ planType }) => planType === curr.planType,
                          )?.payoutDecreasePercentage;
                          const planPercentage =
                            transferFundPercentage ?? planPercentageDecrease[planKey];

                          const baseAmount = curr.enrolleeCount * curr.totalCapitationAmount;
                          const adjustedAmount = planPercentage
                            ? baseAmount * ((100 - planPercentage) / 100)
                            : baseAmount;
                          return acc + adjustedAmount;
                        }, 0) || 0;
                      const _totalAmountForNonPerPlan = ((): number => {
                        return (
                          convertToNumber(transferFund?.totalCapitationAmount) *
                          convertToNumber(transferFund?.enrolleeCount)
                        );
                      })();
                      const _totalAmount = isEnrolleeCapitationAmountPerPlan
                        ? _totalAmountForPerPlan
                        : _totalAmountForNonPerPlan;
                      disableAction = disableAction || !claimAdminApproval || !claimAccountApproval;
                      return (
                        <CompletePayment
                          key={idx}
                          style={{
                            display: 'block',
                            margin: '0 auto',
                            opacity: disableAction ? 0.5 : 1,
                          }}
                          data-tooltip-content="Make Payment"
                          data-tooltip-id="capitationPayoutTableTip"
                          onClick={
                            disableAction
                              ? undefined
                              : () =>
                                  payoutCapitation({
                                    hospitalIds: [row.id],
                                    transferFundId: transferFund?.id as string,
                                    totalCapitationAmount: capitationAmount,
                                    enrolleeCount: convertToNumber(_enrolleeCount),
                                    totalAmountForPayout: transferFund?.amount || _totalAmount,
                                    payoutDecreasePercentage:
                                      rowPercentageDecrease[row.id] ?? payoutDecreasePercentage,
                                    planPercentageDecrease: isEnrolleeCapitationAmountPerPlan
                                      ? planPercentageDecrease
                                      : null,
                                  })
                          }
                          className={classNames({
                            'cursor-pointer': !disableAction,
                          })}
                        />
                      );
                    },
                  )}
                </div>
              );
            },
          },
          {
            key: 'capitationDetails',
            style: { width: isEnrolleeCapitationAmountPerPlan ? widths[10] : widths[8] },
            title: (
              <div className="flex flex-column">
                <span>ES and PS Approval</span>
                <span>ES and PS Approval Date and Time</span>
              </div>
            ),
            CustomElement({
              data,
            }: {
              data: GetHospitalsWithAdmin_hospitals_list_capitationDetails[];
            }) {
              const elem = data?.reduce((acc, curr, idx) => {
                curr.transferFund?.auditApproval?.forEach((item, idx2) => {
                  if (!['OrganizationAdmin', 'ClaimAdmin'].includes(item.approvalGroup)) return acc;
                  acc.push(
                    <div className="flex flex-column" key={`claim-admin-approval-${idx}-${idx2}`}>
                      <span>{item.creatorName || '--'}</span>
                      <span>{formatTableDateTime({ value: new Date(item.createdDate) })}</span>
                    </div>,
                  );
                });
                return acc;
              }, [] as any);
              return <div className="flex flex-column">{elem?.length ? elem : '--'}</div>;
            },
          },
          {
            key: 'capitationDetails',
            style: { width: isEnrolleeCapitationAmountPerPlan ? widths[11] : widths[9] },
            title: (
              <div className="flex flex-column">
                <span>Audit and Finance Approval</span>
                <span>Audit and Finance Approval Date and Time</span>
              </div>
            ),
            CustomElement({
              data,
            }: {
              data: GetHospitalsWithAdmin_hospitals_list_capitationDetails[];
            }) {
              const elem = data?.reduce((acc, curr, idx) => {
                curr.transferFund?.auditApproval?.forEach((item, idx2) => {
                  if (!['ClaimAccount'].includes(item.approvalGroup)) return acc;
                  acc.push(
                    <div className="flex flex-column" key={`audit-approval-${idx}-${idx2}`}>
                      <span>{item.creatorName || '--'}</span>
                      <span>{formatTableDateTime({ value: new Date(item.createdDate) })}</span>
                    </div>,
                  );
                });
                return acc;
              }, [] as any);
              return <div className="flex flex-column">{elem?.length ? elem : '--'}</div>;
            },
          },
          {
            key: 'capitationDetails',
            style: { width: isEnrolleeCapitationAmountPerPlan ? widths[12] : widths[10] },
            title: (
              <div className="flex flex-column">
                <span>Payout Status</span>
                <span>Payout Date and Time</span>
              </div>
            ),
            CustomElement({
              data,
            }: {
              data: GetHospitalsWithAdmin_hospitals_list_capitationDetails[];
            }) {
              return (
                <div className="flex flex-column">
                  {data?.map(({ transferFund }, idx) => {
                    if (!transferFund) return <span key={idx}>--</span>;
                    return transferFund.transferStatus === FundTransactionStatus.Success ? (
                      <div className="flex flex-column" key={`payout-status-${idx}`}>
                        <span className="flex flex-row align-items-center">
                          Success
                          <div className="payout-success-status" />
                        </span>
                        <span>
                          {formatTableDateTime({ value: new Date(transferFund.updatedDate) })}
                        </span>
                      </div>
                    ) : transferFund.transferStatus === FundTransactionStatus.Failed ? (
                      <div className="flex flex-column" key={`payout-status-${idx}`}>
                        <span className="flex flex-row align-items-center">
                          Failed
                          <div className="payout-failed-status" />
                        </span>
                      </div>
                    ) : (
                      <span key={`payout-status-${idx}`}>--</span>
                    );
                  })}
                </div>
              );
            },
          },
          {
            key: 'capitationDetails',
            title: (
              <div>
                <DownloadIcon
                  data-tooltip-content="Download Transaction Receipt"
                  data-tooltip-id="capitationPayoutTableTip"
                  className="cursor-pointer"
                  onClick={() => handleBulkTransactionReceiptPrint?.()}
                />
              </div>
            ),
            style: { width: isEnrolleeCapitationAmountPerPlan ? widths[13] : widths[11] },
            CustomElement({
              row,
              data,
            }: {
              data: GetHospitalsWithAdmin_hospitals_list_capitationDetails[];
              row: any;
            }) {
              const disableAction = data?.every(
                ({ transferFund }) => transferFund?.transferStatus !== 'Success',
              );
              return (
                <div>
                  <DownloadIcon
                    data-tooltip-id="capitationPayoutTableTip"
                    data-tooltip-content="Download Transaction Receipt"
                    className="cursor-pointer"
                    onClick={disableAction ? undefined : () => handleTransactionReceiptPrint?.(row)}
                    style={{ opacity: disableAction ? 0.5 : 1 }}
                  />
                </div>
              );
            },
          },
          {
            key: 'capitationDetails',
            style: { width: isEnrolleeCapitationAmountPerPlan ? widths[14] : widths[12] },
            title: (
              <div>
                <EmailIcon
                  data-tooltip-content="Email Transaction Receipt and Payment Advice"
                  data-tooltip-id="capitationPayoutTableTip"
                  onClick={() => handleBulkEmailTransactionReceipt?.()}
                  // style={{ opacity: 0.5 }}
                  className="cursor-pointer"
                />
              </div>
            ),
            CustomElement({
              row,
              data,
            }: {
              data: GetHospitalsWithAdmin_hospitals_list_capitationDetails[];
              row: any;
            }) {
              const disableAction = data?.every(
                ({ transferFund }) => transferFund?.transferStatus !== 'Success',
              );
              return (
                <div>
                  <EmailIcon
                    data-tooltip-content="Email Transaction Receipt and Payment Advice"
                    data-tooltip-id="capitationPayoutTableTip"
                    onClick={disableAction ? undefined : () => handleEmailTransactionReceipt?.(row)}
                    style={{ opacity: disableAction ? 0.5 : 1 }}
                    className="cursor-pointer"
                  />
                </div>
              );
            },
          },
          {
            key: 'capitationDetails',
            style: { width: isEnrolleeCapitationAmountPerPlan ? widths[15] : widths[13] },
            title: <PhoneIcon />,
            CustomElement: ({
              row,
            }: {
              data: GetHospitalsWithAdmin_hospitals_list['capitationDetails'];
              row: GetHospitalsWithAdmin_hospitals_list;
            }) => {
              const transferFundId = row.capitationDetails?.[0]?.transferFund?.id;
              const disable =
                !transferFundId ||
                row.capitationDetails?.[0]?.transferFund?.transferStatus !== 'Success';

              return (
                <WhatsAppNotificationCell transferFundId={transferFundId || ''} disable={disable} />
              );
            },
          },
          {
            key: 'capitationDetails',
            style: { width: isEnrolleeCapitationAmountPerPlan ? widths[16] : widths[14] },
            title: (
              <PrintPaymentAdviceHeader
                handleBulkPrintPaymentAdvice={handleBulkPrintPaymentAdvice}
              />
            ),
            CustomElement: ({ row, data }) => (
              <PrintPaymentAdviceCell
                rowId={row.id}
                disable={data?.every(
                  ({ transferFund }) => transferFund?.transferStatus !== 'Success',
                )}
                printPaymentAdvice={handleBulkPrintPaymentAdvice}
              />
            ),
          },
        ] as any)
      : []),
  ];
}

function PrintPaymentAdviceHeader({
  handleBulkPrintPaymentAdvice,
}: {
  handleBulkPrintPaymentAdvice?: (() => unknown) | undefined;
}) {
  const [isLoading, setLoading] = useState(false);

  const handlePrintClick = useCallback(async () => {
    if (!handleBulkPrintPaymentAdvice || isLoading) return;

    try {
      setLoading(true);
      await handleBulkPrintPaymentAdvice();
    } catch {
      // no-op
    } finally {
      setLoading(false);
    }
  }, [handleBulkPrintPaymentAdvice, isLoading]);

  return (
    <div>
      {isLoading ? (
        <div className="small-spinner" />
      ) : (
        <PrintIcon
          data-tooltip-content="Print Payment Advice"
          data-tooltip-id="capitationPayoutTableTip"
          onClick={() => {
            handlePrintClick();
          }}
          className="cursor-pointer"
        />
      )}
    </div>
  );
}

function PrintPaymentAdviceCell({
  rowId,
  printPaymentAdvice,
  disable,
}: {
  rowId: string;
  disable: boolean;
  printPaymentAdvice: (id: string) => unknown;
}) {
  const [isLoading, setLoading] = useState(false);
  const handlePrintClick = useCallback(async () => {
    if (isLoading || disable) return;
    try {
      setLoading(true);
      await printPaymentAdvice(rowId);
    } catch {
      // no-op
    } finally {
      setLoading(false);
    }
  }, [isLoading, printPaymentAdvice, rowId]);

  return (
    <div>
      {isLoading ? (
        <div className="small-spinner" />
      ) : (
        <PrintIcon
          data-tooltip-content="Print Payment Advice"
          data-tooltip-id="capitationPayoutTableTip"
          onClick={() => {
            handlePrintClick();
          }}
          className="cursor-pointer"
          style={{ opacity: disable ? 0.5 : 1 }}
        />
      )}
    </div>
  );
}

function WhatsAppNotificationCell({
  transferFundId,
  disable,
}: {
  transferFundId: string;
  disable: boolean;
}) {
  const { addToast } = useToasts();
  const [isLoading, setLoading] = useState(false);
  const [sendWhatsAppNotification] = useMutation<
    SendCapitationPaymentWhatsAppNotification,
    SendCapitationPaymentWhatsAppNotificationVariables
  >(SEND_CAPITATION_PAYMENT_WHATSAPP_NOTIFICATION, {
    onError() {
      addToast('Failed to send WhatsApp notification', {
        appearance: 'error',
      });
    },
    onCompleted() {
      addToast('WhatsApp notification sent successfully', {
        appearance: 'success',
      });
    },
  });

  const handleWhatsAppClick = useCallback(async () => {
    if (isLoading || disable || !transferFundId) return;

    try {
      setLoading(true);
      await sendWhatsAppNotification({
        variables: { transferFundId },
      });
    } catch (error) {
      // no-op
    } finally {
      setLoading(false);
    }
  }, [isLoading, disable, transferFundId, sendWhatsAppNotification]);

  return (
    <div>
      {isLoading ? (
        <div className="small-spinner" />
      ) : (
        <PhoneIcon
          data-tooltip-content="Send WhatsApp Notification"
          data-tooltip-id="capitationPayoutTableTip"
          onClick={handleWhatsAppClick}
          className="cursor-pointer"
          style={{ opacity: disable ? 0.5 : 1 }}
          color={disable ? 'disabled' : 'inherit'}
        />
      )}
    </div>
  );
}
