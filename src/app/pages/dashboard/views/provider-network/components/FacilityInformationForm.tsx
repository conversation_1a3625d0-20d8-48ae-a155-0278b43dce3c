import React, { FC } from 'react';
import { RecordForm } from 'app/shared/components/record-forms/FormWrapper';
import {
  ButtonRow,
  ExtraContent,
  InputRow,
  SelectWrapper,
} from 'app/shared/components/wrappers/Wrapper';
import TextInput from 'app/shared/components/inputs/TextInput';
import Dropdown from 'app/shared/components/dropdown/Dropdown';
import AddressInput from 'app/shared/components/inputs/Address/AddressInput';
import {
  COUNTRIES_OPTIONS,
  LAGOS_POLITICAL_WARD_BY_LGA_MAP,
  LGA_BY_STATE_OPTIONS,
  PROVIDER_CATEGORY,
  PROVIDER_CLASSIFICATION,
  PROVIDER_LEVEL,
  PROVIDER_OWNERSHIP,
  PROVIDER_STATUS,
  PROVIDER_SUBSCRIPTION_PLAN,
  TARIFF_BANDS,
  TITLE_OPTIONS,
} from '@constant/options';
import { stateList } from 'app/shared/utils/hospitalVariables';
import { FlaggedAuthenticationInput } from 'app/shared/components/inputs/AuthenticationInput';
import OutlineButton from 'app/shared/components/buttons/OutlineButton';
import Button from 'app/shared/components/buttons/Button';
import useAppData from 'app/shared/hooks/client/useAppData';
import useHospitalData from 'app/shared/hooks/client/useHospitalData';
import useHmoProviderUtils from 'app/shared/hooks/hmo/useHmoProviderUtils';
import { getUserPayload } from 'app/shared/utils/authentication';
import { IProviderInputs } from '../hooks/useProviderForm';
import {
  EXTENDED_PROVIDER_TYPE_MAP,
  PROVIDER_TYPE_REVERSE,
  PROVIDER_TYPE_MAP,
} from '../../submit-claims/constants';

interface IProps {
  inputs: IProviderInputs;
  isEdit: boolean;
  readOnly: boolean;
  startEdit: boolean;
  toggleStartEdit: () => void;
  handleRegisterUpdate: () => void;
  handleInputChange: (objKey: string, value: any) => void;
  requiredFieldsFilled: boolean;
  isLoading: boolean;
  showPlanVisibility?: boolean;
}

const FacilityInformationForm: FC<IProps> = ({
  inputs,
  isEdit,
  readOnly,
  startEdit,
  toggleStartEdit,
  handleRegisterUpdate,
  handleInputChange,
  requiredFieldsFilled,
  isLoading,
  showPlanVisibility = true,
}) => {
  const { isMobile } = useAppData();
  const { isLashmaAgency } = useHospitalData();
  const showPriceBands = isLashmaAgency;
  const { orgHmoId } = getUserPayload();
  const primaryPhoneNumberCountryCode = inputs?.providerPrimaryPhoneNumber?.countryCode
    ? inputs?.providerPrimaryPhoneNumber?.countryCode.replace(/^\+/, '')
    : '234';
  const primaryPhoneNumberValue = inputs?.providerPrimaryPhoneNumber?.value || '';

  const secondaryPhoneNumberCountryCode = inputs?.providerSecondaryPhoneNumber?.countryCode
    ? inputs?.providerSecondaryPhoneNumber?.countryCode.replace(/^\+/, '')
    : '234';
  const secondaryPhoneNumberValue = inputs?.providerSecondaryPhoneNumber?.value || '';

  const _PROVIDER_TYPES = [
    ...PROVIDER_TYPE_MAP,
    ...(isLashmaAgency ? EXTENDED_PROVIDER_TYPE_MAP : []),
  ];
  const { handleLoadHmoPlans } = useHmoProviderUtils();
  const loadHmoPlans = handleLoadHmoPlans(orgHmoId, 'x/0');
  return (
    <RecordForm>
      <TextInput
        fullWidth
        name="hospitalName"
        title="Provider Name"
        onChange={({ target: { value } }) => handleInputChange('hospitalName', value)}
        value={inputs.hospitalName}
        readOnly={readOnly}
        placeholder="Enter Provider Name"
        isRequired
        skipTranslate
      />
      <AddressInput
        fieldPath="hospitalAddress"
        label="Provider Address"
        handleInputChange={handleInputChange}
        value={inputs.hospitalAddress}
        readOnly={readOnly}
        placeholder="Enter Provider Address"
      />

      <InputRow>
        <SelectWrapper>
          <Dropdown
            title="Country"
            options={COUNTRIES_OPTIONS}
            value={inputs?.country || null}
            placeholder="Select One"
            creatable
            noPadding
            readOnly={readOnly}
            onChange={({ value }) => handleInputChange('country', value)}
          />
        </SelectWrapper>
        <SelectWrapper>
          <Dropdown
            title="State"
            options={(stateList[inputs?.country?.toLowerCase() || ''] || []).map(({ name }) => ({
              label: name,
              value: name,
            }))}
            value={inputs?.state || null}
            placeholder="Select One"
            creatable
            noPadding
            readOnly={readOnly}
            onChange={({ value }) => handleInputChange('state', value)}
          />
        </SelectWrapper>
        <SelectWrapper>
          <Dropdown
            title="Local Government Area"
            options={LGA_BY_STATE_OPTIONS[inputs?.state || ''] || []}
            value={inputs?.lga || null}
            placeholder="Select One"
            creatable
            noPadding
            readOnly={readOnly}
            onChange={({ value }) => handleInputChange('lga', value)}
          />
        </SelectWrapper>
        {inputs?.state === 'Lagos State' ? (
          <SelectWrapper>
            <Dropdown
              title="Political Ward"
              options={LAGOS_POLITICAL_WARD_BY_LGA_MAP[inputs?.lga || ''] || []}
              value={inputs?.politicalWard || null}
              placeholder="Select One"
              creatable
              noPadding
              readOnly={readOnly}
              onChange={({ value }) => handleInputChange('politicalWard', value)}
            />
          </SelectWrapper>
        ) : (
          <SelectWrapper noPadding>
            <TextInput
              fullWidth
              name="politicalWard"
              title="Political Ward"
              placeholder="Enter Political Ward"
              value={inputs.politicalWard}
              onChange={({ target: { value } }) => handleInputChange('politicalWard', value)}
              readOnly={readOnly}
            />
          </SelectWrapper>
        )}
        <SelectWrapper>
          <Dropdown
            onChange={({ value }) => handleInputChange('hospitalContactTitle', value)}
            readOnly={readOnly}
            options={TITLE_OPTIONS}
            value={inputs?.hospitalContactTitle || null}
            placeholder="Select One"
            title="Contact Title"
            creatable
            noPadding
          />
        </SelectWrapper>
        <TextInput
          name="hospitalContactFirstName"
          value={inputs?.hospitalContactFirstName}
          title="Contact First Name"
          onChange={({ target: { value } }) => handleInputChange('hospitalContactFirstName', value)}
          readOnly={readOnly}
          placeholder="Enter First Name"
          isRequired
          skipTranslate
        />
        <TextInput
          name="hospitalContactMiddleName"
          value={inputs?.hospitalContactMiddleName}
          title="Contact Middle Name"
          onChange={({ target: { value } }) =>
            handleInputChange('hospitalContactMiddleName', value)
          }
          readOnly={readOnly}
          placeholder="Enter Middle Name"
          skipTranslate
        />
        <TextInput
          name="hospitalContactLastName"
          value={inputs?.hospitalContactLastName}
          title="Contact Last Name"
          onChange={({ target: { value } }) => handleInputChange('hospitalContactLastName', value)}
          readOnly={readOnly}
          placeholder="Enter Last Name"
          isRequired
          skipTranslate
        />
        <SelectWrapper>
          <Dropdown
            title="Level"
            options={PROVIDER_LEVEL}
            value={inputs?.level || null}
            placeholder="Select One"
            creatable
            noPadding
            readOnly={readOnly}
            onChange={({ value }) => handleInputChange('level', value)}
          />
        </SelectWrapper>
        <SelectWrapper>
          <Dropdown
            title="Ownership"
            options={PROVIDER_OWNERSHIP}
            value={inputs?.ownership}
            placeholder="Select One"
            creatable
            noPadding
            readOnly={readOnly}
            onChange={({ value }) => handleInputChange('ownership', value)}
          />
        </SelectWrapper>
        <TextInput
          name="providerId"
          value={inputs?.providerId}
          title="Provider ID"
          onChange={({ target: { value } }) => handleInputChange('providerId', value)}
          readOnly
          placeholder="Enter Provider ID"
          skipTranslate
        />
        <TextInput
          name="providerCode"
          value={inputs?.providerCode}
          title="Provider Code"
          onChange={({ target: { value } }) => handleInputChange('providerCode', value)}
          readOnly={readOnly}
          placeholder="Enter Provider Code"
          skipTranslate
          isRequired
        />
        <TextInput
          name="hospitalContactEmail"
          value={inputs?.hospitalContactEmail}
          title="Provider Email"
          onChange={({ target: { value } }) => handleInputChange('hospitalContactEmail', value)}
          readOnly={readOnly}
          placeholder="Enter Provider Email"
          isRequired
          skipTranslate
        />
        <TextInput
          name="supportMail"
          value={inputs?.supportMail}
          title="Support Email"
          onChange={({ target: { value } }) => handleInputChange('supportMail', value)}
          readOnly={readOnly}
          placeholder="Enter Support Email"
          skipTranslate
        />
        <SelectWrapper>
          <FlaggedAuthenticationInput
            isRequired
            label="Provider Primary Phone Number"
            name="providerPrimaryPhoneNumber"
            placeholder="Enter Provider Primary Phone Number"
            value={`+${primaryPhoneNumberCountryCode}${primaryPhoneNumberValue}`}
            changeCountryName={(countryName, countryCode) => {
              handleInputChange('providerPrimaryPhoneNumber', {
                value: '',
                countryName,
                countryCode: countryCode.replace(/^\+/, ''),
              });
            }}
            countryName={inputs?.providerPrimaryPhoneNumber?.countryName || 'Nigeria'}
            onChange={({ target: { value } }) => {
              const countryCode = `+${primaryPhoneNumberCountryCode}`;
              const countryCodeLength = countryCode.length;
              if (
                value?.length < countryCodeLength ||
                value.slice(0, countryCodeLength) !== countryCode
              ) {
                return;
              }
              handleInputChange('providerPrimaryPhoneNumber', {
                ...inputs?.providerPrimaryPhoneNumber,
                ...(!inputs?.providerPrimaryPhoneNumber?.countryCode ? { countryCode: '234' } : {}),
                ...(!inputs?.providerPrimaryPhoneNumber?.countryName
                  ? { countryName: 'Nigeria' }
                  : {}),
                value: value.slice(countryCodeLength),
              });
            }}
            smallHeight
            withBorderRadius
            noMargin
            padded={!isMobile}
            fullWidth
            allowFlagSwitch
            fullBorder
            readOnly={readOnly}
          />
        </SelectWrapper>
        <SelectWrapper>
          <FlaggedAuthenticationInput
            label="Provider Secondary Phone Number"
            name="providerSecondaryPhoneNumber"
            placeholder="Enter Provider Secondary Phone Number"
            value={`+${secondaryPhoneNumberCountryCode}${secondaryPhoneNumberValue}`}
            changeCountryName={(countryName, countryCode) => {
              handleInputChange('providerSecondaryPhoneNumber', {
                value: '',
                countryName,
                countryCode: countryCode.replace(/^\+/, ''),
              });
            }}
            countryName={inputs?.providerSecondaryPhoneNumber?.countryName || 'Nigeria'}
            onChange={({ target: { value } }) => {
              const countryCode = `+${secondaryPhoneNumberCountryCode}`;
              const countryCodeLength = countryCode.length;
              if (
                value?.length < countryCodeLength ||
                value.slice(0, countryCodeLength) !== countryCode
              ) {
                return;
              }
              handleInputChange('providerSecondaryPhoneNumber', {
                ...inputs?.providerSecondaryPhoneNumber,
                ...(!inputs?.providerSecondaryPhoneNumber?.countryCode
                  ? { countryCode: '234' }
                  : {}),
                ...(!inputs?.providerSecondaryPhoneNumber?.countryName
                  ? { countryName: 'Nigeria' }
                  : {}),
                value: value.slice(countryCodeLength),
              });
            }}
            smallHeight
            withBorderRadius
            noMargin
            padded={!isMobile}
            fullWidth
            allowFlagSwitch
            fullBorder
            readOnly={readOnly}
          />
        </SelectWrapper>
        {showPriceBands ? (
          <SelectWrapper>
            <Dropdown
              title="Provider Category"
              options={PROVIDER_CATEGORY}
              value={inputs?.providerCategory}
              placeholder="Select One"
              noPadding
              readOnly={readOnly}
              onChange={({ value }) => handleInputChange('providerCategory', value)}
            />
          </SelectWrapper>
        ) : null}
        <SelectWrapper>
          <Dropdown
            title="Provider Status"
            options={PROVIDER_STATUS}
            value={inputs?.planStatus}
            placeholder="Select One"
            noPadding
            readOnly={readOnly}
            onChange={({ value }) => handleInputChange('planStatus', value)}
          />
        </SelectWrapper>
        {showPriceBands ? (
          <SelectWrapper>
            <Dropdown
              title="Tariff Band"
              options={TARIFF_BANDS}
              value={inputs?.tariffBand || null}
              placeholder="Select One"
              noPadding
              readOnly={readOnly}
              onChange={({ value }) => handleInputChange('tariffBand', value)}
            />
          </SelectWrapper>
        ) : null}
        {isLashmaAgency ? null : (
          <>
            <SelectWrapper>
              <Dropdown
                title="Subscription Plan"
                options={PROVIDER_SUBSCRIPTION_PLAN}
                value={inputs?.plan}
                placeholder="Select One"
                noPadding
                readOnly={readOnly}
                onChange={({ value }) => handleInputChange('plan', value)}
                isRequired
              />
            </SelectWrapper>
            <SelectWrapper>
              <Dropdown
                title="Classification"
                options={PROVIDER_CLASSIFICATION}
                value={inputs?.classification}
                placeholder="Select One"
                noPadding
                readOnly={readOnly}
                onChange={({ value }) => handleInputChange('classification', value)}
              />
            </SelectWrapper>
          </>
        )}
        <SelectWrapper>
          <Dropdown
            title="Provider Type"
            options={_PROVIDER_TYPES}
            value={
              PROVIDER_TYPE_REVERSE[inputs.plan || ''] === 'Investigation'
                ? 'Diagnostic Centre'
                : PROVIDER_TYPE_REVERSE[inputs.plan || ''] || inputs?.plan
            }
            placeholder="Select One"
            noPadding
            readOnly={readOnly}
            onChange={({ value }) => handleInputChange('plan', value)}
          />
        </SelectWrapper>
        {showPlanVisibility ? (
          <>
            {(inputs.planVisibility || []).map((plan, index) => (
              <SelectWrapper className="align-items-end" key={index}>
                <div className="flex-1">
                  <Dropdown
                    title="Plan Type"
                    options={[]}
                    value={plan.name}
                    placeholder="Select Plan Type"
                    noPadding
                    readOnly={readOnly}
                    onChange={({ id, label }) => {
                      const newPlanVisibility = [...(inputs.planVisibility || [])];
                      newPlanVisibility[index] = {
                        id,
                        name: label,
                      };
                      handleInputChange('planVisibility', newPlanVisibility);
                    }}
                    isAsync
                    loadOptions={loadHmoPlans}
                  />
                </div>

                {!readOnly && (
                  <div style={{ marginBottom: '12px' }}>
                    <OutlineButton
                      text="Delete"
                      disabled={readOnly}
                      textAlign="center"
                      withIcon={false}
                      onClick={() => {
                        const newItems = inputs.planVisibility?.filter((_, i) => i !== index);
                        handleInputChange('planVisibility', newItems);
                      }}
                    />
                  </div>
                )}
              </SelectWrapper>
            ))}
          </>
        ) : null}
        {!readOnly && showPlanVisibility ? (
          <>
            <ButtonRow position="start">
              <Button
                text={
                  !inputs.planVisibility?.length ? 'Restrict Plan Type' : 'Restrict More Plan Type'
                }
                onClick={() => {
                  const newPlanVisibility = [...(inputs.planVisibility || []), ''];
                  handleInputChange('planVisibility', newPlanVisibility);
                }}
              />
            </ButtonRow>
          </>
        ) : (
          <></>
        )}

        <SelectWrapper>
          <TextInput
            name="enrolleeCount"
            value={inputs?.enrolleeCount}
            title="Provider Enrollee Count"
            onChange={({ target: { value } }) => handleInputChange('enrolleeCount', value)}
            readOnly
            placeholder="Enter Enrollee Count"
            skipTranslate
            fullWidth
          />
        </SelectWrapper>
        <SelectWrapper>
          <TextInput
            name="enrolleeLimit"
            value={inputs?.enrolleeLimit}
            title="Provider Enrollee Limit"
            onChange={({ target: { value } }) => handleInputChange('enrolleeLimit', value)}
            readOnly={readOnly}
            placeholder="Enter Enrollee Limit"
            skipTranslate
            fullWidth
          />
        </SelectWrapper>
      </InputRow>

      {isEdit ? (
        <ExtraContent>
          <ButtonRow position="end">
            {startEdit && (
              <OutlineButton
                withBorderRadius
                withIcon={false}
                text="Cancel"
                onClick={toggleStartEdit}
                disabled={isLoading}
              />
            )}
            <Button
              text={startEdit ? 'Update' : 'Edit'}
              onClick={startEdit ? handleRegisterUpdate : toggleStartEdit}
              disabled={isLoading || (startEdit ? !requiredFieldsFilled : false)}
            />
          </ButtonRow>
        </ExtraContent>
      ) : null}
    </RecordForm>
  );
};

export default FacilityInformationForm;
