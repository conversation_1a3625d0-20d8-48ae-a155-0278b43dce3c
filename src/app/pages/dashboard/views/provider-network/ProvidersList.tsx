import React, { ChangeEvent, FC, useState } from 'react';
import Content from 'app/shared/components/wrappers/Wrapper';
import Table from 'app/shared/components/table/Table';
import { Tooltip as ReactTooltip } from 'react-tooltip';
import { ReactComponent as SearchIcon } from 'assets/icons/search.svg';
import { ReactComponent as ClearFilterIcon } from 'assets/icons/clear-filter.svg';
import SearchTab from 'app/shared/components/table/SearchTab';
import SearchInput from 'app/shared/components/inputs/SearchInput';
import colors from '@constant/colors';
import useAppData from 'app/shared/hooks/client/useAppData';
import OutlineButton from 'app/shared/components/buttons/OutlineButton';
import OutlineIconButton from 'app/shared/components/buttons/OutlineIconButton';
import classNames from 'classnames';
import Button from 'app/shared/components/buttons/Button';
import { GetHospitalsWithAdmin_hospitals_list } from 'graphql-types/GetHospitalsWithAdmin';
import { useHistory, useLocation } from 'react-router-dom';
import Dropdown from 'app/shared/components/dropdown/Dropdown';
import { PROVIDER_CATEGORY, TARIFF_BANDS } from '@constant/options';
import useHospitalData from 'app/shared/hooks/client/useHospitalData';
import { LASHMA_AGENCY, LASHMA_AGENCY_2 } from 'app/shared/utils/is-clinify-hmo';
import { ExcelIcon } from 'assets/index';
import useProviderList from './hooks/useProviderList';
import AsyncProviderOptions from './components/AsyncProviderOptions';
import ProviderNetworkPagination from './components/ProviderNetworkPagination';
import CapitationPayoutTable from './components/CapitationPayoutTable';
import ProviderCapitationTableSearch from './components/ProviderCapitationTableSearch';
import useProviderCapitation from './hooks/useProviderCapitation';
import { _PROVIDER_TYPE_REVERSE } from '../submit-claims/constants';

const COLUMNS = [
  {
    Header: 'S/N',
    accessor: 'number',
    Cell: (arg) => {
      return <span>{arg.cell.row.index + 1}</span>;
    },
  },
  {
    Header: (
      <div className="flex-column">
        <span>Provider ID</span>
        <span>Provider Code</span>
        <span>Level</span>
        <span>Ownership</span>
      </div>
    ),
    id: 'ic8o2md',
    accessor: (row) => row,
    Cell: ({ value }: { value: GetHospitalsWithAdmin_hospitals_list }) => {
      return (
        <div className="flex-column">
          <span>{value.hmoHospitals?.[0]?.hmoProviderUniqueId || '--'}</span>
          <span>{value.hmoHospitals?.[0]?.hmoProviderId || '--'}</span>
          <span>{value.level || '--'}</span>
          <span>{value.ownership || '--'}</span>
        </div>
      );
    },
  },
  {
    Header: (
      <div className="flex-column">
        <span>Provider Type</span>
        <span>Provider Name</span>
        <span>Contact First Name</span>
        <span>Contact Last Name</span>
      </div>
    ),
    accessor: (row) => row,
    id: 'jchf387h',
    Cell: ({ value }: { value: GetHospitalsWithAdmin_hospitals_list }) => {
      return (
        <div className="flex-column">
          <span>{_PROVIDER_TYPE_REVERSE[value.plan || ''] || value.plan || '--'}</span>
          <span>{value?.name || '--'}</span>
          <span>{value?.orgAdmin?.personalInformation?.firstName || '--'}</span>
          <span>{value?.orgAdmin?.personalInformation?.lastName || '--'}</span>
        </div>
      );
    },
  },
  {
    Header: (
      <div className="flex-column">
        <span>Provider Email</span>
        <span>Support Email</span>
        <span>Provider Primary Phone Number</span>
        <span>Provider Secondary Phone Number</span>
      </div>
    ),
    id: 'cdknr8302',
    accessor: (row) => row,
    Cell: ({ value }: { value: GetHospitalsWithAdmin_hospitals_list }) => {
      return (
        <div className="flex-column">
          <span>{value?.orgAdmin?.user?.email || '--'}</span>
          <span>{value?.supportMail || '--'}</span>
          <span>{value.phoneNumber?.value || '--'}</span>
          <span>{value.secondaryPhoneNumber?.value || '--'}</span>
        </div>
      );
    },
  },
  {
    Header: (
      <div className="flex-column">
        <span>Country</span>
        <span>State</span>
        <span>Local Government Area</span>
        <span>Political Ward</span>
      </div>
    ),
    id: '48802fjel',
    accessor: (row) => row,
    Cell: ({ value }: { value: GetHospitalsWithAdmin_hospitals_list }) => {
      return (
        <div className="flex-column">
          <span>{value.country || '--'}</span>
          <span>{value.state || '--'}</span>
          <span>{value.lga || '--'}</span>
          <span>{value.politicalWard || '--'}</span>
        </div>
      );
    },
  },
];

type TableSearchTabProps = {
  handleSearch: (event: ChangeEvent<HTMLInputElement>) => void;
  keyword?: string;
  clearFilter: () => void;
  handleFieldSearch: (field: string, value: string) => void;
  filterOptions: Record<string, string>;
  triggerSearch: () => void;
  onProviderOptionChange: (hospitalId: string) => void;
  handleDownloadExcel: () => void;
  fetchingProviderExcelList: boolean;
};

const TableSearchTab: FC<TableSearchTabProps> = ({
  handleSearch,
  clearFilter,
  handleFieldSearch,
  filterOptions,
  triggerSearch,
  onProviderOptionChange,
  handleDownloadExcel,
}) => {
  const { isMobile } = useAppData();
  const { hospitalName } = useHospitalData();
  const isLashmaAgency = [LASHMA_AGENCY, LASHMA_AGENCY_2].includes(hospitalName);

  return (
    <SearchTab isVitals>
      <AsyncProviderOptions
        onChange={(value) => {
          onProviderOptionChange(value);
        }}
        value={filterOptions.hospitalId || null}
      />
      <SearchInput
        onChange={handleSearch}
        value={filterOptions.keyword}
        placeholder="Enter Provider Name"
        outline
        triggerSearch={triggerSearch}
      />
      <SearchInput
        value={filterOptions.providerId}
        onChange={({ target: { value } }) => handleFieldSearch('providerId', value)}
        placeholder="Enter Provider ID"
        outline
        triggerSearch={triggerSearch}
      />
      <SearchInput
        value={filterOptions.providerCode}
        onChange={({ target: { value } }) => handleFieldSearch('providerCode', value)}
        placeholder="Enter Provider Code"
        outline
        triggerSearch={triggerSearch}
      />
      <SearchInput
        value={filterOptions.email}
        onChange={({ target: { value } }) => handleFieldSearch('email', value)}
        placeholder="Enter Provider Email"
        outline
        triggerSearch={triggerSearch}
      />
      {isLashmaAgency ? (
        <>
          <form data-testid="search-form">
            <Dropdown
              options={[{ label: 'All', value: undefined }, ...PROVIDER_CATEGORY]}
              onChange={({ value }) => handleFieldSearch('providerCategory', value)}
              value={filterOptions.providerCategory || null}
              placeholder="Select Provider Category"
              noPadding
            />
          </form>
          <form data-testid="search-form">
            <Dropdown
              options={[{ label: 'All', value: undefined }, ...TARIFF_BANDS]}
              onChange={({ value }) => handleFieldSearch('providerTariffBand', value)}
              value={filterOptions.providerTariffBand || null}
              placeholder="Select Tariff Band"
              noPadding
            />
          </form>
        </>
      ) : null}

      <Button
        text="Search"
        marginRight
        onClick={triggerSearch}
        className={classNames({ 'full-width': isMobile })}
      />
      <div className="checkout-icons">
        {isMobile ? (
          <OutlineButton
            withBorderRadius
            withIcon
            text="Clear Filter"
            onClick={() => clearFilter()}
            mainColor={colors.secondaryColor}
            icon={<ClearFilterIcon />}
            fullWidth
          />
        ) : (
          <div data-tooltip-content="Clear Filter" data-tooltip-id="capitationPayoutTableTip">
            <OutlineIconButton onClick={() => clearFilter()} withIcon icon={<ClearFilterIcon />} />
          </div>
        )}
      </div>
      {isMobile ? (
        <OutlineButton
          withBorderRadius
          withIcon
          text="Download Provider List"
          onClick={handleDownloadExcel}
          icon={<ExcelIcon width={24} height={24} />}
          fullWidth
        />
      ) : (
        <div data-tooltip-content="Download Provider List" data-tooltip-id="download-provider-list">
          <OutlineIconButton
            onClick={handleDownloadExcel}
            icon={<ExcelIcon width={24} height={24} />}
          />
        </div>
      )}

      <ReactTooltip
        id="download-provider-list"
        place="top"
        className="button-tooltip"
        variant="light"
      />
    </SearchTab>
  );
};

const ProvidersList: FC = () => {
  const [switchPage, updateSwitchPage] = useState(false);

  const history = useHistory();
  const { pathname } = useLocation();

  const {
    items,
    loading,
    filterOptions,
    onRowClick,
    clearFilter,
    handleSearch,
    triggerSearch,
    handleFieldSearch,
    onProviderOptionChange,
    lastItemElementRef,
    handleDownloadExcel,
    fetchingProviderExcelList,
  } = useProviderList();

  const {
    items: providerCapItems,
    loading: providerCapIsLoading,
    filterOptions: providerCapFilterInput,
    handleSearch: providerCapHandleSearch,
    clearFilter: providerCapClearFilter,
    filterByStatus: providerCapFilterByStatus,
    setDateRange: providerCapSetDateRange,
    changeLimit: providerCapChangeLimit,
    pageCount: providerCapPageCount,
    toPage: providerCapToPage,
    currentPageNumber: providerCapCurrentPageNumber,
    payingoutForCapitatedEnrollees,
    handleCapitationPayoutApproval,
    handlePayoutForCapitatedEnrollees,
    refetchHmoProviderCapitation,
    handleEnrolleeCountUpdate,
    agencyPlanTypes,
    payoutSummary,
    handlePrintPaymentAdvice,
    refetchingHmoProviderCapitation,
    handleBulkCapitationPayoutApproval,
    allPayoutApprovedBySelf,
    handlePrintProviderCapitationList,
    updatingCapitationPayoutApproval,
    transactionStatusOption,
    setTransactionStatusOption,
    emailTransactionReceipt,
    handleBulkEmailTransactionReceipt,
    handleBulkPrintPaymentAdvice,
    getHmoCapitatedPaymentSummaryData,
    handlePrintProviderCapitationListExcel,
  } = useProviderCapitation(!switchPage);

  return (
    <Content detailsPage noPadding>
      <div className="heading-table">
        <ProviderNetworkPagination
          switchButton={{
            value: switchPage ? 'Yes' : 'No',
            onChange: () => updateSwitchPage(!switchPage),
          }}
          limit={providerCapFilterInput.filterOptions.take}
          changeLimit={providerCapChangeLimit}
          data={providerCapItems}
          currentPageNumber={+providerCapCurrentPageNumber}
          toPage={providerCapToPage}
          pageCount={providerCapPageCount}
          loading={providerCapIsLoading}
          payoutSummary={payoutSummary}
        />

        <div className="patient-record-list-tab">
          {!switchPage && (
            <Button
              text="Add New Provider"
              onClick={() => history.push(`${pathname}/add`)}
              withIcon
              addButton
              minWidth="auto"
              className="table-add-button"
            />
          )}

          <div className="search-tab-section">
            {switchPage ? (
              <ProviderCapitationTableSearch
                filterOptions={providerCapFilterInput}
                handleSearch={providerCapHandleSearch}
                setDateRange={providerCapSetDateRange}
                clearFilter={providerCapClearFilter}
                filterByStatus={providerCapFilterByStatus}
                agencyPlanTypes={agencyPlanTypes}
                handlePrintProviderCapitationList={handlePrintProviderCapitationList}
                transactionStatusOption={transactionStatusOption}
                setTransactionStatusOption={setTransactionStatusOption}
                handlePrintProviderCapitationListExcel={handlePrintProviderCapitationListExcel}
              />
            ) : (
              <TableSearchTab
                handleSearch={handleSearch}
                keyword={filterOptions.keyword}
                clearFilter={clearFilter}
                handleFieldSearch={handleFieldSearch}
                filterOptions={filterOptions}
                triggerSearch={triggerSearch}
                onProviderOptionChange={onProviderOptionChange}
                handleDownloadExcel={handleDownloadExcel}
                fetchingProviderExcelList={fetchingProviderExcelList}
              />
            )}
          </div>
        </div>
      </div>

      {items?.length === 0 && !loading ? (
        <div className="body-wrap">
          <div className="lookup-wrapper">
            <div>
              <SearchIcon />
              <span className="lookup">Look Up</span>
              <p>Results will show up here</p>
            </div>
          </div>
        </div>
      ) : (
        <>
          {switchPage ? (
            <CapitationPayoutTable
              items={providerCapItems || []}
              handleCapitationPayoutApproval={handleCapitationPayoutApproval}
              payoutCapitation={handlePayoutForCapitatedEnrollees}
              onRefresh={() => refetchHmoProviderCapitation()}
              loadingBankInformation={refetchingHmoProviderCapitation}
              payingOutCapitation={payingoutForCapitatedEnrollees}
              handleEnrolleeCountUpdate={handleEnrolleeCountUpdate}
              currentPageNumber={+providerCapCurrentPageNumber}
              maxRowCount={providerCapFilterInput.filterOptions.take}
              printPaymentAdvice={handlePrintPaymentAdvice}
              handleBulkCapitationPayoutApproval={handleBulkCapitationPayoutApproval}
              allPayoutApprovedBySelf={allPayoutApprovedBySelf}
              handleBulkPrintPaymentAdvice={handleBulkPrintPaymentAdvice}
              authorizeCapitationPayoutLoading={updatingCapitationPayoutApproval}
              emailTransactionReceipt={emailTransactionReceipt}
              handleBulkEmailTransactionReceipt={handleBulkEmailTransactionReceipt as any}
              getCapitatedPaymentSummaryData={(getHmoCapitatedPaymentSummaryData || {}) as any}
            />
          ) : (
            <Table
              columns={COLUMNS}
              data={items}
              onRowClick={onRowClick}
              pageCount={1}
              showPagination={false}
              defaultGoToPath="/facility/provider-network"
              attachId={false}
              loading={loading}
              lastRef={lastItemElementRef}
            />
          )}
        </>
      )}
      <ReactTooltip
        id="capitationPayoutTableTip"
        place="top"
        className="button-tooltip"
        variant="light"
      />
    </Content>
  );
};

export default ProvidersList;
