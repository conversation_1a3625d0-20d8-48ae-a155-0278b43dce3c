import React, { Fragment, useState } from 'react';
import MultiGroupAction from 'app/shared/components/buttons/MultiGroupActions';
import TextArea from 'app/shared/components/inputs/TextArea';
import TextInput from 'app/shared/components/inputs/TextInput';
import { FlaggedAuthenticationInput } from 'app/shared/components/inputs/AuthenticationInput';
import AddressInput from 'app/shared/components/inputs/Address/AddressInput';
import DocumentAttacher from 'app/shared/components/file-uploads/DocumentAttacher';
import ActionSectionSectionWithAuditDetails from 'app/shared/components/record-forms/ActionSectionWithAuditDetails';
import fieldsAreValid from 'app/shared/utils/fieldsAreValid';
import Prompter from 'app/shared/components/prompter/Prompter';
import { InputRow, SelectWrapper } from 'app/shared/components/wrappers/Wrapper';
import DatePicker from 'app/shared/components/pickers/DatePicker';
import Dropdown from 'app/shared/components/dropdown/Dropdown';
import { loadDiagnosisOptions } from 'app/shared/utils/loadIcdAsync';
import { DropdownCheckBox } from 'app/shared/components/dropdown/DropdownCheckBox';
import Modal from 'app/shared/components/modal/Modal';
import { Tooltip as ReactTooltip } from 'react-tooltip';
import { AgreedTariffVariables } from 'graphql-types/AgreedTariff';
import { PreauthUtilisations } from 'graphql-types/PreauthUtilisations';
import { ProcessPreauthUtilizationStatusVariables } from 'graphql-types/ProcessPreauthUtilizationStatus';
import { calculateAgeWithMonth } from 'app/shared/utils/calculateAge';
import SingleDurationInput from 'app/shared/components/inputs/SingleDurationInput';
import useHospitalData from 'app/shared/hooks/client/useHospitalData';
import { OGSHIA_AGENCY } from 'app/shared/utils/is-clinify-hmo';
import usePatientData from 'app/shared/hooks/client/usePatientData';
import MultiDurationInput from 'app/shared/components/inputs/MultipleDurationInput';
import { preAuthInitialState, utilisationInitialState } from './constants';
import {
  BIRTH_COUNT_OPTIONS,
  CLASS_CONSULTATION_OPTIONS,
  DOSAGE_OPTIONS,
  HMO_MEDICATION_FREQUENCY,
  HOSPITAL_UNIT_OPTIONS,
  LASHMA_HMO_DOSAGE_OPTIONS,
  OGSHIA_DIAGNOSIS_LIST,
  PRIORITY_SURGERY_OPTIONS,
  SPECIALTY_OPTIONS,
  UTILIZATION_SPECIALTY_DENTAL_CONSULT,
  UTILIZATION_SPECIALTY_LIST,
  UTILIZATION_SPECIALTY_MEDICAL_CONSULT,
  UTILIZATION_SPECIALTY_OPTOMETRY_CONSULT,
} from '../../../../../@constant/options';
import { getDiagnosisValue, listTypeMapper } from '../admission/AddAdmission';
import { defaultInputProps, withComma } from '../billings/constants';
import useProcessPreauthorizationForm from './hooks/useProcessPreauthorizationForm';
import { OtpInputIcon } from '../../../../../assets';
import LoaderOrError from '../../../../shared/components/loaders/LoaderOrError';
import AuditDetails from '../../../../shared/components/record-forms/AuditDetails';
import calculateTotalUtilization from '../../../../shared/calculate-total-utilization';
import ApprovalGroupForm from '../cliams/components/ApprovalGroupForm';
import ApprovalGroupSummary from '../cliams/components/ApprovalGroupSummary';
import HmoFlagSection from '../../components/forms/HmoFlagSection/HmoFlagSection';
import LazyClaimSummaryTable from '../cliams/components/LazyClaimSummaryTable';
import ReferralComponent from './components/ReferralComponent';
import { OGSHIA_FLAG_DESCRIPTION } from '../../../../shared/utils/ogshia-flag-description';
import UtilizationsHistoryWrapper from './components/UtilizationsHistoryWrapper';

type Props = {
  isOnModal?: boolean;
  defaultId?: string;
  clearAction?: (id?: string) => void;
  filterOptions: Record<string, unknown>;
  refetchList?: () => void;
};
export default function ProcessPreauthorizationForm({
  filterOptions,
  isOnModal,
  defaultId,
  clearAction,
  refetchList,
}: Props) {
  const [listType, setListType] = useState({ 0: 'ICD-10' });
  const {
    handleInputChange,
    inputs,
    readOnly,
    hmoDropdownOptions,
    setInputs,
    authDiagnosis,
    isEdit,
    startEdit,
    toggle,
    action,
    setStartEdit,
    disableActionButton,
    actionText,
    unitPriceText,
    handleMultipleFieldsChange,
    fetchingData,
    errorFetching,
    processPreauthUtilizationStatus,
    onQuantityChange,
    flagPreauthorization,
    processBulkUtilizationsStatus,
  } = useProcessPreauthorizationForm({
    filterOptions,
    isOnModal,
    defaultId,
    clearAction,
    refetchList,
  });
  const [showHistory, setShowHistory] = useState(false);

  const { hospitalName } = useHospitalData();
  const isOgshiaAgency = hospitalName === OGSHIA_AGENCY;

  const { enrolleeDetails } = usePatientData(inputs?.profile?.clinifyId);

  const loadVisitationTypes = () => {};
  if (fetchingData || errorFetching) {
    return <LoaderOrError loading={fetchingData} error={errorFetching} />;
  }

  return (
    <>
      <div className="flex flex-row gap-8 justify-content-end" style={{ marginRight: 35 }}>
        <input
          type="checkbox"
          checked={showHistory}
          onChange={({ target: { checked } }) => setShowHistory(checked)}
          className="cursor-pointer"
        />
        <small>PA History</small>
      </div>
      {showHistory && (
        <UtilizationsHistoryWrapper
          type="Preauth"
          profileId={inputs.profile?.id}
          date={inputs.requestDateTime}
        />
      )}
      <div className="flex flex-row justify-content-end">
        <HmoFlagSection
          flags={inputs.flags?.map(({ flag }) => flag)}
          action={flagPreauthorization}
          tooltipDescription={isOgshiaAgency ? OGSHIA_FLAG_DESCRIPTION : undefined}
          variant={isOgshiaAgency ? 'Green-Red' : undefined}
        />
      </div>
      <div className="header">
        <div className="force-full-width title">PREAUTHORIZATIONS SUMMARY</div>
      </div>
      <LazyClaimSummaryTable
        enrolleeInformation={{
          fullName: inputs.profile?.fullName,
          gender: inputs.profile?.gender,
          phoneNumber: inputs.profile?.user?.phoneNumber,
          secondaryPhoneNumber: inputs.profile?.secondaryPhoneNumber?.value,
          diagnosis: inputs.diagnosis,
          enrolleeNumber: inputs.enrolleeNumber,
          age: calculateAgeWithMonth(inputs.profile?.personalInformation?.dateOfBirth, true),
          enrolleePlanType: (enrolleeDetails?.hmoProfile as any)?.memberPlan || '',
          enrolleePlanCategory: enrolleeDetails?.hmoProfile?.planCategory || '',
          profileId: inputs.profile?.id,
        }}
        providerInformation={{
          name: inputs.facilityName,
          address: inputs.facilityAddress,
        }}
        referringProvider={{
          from: inputs.referredFrom,
          to: inputs.referredTo,
        }}
        utilizations={inputs.utilizations}
        authorizationCode={inputs.referralCode}
        onProcessUtilization={processPreauthUtilizationStatus}
        onProcessBulkUtilizations={processBulkUtilizationsStatus}
        treatmentDateTime={inputs.requestDateTime}
        hmoClaimId={inputs.id}
        showComments
        showFlagControl
        utilizationOrigin="PREAUTH"
        showVitalsSummary
        onQuantityChange={onQuantityChange}
      />
      <div className="header">
        <div className="force-full-width title">PREAUTHORIZATIONS DETAILED</div>
      </div>
      <InputRow>
        <SelectWrapper noPadding>
          <DatePicker
            label="Treatment Date and Time"
            isRequired
            withBorderRadius
            onChange={(date) => handleInputChange('requestDateTime', date)}
            value={inputs.requestDateTime}
            placeholderText="Select Date and Time"
            readOnly={false}
            minDate={new Date(new Date().getFullYear() - 100, new Date().getMonth())}
            maxDate={new Date(new Date().getFullYear() + 100, new Date().getMonth())}
            disabled={readOnly}
          />
        </SelectWrapper>
        <SelectWrapper>
          <Dropdown
            title="Coverage Name"
            options={hmoDropdownOptions}
            onChange={({ value, enrolleeId, memberUniqueId }) =>
              setInputs({ ...inputs, providerId: value, enrolleeId, memberUniqueId })
            }
            placeholder="Select One"
            isRequired
            value={inputs.provider?.name}
            readOnly
            noPadding
            skipTranslate
          />
        </SelectWrapper>
      </InputRow>
      <InputRow>
        <SelectWrapper noPadding>
          <DatePicker
            label="Treatment Start Date and Time"
            withBorderRadius
            onChange={(date) => handleInputChange('treatmentStartDate', date)}
            value={inputs.treatmentStartDate}
            placeholderText="Select Date and Time"
            readOnly={false}
            minDate={new Date(new Date().getFullYear() - 100, new Date().getMonth())}
            maxDate={new Date(new Date().getFullYear() + 100, new Date().getMonth())}
            disabled={readOnly}
          />
        </SelectWrapper>
        <SelectWrapper noPadding>
          <DatePicker
            label="Treatment End Date and Time"
            withBorderRadius
            onChange={(date) => handleInputChange('treatmentEndDate', date)}
            value={inputs.treatmentEndDate}
            placeholderText="Select Date and Time"
            readOnly={false}
            minDate={new Date(new Date().getFullYear() - 100, new Date().getMonth())}
            maxDate={new Date(new Date().getFullYear() + 100, new Date().getMonth())}
            disabled={readOnly}
          />
        </SelectWrapper>
      </InputRow>

      {inputs.provider?.id ? (
        <>
          <InputRow>
            <SelectWrapper noFlex>
              <Dropdown
                title="Visitation Type"
                options={[]}
                onChange={({ label, value }) => {
                  setInputs({
                    ...inputs,
                    serviceType: label,
                    serviceTypeCode: value,
                    utilizations: preAuthInitialState.utilizations,
                  });
                }}
                placeholder="Select One"
                isRequired
                value={inputs.serviceType || null}
                readOnly={readOnly}
                isAsync
                loadOptions={loadVisitationTypes}
                additional={{
                  providerId: inputs.providerId,
                  action: 'visitation',
                }}
                noPadding
                creatable={false}
              />
            </SelectWrapper>
            <SelectWrapper>
              <Dropdown
                title="Priority"
                options={PRIORITY_SURGERY_OPTIONS}
                onChange={({ value }) => handleInputChange('priority', value)}
                placeholder="Select One"
                value={inputs.priority || null}
                readOnly={readOnly}
                creatable
                noPadding
              />
            </SelectWrapper>
          </InputRow>
          {authDiagnosis.map((diagnose, idx) => {
            const { diagnosisICD10 = '', diagnosisICD11 = '', diagnosisSNOMED = '' } = {
              ...diagnose,
            };
            return (
              <Fragment key={idx}>
                <SelectWrapper fullWidth>
                  {isOgshiaAgency ? (
                    <Dropdown
                      title="Clinical Diagnosis"
                      options={OGSHIA_DIAGNOSIS_LIST}
                      onChange={({ value }) => {
                        const newData = [...(inputs.diagnosis || [])];
                        newData[idx] = {
                          ...newData[idx],
                          diagnosisICD10: value,
                        };
                        handleInputChange('diagnosis', newData);
                      }}
                      placeholder="Select One"
                      value={diagnosisICD10 || null}
                      readOnly={readOnly}
                      creatable
                      noPadding
                    />
                  ) : (
                    <Dropdown
                      title="Clinical Diagnosis"
                      options={[]}
                      onChange={({ value }) => {
                        const newData = [...(inputs.diagnosis || [])];
                        newData[idx] = {
                          ...newData[idx],
                          [listTypeMapper[listType[`${idx}`] || 'ICD-10']]: value,
                        };
                        handleInputChange('diagnosis', newData);
                      }}
                      placeholder="Select One"
                      value={
                        getDiagnosisValue(
                          diagnosisICD10,
                          diagnosisICD11,
                          diagnosisSNOMED,
                          listType[`${idx}`] || 'ICD-10',
                        ) || null
                      }
                      loadOptions={loadDiagnosisOptions}
                      isAsync
                      creatable
                      noPadding
                      readOnly={readOnly}
                      isRequired
                      additional={{ dropdownType: listType[`${idx}`] || 'ICD-10' }}
                      topRightComponent={
                        <DropdownCheckBox
                          value={listType[`${idx}`] || 'ICD-10'}
                          onChange={(value) => {
                            setListType({
                              ...listType,
                              [idx]: value,
                            });
                          }}
                        />
                      }
                      cacheUniqs={[listType[`${idx}`] || 'ICD-10']}
                    />
                  )}
                </SelectWrapper>
                <SelectWrapper fullWidth>
                  {!readOnly && (
                    <MultiGroupAction
                      items={authDiagnosis}
                      index={idx}
                      initialItem={{
                        diagnosisICD10: null,
                        diagnosisICD11: null,
                        diagnosisSNOMED: null,
                      }}
                      onClick={(value) => {
                        handleInputChange('diagnosis', value);
                      }}
                    />
                  )}
                </SelectWrapper>
              </Fragment>
            );
          })}

          <SelectWrapper fullWidth noPadding>
            <TextArea
              name="presentingComplain"
              label="Presenting Complaints"
              fullWidth
              onChange={({ target: { value } }) => handleInputChange('presentingComplain', value)}
              value={inputs.presentingComplain}
              readOnly={readOnly}
              fieldId={inputs?.id}
              isRequired
              createdBy={inputs?.creatorId}
            />
          </SelectWrapper>
          {inputs?.utilizations.map((utilisation, idx) => (
            <Fragment key={idx}>
              <Utilisations
                visitationType={inputs.serviceTypeCode}
                loadVisitationTypes={loadVisitationTypes}
                utilisation={utilisation}
                handleInputChange={(name, value) =>
                  handleMultipleFieldsChange('utilizations', name, value, idx)
                }
                getTariff={() => {}}
                index={idx}
                loadingPrice={false}
                unitPriceText={unitPriceText}
                readOnly={readOnly || !inputs.providerId}
                isEdit={isEdit}
                providerId={inputs.providerId}
                enrolleeId={inputs.enrolleeId}
                treatmentDate={inputs.requestDateTime}
                processPreauthUtilization={processPreauthUtilizationStatus}
                onQuantityChange={onQuantityChange}
                providerName={inputs.provider?.name}
              />
              {!isEdit && (
                <MultiGroupAction
                  items={inputs.utilizations}
                  index={idx}
                  initialItem={utilisationInitialState}
                  onClick={(value) => {
                    handleInputChange('utilizations', value);
                  }}
                />
              )}
            </Fragment>
          ))}
          <InputRow>
            <TextInput
              name="totalQuantity"
              title="Total Quantity"
              value={calculateTotalUtilization(inputs.utilizations).totalQuantity}
              readOnly
            />
            <FlaggedAuthenticationInput
              {...defaultInputProps}
              name="grandTotal"
              label="Grand Total"
              value={withComma(calculateTotalUtilization(inputs.utilizations).grandTotal)}
              readOnly
            />
          </InputRow>
          <InputRow>
            <ReferralComponent inputs={inputs} readOnly={readOnly} setInputs={setInputs} />
          </InputRow>
          <InputRow>
            <TextInput
              name="doctor"
              title="Requested By"
              readOnly
              onChange={() => {}}
              value={inputs.requestedBy}
              placeholder="Enter Doctor's Name"
              skipTranslate
            />
            <SelectWrapper>
              <Dropdown
                creatable
                title="Specialty"
                options={SPECIALTY_OPTIONS}
                onChange={() => {}}
                placeholder="Select One"
                value={inputs.specialty || null}
                readOnly
                noPadding
              />
            </SelectWrapper>
          </InputRow>
          <InputRow>
            <SelectWrapper>
              <Dropdown
                title="Rank"
                options={CLASS_CONSULTATION_OPTIONS}
                onChange={() => {}}
                placeholder="Select One"
                value={inputs.rank || null}
                readOnly
                creatable
                noPadding
              />
            </SelectWrapper>
            <SelectWrapper>
              <Dropdown
                title="Department"
                options={HOSPITAL_UNIT_OPTIONS}
                placeholder="Select One"
                value={inputs?.department || null}
                onChange={() => {}}
                readOnly
                creatable
                noPadding
              />
            </SelectWrapper>
          </InputRow>
          <TextInput
            fullWidth
            name="facilityName"
            title="Facility Name"
            onChange={() => {}}
            readOnly
            value={inputs.facilityName}
            placeholder="Enter Facility Name"
            skipTranslate
          />
          <AddressInput
            label="Facility Address"
            fieldPath="facilityAddress"
            handleInputChange={() => {}}
            readOnly
            value={inputs.facilityAddress}
            placeholder="Enter Facility Address"
          />
          <InputRow>
            <FlaggedAuthenticationInput
              {...defaultInputProps}
              label="Enrollee Phone Number"
              placeholder="Enter Enrollee Phone Number"
              value={`${inputs?.enrolleePhoneNumber?.countryCode || '234'} ${
                inputs?.enrolleePhoneNumber?.value || ''
              }`}
              onChange={() => {}}
              name="enrolleePhoneNumber"
              countryName={inputs?.enrolleePhoneNumber?.countryName || 'Nigeria'}
              changeCountryName={() => {}}
            />
            <TextInput
              name="enrolleeEmail"
              title="Enrollee Email"
              onChange={() => {}}
              readOnly
              value={inputs.enrolleeEmail}
              placeholder="Enter Enrollee Email"
            />
          </InputRow>
          <SelectWrapper fullWidth noPadding>
            <TextArea
              name="additionalNote"
              label="Clinical Findings"
              fullWidth
              onChange={({ target: { value } }) => handleInputChange('additionalNote', value)}
              value={inputs.additionalNote}
              readOnly={readOnly}
              fieldId={inputs?.id}
              createdBy={inputs?.creatorId}
            />
          </SelectWrapper>
          <InputRow>
            <DocumentAttacher
              documents={inputs.documentUrl}
              handleInputChange={handleInputChange}
              readOnly={readOnly}
              type="preauthorization"
            />
          </InputRow>
          <ActionSectionSectionWithAuditDetails
            isEdit={isEdit}
            isEditActive={startEdit}
            toggle={toggle}
            cancelAction={() => setStartEdit(false)}
            disableActionButton={
              disableActionButton ||
              isEdit ||
              !fieldsAreValid('authutilizationsfields', inputs.utilizations, true)
            }
            disableDeleteButton
            onAction={action}
            actionText={actionText}
            inputs={inputs}
          />
        </>
      ) : null}

      <Modal
        modalContent={
          <Prompter
            text="Are you sure you want to delete this record?"
            actionText="Delete"
            deleteAction={() => {}}
            cancelAction={() => {}}
            disabled
          />
        }
        isShown={false}
        hide={() => {}}
        handleDone={() => {}}
        isAuthentication
      />
      <ReactTooltip
        id="processPreauthorizationFormToolTip"
        place="top"
        className="otpTip"
        variant="light"
      />
    </>
  );
}

type UtilisationsProps = {
  utilisation: Partial<PreauthUtilisations>;
  handleInputChange: (field: string, value: string) => void;
  getTariff: (input: AgreedTariffVariables['input']) => void;
  index: number;
  unitPriceText: string;
  readOnly?: boolean;
  loadingPrice?: boolean;
  loadingDetails?: boolean;
  isEdit?: boolean;
  providerId: string;
  loadVisitationTypes: any;
  visitationType: any;
  enrolleeId: string;
  treatmentDate: string;
  isClinifyHmo?: boolean;
  providerName: string;
  processPreauthUtilization?: (
    input: ProcessPreauthUtilizationStatusVariables['input'],
    onDone?: () => void,
  ) => void;
  onQuantityChange: (quantity: string, id?: string) => void;
};
function Utilisations({
  utilisation,
  handleInputChange,
  getTariff,
  index,
  unitPriceText,
  readOnly,
  loadingPrice,
  loadingDetails,
  isEdit,
  providerId,
  loadVisitationTypes,
  visitationType,
  enrolleeId,
  treatmentDate,
  isClinifyHmo,
  processPreauthUtilization,
  onQuantityChange,
  providerName,
}: UtilisationsProps) {
  const { isLashmaAgency } = useHospitalData();

  const showDrugFields = ['drug', 'medicine', 'injection'].some(
    (keyword) =>
      `${utilisation.category}${utilisation.type}`.toLowerCase().includes(keyword) &&
      !utilisation.category?.toLowerCase()?.includes('fees') &&
      !utilisation.category?.toLowerCase()?.includes('admission'),
  );
  const showDeliveryFields = ['delivery', 'caesarean'].some((keyword) =>
    `${utilisation.category}${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const showSpecialty = ['consult'].some((keyword) =>
    `${utilisation.category}${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isMedicalConsult = ['medical', 'consult'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isDentalConsult = ['dental', 'consult'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isOptometryConsult = ['optometry', 'consult'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );

  let SPECIALTY_LIST = UTILIZATION_SPECIALTY_LIST;

  if (isMedicalConsult) {
    SPECIALTY_LIST = UTILIZATION_SPECIALTY_MEDICAL_CONSULT;
  } else if (isDentalConsult) {
    SPECIALTY_LIST = UTILIZATION_SPECIALTY_DENTAL_CONSULT;
  } else if (isOptometryConsult) {
    SPECIALTY_LIST = UTILIZATION_SPECIALTY_OPTOMETRY_CONSULT;
  }

  const HMO_DOSAGE_OPTIONS = isLashmaAgency ? LASHMA_HMO_DOSAGE_OPTIONS : DOSAGE_OPTIONS;

  return (
    <>
      <InputRow>
        <SelectWrapper fullWidth>
          <Dropdown
            creatable={false}
            title="Utilization Type"
            options={[]}
            onChange={({ value }) => {
              handleInputChange('type', value);
              getTariff({
                providerId,
                position: index,
                id: value,
                utilizationId: utilisation.utilizationId,
                enrolleeId,
                treatmentDate,
                visitTypeId: visitationType,
              });
            }}
            placeholder="Select One"
            isRequired
            value={utilisation.type || null}
            readOnly={readOnly}
            isAsync
            loadOptions={loadVisitationTypes}
            additional={{
              utilizationId: utilisation?.utilizationId,
              action: 'utilizationProcedure',
              providerId,
              visitationType,
            }}
            noPadding
            cacheUniqs={[utilisation?.utilizationId]}
          />
        </SelectWrapper>
      </InputRow>
      <InputRow>
        <SelectWrapper>
          <Dropdown
            creatable={false}
            title="Utilization Category"
            options={[]}
            onChange={() => {}}
            placeholder="Select One"
            isRequired
            value={utilisation.category || null}
            readOnly={readOnly}
            isAsync
            loadOptions={loadVisitationTypes}
            additional={{
              action: 'utilizationCategory',
              providerId,
              visitationType,
            }}
            noPadding
            cacheUniqs={[visitationType]}
          />
        </SelectWrapper>
        <TextInput
          name="utilizationCode"
          title="Utilization Code"
          onChange={({ target: { value } }) => {
            const valueToUse = (value || '').replaceAll(' ', '');
            getTariff({
              providerId,
              position: index,
              id: valueToUse,
              utilizationId: isClinifyHmo ? undefined : utilisation.utilizationId,
              enrolleeId,
              treatmentDate,
              visitTypeId: visitationType,
            });
          }}
          value={utilisation.utilizationCode}
          placeholder="Enter Utilization Code"
          readOnly
        />
      </InputRow>
      {showDrugFields ? (
        <InputRow>
          <SelectWrapper noPadding>
            <TextInput
              name="dosage"
              title="Dosage"
              placeholder="Enter Dosage"
              onChange={({ target: { value } }) => {
                if (Number(value) > 10) return;
                handleInputChange('dosage', value);
              }}
              value={utilisation.dosage}
              readOnly
              fullWidth
              withTag
              equalTagWith
              isRequired
              type="number"
              tag={
                <Dropdown
                  options={HMO_DOSAGE_OPTIONS}
                  value={utilisation.dosageUnit || null}
                  onChange={({ value }) => handleInputChange('dosageUnit', value)}
                  placeholder="Select One"
                  readOnly
                  withTag
                  noPadding
                  creatable
                />
              }
            />
          </SelectWrapper>
          <SelectWrapper padded>
            <SingleDurationInput
              title="Duration (Month:Week:Day)"
              onChange={() => {}}
              readOnly
              value={utilisation.duration}
              isRequired
            />
          </SelectWrapper>
          <SelectWrapper>
            <Dropdown
              noPadding
              title="Frequency"
              options={HMO_MEDICATION_FREQUENCY}
              onChange={() => {}}
              readOnly
              value={utilisation.frequency || null}
              placeholder="Enter Frequency"
              creatable
              isRequired
            />
          </SelectWrapper>
          <TextInput
            name="category"
            title="Medication Category"
            readOnly
            onChange={({ target: { value } }) => handleInputChange('medicationCategory', value)}
            value={utilisation.medicationCategory}
            placeholder="Enter Medication Category"
            isRequired
          />
        </InputRow>
      ) : null}
      <InputRow>
        <TextInput
          name="quantity"
          title="Quantity"
          onChange={({ target: { value } }) => {
            handleInputChange('quantity', value);
            onQuantityChange(value, utilisation.id);
          }}
          value={utilisation.quantity}
          placeholder="Enter Quantity"
          readOnly={false}
          isRequired
        />
        <FlaggedAuthenticationInput
          {...defaultInputProps}
          label="Unit Price"
          name="unitPrice"
          onChange={({ target: { value } }) => handleInputChange('price', value)}
          value={utilisation.price ? `${withComma(utilisation.price)}` : undefined}
          placeholder={unitPriceText}
          readOnly
          loading={loadingPrice}
        />
      </InputRow>
      {showSpecialty ? (
        <InputRow>
          <SelectWrapper>
            <Dropdown
              noPadding
              title="Specialty"
              options={SPECIALTY_LIST}
              onChange={({ value }) => handleInputChange('specialty', value)}
              readOnly
              value={utilisation.specialty || null}
              isRequired
            />
          </SelectWrapper>
        </InputRow>
      ) : null}
      {showDeliveryFields ? (
        <InputRow>
          <SelectWrapper>
            <Dropdown
              noPadding
              title="Birth Count"
              options={BIRTH_COUNT_OPTIONS}
              onChange={({ value }) => handleInputChange('birthCount', value)}
              readOnly
              value={utilisation.birthCount || null}
              isRequired
            />
          </SelectWrapper>
          <SelectWrapper noPadding>
            <DatePicker
              label="Delivery Date and Time"
              withBorderRadius
              onChange={(date) => handleInputChange('deliveryDateTime', date)}
              value={utilisation.deliveryDateTime}
              placeholderText="Select Date and Time"
              readOnly
              minDate={new Date(new Date().getFullYear() - 100, new Date().getMonth())}
              maxDate={new Date()}
              disabled
              isRequired
            />
          </SelectWrapper>
          <SelectWrapper padded>
            <MultiDurationInput
              title="Gestational Age (Month:Week:Day)"
              onChange={(duration) => handleInputChange('gestationalAge', duration)}
              value={utilisation?.gestationalAge}
              readOnly
              disableBox={[2]}
              minValue={{ 0: 0, 1: 1 }}
              maxValue={{ 0: 30, 1: 42 }}
              isRequired
            />
          </SelectWrapper>
        </InputRow>
      ) : null}
      <InputRow>
        <FlaggedAuthenticationInput
          {...defaultInputProps}
          label="Total Price"
          name="totalPrice"
          onChange={() => {}}
          value={
            utilisation.price
              ? `${withComma(Number(utilisation.price) * Number(utilisation.quantity))}`
              : undefined
          }
          placeholder={unitPriceText}
          readOnly
        />
        <TextInput
          readOnly
          name="PACode"
          value={utilisation.paCode}
          title={`PA Code - ${utilisation.status || 'Processing'}`}
          onChange={({ target: { value } }) => {
            handleInputChange('paCode', value);
          }}
          placeholder="Enter PA Code"
          withIcon
          iconWithBrownLeftBorder
          loading={loadingDetails}
          skipTranslate
          icon={
            <div
              data-tooltip-content="Click to get PA Code"
              data-tooltip-id="processPreauthorizationFormToolTip"
              className="otp-button"
              data-tooltip-hidden={!isEdit}
            >
              <OtpInputIcon />
            </div>
          }
        />
      </InputRow>
      <div className="medication-dispense-action-row parent">
        <div className="audit-section">
          {!!utilisation.id && <AuditDetails inputs={utilisation} />}
        </div>
      </div>
      <ApprovalGroupForm
        providerName={providerName}
        utilization={utilisation}
        processUtilization={processPreauthUtilization}
      />
      <ApprovalGroupSummary providerName={providerName} utilization={utilisation} />
    </>
  );
}
