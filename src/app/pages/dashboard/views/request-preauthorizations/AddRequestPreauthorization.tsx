import {
  BIRTH_COUNT_OPTIONS,
  CLASS_CONSULTATION_OPTIONS,
  HOSPITAL_UNIT_OPTIONS,
  HMO_MEDICATION_FREQUENCY,
  OGSHIA_DIAGNOSIS_LIST,
  PRIORITY_SURGERY_OPTIONS,
  SPECIALTY_OPTIONS,
  UTIL<PERSON>ZATION_SPECIALTY_LIST,
  LASHMA_HMO_DOSAGE_OPTIONS,
  DOSAGE_OPTIONS,
  UTILIZATION_SPECIALTY_MEDICAL_CONSULT,
  UTILIZATION_SPECIALTY_DENTAL_CONSULT,
  UTILIZATION_SPECIALTY_OPTOMETRY_CONSULT,
} from '@constant/options';
import { AgreedTariffVariables } from 'graphql-types/AgreedTariff';
import MultiGroupAction from 'app/shared/components/buttons/MultiGroupActions';
import Dropdown from 'app/shared/components/dropdown/Dropdown';
import { DropdownCheckBox } from 'app/shared/components/dropdown/DropdownCheckBox';
import DocumentAttacher from 'app/shared/components/file-uploads/DocumentAttacher';
import AddressInput from 'app/shared/components/inputs/Address/AddressInput';
import { FlaggedAuthenticationInput } from 'app/shared/components/inputs/AuthenticationInput';
import TextArea from 'app/shared/components/inputs/TextArea';
import TextInput from 'app/shared/components/inputs/TextInput';
import LoaderOrError from 'app/shared/components/loaders/LoaderOrError';
import { Modal } from 'app/shared/components/modal/Modal';
import DatePicker from 'app/shared/components/pickers/DatePicker';
import Prompter from 'app/shared/components/prompter/Prompter';
import ActionSectionSectionWithAuditDetails from 'app/shared/components/record-forms/ActionSectionWithAuditDetails';
import { FormContent, RecordForm } from 'app/shared/components/record-forms/FormWrapper';
import { InputRow, SelectWrapper } from 'app/shared/components/wrappers/Wrapper';
import fieldsAreValid from 'app/shared/utils/fieldsAreValid';
import { loadDiagnosisOptions } from 'app/shared/utils/loadIcdAsync';
import React, { Fragment, useEffect, useMemo, useState } from 'react';
import { Tooltip as ReactTooltip } from 'react-tooltip';
import { OtpInputIcon } from 'assets';
import { PreauthUtilisations } from 'graphql-types/PreauthUtilisations';
import { getPhoneExtensionCode, getUserPayload } from 'app/shared/utils/authentication';
import SingleDurationInput from 'app/shared/components/inputs/SingleDurationInput';
import { useToasts } from 'react-toast-notifications';
import CheckBoxWithLabel from 'app/shared/components/inputs/CheckBoxWithLabel';
import useHospitalData from 'app/shared/hooks/client/useHospitalData';
import moment from 'moment/moment';
import usePatientData from 'app/shared/hooks/client/usePatientData';
import cloneDeep from 'lodash.clonedeep';
import classNames from 'classnames';
import { FlaggedRuleItem } from 'app/shared/types/business-rule';
import { hasRuleCategory } from 'app/shared/utils/common';
import useBusinessRulesFlaggedItems from 'app/pages/dashboard/components/forms/HmoFlagSection/hooks/useBusinessRulesFlaggedItems';
import MultiDurationInput from 'app/shared/components/inputs/MultipleDurationInput';
import { defaultInputProps, withComma } from '../billings/constants';
import {
  ANNUAL_SCREENING_DIAGNOSIS,
  calculateMedicationQuantity,
  LASHMA_AUTO_APPROVE_DIAGNOSIS,
  LASHMA_AUTO_APPROVE_DIAGNOSIS_2,
  LASHMA_AUTO_APPROVE_DIAGNOSIS_3,
  LASHMA_AUTO_APPROVE_MED_CATEGORY,
  LASHMA_AUTO_APPROVE_MED_CATEGORY_2,
  LASHMA_AUTO_APPROVE_MED_CATEGORY_3,
  SINGLE_COUNT_UTILIZATIONS,
  utilisationInitialState,
  UTILIZATION_DRUGS_TO_CLASSIFICATION_MAP,
} from './constants';
import useRequestPreauthorizationForm from './hooks/useRequestPreauthorizationForm';
import { getDiagnosisValue, listTypeMapper } from '../admission/AddAdmission';
import ActionHeader from './components/ActionHeader';
import Notice from './components/Notice';
import useRequestPreauthorizationAPILoader from './hooks/useRequestPreauthorizationAPILoader';
import {
  isHmoClaimsManagedByClinify,
  LASHMA_AGENCY,
  LASHMA_AGENCY_2,
  OGSHIA_AGENCY,
} from '../../../../shared/utils/is-clinify-hmo';
import { PaCodeStatus } from '../../../../../graphql-types/globalTypes';
import calculateTotalUtilization from '../../../../shared/calculate-total-utilization';
import ReferralComponent from './components/ReferralComponent';
import useHmoProviderHospitalOptions from './hooks/useHmoProviderHospitalOptions';
import useMappedUtilizationToDiagnosis from './hooks/useMappedUtilizationToDiagnosis';
import utilizationDiagnosisMap from './utilization-diagnosis.map.json';
import useFilterUtilizationOptions from './hooks/useFilterUtilizationOptions';
import { formatUtilizationPaymentModelText } from '../submit-claims/constants';
import UtilizationsSummaryTable from '../submit-claims/components/UtilizationsSummary';

const FORM_TAG = 'Preauthorization';

interface UtilisationsProp {
  utilisation: PreauthUtilisations;
  handleInputChange: (field: string, value: string) => void;
  getTariff: (input: AgreedTariffVariables['input']) => void;
  setItems: (input: Record<any, any>) => void;
  index: number;
  unitPriceText: string;
  readOnly?: boolean;
  loadingPrice?: boolean;
  handleGetDetail: () => void;
  loadingDetails?: boolean;
  isEdit?: boolean;
  getProviderId: (input: string) => string;
  providerId: string;
  loadVisitationTypes: any;
  visitationType: any;
  enrolleeId: string;
  treatmentDate: string;
  isClinifyHmo?: boolean;
  facilityId?: string;
  providerName: string;
  externalPlanTypeId?: string;
  setInputs: React.Dispatch<any>;
  isExternalPlanType: boolean;
  flaggedItems?: FlaggedRuleItem[];
  utilizationOnlyServices?: null | string[];
}

const Utilisations: React.FC<UtilisationsProp> = ({
  utilisation,
  handleInputChange,
  getTariff,
  index,
  unitPriceText,
  readOnly,
  setItems,
  loadingPrice,
  handleGetDetail,
  loadingDetails,
  isEdit,
  providerId,
  loadVisitationTypes,
  visitationType,
  enrolleeId,
  treatmentDate,
  isClinifyHmo,
  facilityId,
  providerName,
  externalPlanTypeId,
  setInputs,
  isExternalPlanType,
  flaggedItems = [],
  utilizationOnlyServices,
}) => {
  const { isLashmaEnrollee } = usePatientData();
  const { isProviderHmoClaimsManagedByClinify } = useHospitalData();
  const {
    acceptedOptions,
    rejectedOptions,
    acceptedUtilizationTypeOptions,
    loadAllUtilizationTypes,
    toggleFetchAllTypes,
    fetchAllTypes,
    LABORATORY,
    RADIOLOGY,
    DRUG,
    utilizationResetKey,
  } = useFilterUtilizationOptions(
    'PREAUTH',
    providerName,
    providerId,
    visitationType,
    utilisation?.category || '',
    utilizationOnlyServices,
  );

  const { onUtilizationTypeChange } = useMappedUtilizationToDiagnosis({
    setInputs,
    diagnosisFieldName: 'diagnosis',
    utilizationFieldName: 'utilizations',
    map: utilizationDiagnosisMap,
    utilIdx: index,
    loadAllUtilizationTypes,
    enrolleeId,
    providerId,
    isExternalPlanType,
  });

  const handleQuantityUpdate = (field: string, value: string | Record<string, any>) => {
    const resetQuantityToOne = SINGLE_COUNT_UTILIZATIONS.includes(utilisation.type || '');
    const _update = calculateMedicationQuantity(field, value, {
      dosage: utilisation.dosage,
      duration: utilisation.duration,
      frequency: utilisation.frequency,
    });
    if (resetQuantityToOne && isLashmaEnrollee) {
      _update.quantity = '1';
    }
    setInputs((_prev) => {
      const tempUtilizations = cloneDeep(_prev.utilizations || []);
      tempUtilizations[index] = {
        ...tempUtilizations[index],
        ..._update,
      };
      return { ..._prev, utilizations: tempUtilizations };
    });
  };

  const cannotEdit = utilisation.status && utilisation.status !== 'Pending';
  const showDrugFields = ['drug', 'medicine', 'injection'].some(
    (keyword) =>
      `${utilisation.category}${utilisation.type}`.toLowerCase().includes(keyword) &&
      !utilisation.category?.toLowerCase()?.includes('fees') &&
      !utilisation.category?.toLowerCase()?.includes('admission'),
  );
  const showDeliveryFields = ['delivery', 'caesarean'].some((keyword) =>
    `${utilisation.category}${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const showSpecialty = ['consult'].some((keyword) =>
    `${utilisation.category}${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isToothExtraction = ['tooth', 'extraction'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isBloodTransfusion = ['blood', 'transfusion'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isLimbXray = ['limb', 'xray'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isDentalRadiograph = ['dental', 'radiograph'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isCrossMatching = ['cross', 'matching'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isMedicalConsult = ['medical', 'consult'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isDentalConsult = ['dental', 'consult'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isOptometryConsult = ['optometry', 'consult'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isWoundDressing = ['wound', 'dressing'].every((keyword) =>
    `${utilisation.type}`.toLowerCase().includes(keyword),
  );
  const isInjectionService = utilisation.category?.toLowerCase()?.includes('injection');
  const isInfusionService = utilisation.category?.toLowerCase()?.includes('infusion');

  const rejectionReasons =
    utilisation?.rejectionReason?.length && Array.isArray(utilisation.rejectionReason)
      ? utilisation.rejectionReason
      : [''];

  const medicationIsPack = useMemo(() => {
    if (!showDrugFields || !utilisation.type) return false;
    return SINGLE_COUNT_UTILIZATIONS.includes(utilisation.type);
  }, [showDrugFields, utilisation.type]);

  const disableQuantity =
    isLashmaEnrollee &&
    !(
      (showDrugFields && !medicationIsPack) ||
      isInjectionService ||
      isInfusionService ||
      isToothExtraction ||
      isBloodTransfusion ||
      isLimbXray ||
      isDentalRadiograph ||
      isCrossMatching ||
      isWoundDressing
    );

  const hidePaCode = useMemo(
    () => isProviderHmoClaimsManagedByClinify && utilisation.status?.toLowerCase() === 'pending',
    [isProviderHmoClaimsManagedByClinify, utilisation.status],
  );

  const HMO_DOSAGE_OPTIONS = isLashmaEnrollee ? LASHMA_HMO_DOSAGE_OPTIONS : DOSAGE_OPTIONS;
  const categoryFlaggedItems = useMemo(() => {
    return (
      utilisation.utilizationCode &&
      hasRuleCategory(utilisation.utilizationCode, 'utilizationCategory', flaggedItems)
    );
  }, [flaggedItems, utilisation.category]);

  const typeFlaggedItems = useMemo(() => {
    return (
      utilisation.utilizationCode &&
      hasRuleCategory(utilisation.utilizationCode, 'utilizationType', flaggedItems)
    );
  }, [flaggedItems, utilisation.type]);

  const quantityFlaggedItems = useMemo(() => {
    return (
      utilisation.utilizationCode &&
      hasRuleCategory(utilisation.utilizationCode, 'quantity', flaggedItems)
    );
  }, [flaggedItems, utilisation.quantity]);

  let SPECIALTY_LIST = UTILIZATION_SPECIALTY_LIST;

  if (isMedicalConsult) {
    SPECIALTY_LIST = UTILIZATION_SPECIALTY_MEDICAL_CONSULT;
  } else if (isDentalConsult) {
    SPECIALTY_LIST = UTILIZATION_SPECIALTY_DENTAL_CONSULT;
  } else if (isOptometryConsult) {
    SPECIALTY_LIST = UTILIZATION_SPECIALTY_OPTOMETRY_CONSULT;
  }

  return (
    <>
      <SelectWrapper fullWidth>
        {fetchAllTypes ? (
          <Dropdown
            creatable={false}
            title="Utilization Type"
            options={[]}
            onChange={({ label, code, hmoPlanBenefitId, utilizationCategory }) => {
              handleInputChange('type', code);
              setItems({
                type: label,
                utilizationCode: (code || '').replaceAll(' ', ''),
                price: '0',
                category: utilizationCategory,
                utilizationId: hmoPlanBenefitId,
                dosage: null,
                dosageUnit: null,
                duration: null,
                frequency: null,
                medicationCategory: null,
                quantity: '1',
              });
              getTariff({
                providerId,
                position: index,
                id: (code || '').replaceAll(' ', ''),
                enrolleeId,
                treatmentDate,
                visitTypeId: visitationType,
                facilityId,
                externalPlanId: externalPlanTypeId,
              });
            }}
            onValueChange={(selection) => {
              const label = selection?.label;
              onUtilizationTypeChange(label);
            }}
            placeholder="Select One"
            isRequired
            overrideIsRequired
            value={utilisation.type || null}
            readOnly={readOnly || cannotEdit}
            isAsync
            loadOptions={loadAllUtilizationTypes}
            noPadding
            fieldName={`${FORM_TAG}_utilization-type`}
            cacheUniqs={[
              `${utilisation?.utilizationId}-${externalPlanTypeId}-${utilizationResetKey}-${utilizationOnlyServices?.length}`,
            ]}
            withError={typeFlaggedItems}
            additional={
              utilizationOnlyServices?.length
                ? { acceptedOptions: utilizationOnlyServices }
                : undefined
            }
          />
        ) : (
          <Dropdown
            creatable={false}
            title="Utilization Type"
            options={[]}
            onChange={({ value, label }) => {
              handleInputChange('type', value);
              setItems({
                type: label,
                utilizationCode: value,
                price: '0',
                dosage: null,
                dosageUnit: null,
                duration: null,
                frequency: null,
                medicationCategory: null,
                quantity: '1',
              });
              getTariff({
                providerId,
                position: index,
                id: value,
                utilizationId: utilisation.utilizationId,
                enrolleeId,
                treatmentDate,
                visitTypeId: visitationType,
                facilityId,
                externalPlanId: externalPlanTypeId,
              });
            }}
            onValueChange={(selection) => {
              const label = selection?.label;
              onUtilizationTypeChange(label);
            }}
            placeholder="Select One"
            isRequired
            overrideIsRequired
            value={utilisation.type || null}
            readOnly={readOnly || cannotEdit}
            isAsync
            loadOptions={loadVisitationTypes}
            additional={{
              utilizationId: utilisation?.utilizationId,
              action: 'utilizationProcedure',
              providerId,
              visitationType,
              hospitalId: facilityId || '',
              externalPlanTypeId,
              acceptedOptions:
                utilizationOnlyServices?.length && utilisation.category === 'Drug'
                  ? utilizationOnlyServices
                  : [LABORATORY, RADIOLOGY, DRUG].includes(utilisation.category || '')
                  ? acceptedUtilizationTypeOptions
                  : [],
            }}
            noPadding
            cacheUniqs={[
              `${utilisation?.utilizationId}-${externalPlanTypeId}-${utilizationResetKey}-${utilizationOnlyServices?.length}`,
            ]}
            onFocus={() => {
              if (!utilisation.category) {
                toggleFetchAllTypes(true);
              }
            }}
            withError={typeFlaggedItems}
          />
        )}
      </SelectWrapper>
      <SelectWrapper>
        <Dropdown
          creatable={false}
          title="Utilization Category"
          options={[]}
          onChange={({ value, label }) => {
            setItems({
              category: label,
              utilizationId: value,
              utilizationCode: null,
              type: null,
              quantity: '1',
              price: '0',
              dosage: null,
              dosageUnit: null,
              duration: null,
              frequency: null,
              medicationCategory: null,
            });
          }}
          placeholder="Select One"
          isRequired
          overrideIsRequired
          value={utilisation.category || null}
          readOnly={readOnly || cannotEdit}
          isAsync
          loadOptions={loadVisitationTypes}
          additional={{
            action: 'utilizationCategory',
            providerId,
            visitationType,
            hospitalId: facilityId || '',
            acceptedOptions,
            rejectedOptions,
          }}
          noPadding
          cacheUniqs={[visitationType, utilizationOnlyServices?.length]}
          fieldName={`${FORM_TAG}_utilization-category`}
          withError={categoryFlaggedItems}
        />
      </SelectWrapper>
      <TextInput
        name="utilizationCode"
        title="Utilization Code"
        onChange={({ target: { value } }) => {
          const valueToUse = (value || '').replaceAll(' ', '');
          setItems({
            utilizationCode: valueToUse,
            price: '0',
            type: null,
            dosage: null,
            dosageUnit: null,
            duration: null,
            frequency: null,
            medicationCategory: null,
          });
          getTariff({
            providerId,
            position: index,
            id: valueToUse,
            utilizationId: isClinifyHmo ? undefined : utilisation.utilizationId,
            enrolleeId,
            treatmentDate,
            visitTypeId: visitationType,
            facilityId,
            externalPlanId: externalPlanTypeId,
          });
        }}
        value={utilisation.utilizationCode}
        placeholder="Enter Utilization Code"
        fieldName={`${FORM_TAG}_utilization-code`}
        readOnly
      />
      {showDrugFields ? (
        <>
          <SelectWrapper noPadding>
            <TextInput
              name="dosage"
              title="Dosage"
              placeholder="Enter Dosage"
              onChange={({ target: { value } }) => {
                if (Number(value) > 10) return;
                handleQuantityUpdate('dosage', value);
              }}
              value={utilisation.dosage}
              readOnly={readOnly || cannotEdit}
              fullWidth
              withTag
              equalTagWith
              isRequired
              type="number"
              fieldName={`${FORM_TAG}_utilization-dosage`}
              tag={
                <Dropdown
                  options={HMO_DOSAGE_OPTIONS}
                  value={utilisation.dosageUnit || null}
                  onChange={({ value }) => handleInputChange('dosageUnit', value)}
                  placeholder="Select One"
                  readOnly={readOnly || isLashmaEnrollee || cannotEdit}
                  withTag
                  noPadding
                  creatable
                />
              }
            />
          </SelectWrapper>
          <SelectWrapper padded>
            <SingleDurationInput
              title="Duration (Month:Week:Day)"
              onChange={(duration) => handleQuantityUpdate('duration', duration)}
              readOnly={readOnly || cannotEdit}
              value={utilisation.duration}
              isRequired
              fieldName={`${FORM_TAG}_utilization-duration`}
            />
          </SelectWrapper>
          <SelectWrapper>
            <Dropdown
              noPadding
              title="Frequency"
              options={HMO_MEDICATION_FREQUENCY}
              onChange={(selection) => handleQuantityUpdate('frequency', selection)}
              readOnly={readOnly || cannotEdit}
              value={utilisation.frequency || null}
              placeholder="Enter Frequency"
              creatable={false}
              isRequired
              fieldName={`${FORM_TAG}_utilization-frequency`}
            />
          </SelectWrapper>
          <TextInput
            name="category"
            title="Medication Category"
            readOnly={readOnly || isLashmaEnrollee || cannotEdit}
            onChange={({ target: { value } }) => handleInputChange('medicationCategory', value)}
            value={utilisation.medicationCategory}
            placeholder="Enter Medication Category"
            isRequired
            overrideIsRequired
            fieldName={`${FORM_TAG}_utilization-medication-category`}
          />
        </>
      ) : null}
      <TextInput
        name="quantity"
        title="Quantity"
        onChange={({ target: { value } }) => handleInputChange('quantity', value)}
        value={utilisation.quantity}
        placeholder="Enter Quantity"
        readOnly={readOnly || cannotEdit || isExternalPlanType || disableQuantity}
        type="number"
        isRequired
        overrideIsRequired
        fieldName={`${FORM_TAG}_utilization-quantity`}
        withError={quantityFlaggedItems}
      />
      <FlaggedAuthenticationInput
        {...defaultInputProps}
        label={`Unit Price${
          utilisation?.paymentModel
            ? ` - ${formatUtilizationPaymentModelText(utilisation.paymentModel)}`
            : ''
        }`}
        name="unitPrice"
        onChange={({ target: { value } }) => handleInputChange('price', value)}
        value={utilisation.price ? `${withComma(utilisation.price)}` : undefined}
        placeholder={unitPriceText}
        readOnly
        loading={loadingPrice}
      />

      {showSpecialty ? (
        <InputRow>
          <SelectWrapper>
            <Dropdown
              noPadding
              title="Specialty"
              options={SPECIALTY_LIST}
              onChange={({ value }) => handleInputChange('specialty', value)}
              readOnly={readOnly || cannotEdit}
              value={utilisation.specialty || null}
              isRequired
              overrideIsRequired
              fieldName={`${FORM_TAG}_utilization-specialty`}
            />
          </SelectWrapper>
        </InputRow>
      ) : null}
      {showDeliveryFields ? (
        <InputRow>
          <SelectWrapper>
            <Dropdown
              noPadding
              title="Birth Count"
              options={BIRTH_COUNT_OPTIONS}
              onChange={({ value }) => handleInputChange('birthCount', value)}
              readOnly={readOnly || cannotEdit}
              value={utilisation.birthCount || null}
              isRequired
              overrideIsRequired
              fieldName={`${FORM_TAG}_utilization-birth-count`}
            />
          </SelectWrapper>
          <SelectWrapper noPadding>
            <DatePicker
              label="Delivery Date and Time"
              withBorderRadius
              onChange={(date) => handleInputChange('deliveryDateTime', date)}
              value={utilisation.deliveryDateTime}
              placeholderText="Select Date and Time"
              readOnly={readOnly || cannotEdit}
              minDate={new Date(new Date().getFullYear() - 100, new Date().getMonth())}
              maxDate={new Date()}
              disabled={readOnly}
              isRequired
              overrideIsRequired
              fieldName={`${FORM_TAG}_utilization-delivery-date-and-time`}
            />
          </SelectWrapper>
          <SelectWrapper padded>
            <MultiDurationInput
              title="Gestational Age (Month:Week:Day)"
              onChange={(duration) => handleInputChange('gestationalAge', duration)}
              value={utilisation?.gestationalAge}
              readOnly={readOnly || cannotEdit}
              disableBox={[2]}
              minValue={{ 0: 0, 1: 1 }}
              maxValue={{ 0: 30, 1: 42 }}
              isRequired
              overrideIsRequired
              fieldName={`${FORM_TAG}_utilization-gestation-age`}
            />
          </SelectWrapper>
        </InputRow>
      ) : null}
      <FlaggedAuthenticationInput
        {...defaultInputProps}
        label="Total Price"
        name="totalPrice"
        onChange={() => {}}
        value={
          utilisation.price
            ? `${withComma(Number(utilisation.price) * Number(utilisation.quantity))}`
            : undefined
        }
        placeholder={unitPriceText}
        readOnly
      />
      <TextInput
        readOnly
        name="PACode"
        value={hidePaCode ? '' : utilisation.paCode}
        title={`PA Code - ${utilisation.status || 'Processing'}`}
        onChange={() => {}}
        placeholder="Enter PA Code"
        withIcon
        iconWithBrownLeftBorder
        loading={loadingDetails}
        skipTranslate
        icon={
          <div
            data-tooltip-content="Click to get PA Code"
            data-tooltip-id="addPreauthorisationToolTip"
            data-tooltip-hidden={!isEdit}
            className="otp-button"
            onClick={isEdit ? handleGetDetail : undefined}
          >
            <OtpInputIcon />
          </div>
        }
      />
      {utilisation.status === PaCodeStatus.Rejected && (
        <InputRow>
          {[LASHMA_AGENCY, LASHMA_AGENCY_2].includes(providerName) ? (
            <>
              {rejectionReasons.map((_rejectionReason, _idx) => (
                <InputRow key={_idx}>
                  <SelectWrapper fullWidth>
                    <Dropdown
                      title="Rejection Reason"
                      options={[]}
                      placeholder="Select One"
                      value={_rejectionReason || null}
                      onChange={() => {}}
                      readOnly
                      creatable
                      noPadding
                    />
                  </SelectWrapper>
                  {_rejectionReason === 'Others' && (
                    <TextArea
                      name="reasonForRejection"
                      label="Specify Rejection Reason"
                      fullWidth
                      onChange={() => {}}
                      value={utilisation?.specifyReasonForRejection}
                      readOnly
                    />
                  )}
                </InputRow>
              ))}
            </>
          ) : (
            <TextArea
              name="reasonForRejection"
              label="Reason for Rejection"
              fullWidth
              onChange={() => {}}
              value={utilisation.statusDescription}
              readOnly
              fieldName={`${FORM_TAG}_utilization-reason-for-rejection`}
            />
          )}
        </InputRow>
      )}
    </>
  );
};

export interface AddRequestPreauthorizationProps {
  isOnModal?: boolean;
  defaultId?: string;
  clearAction?: (id?: string) => void;
  filterOptions: any;
  refetchList?: () => void;
}

const AddPreauthorisation: React.FC<AddRequestPreauthorizationProps> = ({
  filterOptions,
  isOnModal,
  defaultId,
  clearAction,
  refetchList,
}) => {
  const [listType, setListType] = useState({ 0: 'ICD-10' });
  const { orgHmoId } = getUserPayload();

  const {
    isEdit,
    startEdit,
    setStartEdit,
    handleInputChange,
    handleMultipleFieldsChange,
    inputs,
    actionText,
    action,
    readOnly,
    disableActionButton,
    fetchingData,
    errorFetching,
    unitPriceText,
    toggle,
    getTariff,
    setListInputs,
    loading,
    authDiagnosis,
    setInputs,
    hmoDropdownOptions,
    syncingPreAuth,
    syncPreAuthAction,
    getProviderId,
    isLashmaEnrollee,
    externalPlanType,
    onVisitationTypeChange,
    onTreatmentEndDateChange,
    onTreatmentStartDateChange,
  } = useRequestPreauthorizationForm({
    filterOptions,
    isOnModal,
    defaultId,
    clearAction,
    refetchList,
  });
  const { isLashmaProvider, isLashmaAgency, plan } = useHospitalData();
  const { addToast } = useToasts();
  const { loadVisitationTypes } = useRequestPreauthorizationAPILoader();
  const { loadHmoHospitals } = useHmoProviderHospitalOptions(inputs.providerId);
  const provider = hmoDropdownOptions?.find(({ value }) => value === inputs?.providerId);
  const { flaggedItems, setFlaggedItems } = useBusinessRulesFlaggedItems();
  const { loadAllUtilizationTypes, toggleFetchAllTypes } = useFilterUtilizationOptions(
    'PREAUTH',
    provider?.label || '',
    inputs.providerId,
    inputs.serviceTypeCode,
  );

  const isExternalPlanType = inputs?.isExternalPlanType;
  const { onDiagnosisChange } = useMappedUtilizationToDiagnosis({
    setInputs,
    map: utilizationDiagnosisMap,
    diagnosisFieldName: 'diagnosis',
    utilizationFieldName: 'utilizations',
    utilIdx: 0,
    loadAllUtilizationTypes,
    providerId: inputs.providerId,
    enrolleeId: inputs.enrolleeId || inputs.enrolleeNumber,
    facilityId: inputs.facilityId,
    getTariff,
    visitationType: inputs.serviceTypeCode,
    treatmentDate: inputs.requestDateTime,
    isExternalPlanType,
    externalPlanTypeId: inputs.externalPlanTypeId,
  });

  useEffect(() => {
    if (!inputs.providerId && inputs.provider?.id) {
      const currentInput = { ...inputs };
      const providerId = currentInput.provider.id;
      delete currentInput.provider;
      setInputs({
        ...currentInput,
        providerId,
      });
    }
  }, [inputs.providerId, inputs.provider?.id]);
  const isClinifyHmo = useMemo(() => {
    return isHmoClaimsManagedByClinify(
      hmoDropdownOptions.find(({ value }) => value === inputs?.providerId)?.label,
    );
  }, [inputs.providerId, hmoDropdownOptions.length]);
  const withNotice = provider?.label?.toLowerCase() === 'Leadway Health Limited'.toLowerCase();
  const isOgshia = provider?.label === OGSHIA_AGENCY;

  const _disableActionButton = isClinifyHmo ? disableActionButton : disableActionButton || isEdit;
  const diagnosisIsValid = authDiagnosis.every((_diag) => Object.values(_diag).some(Boolean));

  useEffect(() => {
    if (
      !!provider?.memberPlanId &&
      !!externalPlanType?.id &&
      provider.memberPlanId === externalPlanType.id
    ) {
      setInputs({
        ...inputs,
        isExternalPlanType: true,
        externalPlanTypeId: externalPlanType?.id,
        priority: 'Emergency',
        serviceType: '',
        serviceTypeCode: '',
        utilizations: [
          {
            category: '',
            utilizationId: '',
            utilizationCode: null,
            type: null,
            quantity: '1',
            price: '0',
          },
        ],
      });
    }
  }, [provider?.memberPlanId, externalPlanType?.id]);

  const isInpatient = (inputs.serviceType || '').toLowerCase() === 'inpatient';

  const isDiagnosisCenter =
    plan?.toLowerCase()?.includes('investigation') ||
    plan?.toLowerCase()?.includes('laboratory') ||
    plan?.toLowerCase()?.includes('radiology');
  const disableUtilization = isLashmaEnrollee && isDiagnosisCenter;

  const isSubmitted = !!inputs?.claimId;

  const LASHMA_AUTO_APPROVE_DRUGS = useMemo(() => {
    if (!isLashmaEnrollee) return null;
    if (!authDiagnosis.length) return null;
    const isAntiMalaria = authDiagnosis?.every((_item) =>
      LASHMA_AUTO_APPROVE_DIAGNOSIS.includes(_item?.diagnosisICD10),
    );
    const isWormTreatment = authDiagnosis?.every((_item) =>
      LASHMA_AUTO_APPROVE_DIAGNOSIS_2.includes(_item?.diagnosisICD10),
    );
    const isInsomiaTreatment = authDiagnosis?.every((_item) =>
      LASHMA_AUTO_APPROVE_DIAGNOSIS_3.includes(_item?.diagnosisICD10),
    );
    if (!(isAntiMalaria || isWormTreatment || isInsomiaTreatment)) {
      return null;
    }

    let autoApproveDrugs: string[] = [];
    if (isAntiMalaria) {
      autoApproveDrugs = Object.keys(UTILIZATION_DRUGS_TO_CLASSIFICATION_MAP).filter((_item) =>
        LASHMA_AUTO_APPROVE_MED_CATEGORY.includes(
          UTILIZATION_DRUGS_TO_CLASSIFICATION_MAP[_item].medicationCategory,
        ),
      );
    } else if (isWormTreatment) {
      autoApproveDrugs = Object.keys(UTILIZATION_DRUGS_TO_CLASSIFICATION_MAP).filter((_item) =>
        LASHMA_AUTO_APPROVE_MED_CATEGORY_2.includes(
          UTILIZATION_DRUGS_TO_CLASSIFICATION_MAP[_item].medicationCategory,
        ),
      );
    } else {
      autoApproveDrugs = Object.keys(UTILIZATION_DRUGS_TO_CLASSIFICATION_MAP).filter((_item) =>
        LASHMA_AUTO_APPROVE_MED_CATEGORY_3.includes(
          UTILIZATION_DRUGS_TO_CLASSIFICATION_MAP[_item].medicationCategory,
        ),
      );
    }

    return autoApproveDrugs;
  }, [authDiagnosis]);

  const isAnnualScreening = useMemo(() => {
    if (!isLashmaEnrollee) return false;
    if ([...(inputs.diagnosis || [])]?.[0]?.diagnosisICD10 === ANNUAL_SCREENING_DIAGNOSIS) {
      return true;
    }
    if (inputs?.utilizations?.[0]?.type?.toLowerCase().includes('annual screening')) {
      return true;
    }
    return false;
  }, [inputs?.diagnosis, inputs?.utilizations]);

  const handleDiagnosisChange = (idx: number, value: string) => {
    if (isLashmaEnrollee && value === ANNUAL_SCREENING_DIAGNOSIS) {
      handleInputChange('diagnosis', [
        { diagnosisICD10: ANNUAL_SCREENING_DIAGNOSIS, diagnosisICD11: '', diagnosisSNOMED: '' },
      ]);
      onDiagnosisChange(value, 0);

      const annualScreeningUtil = (inputs?.utilizations || []).find((_item) =>
        _item?.type?.toLowerCase().includes('annual screening'),
      );

      if (!annualScreeningUtil) {
        handleInputChange('utilizations', [
          {
            category: '',
            utilizationId: '',
            utilizationCode: null,
            type: null,
            quantity: '1',
            price: '0',
          },
        ]);
      } else {
        handleInputChange('utilizations', [annualScreeningUtil]);
      }
      return;
    }

    const newData = [...(inputs.diagnosis || [])];
    newData[idx] = {
      ...newData[idx],
      [listTypeMapper[listType[`${idx}`] || 'ICD-10']]: value,
    };
    handleInputChange('diagnosis', newData);
    onDiagnosisChange(value, idx);
  };

  if (fetchingData || errorFetching)
    return <LoaderOrError loading={fetchingData} error={errorFetching} />;

  return (
    <>
      <UtilizationsSummaryTable
        data={{ ...inputs }}
        providerName={provider?.label || ''}
        className={classNames({ hide: !!orgHmoId })}
        type="Preauthorization"
        setInputs={setInputs}
        setItems={(value, i) => {
          setInputs((p) => {
            const prev = cloneDeep(p);
            prev.utilizations[i] = { ...prev.utilizations[i], ...value };
            return { ...prev };
          });
        }}
        getTariff={getTariff}
        action={action}
        disableEdit={
          _disableActionButton ||
          (isEdit && !startEdit ? false : isClinifyHmo && !diagnosisIsValid) ||
          !fieldsAreValid('authutilizationsfields', inputs.utilizations, true)
        }
        isEdit={isEdit}
        readOnly={readOnly}
        utilizationOnlyServices={LASHMA_AUTO_APPROVE_DRUGS}
        flaggedItems={flaggedItems}
        onAddNewItem={
          !(!isExternalPlanType && !disableUtilization && !isAnnualScreening)
            ? undefined
            : () => {
                setInputs((prev) => ({
                  ...prev,
                  utilizations: [...prev.utilizations, utilisationInitialState],
                }));
              }
        }
      />
      <RecordForm clear>
        {withNotice ? <Notice /> : null}
        <FormContent>
          <div className="translate-select__container">
            <ActionHeader
              onButtonClick={syncPreAuthAction}
              loading={syncingPreAuth}
              disabled={!isEdit}
            />
          </div>

          {orgHmoId ? (
            <>
              <SelectWrapper fullWidth>
                <Dropdown
                  title="Facility Name"
                  options={[]}
                  onChange={({ value, hospitalId, address }) => {
                    setInputs({
                      ...inputs,
                      facilityId: hospitalId,
                      facilityName: value,
                      facilityAddress: address,
                    });
                  }}
                  placeholder="Select One"
                  readOnly={false}
                  value={inputs.facilityName || null}
                  loadOptions={loadHmoHospitals}
                  isAsync
                  creatable={false}
                  noPadding
                  isRequired
                  overrideIsRequired
                />
              </SelectWrapper>
              <AddressInput
                label="Facility Address"
                fieldPath="facilityAddress"
                handleInputChange={() => {}}
                readOnly
                value={inputs.facilityAddress}
                placeholder="Enter Facility Address"
              />
            </>
          ) : null}

          <InputRow>
            <SelectWrapper noPadding>
              <DatePicker
                label="Treatment Date and Time"
                isRequired
                overrideIsRequired
                withBorderRadius
                onChange={(date) => {
                  if (
                    [LASHMA_AGENCY, LASHMA_AGENCY_2].includes(provider?.label as string) &&
                    moment(new Date()).diff(moment(date), 'hours') > 720
                  ) {
                    addToast('30 Days Submission Exceeded', { appearance: 'error' });
                  } else {
                    handleInputChange('requestDateTime', date);
                  }
                }}
                value={inputs.requestDateTime}
                placeholderText="Select Date and Time"
                readOnly={false}
                minDate={new Date(new Date().getFullYear() - 100, new Date().getMonth())}
                maxDate={new Date()}
                disabled={readOnly}
                fieldName={`${FORM_TAG}_treatment-date-and-time`}
              />
            </SelectWrapper>
            <SelectWrapper>
              <Dropdown
                title="Coverage Name"
                options={hmoDropdownOptions}
                onChange={({ value, enrolleeId, memberUniqueId }) =>
                  setInputs({ ...inputs, providerId: value, enrolleeId, memberUniqueId })
                }
                placeholder="Select One"
                isRequired
                overrideIsRequired
                value={inputs.providerId ? inputs.providerId : null}
                readOnly={readOnly}
                noPadding
                skipTranslate
                topRightComponent={
                  (externalPlanType?.id || inputs.isExternalPlanType) &&
                  (isLashmaProvider || isLashmaAgency) ? (
                    <CheckBoxWithLabel
                      className="checkbox-container d-flex align-items-center gap-x-2"
                      label="LASHMA-AID"
                      onChange={() => {
                        setInputs({
                          ...inputs,
                          isExternalPlanType: !inputs.isExternalPlanType,
                          priority: !inputs.isExternalPlanType ? 'Emergency' : null,
                          externalPlanTypeId: !inputs.isExternalPlanType
                            ? externalPlanType?.id
                            : undefined,
                          serviceType: '',
                          serviceTypeCode: '',
                          utilizations: [
                            {
                              category: '',
                              utilizationId: '',
                              utilizationCode: null,
                              type: null,
                              quantity: '1',
                              price: '0',
                            },
                          ],
                        });
                      }}
                      checked={inputs.isExternalPlanType}
                      disable
                      // disable={
                      //   isEdit || readOnly || externalPlanType?.id === provider?.memberPlanId
                      // }
                    />
                  ) : null
                }
              />
            </SelectWrapper>
          </InputRow>
          <InputRow>
            <SelectWrapper noPadding>
              <DatePicker
                label="Treatment Start Date and Time"
                withBorderRadius
                onChange={onTreatmentStartDateChange}
                value={inputs.treatmentStartDate}
                placeholderText="Select Date and Time"
                readOnly={false}
                minDate={new Date(new Date().getFullYear() - 100, new Date().getMonth())}
                maxDate={new Date()}
                disabled={readOnly || isSubmitted}
                isRequired={isInpatient}
                overrideIsRequired={isInpatient}
                fieldName={`${FORM_TAG}_treatment-start-date-and-time`}
              />
            </SelectWrapper>
            <SelectWrapper noPadding>
              <DatePicker
                label="Treatment End Date and Time"
                withBorderRadius
                onChange={onTreatmentEndDateChange}
                value={inputs.treatmentEndDate}
                placeholderText="Select Date and Time"
                readOnly={false}
                minDate={new Date(new Date().getFullYear() - 100, new Date().getMonth())}
                maxDate={new Date(new Date().getFullYear() + 100, new Date().getMonth())}
                disabled={readOnly || isSubmitted}
                isRequired={isInpatient}
                overrideIsRequired={isInpatient}
                fieldName={`${FORM_TAG}_treatment-end-date-and-time`}
              />
            </SelectWrapper>
          </InputRow>

          {inputs.providerId ? (
            <>
              <InputRow>
                <SelectWrapper noFlex>
                  <Dropdown
                    title="Visitation Type"
                    options={[]}
                    onChange={({ label, value }) => {
                      onVisitationTypeChange(label, value);
                    }}
                    placeholder="Select One"
                    isRequired
                    overrideIsRequired
                    value={inputs.serviceType || null}
                    readOnly={
                      readOnly ||
                      !inputs.facilityName ||
                      isHmoClaimsManagedByClinify(inputs.facilityName || '') ||
                      isSubmitted
                    }
                    isAsync
                    loadOptions={loadVisitationTypes}
                    additional={{
                      action: 'visitation',
                      providerId: inputs.providerId,
                      hospitalId: inputs.facilityId || '',
                      externalPlanTypeId: inputs.externalPlanTypeId,
                    }}
                    noPadding
                    creatable={false}
                    cacheUniqs={[
                      `${inputs.providerId}_${inputs.facilityId}_${inputs.serviceTypeCode}_${inputs.externalPlanTypeId}`,
                    ]}
                    fieldName={`${FORM_TAG}_visitation-type`}
                  />
                </SelectWrapper>
                <SelectWrapper>
                  <Dropdown
                    title="Priority"
                    options={PRIORITY_SURGERY_OPTIONS}
                    onChange={({ value }) => handleInputChange('priority', value)}
                    placeholder="Select One"
                    value={inputs.priority || null}
                    readOnly={readOnly}
                    creatable
                    noPadding
                    fieldName={`${FORM_TAG}_priority`}
                  />
                </SelectWrapper>
              </InputRow>
              {authDiagnosis.map((diagnose, idx) => {
                const { diagnosisICD10 = '', diagnosisICD11 = '', diagnosisSNOMED = '' } = {
                  ...diagnose,
                };

                return (
                  <Fragment key={idx}>
                    <SelectWrapper fullWidth>
                      {isOgshia ? (
                        <Dropdown
                          title="Clinical Diagnosis"
                          options={OGSHIA_DIAGNOSIS_LIST}
                          onChange={({ value }) => {
                            const newData = [...(inputs.diagnosis || [])];
                            newData[idx] = {
                              ...newData[idx],
                              diagnosisICD10: value,
                            };
                            handleInputChange('diagnosis', newData);
                          }}
                          placeholder="Select One"
                          value={diagnosisICD10 || null}
                          readOnly={readOnly}
                          noPadding
                          isRequired
                          overrideIsRequired
                          fieldName={`${FORM_TAG}_clinical-diagnosis`}
                        />
                      ) : (
                        <Dropdown
                          title="Clinical Diagnosis"
                          onChange={({ value }) => handleDiagnosisChange(idx, value)}
                          placeholder="Select One"
                          value={
                            getDiagnosisValue(
                              diagnosisICD10,
                              diagnosisICD11,
                              diagnosisSNOMED,
                              listType[`${idx}`] || 'ICD-10',
                            ) || null
                          }
                          loadOptions={isExternalPlanType ? undefined : loadDiagnosisOptions}
                          isAsync={!isExternalPlanType}
                          creatable={false}
                          noPadding
                          readOnly={readOnly}
                          isRequired
                          overrideIsRequired
                          additional={{ dropdownType: listType[`${idx}`] || 'ICD-10' }}
                          onFocus={
                            isExternalPlanType
                              ? () => {
                                  toggleFetchAllTypes(true);
                                }
                              : undefined
                          }
                          options={
                            isExternalPlanType
                              ? Object.values(utilizationDiagnosisMap)
                                  .flat()
                                  .map((diagnosis) => ({ value: diagnosis, label: diagnosis }))
                              : []
                          }
                          topRightComponent={
                            <DropdownCheckBox
                              value={listType[`${idx}`] || 'ICD-10'}
                              onChange={
                                isExternalPlanType
                                  ? undefined
                                  : (value) => {
                                      setListType({
                                        ...listType,
                                        [idx]: value,
                                      });
                                    }
                              }
                            />
                          }
                          cacheUniqs={[listType[`${idx}`] || 'ICD-10']}
                          fieldName={`${FORM_TAG}_clinical-diagnosis`}
                        />
                      )}
                    </SelectWrapper>
                    <SelectWrapper fullWidth>
                      {!readOnly && !isExternalPlanType && (
                        <MultiGroupAction
                          items={authDiagnosis}
                          index={idx}
                          initialItem={{
                            diagnosisICD10: null,
                            diagnosisICD11: null,
                            diagnosisSNOMED: null,
                          }}
                          onClick={(value) => {
                            handleInputChange('diagnosis', value);
                          }}
                          disableAdd={
                            (isLashmaEnrollee && authDiagnosis.length > 2) || isAnnualScreening
                          }
                          buttonAddText="Add More Diagnosis"
                        />
                      )}
                    </SelectWrapper>
                  </Fragment>
                );
              })}

              <SelectWrapper fullWidth noPadding>
                <TextArea
                  name="presentingComplain"
                  label="Presenting Complaints"
                  fullWidth
                  onChange={({ target: { value } }) =>
                    handleInputChange('presentingComplain', value)
                  }
                  value={inputs.presentingComplain}
                  readOnly={readOnly}
                  fieldId={inputs?.id}
                  isRequired
                  overrideIsRequired
                  createdBy={inputs?.creatorId}
                  fieldName={`${FORM_TAG}_presenting-complaints`}
                />
              </SelectWrapper>
              <InputRow>
                <>
                  {inputs?.utilizations.map((utilisation, idx) => (
                    <Fragment key={idx}>
                      <Utilisations
                        visitationType={inputs.serviceTypeCode}
                        loadVisitationTypes={loadVisitationTypes}
                        utilisation={utilisation}
                        handleInputChange={(name, value) => {
                          setFlaggedItems([]);
                          handleMultipleFieldsChange('utilizations', name, value, idx);
                        }}
                        getTariff={getTariff}
                        index={idx}
                        loadingPrice={loading}
                        unitPriceText={unitPriceText}
                        readOnly={readOnly || !inputs.providerId}
                        setItems={(value) => setListInputs('utilizations', value, idx)}
                        handleGetDetail={syncPreAuthAction}
                        loadingDetails={syncingPreAuth}
                        isEdit={isEdit}
                        providerId={inputs.providerId}
                        getProviderId={getProviderId}
                        enrolleeId={inputs.enrolleeId || inputs.enrolleeNumber || ''}
                        treatmentDate={inputs.requestDateTime}
                        isClinifyHmo={isClinifyHmo}
                        facilityId={inputs.facilityId}
                        providerName={provider?.label || ''}
                        externalPlanTypeId={inputs.externalPlanTypeId}
                        setInputs={setInputs}
                        isExternalPlanType={isExternalPlanType}
                        flaggedItems={flaggedItems}
                        utilizationOnlyServices={LASHMA_AUTO_APPROVE_DRUGS}
                      />
                      {(!isEdit || (isEdit && startEdit && isClinifyHmo)) &&
                        !isExternalPlanType &&
                        !disableUtilization &&
                        !isAnnualScreening && (
                          <MultiGroupAction
                            items={inputs.utilizations}
                            index={idx}
                            initialItem={utilisationInitialState}
                            onClick={(value) => handleInputChange('utilizations', value)}
                            disableDelete={utilisation.status && utilisation.status !== 'Pending'}
                            buttonAddText="Add More Services"
                          />
                        )}
                    </Fragment>
                  ))}
                </>
              </InputRow>
              <InputRow>
                <TextInput
                  name="totalQuantity"
                  title="Total Quantity"
                  value={calculateTotalUtilization(inputs.utilizations).totalQuantity}
                  readOnly
                />
                <FlaggedAuthenticationInput
                  {...defaultInputProps}
                  name="grandTotal"
                  label="Grand Total"
                  value={withComma(calculateTotalUtilization(inputs.utilizations).grandTotal)}
                  readOnly
                />
              </InputRow>
              <InputRow>
                <ReferralComponent inputs={inputs} readOnly={readOnly} setInputs={setInputs} />
              </InputRow>
              <InputRow>
                <TextInput
                  name="doctor"
                  title="Requested By"
                  readOnly
                  onChange={() => {}}
                  value={inputs.requestedBy}
                  placeholder="Enter Doctor's Name"
                  skipTranslate
                />
                <SelectWrapper>
                  <Dropdown
                    creatable
                    title="Specialty"
                    options={SPECIALTY_OPTIONS}
                    onChange={() => {}}
                    placeholder="Select One"
                    value={inputs.specialty || null}
                    readOnly
                    noPadding
                  />
                </SelectWrapper>
              </InputRow>
              <InputRow>
                <SelectWrapper>
                  <Dropdown
                    title="Rank"
                    options={CLASS_CONSULTATION_OPTIONS}
                    onChange={() => {}}
                    placeholder="Select One"
                    value={inputs.rank || null}
                    readOnly
                    creatable
                    noPadding
                  />
                </SelectWrapper>
                <SelectWrapper>
                  <Dropdown
                    title="Department"
                    options={HOSPITAL_UNIT_OPTIONS}
                    placeholder="Select One"
                    value={inputs?.department || null}
                    onChange={() => {}}
                    readOnly
                    creatable
                    noPadding
                  />
                </SelectWrapper>
              </InputRow>
              {orgHmoId ? null : (
                <>
                  <TextInput
                    fullWidth
                    name="facilityName"
                    title="Facility Name"
                    onChange={() => {}}
                    readOnly
                    value={inputs.facilityName}
                    placeholder="Enter Facility Name"
                    skipTranslate
                  />
                  <AddressInput
                    label="Facility Address"
                    fieldPath="facilityAddress"
                    handleInputChange={() => {}}
                    readOnly
                    value={inputs.facilityAddress}
                    placeholder="Enter Facility Address"
                  />
                </>
              )}
              <InputRow>
                <FlaggedAuthenticationInput
                  name="enrolleePhoneNumber"
                  value={`+${inputs?.enrolleePhoneNumber?.countryCode || '234'}${
                    inputs?.enrolleePhoneNumber?.value || ''
                  }`}
                  onChange={({ target: { value } }) => {
                    const countryCode = getPhoneExtensionCode(
                      inputs?.enrolleePhoneNumber?.countryName || 'Nigeria',
                    );
                    if (value.slice(0, countryCode.length) === countryCode) {
                      value = (value || '').slice(countryCode?.length);
                    }
                    handleInputChange('enrolleePhoneNumber', {
                      ...inputs?.enrolleePhoneNumber,
                      value,
                    });
                  }}
                  label="Enrollee Phone Number"
                  placeholder="Enter Enrollee Phone Number"
                  countryName={inputs?.enrolleePhoneNumber?.countryName || 'Nigeria'}
                  smallHeight
                  readOnly={readOnly || !isClinifyHmo}
                  changeCountryName={(countryName, countryCode) => {
                    handleInputChange('enrolleePhoneNumber', {
                      value: '',
                      countryName,
                      countryCode: countryCode.slice(1),
                    });
                  }}
                  fullBorder
                  withBorderRadius
                  allowFlagSwitch
                  noMargin
                  fullWidth
                  padded
                  halfWidth
                />
                <TextInput
                  name="enrolleeEmail"
                  title="Enrollee Email"
                  onChange={({ target: { value } }) => handleInputChange('enrolleeEmail', value)}
                  readOnly={readOnly || !isClinifyHmo}
                  value={inputs.enrolleeEmail}
                  placeholder="Enter Enrollee Email"
                />
              </InputRow>
              <SelectWrapper fullWidth noPadding>
                <TextArea
                  name="additionalNote"
                  label="Clinical Findings"
                  fullWidth
                  isRequired
                  overrideIsRequired
                  onChange={({ target: { value } }) => handleInputChange('additionalNote', value)}
                  value={inputs.additionalNote}
                  readOnly={readOnly}
                  fieldId={inputs?.id}
                  createdBy={inputs?.creatorId}
                />
              </SelectWrapper>
              <InputRow>
                <DocumentAttacher
                  documents={inputs.documentUrl}
                  handleInputChange={handleInputChange}
                  readOnly={readOnly}
                  type="preauthorization"
                />
              </InputRow>
              <ActionSectionSectionWithAuditDetails
                isEdit={isEdit}
                isEditActive={startEdit}
                toggle={toggle}
                cancelAction={() => setStartEdit(false)}
                disableActionButton={
                  _disableActionButton ||
                  (isEdit && !startEdit ? false : isClinifyHmo && !diagnosisIsValid) ||
                  !fieldsAreValid('authutilizationsfields', inputs.utilizations, true)
                }
                disableDeleteButton={!startEdit}
                onAction={action}
                actionText={actionText}
                inputs={inputs}
              />
            </>
          ) : null}
        </FormContent>

        <Modal
          modalContent={
            <Prompter
              text="Are you sure you want to delete this record?"
              actionText="Delete"
              deleteAction={() => {}}
              cancelAction={() => {}}
              disabled
            />
          }
          isShown={false}
          hide={() => {}}
          handleDone={() => {}}
          isAuthentication
        />
        <ReactTooltip
          id="addPreauthorisationToolTip"
          place="top"
          className="otpTip"
          variant="light"
        />
      </RecordForm>
    </>
  );
};

export default AddPreauthorisation;
