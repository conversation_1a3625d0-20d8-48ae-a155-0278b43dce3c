import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { investigationStatus, UserType } from 'graphql-types/globalTypes';
import { useLazyQuery, useMutation, useQuery } from '@apollo/client';
import { useHistory, useLocation } from 'react-router-dom';
import { useToasts } from 'react-toast-notifications';
import useClinifyId from 'app/shared/hooks/client/useClinifyId';
import errorHandler from 'app/shared/utils/errorHandler';
import useCan from 'app/shared/hooks/auth/useCan';
import { IconTypes } from 'app/shared/utils/generatePermissionsParameter';
import { useGetCacheProfile } from 'app/shared/hooks/fetch/useCacheProfile';
import cache from 'apollo/cache';
import cloneDeep from 'lodash.clonedeep';
import {
  AddInvestigationLabResult,
  AddInvestigationLabResultVariables,
} from 'graphql-types/AddInvestigationLabResult';
import {
  UpdateInvestigationLabResult,
  UpdateInvestigationLabResultVariables,
} from 'graphql-types/UpdateInvestigationLabResult';
import {
  UpdateInvestigationStatus,
  UpdateInvestigationStatusVariables,
} from 'graphql-types/UpdateInvestigationStatus';
import {
  GetTestReferenceRangeFields,
  GetTestReferenceRangeFieldsVariables,
} from 'graphql-types/GetTestReferenceRangeFields';
import { SEND_INVST_TO_PATIENT_EMAIL } from 'apollo-queries/mains/investigation';
import { getUserPayload } from 'app/shared/utils/authentication';
import { usePrintPdf } from 'app/shared/hooks/forms/usePrintPdf';
import timerCalculator from 'app/shared/utils/timerCalculator';
import { useAPIOptionAsync } from 'app/shared/hooks/fetch/useApiOptions';
import { usePaymentOption } from 'app/shared/hooks/fetch/usePaymentOption';
import useHospitalData from 'app/shared/hooks/client/useHospitalData';
import useHmoProviderOptions from 'app/shared/hooks/forms/useHmoProviderOptions';
import { GET_TEST_REFERENCE_RANGE_FIELDS } from 'apollo-queries/mains/hospital';
import ThemeContext from '@constant/themes';
import {
  SendInvestigationToPatientEmail,
  SendInvestigationToPatientEmailVariables,
} from 'graphql-types/SendInvestigationToPatientEmail';
import { GET_USER_PROFILE } from 'apollo-queries/mains/user';
import usePinInterceptor from 'app/shared/hooks/auth/usePinInterceptor';
import { GetUserProfile, GetUserProfileVariables } from 'graphql-types/GetUserProfile';
import { useMandatoryFields } from 'app/shared/hooks/forms/useMandatoryFields';
import { useLexicalEditorsContext } from 'app/shared/contexts/LexicalEdtitorsContext';
import { delay } from 'app/shared/utils/delay';
import {
  calculate_EUCR_EGFR,
  defaultResParams,
  initializeResultList,
  mapRecordType,
  RequestTypeEnum,
} from '../../common/constants';
import { makeLabDocDef } from '../../common/resultPrintDoc';
import { calculateAgeRange } from '../../../settings/constants/reference-range-fields';
import { useModifiedInvestigationResultInputFields } from '../../../../../../shared/contexts/ModifiedLabResultInputFieldsContext';

export default ({ userType, refetchList }) => {
  const theme = useContext(ThemeContext);
  const [readOnly, setReadOnly] = useState(false);
  const [openVerifiedSignatureModal, setOpenVerifiedSignatureModal] = useState<{
    index?: number;
    open?: boolean;
  }>();
  const [openPerformedBySignatureModal, setOpenPerformedBySignatureModal] = useState<{
    index?: number;
    open?: boolean;
  }>();
  const [openPathologistSignatureModal, setOpenPathologistSignatureModal] = useState<{
    index?: number;
    open?: boolean;
  }>();
  const [openRadiologistSignatureModal, setOpenRadiologistSignatureModal] = useState<{
    index?: number;
    open?: boolean;
  }>();
  const [requestType, setRequestType] = useState<RequestTypeEnum>(RequestTypeEnum.Laboratory);
  const shouldUpdateRef = useRef(true);
  const { auditFields } = useGetCacheProfile();
  const { openPdf } = usePrintPdf();
  const { pathname } = useLocation();
  const { addToast } = useToasts();
  const { loadAPIOnOpen: loadStaffOnOpen } = useAPIOptionAsync('staffs', {});
  const { paymentType, paymentOption } = usePaymentOption();
  const { cleanupImageNodes } = useLexicalEditorsContext();
  const { facilityLogo, website, preferenceId } = useHospitalData();
  const {
    clinifyId,
    orgId,
    patientIsSelected,
    clinifyIdOnUrl: patientId,
    searchParam,
  } = useClinifyId();
  const {
    modifiedFields,
    reset: resetModifiedFields,
  } = useModifiedInvestigationResultInputFields();

  const pathArr = pathname.split('/');
  const id = pathArr[3];

  const isPatient = userType === UserType.Patient;
  const defaultParams = defaultResParams[requestType];
  const recordType = mapRecordType[requestType];

  const userTypeToSubjectMapper = {
    [RequestTypeEnum.Radiology]: 'InvestigationRadiology',
    [RequestTypeEnum.Laboratory]: 'InvestigationLabResult',
  };

  const saveDataPathMapper = {
    [RequestTypeEnum.Laboratory]: 'addInvestigationLabResult',
    [RequestTypeEnum.Radiology]: 'addInvestigationRadiologyResult',
  };

  const updateDataPathMapper = {
    [RequestTypeEnum.Laboratory]: 'updateInvestigationLabResult',
    [RequestTypeEnum.Radiology]: 'updateInvestigationRadiologyResult',
  };

  const { orgName, orgAddress } = getUserPayload();
  const [inputs, setInput] = useState<Record<any, any>>({
    ...defaultParams.initialState,
    facilityName: orgName,
    facilityAddress: orgAddress,
  });
  const { refetch } = useQuery<GetTestReferenceRangeFields, GetTestReferenceRangeFieldsVariables>(
    GET_TEST_REFERENCE_RANGE_FIELDS,
    {
      onCompleted: (data) => {
        const customRange = data?.fetchTestReferenceRanges;
        if (customRange?.length) {
          handleSetDefaultReferenceRanges(customRange);
        }
      },
    },
  );
  const updateInputsOnData = (data, dataPath = 'investigation', omitSetRequestType = false) => {
    const investigation = data?.[dataPath] || {};
    const requestType = investigation?.requestType;
    if (!omitSetRequestType) setRequestType(requestType);
    const recordTypeToUse = mapRecordType[requestType];
    const test = investigation?.[recordTypeToUse];
    const testCases =
      recordTypeToUse === 'labResult' ? investigation?.testInfo : investigation?.examinationType;
    const details = initializeResultList(
      testCases || [],
      recordTypeToUse,
      test?.details,
      paymentType,
      auditFields,
    );
    if (test?.id && !modifiedFields.length) setReadOnly(true);
    const stateValues = {
      ...inputs,
      ...test,
      details,
      facilityName: orgName,
      facilityAddress: orgAddress,
    };
    setInput(stateValues);
    window.dispatchEvent(
      new CustomEvent(`Investigation:${investigation.id}`, { detail: { inputs: stateValues } }),
    );

    const testNames = Object.values(stateValues.details)
      .map((detail: any) => detail?.testName)
      .filter((i) => !!i);

    const shouldSkipReferenceRange = !!test?.id || !testNames?.length || !preferenceId;

    if (!shouldSkipReferenceRange) {
      refetch({
        facilityPreferenceId: preferenceId as string,
        testNames,
      });
    }
  };
  const { loading: fetchingData, error: fetchingErr, data } = useQuery<any>(
    defaultParams.fetchQuery,
    {
      variables: {
        clinifyId,
        id,
      },
      onCompleted: (data) => {
        if (patientId && data?.investigation?.profile?.clinifyId !== patientId) {
          replace(`/facility/process-investigation${searchParam}`);
          return refetchList?.();
        }
        updateInputsOnData(data);
      },
    },
  );
  const testNames = Object.values(inputs?.details)
    .map((detail: any) => detail?.testName)
    .filter((i) => !!i);
  const isEdit = !!data?.investigation?.[recordType]?.id;
  const skipReferenceRange = isEdit || !testNames?.length || !preferenceId;
  const { replace } = useHistory();
  const { data: selectedPatient, loading: loadingSelectePatient } = useQuery<
    GetUserProfile,
    GetUserProfileVariables
  >(GET_USER_PROFILE, {
    variables: { id: patientId as string },
    skip: !patientIsSelected,
  });

  const { hmoDropdownOptions } = useHmoProviderOptions(clinifyId);

  const labResultExist = !!data?.investigation?.labResult?.details?.length;
  const query = isEdit ? defaultParams.updateQuery : defaultParams.addQuery;
  const rest = cloneDeep(inputs);
  delete rest.id;

  const [mutateRecord, { loading: mutateLoading }] = useMutation<
    AddInvestigationLabResult | UpdateInvestigationLabResult,
    UpdateInvestigationLabResultVariables | AddInvestigationLabResultVariables
  >(query, {
    onCompleted: () => {
      setReadOnly(true);
      addToast(`Record ${isEdit ? 'Updated' : 'Added'} Successfully`, { appearance: 'success' });
    },
    onError: (err) => addToast(errorHandler(err), { appearance: 'error' }),
    update: (cache, { data }) => {
      setReadOnly(true);
      const dataPath = shouldUpdateRef.current
        ? updateDataPathMapper?.[requestType]
        : saveDataPathMapper?.[requestType];

      cache.writeQuery({
        query: defaultParams.fetchQuery,
        data: {
          investigation: data?.[dataPath],
        },
        variables: { clinifyId, id },
      });
      const resultPath =
        requestType === RequestTypeEnum.Laboratory ? 'labResult' : 'radiologyResult';
      const { creatorName, lastModifierName, updatedDate, createdDate } = data?.[dataPath]?.[
        resultPath
      ];
      setInput({ ...inputs, creatorName, lastModifierName, updatedDate, createdDate });
    },
  });
  const { addSubRecordAction } = usePinInterceptor({
    updateAction: mutateRecord,
    addAction: mutateRecord,
    icon: 'investigation',
  });

  const [_updateStatus, { loading: updatingStatus }] = useMutation<
    UpdateInvestigationStatus,
    UpdateInvestigationStatusVariables
  >(defaultParams.updateStatusQuery, {
    onCompleted: (data) => {
      const newStatus = data?.updateInvestigationStatus?.status || 'Updated';
      addToast(`Record ${newStatus} Successfully`, { appearance: 'success' });
    },
    onError: (err) => {
      if (err && err?.message?.includes('No Result'))
        err.message = 'Please Save Investigation Result';
      addToast(errorHandler(err), { appearance: 'error' });
      const cachedData: any = cache.readQuery({
        query: defaultParams.fetchQuery,
        variables: { clinifyId, id },
      });
      cache.writeQuery({
        query: defaultParams.fetchQuery,
        data: {
          investigation: {
            ...cachedData?.investigation,
          },
        },
        variables: { clinifyId, id },
      });
    },
  });
  const { addSubRecordAction: updateStatus } = usePinInterceptor({
    updateAction: _updateStatus,
    addAction: _updateStatus,
    icon: 'investigation',
  });

  const [sendResultToPatientEmail] = useLazyQuery<
    SendInvestigationToPatientEmail,
    SendInvestigationToPatientEmailVariables
  >(SEND_INVST_TO_PATIENT_EMAIL, {
    variables: {
      id: data?.investigation?.id,
      origin: window.location.origin,
    },
    onCompleted: () => {
      addToast('Result Sent To Patient Successfully', { appearance: 'success' });
    },
    onError: () =>
      addToast('Error Sending Result To Patient, Please Try Again', { appearance: 'error' }),
  });

  const handleInputChange = (key: string, value: any) => {
    return setInput({ ...inputs, [key]: value });
  };

  const printResults = async (resultToPrint: any = null) => {
    const docDef = await makeLabDocDef(
      resultToPrint || data?.investigation,
      data?.investigation?.profile,
      theme,
      auditFields.fullName,
      { facilityLogo: facilityLogo as string, website: website as string },
    );
    openPdf(docDef);
  };

  const handleSetTestValue = (
    value: any,
    name: string,
    detailIndex: number,
    resultIndex: number,
    setValueOn: 'testResults' | 'extraTestResults' = 'testResults',
  ) => {
    const details = JSON.parse(JSON.stringify({ ...inputs }?.details));
    const result = details[detailIndex]?.[setValueOn][resultIndex];
    const update = { ...result, [name]: value };
    const egfrFieldName = 'EGFR';

    if (
      details[detailIndex]?.testName === 'EUCR (ELECTROLYTES UREA AND CREATININE) - CORE' &&
      update?.name === 'Creatinine'
    ) {
      const { gender, dateOfBirth } = selectedPatient?.profile.personalInformation || {};
      const today = new Date();
      const birthDate = new Date(dateOfBirth);
      const ageInYears = today.getFullYear() - birthDate.getFullYear();

      const __result = calculate_EUCR_EGFR(
        ageInYears,
        gender as string,
        Number(update?.value),
        update?.unit,
      );
      const egfrValue = __result ? `${__result} mL/min/1.73m\u00B2` : '';

      const egfrIndex = details[detailIndex]?.[setValueOn].findIndex(
        (f) => f.name === egfrFieldName,
      );

      if (egfrIndex !== -1) {
        details[detailIndex][setValueOn][egfrIndex] = {
          ...details[detailIndex][setValueOn][egfrIndex],
          value: egfrValue,
        };
      }
    }

    /**
     * select the detail index,
     * select what result type to set value on: => testResults or extraTestResults
     * apply update to the specific result index
     */
    details[detailIndex][setValueOn][resultIndex] = update;
    setInput({ ...inputs, details });
  };

  useEffect(() => {
    if (!skipReferenceRange) {
      refetch({
        facilityPreferenceId: preferenceId as string,
        testNames,
      });
    }
  }, [JSON.stringify(testNames)]);

  const handleSetDefaultReferenceRanges = async (referenceRanges: Record<string, any>[]) => {
    if (!referenceRanges) return;
    const details = JSON.parse(JSON.stringify({ ...inputsRef?.current }?.details));
    const _details = cloneDeep(details);
    const { gender, dateOfBirth } = selectedPatient?.profile.personalInformation || {};

    let rangeKey = 'referenceRange';
    let patientAgeRange = 'All';
    if (dateOfBirth) {
      patientAgeRange = calculateAgeRange(new Date(dateOfBirth));
    }
    switch (gender) {
      case 'Male':
        rangeKey = 'maleReferenceRange';
        break;
      case 'Female':
        rangeKey = 'femaleReferenceRange';
        break;
      default:
        rangeKey = 'referenceRange';
        break;
    }

    Object.keys(_details).forEach((idx) => {
      let _testResult = cloneDeep(_details[idx]);
      referenceRanges.forEach((range) => {
        if (_testResult?.testName === range?.testName) {
          const ranges = JSON.parse(range?.[rangeKey] || '{}');
          const defaultRanges = JSON.parse(range?.referenceRange || '{}');
          _testResult = _testResult.testResults.map((item) => {
            return {
              ...item,
              range:
                ranges?.[patientAgeRange]?.[item.name] ||
                ranges?.All?.[item.name] ||
                defaultRanges?.[patientAgeRange]?.[item.name] ||
                defaultRanges?.All?.[item.name],
            };
          });
          _details[idx].testResults = _testResult;
        }
      });
    });
    setInput({ ...inputs, details: _details });
  };

  const handleMultiInputChange = (key: string, value: any, index: number) => {
    const keysCanChangeDuration = ['collectionDate', 'resultDate'];
    const itemToUpdate = inputs?.details?.[index];
    const newValue = { ...itemToUpdate, [key]: value };
    if (keysCanChangeDuration.includes(key)) {
      const { collectionDate = '', resultDate = '' } = newValue;
      newValue.duration =
        collectionDate && resultDate
          ? timerCalculator(
              new Date(collectionDate).getTime(),
              new Date(resultDate).getTime(),
              false,
            )
          : '00:00:00';
    }
    const update = { ...inputs, details: { ...inputs?.details, [index]: newValue } };
    setInput(update);
  };

  const setVerifiedBySignatureData = (index: number) => (
    signature: string,
    signatureType: string,
  ) => {
    const itemToUpdate = inputs?.details?.[index];
    const newValue = {
      ...itemToUpdate,
      verifiedBySignature: signature,
      verifiedBySignatureType: signatureType,
      verifiedBySignatureDateTime: new Date().toISOString(),
    };
    const newDetails = inputs?.details;
    newDetails[index] = newValue;
    const update = { ...inputs, details: newDetails };
    setInput(update);
  };

  const setPerformedBySignatureData = (index: number) => (
    signature: string,
    signatureType: string,
  ) => {
    const itemToUpdate = inputs?.details?.[index];
    const isRadiology = requestType === RequestTypeEnum.Radiology;
    const newValue = isRadiology
      ? {
          ...itemToUpdate,
          radiographerSignature: signature,
          radiographerSignatureType: signatureType,
          radiographerSignatureDateTime: new Date().toISOString(),
        }
      : {
          ...itemToUpdate,
          performedBySignature: signature,
          performedBySignatureType: signatureType,
          performedBySignatureDateTime: new Date().toISOString(),
        };
    const newDetails = inputs?.details;
    newDetails[index] = newValue;
    const update = { ...inputs, details: newDetails };
    setInput(update);
  };

  const setPathologistSignatureData = (index: number) => (
    signature: string,
    signatureType: string,
  ) => {
    const itemToUpdate = inputs?.details?.[index];
    const newValue = {
      ...itemToUpdate,
      pathologistSignature: signature,
      pathologistSignatureType: signatureType,
      pathologistSignatureDateTime: new Date().toISOString(),
    };
    const newDetails = inputs?.details;
    newDetails[index] = newValue;
    const update = { ...inputs, details: newDetails };
    setInput(update);
  };

  const setRadiologistSignatureData = (index: number) => (
    signature: string,
    signatureType: string,
  ) => {
    const itemToUpdate = inputs?.details?.[index];
    const newValue = {
      ...itemToUpdate,
      radiologistSignature: signature,
      radiologistSignatureType: signatureType,
      radiologistSignatureDateTime: new Date().toISOString(),
    };
    const newDetails = inputs?.details;
    newDetails[index] = newValue;
    const update = { ...inputs, details: newDetails };
    setInput(update);
  };

  const setOpenVerifiedBySignature = (index: number) => (open: boolean) => {
    setOpenVerifiedSignatureModal((prev) => ({ ...prev, [index]: open }));
  };

  const setOpenPerformedBySignature = (index: number) => (open: boolean) => {
    setOpenPerformedBySignatureModal((prev) => ({ ...prev, [index]: open }));
  };

  const setOpenPathologistSignature = (index: number) => (open: boolean) => {
    setOpenPathologistSignatureModal((prev) => ({ ...prev, [index]: open }));
  };

  const setOpenRadiologistSignature = (index: number) => (open: boolean) => {
    setOpenRadiologistSignatureModal((prev) => ({ ...prev, [index]: open }));
  };

  const handleUpdateStatus = (newStatus: investigationStatus) =>
    updateStatus({ variables: { id: data?.investigation?.id, status: newStatus } });

  const subject = userTypeToSubjectMapper[requestType];
  const canAdd = useCan(subject as IconTypes, 'Create', {}, true);
  const canEdit = useCan(subject as IconTypes, 'Edit', {}, false);

  const inputsRef = useRef(inputs);

  useEffect(() => {
    inputsRef.current = inputs;
  }, [inputs]);

  const handleMutateRecord = async (isUpdate = false) => {
    shouldUpdateRef.current = isUpdate;
    await cleanupImageNodes();
    await delay(100);
    resetModifiedFields();
    addSubRecordAction({
      variables: {
        id: clinifyId,
        input: {
          ...rest,
          details: Object.values(inputsRef.current?.details || inputs?.details),
          additionalNote:
            requestType === RequestTypeEnum.Laboratory ? inputs?.additionalNote : undefined,
          concealAdditionalNote: undefined,
        },
        investigationId: data?.investigation?.id,
      },
    });
  };

  const onHmoProviderChange = (value) => {
    setInput((prev) => {
      return { ...prev, hmoProviderId: value };
    });
  };

  const onRemoveClaimHandler = () => {
    setInput((prev) => ({ ...prev, hmoProviderId: null, hmoClaim: null }));
  };

  const showPaDetails = data?.investigation?.id && data?.investigation?.hospitalId === orgId;

  const investigation = useMemo(() => {
    const path = data?.investigation?.requestType === 'Laboratory' ? 'testInfo' : 'examinationType';
    const _investigation = { ...data?.investigation };
    if (_investigation) {
      _investigation[path] = _investigation[path]?.map((singleData) => ({
        ...singleData,
        preauthorizationDetails: _investigation.preauthorizationDetails?.find(
          (preauthDetails) => preauthDetails.id === singleData.preauthorizationDetailsId,
        ),
      }));
    }

    return _investigation || {};
  }, [data?.investigation]);

  const showClaimOnForm = !inputs?.id || investigation?.hospitalId === orgId;

  const resultsInputs: Record<string, any>[] = Object.values(inputs?.details || [inputs?.details]);

  const formName =
    requestType === RequestTypeEnum.Laboratory
      ? 'Process Investigation'
      : 'Process Investigation (Radiology)';
  const { fieldsAreValid } = useMandatoryFields({ form: formName, inputs: resultsInputs });

  return {
    investigation,
    fetchingData: fetchingData || loadingSelectePatient,
    inputs,
    fetchingErr,
    handleInputChange,
    populate: !isPatient,
    mutateRecord: handleMutateRecord,
    mutateLoading,
    readOnly,
    setReadOnly,
    handleUpdateStatus,
    updatingStatus,
    handleMultiInputChange,
    handleSetTestValue,
    canAdd,
    canEdit,
    isEdit,
    sendResultToPatientEmail,
    requestType,
    labResultExist,
    printResults,
    loadStaffOnOpen,
    paymentOption,
    hmoDropdownOptions,
    onHmoProviderChange,
    showPaDetails,
    showClaimOnForm,
    onRemoveClaimHandler,
    setVerifiedBySignatureData,
    setOpenVerifiedBySignature,
    openVerifiedSignatureModal,
    setPerformedBySignatureData,
    setOpenPerformedBySignature,
    openPerformedBySignatureModal,
    setPathologistSignatureData,
    setOpenPathologistSignature,
    openPathologistSignatureModal,
    setRadiologistSignatureData,
    setOpenRadiologistSignature,
    openRadiologistSignatureModal,
    fieldsAreValid,
  };
};
