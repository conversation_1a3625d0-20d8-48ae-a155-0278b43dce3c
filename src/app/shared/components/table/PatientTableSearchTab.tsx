import './styles/patientTableSearchTab.scss';

import colors from '@constant/colors';
import { DOCTOR_STATUS_OPTIONS, REQUEST_INVESTIGATION_OPTIONS } from '@constant/options';
import Dropdown from 'app/shared/components/dropdown/Dropdown';
import { DatePickerWrapper } from 'app/shared/components/wrappers/Wrapper';
import useAppData from 'app/shared/hooks/client/useAppData';
import useClinifyId from 'app/shared/hooks/client/useClinifyId';
import { userType } from 'app/shared/utils/authTracker';
import ApproveIcon from '@material-ui/icons/Check';
import RejectIcon from '@material-ui/icons/Close';
import GroupAddIcon from '@material-ui/icons/GroupAdd';
import { ReactComponent as ArchiveIcon } from 'assets/icons/archive.svg';
import { ReactComponent as AssignIcon } from 'assets/icons/assign.svg';
import { ReactComponent as SendMoneyIcon } from 'assets/icons/bill-sendMoney.svg';
import { ReactComponent as CheckInIcon } from 'assets/icons/checkin.svg';
import { ReactComponent as CheckOutIcon } from 'assets/icons/checkout.svg';
import { ReactComponent as ClearFilterIcon } from 'assets/icons/clear-filter.svg';
import { ReactComponent as FundWalletIcon } from 'assets/icons/fundwallet.svg';
import { ReactComponent as ReassignIcon } from 'assets/icons/reassign.svg';
import { ReactComponent as TrashIcon } from 'assets/icons/trash.svg';
import { ReactComponent as UnarchiveIcon } from 'assets/icons/unarchive.svg';
import { ReactComponent as SubmitClaimIcon } from 'assets/icons/submit_claims.svg';
import { ReactComponent as ViewArchivedIcon } from 'assets/icons/view-archived.svg';
import { ReactComponent as ViewUnarchivedIcon } from 'assets/icons/view-unarchived.svg';
import { ReactComponent as PrinterIcon } from 'assets/icons/printer-device.svg';
import { ReactComponent as MovePreauthToClaimsIcon } from 'assets/icons/move-preauth-to-claims-icon.svg';
import { ReactComponent as CheckoutIcon2 } from 'assets/icons/new-checkout-icon.svg';
import {
  FilterInput,
  InvestigationFilterInput,
  OrganisationProfileType,
  UserType,
  VitalFilterInput,
} from 'graphql-types/globalTypes';
import React, { ChangeEvent, FC, useContext } from 'react';
import RefreshIcon from '@material-ui/icons/Refresh';
import SendMailIcon from '@material-ui/icons/Mail';
import PeopleIcon from '@material-ui/icons/People';
import PdfIcon from '@material-ui/icons/PictureAsPdf';
import { useHistory } from 'react-router-dom';
import { Tooltip as ReactTooltip } from 'react-tooltip';
import useGetPaymentTypeOptions from 'app/shared/hooks/forms/useGetPaymentTypeOptions';
import { ReactComponent as CompletePayment } from 'assets/icons/complete-payment.svg';
import {
  EXTENDED_PROVIDER_TYPES,
  PROVIDER_TYPES,
} from 'app/pages/dashboard/views/submit-claims/constants';
import { OGSHIA_CLAIMS_FLAG_OPTIONS } from 'app/shared/utils/ogshia-flag-description';
import classNames from 'classnames';
import DownloadIcon from '@material-ui/icons/GetApp';
import { PROVIDER_CODE_TO_TRAINING_DOCS_MAP } from 'app/pages/dashboard/views/cliams/utils/training-docs-resource';
import Button from '../buttons/Button';
import { OutlineButton } from '../buttons/OutlineButton';
import { OutlineIconButton } from '../buttons/OutlineIconButton';
import SearchInput from '../inputs/SearchInput';
import DatePicker from '../pickers/DatePicker';
import SearchTab from './SearchTab';
import {
  BILL_TYPE_DROPDOWN,
  COVERAGE_TYPE_OPTIONS,
} from '../../../pages/dashboard/views/billings/constants';
import { WALLET_TRANSACTION_TYPE_OPTIONS } from '../../../pages/dashboard/views/wallet/constants';
import { InvoicePaymentStatusOptions } from '../../../pages/dashboard/views/invoice/contants';
import ProviderOptions from '../../../pages/dashboard/views/provider-network/components/ProviderOptions';
import { getUserPayload } from '../../utils/authentication';
import CoverageFilterComponent from '../inputs/CoverageFilterComponent';
import PortalRegisteredAccounts from '../../../pages/dashboard/views/cliams/components/PortalRegisteredAccounts';
import TrackboardTooltip from '../../../pages/dashboard/views/inpatient-list/components/TrackboardTooltip';
import useHospitalData from '../../hooks/client/useHospitalData';
import ServiceTypeFilterComponent from '../inputs/ServiceTypeFilterComponent';
import { ExcelIcon } from '../../../../assets';
import {
  TRANSACTION_CATEGORY_OPTIONS,
  TRANSACTION_TYPE_OPTIONS,
} from '../../../pages/dashboard/views/transactions/contants';
import { OGSHIA_AGENCY } from '../../utils/is-clinify-hmo';
import FacilityStaffOptions from '../../../pages/dashboard/views/provider-network/components/FacilityStaffOptions';
import HmoPlanOptions from '../../../pages/dashboard/views/provider-network/components/HmoPlanOptions';
import { BulkRegistrationActions } from './BulkRegistrationActions';
import { openRemoteResources } from '../../../pages/dashboard/views/cliams/utils/open-remote-resources';
import ThemeContext from '../../../../@constant/themes';
import { TimeSortFilterComponent } from './TimeSortFilterComponent';
import FetchEnrollmentAgencies from './components/FetchEnrollmentAgencies';

type OptionType = {
  label: string;
  value?: string | void | null;
};

export type DateFilterRangeType = {
  maxDate?: Date;
  minDate?: Date;
};

export interface PatientSearchTabProps {
  filterOptions: (VitalFilterInput | FilterInput | InvestigationFilterInput) & {
    status?: string;
    hospitalId?: string;
    providerType?: string;
    transactionType?: string;
    transactionCategory?: string;
    flag?: string;
    filterDateField?: string;
  };
  canAdd?: boolean;
  isInitialFetch?: boolean;
  setDateRange: (key: 'from' | 'to') => any;
  clearFilter: () => void;
  handleSearch: (event: ChangeEvent<HTMLInputElement> | ChangeEvent<HTMLSelectElement>) => void;
  handleSelect?: (data: any) => void;
  toggleViewArchive?: () => void;
  handleArchive?: (records: string[], archive?: boolean) => void;
  handleDelete?: (records: string[]) => void;
  showSearchTab?: boolean;
  recordsSelected?: string[];
  archive?: boolean;
  isOnModal?: boolean;
  showAddButton?: boolean;
  selectedRecordType?: string;
  showStatusDropdown?: boolean;
  billServiceTypeDropdown?: boolean;
  statusDropdownLabel?: string;
  statusDropDownOptions?: OptionType[];
  showDelete?: boolean;
  isFrontDesk?: boolean;
  handleAddNew?: () => void;
  toggle: () => void;
  toggleArchive: () => void;
  showArchiveModalPrompt: boolean;
  showDeleteBtn?: boolean;
  showAchived?: boolean;
  checkOut?: () => void;
  reassign?: () => void;
  filterByStatus: (value: any, objKey?: string) => void;
  filterByStatusDelayed: (value: any, objKey?: string) => void;
  handleMultipleFilterUpdate: (items: Record<string, any>) => void;
  canBulkCheckIn?: boolean;
  canBulkReAssign?: boolean;
  canBulkAssign?: boolean;
  canBulkCheckOut?: boolean;
  showDoctorStatusOption?: boolean;
  dateFilterEnabled?: boolean;
  dateFilterDisabled?: boolean;
  recordTypeLower?: string;
  isSupplies?: boolean;
  dateFilterRange?: DateFilterRangeType;
  canArchive?: boolean;
  canDelete?: boolean;
  billing?: any;
  showRefreshBtn?: boolean;
  handleRefresh?: () => void;
  handleSubmitClaim?: () => void;
  handlePrintClaim?: () => void;
  handleEmailClaim?: () => void;
  showHmoOptions?: boolean;
  hmoOptions?: OptionType[];
  filterByHmo?: (value: any, objKey?: string) => void;
  hmoDropdownLabel?: string;
  movePreauthsToClaims?: () => void;
  handleSendClaimReport: () => void;
  disableMoveToClaims?: boolean;
  disableSubmitBatch?: boolean;
  showPrimaryButton?: boolean;
  primaryText?: string;
  primaryAction?: () => void;
  PrimaryIcon?: React.ReactElement;
  showWalletTransactionFilter?: boolean;
  showInvoicePaymentFilter?: boolean;
  disablePrintReport?: boolean;
  handlePrintReport?: () => void;
  virtualCareFilterOptions?: Record<string, any>;
  virtualCareFilterByStatus?: (value: any, objKey?: string) => void;
  showPrintButton?: boolean;
  handlePrintAction?: () => void;
  handleBulkActivate?: () => Promise<unknown>;
  downloadEnrolleeList?: () => Promise<unknown>;
  showProviderDropdown?: boolean;
  overrideAddNew?: boolean;
  showCoverageFilter?: boolean;
  handleAcceptOrRejectClick?: (value: string) => void;
  printButtonIsActive?: boolean;
  overrideHmoProviderOptionsValue?: OptionType[];
  printPaymentAdvice?: () => void;
  emailPaymentAdvice?: () => void;
  handlePrintAppointmentsSummary?: () => void;
  showCompletePaymentBtn?: boolean;
  handleCompletePayment?: (ids: string[]) => void;
  showServiceTypeFilter?: boolean;
  downloadEmployerInvoice?: () => Promise<unknown>;
  showProviderTypes?: boolean;
  showPlanCategory?: boolean;
  planCategoryList?: OptionType[];
  showFeedbackCategory?: boolean;
  feedbackCategoryList?: OptionType[];
  showDownloadButton?: boolean;
  downloadingData?: boolean;
  handleDataDownload?: () => void;
  showFilterDateField?: boolean;
  filterDateFieldOptions?: OptionType[];
  searchInputTopRightComponent?: JSX.Element;
  showTimeSortFilter?: boolean;
  showBulkRegistrationUpload?: boolean;
}

const PatientTableSearchTab: FC<PatientSearchTabProps> = ({
  showSearchTab,
  isInitialFetch,
  filterOptions,
  canAdd,
  setDateRange,
  clearFilter,
  handleSearch,
  handleSelect = () => {},
  archive,
  showDoctorStatusOption,
  toggleViewArchive,
  recordsSelected = [],
  isOnModal,
  selectedRecordType,
  showAddButton,
  handleAddNew = () => {},
  toggle,
  showDeleteBtn = true,
  showStatusDropdown = false,
  billServiceTypeDropdown = false,
  isFrontDesk,
  statusDropdownLabel = 'All Status',
  statusDropDownOptions = [],
  checkOut,
  reassign,
  canBulkAssign,
  canBulkCheckIn,
  canBulkReAssign,
  canBulkCheckOut,
  filterByStatus,
  handleMultipleFilterUpdate,
  dateFilterEnabled,
  dateFilterDisabled,
  recordTypeLower,
  showAchived,
  dateFilterRange,
  canArchive,
  canDelete,
  billing,
  showRefreshBtn,
  handleRefresh = () => {},
  handleSubmitClaim = () => {},
  handlePrintClaim = () => {},
  handleEmailClaim = () => {},
  showHmoOptions = false,
  hmoOptions = [],
  filterByHmo = () => {},
  hmoDropdownLabel = 'All Coverage',
  movePreauthsToClaims = () => {},
  handleSendClaimReport = () => {},
  disableMoveToClaims,
  disableSubmitBatch,
  showPrimaryButton,
  primaryText,
  primaryAction,
  PrimaryIcon,
  showWalletTransactionFilter,
  showInvoicePaymentFilter,
  handlePrintReport,
  disablePrintReport,
  showPrintButton,
  handlePrintAction,
  handleBulkActivate,
  downloadEnrolleeList,
  showProviderDropdown,
  overrideAddNew,
  toggleArchive,
  showCoverageFilter,
  handleAcceptOrRejectClick,
  printButtonIsActive,
  overrideHmoProviderOptionsValue,
  printPaymentAdvice,
  emailPaymentAdvice,
  handlePrintAppointmentsSummary,
  showCompletePaymentBtn,
  handleCompletePayment,
  showServiceTypeFilter,
  downloadEmployerInvoice,
  showProviderTypes,
  showPlanCategory,
  planCategoryList,
  showFeedbackCategory,
  feedbackCategoryList,
  showDownloadButton,
  showBulkRegistrationUpload = true,
  downloadingData,
  handleDataDownload,
  showFilterDateField,
  filterDateFieldOptions,
  searchInputTopRightComponent,
  showTimeSortFilter,
}) => {
  const history = useHistory();
  const { clinifyIdOnUrl, searchParam, hmoId, hospitalIdOnUrl } = useClinifyId();
  const {
    isProviderHmoClaimsManagedByClinify,
    isLashmaAgency,
    hospitalHasSingleHmoProviderSupport,
    isProviderFromHmoWithoutSubscription,
  } = useHospitalData();
  const { providerCode } = useContext(ThemeContext);
  const { partnerId, isPartnerProfile, orgHmoId, orgName, orgId } = getUserPayload();
  const isHmoUser = !!orgHmoId;

  const loggedInUser = userType();
  const isPatient = loggedInUser === UserType.Patient;
  const isEnrollmentAdmin = loggedInUser === UserType.EnrollmentAdmin;
  const isEnrollmentAgent = loggedInUser === UserType.EnrollmentAgent;
  const isEnrollmentAgency = loggedInUser === UserType.EnrollmentAgency;
  const isEnrollmentTpaNonTpa = loggedInUser === UserType.EnrollmentTpaNonTpa;
  const isFieldManager = loggedInUser === UserType.FieldManager;
  const isFieldOfficer = loggedInUser === UserType.FieldOfficer;

  const { paymentTypeOptions } = useGetPaymentTypeOptions();

  const {
    location: { pathname },
  } = history;
  const isVisible = showSearchTab && !isInitialFetch;
  const dateFilterReadOnly = dateFilterEnabled || isVisible;
  const currentUrl = history.location.pathname;
  const { isMobile } = useAppData();
  const recordPathMapper = {
    laboratory: 'Lab Test',
    'vital-signs': 'Vital Sign',
    radiology: 'Radiology Exam',
    surgery: 'Procedure',
    'patient-registration': 'Patient',
    'request-investigation': 'Investigation',
    'request-referral': 'Referral',
    'request-preauthorizations': 'Preauthorization',
    supplies: 'Inventory',
    supply: 'Inventory',
    billing: 'Bill',
    'submit-claim': 'Claim',
    'nursing-services': 'Service',
    invoices: 'Invoice',
    'handover-notes': 'Note',
    'medical-reports': 'Report',
    'patient-wallet': 'Deposit',
    transfers: 'Transfer',
    referrals: 'Referral',
    plans: 'Plan',
  };
  const overrideTextMapper = {};

  const currentUrlSplit = currentUrl.split('/');
  const recordPath = currentUrlSplit[2];
  let addNewText =
    recordPathMapper[recordPath] || `${recordPath?.[0].toUpperCase()}${recordPath?.slice(1)}`;

  if (recordPath === 'provider-network') {
    if (currentUrlSplit[3] === 'patient-registration') {
      addNewText = 'Patient';
    }
  }

  let _pathname = pathname;
  if (addNewText === 'Pregnancy-care' || addNewText === 'Deposit') {
    addNewText = selectedRecordType;
  }

  if (addNewText === 'Bundles' && selectedRecordType) {
    addNewText = `${selectedRecordType} Bundle`;
    _pathname = `${_pathname}/${selectedRecordType.toLocaleLowerCase()}`;
  }

  if (addNewText === 'Feedbacks') {
    addNewText = 'Feedback';
  }

  if (recordTypeLower === 'oncology') {
    _pathname = `${_pathname}/oncology`;
  }

  let refreshText = 'Refresh';
  if (recordTypeLower === 'hmoclaim' || recordTypeLower === 'preauthorization') {
    refreshText = 'Fetch Status';
  }

  const noRecordsSelected = !recordsSelected?.length;
  const ActiveArchive = archive
    ? { Icon: UnarchiveIcon, Text: 'Unarchive' }
    : { Icon: ArchiveIcon, Text: 'Archive' };
  const ActiveViewArchive = archive
    ? { Icon: ViewUnarchivedIcon, Text: 'View Unarchived' }
    : { Icon: ViewArchivedIcon, Text: 'View Archived' };
  const iconColor = (condition) =>
    noRecordsSelected || condition ? colors.tintGrey : colors.secondaryColor;

  const canViewBalance = [UserType.Patient, UserType.OrganizationFrontDeskOfficer].includes(
    loggedInUser as UserType,
  );

  const showInvestigationTypeDropdown = ['investigations', 'request-investigation'].includes(
    recordPath,
  );
  const isTransactionsList = recordPath === 'transactions';
  const isClaimsPayoutList = recordTypeLower === 'claimspayout';
  const showFlagDropdownFilter =
    ['claims', 'submit-claim'].includes(recordPath) && isHmoUser && orgName === OGSHIA_AGENCY;

  if ((hmoId || isProviderFromHmoWithoutSubscription) && recordPath === 'patient-registration') {
    addNewText = 'Enrollee';
  }
  const isEnrolleeRegistration =
    recordPath === 'patient-registration' &&
    (isHmoUser || (isProviderHmoClaimsManagedByClinify && hospitalHasSingleHmoProviderSupport));

  return (
    <div className="patient-record-list-tab">
      {canAdd && showAddButton && (
        <Button
          text={
            overrideTextMapper[recordPath] ||
            `Add New ${isOnModal ? selectedRecordType : addNewText}`
          }
          onClick={() =>
            isOnModal || overrideAddNew
              ? handleAddNew()
              : history.push(`${_pathname}/add${searchParam}`)
          }
          withIcon
          addButton
          minWidth="auto"
          className="table-add-button"
        />
      )}
      {showPrimaryButton && (
        <Button
          text={primaryText}
          onClick={primaryAction}
          withIcon
          icon={PrimaryIcon}
          minWidth="auto"
          className="table-add-button"
        />
      )}
      <div className="search-tab-section">
        <SearchTab isVitals>
          {recordTypeLower === 'preauthorization' && (
            <>
              {isMobile ? (
                <OutlineButton
                  withBorderRadius
                  withIcon
                  text="Move to Claims"
                  onClick={movePreauthsToClaims}
                  mainColor={colors.secondaryColor}
                  icon={<MovePreauthToClaimsIcon />}
                  fullWidth
                  disabled={disableMoveToClaims}
                />
              ) : (
                <div
                  data-tooltip-content="Move to Claims"
                  data-tooltip-id="patientTableSearchTabTip"
                >
                  <OutlineIconButton
                    onClick={movePreauthsToClaims}
                    noHover
                    icon={
                      <MovePreauthToClaimsIcon
                        color={disableMoveToClaims ? colors.tintGrey : colors.secondaryColor}
                      />
                    }
                    disabled={disableMoveToClaims}
                  />
                </div>
              )}
            </>
          )}
          {isFrontDesk && recordTypeLower !== 'appointment' && (
            <>
              {isMobile ? (
                <OutlineButton
                  withBorderRadius
                  withIcon
                  text="Check Out"
                  onClick={checkOut}
                  mainColor={colors.secondaryColor}
                  icon={<CheckoutIcon2 />}
                  fullWidth
                  disabled={!canBulkCheckOut}
                />
              ) : (
                <div data-tooltip-content="Check Out" data-tooltip-id="patientTableSearchTabTip">
                  <OutlineIconButton
                    onClick={checkOut}
                    noHover
                    icon={<CheckoutIcon2 />}
                    disabled={!canBulkCheckOut}
                  />
                </div>
              )}
            </>
          )}
          <div className={classNames({ 'mt-3': isMobile })} />
          <SearchInput
            onChange={handleSearch}
            value={filterOptions.keyword}
            readOnly={!isVisible}
            noPadding
            topRightComponent={searchInputTopRightComponent}
          />
          {showDoctorStatusOption && (
            <Dropdown
              options={DOCTOR_STATUS_OPTIONS}
              onChange={handleSelect}
              readOnly={!isVisible}
              value="Approved"
              placeholder="Select One"
              noPadding
            />
          )}
          {billing && (
            <>
              <div className="status-container">
                <Dropdown
                  noPadding
                  options={BILL_TYPE_DROPDOWN}
                  placeholder="Select Bill Type"
                  onChange={({ value }) => filterByStatus(value, 'serviceType')}
                  readOnly={
                    !isVisible ||
                    (loggedInUser === UserType.Patient && billing?.value !== 'Pay Bill')
                  }
                  className="pr-0"
                />
              </div>
              <div className="status-container">
                <Dropdown
                  noPadding
                  options={[
                    {
                      value: 'All',
                      label: 'All',
                    },
                    ...paymentTypeOptions,
                  ]}
                  placeholder="Select Payment Type"
                  onChange={({ value }) => filterByStatus(value, 'paymentType')}
                  readOnly={
                    !isVisible ||
                    (loggedInUser === UserType.Patient && billing?.value !== 'Pay Bill')
                  }
                  className="pr-0"
                />
              </div>
              {clinifyIdOnUrl ? null : (
                <div className="status-container">
                  <Dropdown
                    noPadding
                    options={COVERAGE_TYPE_OPTIONS}
                    placeholder="Select Coverage Type"
                    onChange={({ value }) => filterByStatus(value, 'coverageType')}
                    readOnly={
                      !isVisible ||
                      (loggedInUser === UserType.Patient && billing?.value !== 'Pay Bill')
                    }
                    className="pr-0"
                  />
                </div>
              )}
            </>
          )}
          {isTransactionsList && (
            <>
              <div className="status-container">
                <Dropdown
                  options={TRANSACTION_TYPE_OPTIONS}
                  readOnly={!isVisible}
                  placeholder="All Transaction Type"
                  className="pr-0"
                  value={filterOptions.transactionType || null}
                  onChange={({ value }) => filterByStatus(value, 'transactionType')}
                />
              </div>
              <div className="status-container">
                <Dropdown
                  options={TRANSACTION_CATEGORY_OPTIONS}
                  readOnly={!isVisible}
                  placeholder="All Transaction Category"
                  className="pr-0"
                  value={filterOptions.transactionCategory || null}
                  onChange={({ value }) => filterByStatus(value, 'transactionCategory')}
                />
              </div>
            </>
          )}
          {showPlanCategory && (
            <div className="status-container">
              <Dropdown
                options={[...(planCategoryList || [])]}
                readOnly={!isVisible}
                placeholder="Select Category"
                value={(filterOptions as any).category || null}
                onChange={({ value }) => filterByStatus(value, 'category')}
                noPadding
                className="pr-0"
              />
            </div>
          )}

          {showStatusDropdown && (
            <div className="status-container">
              <Dropdown
                noPadding
                readOnly={!isVisible}
                options={statusDropDownOptions}
                onChange={({ value }) =>
                  filterByStatus(value, isEnrolleeRegistration ? 'enrolleeStatus' : undefined)
                }
                placeholder={statusDropdownLabel}
                value={
                  isEnrolleeRegistration
                    ? (filterOptions as any).enrolleeStatus
                    : filterOptions.status || null
                }
                className={showCoverageFilter || showFeedbackCategory ? 'pr-0' : ''}
              />
            </div>
          )}
          {showFeedbackCategory && (
            <div className="status-container">
              <Dropdown
                options={[...(feedbackCategoryList || [])]}
                placeholder="All Categories"
                value={(filterOptions as any).category || null}
                onChange={({ value }) => filterByStatus(value, 'category')}
                noPadding
                className="pr-0"
              />
            </div>
          )}
          {showProviderTypes && (hmoId || (isPartnerProfile && partnerId)) && !hospitalIdOnUrl && (
            <div className="status-container">
              <Dropdown
                noPadding
                readOnly={!isVisible}
                options={[...PROVIDER_TYPES, ...(isLashmaAgency ? EXTENDED_PROVIDER_TYPES : [])]}
                onChange={({ value }) => filterByStatus(value, 'providerType')}
                placeholder="All Provider Types"
                value={filterOptions.providerType || null}
                className="pr-0"
              />
            </div>
          )}
          {isClaimsPayoutList && (
            <ProviderOptions
              onChange={(value) => {
                filterByStatus(value, 'hospitalId');
              }}
              value={filterOptions.hospitalId || null}
              placeholder="All Providers"
              showAll
              onlyRelated
            />
          )}
          {showProviderDropdown &&
            (hmoId || (isPartnerProfile && partnerId)) &&
            !hospitalIdOnUrl && (
              <ProviderOptions
                onChange={(value) => filterByStatus(value, 'hospitalId')}
                value={filterOptions.hospitalId || null}
                readOnly={!isVisible}
                placeholder="All Providers"
                showAll
                overrideOptions={overrideHmoProviderOptionsValue}
                onlyRelated
              />
            )}
          {showFlagDropdownFilter && (
            <div className="status-container">
              <Dropdown
                noPadding
                readOnly={!isVisible}
                options={OGSHIA_CLAIMS_FLAG_OPTIONS}
                onChange={({ value }) => filterByStatus(value, 'flag')}
                placeholder="All Flags"
                value={filterOptions.flag || null}
              />
            </div>
          )}
          {showCoverageFilter && !isEnrolleeRegistration && (
            <CoverageFilterComponent
              readOnly={!isVisible}
              filterOptions={filterOptions}
              filterByStatus={filterByStatus}
              handleMultipleFilterUpdate={handleMultipleFilterUpdate}
            />
          )}
          {showServiceTypeFilter && (
            <ServiceTypeFilterComponent
              readOnly={!isVisible}
              filterOptions={filterOptions}
              filterByStatus={filterByStatus}
            />
          )}
          {showInvestigationTypeDropdown && (
            <div className="status-container">
              <Dropdown
                noPadding
                readOnly={!isVisible}
                options={[{ value: 'All', label: 'All' }, ...REQUEST_INVESTIGATION_OPTIONS]}
                onChange={({ value }) => filterByStatus(value, 'requestType')}
                placeholder="Select Investigation Type"
                value={(filterOptions as InvestigationFilterInput).requestType}
              />
            </div>
          )}
          {showWalletTransactionFilter && (
            <div className="status-container">
              <Dropdown
                value={(filterOptions as any)?.transactionType || null}
                noPadding
                options={WALLET_TRANSACTION_TYPE_OPTIONS}
                placeholder="Select Transaction Type"
                onChange={({ value }) => filterByStatus(value, 'transactionType')}
              />
            </div>
          )}
          {showInvoicePaymentFilter && (
            <div className="status-container">
              <Dropdown
                noPadding
                value={(filterOptions as any).paymentStatus || null}
                options={InvoicePaymentStatusOptions}
                placeholder="Select Payment Status"
                onChange={({ value }) => filterByStatus(value, 'paymentStatus')}
              />
            </div>
          )}
          {showHmoOptions && (
            <div className="status-container">
              <Dropdown
                noPadding
                readOnly={!isVisible}
                options={hmoOptions}
                onChange={({ value }) => filterByHmo(value)}
                placeholder={hmoDropdownLabel}
              />
            </div>
          )}
          {billServiceTypeDropdown && (
            <div className="status-container">
              <Dropdown
                noPadding
                readOnly={!isVisible}
                options={[
                  { value: 'All', label: 'All' },
                  { value: 'Bill Now', label: 'Bill Now' },
                  { value: 'Bill Later', label: 'Bill Later' },
                ]}
                onChange={({ value }) => filterByStatus(value === 'Bill Later', 'autoGenerated')}
                placeholder="All Service Type"
              />
            </div>
          )}
          {isEnrolleeRegistration && (
            <>
              {(isEnrollmentAdmin || isEnrollmentAgent || isEnrollmentAgency) && (
                <div className="status-container">
                  <FacilityStaffOptions
                    placeholder="All Agents"
                    onChange={(value) => filterByStatus(value, 'enrollmentAgentId')}
                    readOnly={!isVisible || isEnrollmentAgent}
                    className="pr-0"
                    profileType={OrganisationProfileType.EnrollmentAgent}
                    value={(filterOptions as any).enrollmentAgentId || null}
                    hospitalId={orgId as string}
                    showAll
                    showSelf={isEnrollmentAgent || isEnrollmentAgency}
                  />
                </div>
              )}
              {isEnrollmentTpaNonTpa && (
                <div className="status-container">
                  <FetchEnrollmentAgencies
                    placeholder="All Agencies"
                    onChange={(value) => filterByStatus(value, 'enrollmentAgencyId')}
                    readOnly={!isVisible}
                    className="pr-0"
                    value={(filterOptions as any).enrollmentAgencyId || null}
                    showAll
                  />
                </div>
              )}
              {(isFieldManager || isFieldOfficer) && (
                <div className="status-container">
                  <FacilityStaffOptions
                    placeholder="All Officers"
                    onChange={(value) => filterByStatus(value, 'enrollmentAgentId')}
                    readOnly={!isVisible || isFieldOfficer}
                    className="pr-0"
                    profileType={OrganisationProfileType.FieldOfficer}
                    value={(filterOptions as any).enrollmentAgentId || null}
                    hospitalId={orgId as string}
                    showAll
                    showSelf={isFieldOfficer}
                  />
                </div>
              )}
              {isHmoUser && !isEnrollmentTpaNonTpa && !isEnrollmentAgency && (
                <ProviderOptions
                  onChange={(value) => {
                    filterByStatus(value, 'facilityId');
                  }}
                  value={(filterOptions as any).facilityId || null}
                  placeholder="All Providers"
                  showAll
                  onlyRelated
                />
              )}
              <div className="status-container">
                <HmoPlanOptions
                  onChange={(value) => filterByStatus(value, 'planId')}
                  value={(filterOptions as any).planId || null}
                  placeholder="All Plan Types"
                  readOnly={!isVisible}
                  providerId={orgHmoId as string}
                />
              </div>
            </>
          )}
          {showFilterDateField && (
            <div className="flex" style={{ width: isMobile ? '100%' : 150 }}>
              <Dropdown
                placeholder="Select One"
                value={filterOptions?.filterDateField || null}
                onChange={({ value }) => {
                  filterByStatus(value, 'filterDateField');
                }}
                readOnly={!dateFilterReadOnly}
                noPadding
                options={filterDateFieldOptions}
                fullWidth
                noPaddingAll
              />
            </div>
          )}
          <DatePickerWrapper className={showStatusDropdown ? 'no-margin-left' : ''}>
            <DatePicker
              placeholderText="From"
              value={filterOptions?.dateRange?.from}
              onChange={setDateRange('from')}
              readOnly={!dateFilterReadOnly || dateFilterDisabled}
              placeholder="Select Date (From)"
              type="DateOnly"
              width="170px"
              noSidePadding
              minDate={dateFilterRange?.minDate}
              maxDate={new Date(new Date().getFullYear() + 100, new Date().getMonth())}
            />
            <DatePicker
              placeholderText="To"
              value={filterOptions?.dateRange?.to}
              onChange={setDateRange('to')}
              placeholder="Select Date (To)"
              readOnly={!dateFilterReadOnly || dateFilterDisabled}
              minDate={filterOptions?.dateRange?.from || new Date()}
              maxDate={
                dateFilterRange?.maxDate ||
                new Date(new Date().getFullYear() + 100, new Date().getMonth())
              }
              type="DateOnly"
              width="170px"
              noSidePadding
            />
          </DatePickerWrapper>
          {showTimeSortFilter && (
            <TimeSortFilterComponent
              timeSortOrder={(filterOptions as any).timeSortOrder}
              filterByStatus={filterByStatus}
            />
          )}
          {isMobile ? (
            <OutlineButton
              withBorderRadius
              withIcon
              text="Clear Filter"
              onClick={() => clearFilter()}
              mainColor={colors.secondaryColor}
              icon={<ClearFilterIcon />}
              fullWidth
            />
          ) : (
            <div data-tooltip-content="Clear Filter" data-tooltip-id="patientTableSearchTabTip">
              <OutlineIconButton
                onClick={() => clearFilter()}
                withIcon
                icon={<ClearFilterIcon />}
              />
            </div>
          )}
        </SearchTab>
      </div>
      <div className="delete-archive-section">
        {isMobile ? (
          <>
            {showAchived && (
              <>
                <OutlineButton
                  withBorderRadius
                  withIcon
                  text={ActiveArchive.Text}
                  onClick={() => toggleArchive()}
                  mainColor={iconColor(!canArchive)}
                  icon={<ActiveArchive.Icon color={iconColor(!canArchive)} />}
                  disabled={noRecordsSelected || !canArchive}
                  fullWidth
                />

                <OutlineButton
                  withBorderRadius
                  mainColor={colors.secondaryColor}
                  withIcon
                  text={ActiveViewArchive.Text}
                  onClick={toggleViewArchive}
                  icon={<ActiveViewArchive.Icon />}
                  fullWidth
                />
              </>
            )}

            {showDeleteBtn && (
              <OutlineButton
                withBorderRadius
                withIcon
                text="Delete"
                mainColor={iconColor(!canDelete)}
                onClick={toggle}
                icon={<TrashIcon color={iconColor(!canDelete)} />}
                deleteButton
                disabled={noRecordsSelected || !canDelete}
                fullWidth
              />
            )}
            {showDownloadButton && (
              <OutlineButton
                withBorderRadius
                withIcon
                text="Download Report"
                onClick={handleDataDownload}
                fullWidth
                disabled={downloadingData}
                className={classNames({ 'not-allowed': downloadingData })}
                icon={<DownloadIcon width={24} height={24} />}
              />
            )}
            {isHmoUser && recordTypeLower === 'patient' && (
              <>
                <OutlineButton
                  withBorderRadius
                  withIcon
                  text="Download List"
                  onClick={downloadEnrolleeList}
                  icon={<ExcelIcon width={24} height={24} />}
                  fullWidth
                />
                <OutlineButton
                  withBorderRadius
                  withIcon
                  text="Bulk Activate"
                  mainColor={iconColor(noRecordsSelected)}
                  onClick={handleBulkActivate}
                  icon={<GroupAddIcon />}
                  disabled={noRecordsSelected}
                  fullWidth
                />
              </>
            )}
            {!!downloadEmployerInvoice && isHmoUser && recordTypeLower === 'invoice' && (
              <OutlineButton
                withBorderRadius
                withIcon
                text="Download List"
                onClick={downloadEmployerInvoice}
                icon={<ExcelIcon width={24} height={24} />}
                fullWidth
              />
            )}
            {showPrintButton && (
              <OutlineButton
                text="Print"
                withBorderRadius
                withIcon
                mainColor={colors.secondaryColor}
                onClick={handlePrintAction}
                fullWidth
                icon={<PrinterIcon />}
              />
            )}
            {showRefreshBtn && (
              <OutlineButton
                withBorderRadius
                withIcon
                text={refreshText}
                onClick={() => handleRefresh()}
                mainColor={colors.secondaryColor}
                icon={<RefreshIcon />}
                fullWidth
              />
            )}
            {recordTypeLower === 'preauthorization' && (
              <>
                <OutlineButton
                  withBorderRadius
                  withIcon
                  text="Move to Claims"
                  onClick={movePreauthsToClaims}
                  mainColor={colors.secondaryColor}
                  icon={<SubmitClaimIcon color={iconColor(false)} />}
                  fullWidth
                  disabled={disableMoveToClaims}
                />
                {isHmoUser && (
                  <>
                    <OutlineButton
                      text="Approve"
                      onClick={() => {
                        handleAcceptOrRejectClick?.('Approved');
                      }}
                      withBorderRadius
                      withIcon
                      mainColor={colors.secondaryColor}
                      icon={<ApproveIcon htmlColor={iconColor(false)} />}
                      fullWidth
                      disabled
                    />
                    <OutlineButton
                      text="Reject"
                      onClick={() => {
                        handleAcceptOrRejectClick?.('Rejected');
                      }}
                      withBorderRadius
                      withIcon
                      mainColor={colors.secondaryColor}
                      icon={<RejectIcon htmlColor={iconColor(false)} />}
                      fullWidth
                      disabled
                    />
                  </>
                )}
              </>
            )}
            {recordTypeLower === 'appointment' && (
              <OutlineButton
                withBorderRadius
                withIcon
                text="Check In"
                onClick={reassign}
                mainColor={colors.secondaryColor}
                icon={
                  <AssignIcon color={!canBulkAssign ? colors.tintGrey : colors.secondaryColor} />
                }
                disabled={!canBulkAssign}
                fullWidth
              />
            )}
            {isFrontDesk && recordTypeLower !== 'appointment' && (
              <>
                <OutlineButton
                  withBorderRadius
                  mainColor={colors.secondaryColor}
                  withIcon
                  text="Assign"
                  onClick={reassign}
                  icon={
                    <ReassignIcon
                      color={!canBulkAssign ? colors.tintGrey : colors.secondaryColor}
                    />
                  }
                  fullWidth
                  disabled={!canBulkAssign}
                />
                <OutlineButton
                  withBorderRadius
                  mainColor={colors.secondaryColor}
                  withIcon
                  text="Transfer"
                  onClick={reassign}
                  icon={
                    <ReassignIcon
                      color={!canBulkReAssign ? colors.tintGrey : colors.secondaryColor}
                    />
                  }
                  fullWidth
                  disabled={!canBulkReAssign}
                />
                <OutlineButton
                  withBorderRadius
                  mainColor={colors.secondaryColor}
                  withIcon
                  text="Check In"
                  onClick={checkOut}
                  icon={
                    <ReassignIcon
                      color={!canBulkCheckIn ? colors.tintGrey : colors.secondaryColor}
                    />
                  }
                  fullWidth
                  disabled={!canBulkCheckIn}
                />
                <OutlineButton
                  withBorderRadius
                  mainColor={colors.secondaryColor}
                  withIcon
                  text="Check Out"
                  onClick={checkOut}
                  icon={
                    <ReassignIcon
                      color={!canBulkCheckOut ? colors.tintGrey : colors.secondaryColor}
                    />
                  }
                  fullWidth
                  disabled={!canBulkCheckOut}
                />
              </>
            )}
            {recordTypeLower === 'hmoclaim' && (
              <OutlineButton
                withBorderRadius
                withIcon
                text="Submit Batch"
                mainColor={iconColor(false)}
                onClick={() => handleSubmitClaim()}
                icon={<SubmitClaimIcon color={iconColor(false)} />}
                deleteButton
                disabled={disableSubmitBatch}
                fullWidth
              />
            )}
            {recordPath === 'claims' && !isHmoUser && (
              <OutlineButton
                withBorderRadius
                withIcon
                text="Send Report"
                mainColor={iconColor(false)}
                onClick={() => handleSendClaimReport()}
                icon={<SendMailIcon />}
                deleteButton
                fullWidth
              />
            )}
          </>
        ) : (
          <>
            {showAchived && (
              <>
                <div
                  data-tooltip-content={ActiveArchive.Text}
                  data-tooltip-id="patientTableSearchTabTip"
                >
                  <OutlineIconButton
                    onClick={() => toggleArchive()}
                    icon={<ActiveArchive.Icon color={iconColor(!canArchive)} />}
                    disabled={noRecordsSelected || !canArchive}
                  />
                </div>
                <div
                  data-tooltip-content={ActiveViewArchive.Text}
                  data-tooltip-id="patientTableSearchTabTip"
                >
                  <OutlineIconButton
                    onClick={toggleViewArchive}
                    icon={<ActiveViewArchive.Icon />}
                  />
                </div>
              </>
            )}
            {showDeleteBtn && (
              <div data-tooltip-content="Delete" data-tooltip-id="patientTableSearchTabTip">
                <OutlineIconButton
                  onClick={toggle}
                  icon={<TrashIcon color={iconColor(!canDelete)} />}
                  deleteButton
                  disabled={noRecordsSelected || !canDelete}
                />
              </div>
            )}
            {showDownloadButton && (
              <div
                data-tooltip-content="Download Report"
                data-tooltip-id="patientTableSearchTabTip"
              >
                <OutlineIconButton
                  onClick={handleDataDownload}
                  icon={
                    downloadingData ? (
                      <div className="small-spinner" />
                    ) : (
                      <DownloadIcon width={24} height={24} />
                    )
                  }
                />
              </div>
            )}
            {showBulkRegistrationUpload && <BulkRegistrationActions />}
            {isHmoUser && recordTypeLower === 'patient' && (
              <>
                <div
                  data-tooltip-content="Download List"
                  data-tooltip-id="patientTableSearchTabTip"
                >
                  <OutlineIconButton
                    onClick={downloadEnrolleeList}
                    icon={<ExcelIcon width={24} height={24} />}
                  />
                </div>
                <div
                  data-tooltip-content="Bulk Activate"
                  data-tooltip-id="patientTableSearchTabTip"
                >
                  <OutlineIconButton
                    onClick={handleBulkActivate}
                    icon={<GroupAddIcon />}
                    disabled={noRecordsSelected}
                  />
                </div>
              </>
            )}
            {!!downloadEmployerInvoice && isHmoUser && recordTypeLower === 'invoice' && (
              <div data-tooltip-content="Download List" data-tooltip-id="patientTableSearchTabTip">
                <OutlineIconButton
                  onClick={downloadEmployerInvoice}
                  icon={<ExcelIcon width={24} height={24} />}
                />
              </div>
            )}
            {showPrintButton && (
              <div data-tooltip-content="Print" data-tooltip-id="patientTableSearchTabTip">
                <OutlineIconButton
                  onClick={handlePrintAction}
                  icon={<PrinterIcon />}
                  disabled={!handlePrintAction || (!printButtonIsActive && noRecordsSelected)}
                />
              </div>
            )}
            {showRefreshBtn && (
              <div data-tooltip-content={refreshText} data-tooltip-id="patientTableSearchTabTip">
                <OutlineIconButton
                  onClick={() => handleRefresh()}
                  withIcon
                  icon={<RefreshIcon />}
                />
              </div>
            )}
            {recordTypeLower === 'preauthorization' && (
              <>
                <div
                  data-tooltip-content="Move to Claims"
                  data-tooltip-id="patientTableSearchTabTip"
                >
                  <OutlineIconButton
                    onClick={movePreauthsToClaims}
                    withIcon
                    icon={<SubmitClaimIcon color={colors.tintGrey} />}
                    disabled={disableMoveToClaims}
                  />
                </div>
                {isHmoUser && (
                  <>
                    <div data-tooltip-content="Approve" data-tooltip-id="patientTableSearchTabTip">
                      <OutlineIconButton
                        onClick={() => {
                          handleAcceptOrRejectClick?.('Approved');
                        }}
                        withIcon
                        icon={<ApproveIcon />}
                        disabled
                      />
                    </div>
                    <div data-tooltip-content="Reject" data-tooltip-id="patientTableSearchTabTip">
                      <OutlineIconButton
                        onClick={() => {
                          handleAcceptOrRejectClick?.('Rejected');
                        }}
                        withIcon
                        icon={<RejectIcon />}
                        disabled
                      />
                    </div>
                  </>
                )}
              </>
            )}
            {recordTypeLower === 'appointment' && (
              <div data-tooltip-content="Check In" data-tooltip-id="patientTableSearchTabTip">
                <OutlineIconButton
                  onClick={reassign}
                  icon={
                    <CheckInIcon color={!canBulkAssign ? colors.tintGrey : colors.secondaryColor} />
                  }
                  disabled={!canBulkAssign}
                />
              </div>
            )}
            {recordTypeLower === 'appointment' && !isPatient && (
              <div
                data-tooltip-content="Print Appointments Summary"
                data-tooltip-id="patientTableSearchTabTip"
              >
                <OutlineIconButton
                  onClick={handlePrintAppointmentsSummary}
                  icon={<PrinterIcon />}
                />
              </div>
            )}
            {isFrontDesk && recordTypeLower !== 'appointment' && (
              <>
                <div data-tooltip-content="Assign" data-tooltip-id="patientTableSearchTabTip">
                  <OutlineIconButton
                    onClick={reassign}
                    icon={
                      <AssignIcon
                        color={!canBulkAssign ? colors.tintGrey : colors.secondaryColor}
                      />
                    }
                    disabled={!canBulkAssign}
                  />
                </div>
                <div data-tooltip-content="Transfer" data-tooltip-id="patientTableSearchTabTip">
                  <OutlineIconButton
                    onClick={reassign}
                    icon={
                      <ReassignIcon
                        color={!canBulkReAssign ? colors.tintGrey : colors.secondaryColor}
                      />
                    }
                    disabled={!canBulkReAssign}
                  />
                </div>
                <div data-tooltip-content="Check In" data-tooltip-id="patientTableSearchTabTip">
                  <OutlineIconButton
                    onClick={checkOut}
                    icon={
                      <CheckInIcon
                        color={!canBulkCheckIn ? colors.tintGrey : colors.secondaryColor}
                      />
                    }
                    disabled={!canBulkCheckIn}
                  />
                </div>
                <div data-tooltip-content="Check Out" data-tooltip-id="patientTableSearchTabTip">
                  <OutlineIconButton
                    onClick={checkOut}
                    icon={
                      <CheckOutIcon
                        color={!canBulkCheckOut ? colors.tintGrey : colors.secondaryColor}
                      />
                    }
                    disabled={!canBulkCheckOut}
                  />
                </div>
              </>
            )}
            {recordTypeLower === 'hmoclaim' && (
              <>
                <div data-tooltip-content="Submit Batch" data-tooltip-id="patientTableSearchTabTip">
                  <OutlineIconButton
                    onClick={() => handleSubmitClaim()}
                    icon={<SubmitClaimIcon color={colors.tintGrey} />}
                    disabled={disableSubmitBatch}
                  />
                </div>
                {!!clinifyIdOnUrl && (
                  <>
                    <div
                      data-tooltip-content="Print Services Rendered"
                      data-tooltip-id="patientTableSearchTabTip"
                    >
                      <OutlineIconButton
                        onClick={() => handlePrintClaim?.()}
                        icon={<PrinterIcon />}
                      />
                    </div>
                    <div
                      data-tooltip-content="Email Services Rendered"
                      data-tooltip-id="patientTableSearchTabTip"
                    >
                      <OutlineIconButton
                        onClick={() => handleEmailClaim?.()}
                        icon={<SendMailIcon />}
                      />
                    </div>
                  </>
                )}
                {isHmoUser && (
                  <>
                    <div data-tooltip-content="Approve" data-tooltip-id="patientTableSearchTabTip">
                      <OutlineIconButton
                        onClick={() => {
                          handleAcceptOrRejectClick?.('Approved');
                        }}
                        withIcon
                        disabled
                        icon={<ApproveIcon />}
                      />
                    </div>
                    <div data-tooltip-content="Reject" data-tooltip-id="patientTableSearchTabTip">
                      <OutlineIconButton
                        onClick={() => {
                          handleAcceptOrRejectClick?.('Rejected');
                        }}
                        withIcon
                        disabled
                        icon={<RejectIcon />}
                      />
                    </div>
                  </>
                )}
              </>
            )}
            {recordPath === 'immunization' && (
              <div data-tooltip-content="Print Report" data-tooltip-id="patientTableSearchTabTip">
                <OutlineIconButton
                  onClick={handlePrintReport}
                  withIcon
                  icon={<PrinterIcon />}
                  disabled={disablePrintReport}
                />
              </div>
            )}
            {recordPath === 'claims' && (
              <>
                {!isHmoUser && (
                  <>
                    <div
                      data-tooltip-content="Print Payment Advice"
                      data-tooltip-id="patientTableSearchTabTip"
                    >
                      <OutlineIconButton
                        onClick={() => {
                          printPaymentAdvice?.();
                        }}
                        icon={<PrinterIcon />}
                        disabled={false}
                      />
                    </div>
                    <div
                      data-tooltip-content="Email Payment Advice"
                      data-tooltip-id="patientTableSearchTabTip"
                    >
                      <OutlineIconButton
                        onClick={() => handleSendClaimReport()}
                        icon={<SendMailIcon />}
                        disabled={false}
                      />
                    </div>
                  </>
                )}
                {!isHmoUser && (
                  <div
                    data-tooltip-content="Claims Emails"
                    data-tooltip-id="patientTableSearchTabTip"
                  >
                    <TrackboardTooltip
                      showOnClick
                      interactive
                      content={<PortalRegisteredAccounts />}
                    >
                      <div>
                        <OutlineIconButton icon={<PeopleIcon />} disabled={false} />
                      </div>
                    </TrackboardTooltip>
                  </div>
                )}
                {isHmoUser && (
                  <>
                    <div
                      data-tooltip-content="Print Payment Advice"
                      data-tooltip-id="patientTableSearchTabTip"
                    >
                      <OutlineIconButton
                        onClick={() => {
                          printPaymentAdvice?.();
                        }}
                        icon={<PrinterIcon />}
                        disabled={false}
                      />
                    </div>
                    <div
                      data-tooltip-content="Email Payment Advice"
                      data-tooltip-id="patientTableSearchTabTip"
                    >
                      <OutlineIconButton
                        onClick={() => {
                          emailPaymentAdvice?.();
                        }}
                        icon={<SendMailIcon />}
                        disabled={false}
                      />
                    </div>
                  </>
                )}
                {isProviderHmoClaimsManagedByClinify &&
                  providerCode &&
                  PROVIDER_CODE_TO_TRAINING_DOCS_MAP[providerCode] && (
                    <div
                      data-tooltip-content="Download Training Document"
                      data-tooltip-id="patientTableSearchTabTip"
                    >
                      <OutlineIconButton
                        onClick={() => {
                          openRemoteResources([PROVIDER_CODE_TO_TRAINING_DOCS_MAP[providerCode]]);
                        }}
                        icon={<PdfIcon />}
                        disabled={false}
                      />
                    </div>
                  )}
                {isHmoUser && showCompletePaymentBtn && (
                  <div
                    data-tooltip-content="Bulk Payment"
                    data-tooltip-id="patientTableSearchTabTip"
                  >
                    <OutlineIconButton
                      onClick={() => handleCompletePayment?.(recordsSelected)}
                      icon={<CompletePayment />}
                      disabled={noRecordsSelected}
                    />
                  </div>
                )}
                <div style={{ width: 20 }} />
              </>
            )}
            <ReactTooltip
              id="patientTableSearchTabTip"
              place="top"
              className="z-index-1000 bg-white"
              variant="light"
            />
          </>
        )}
      </div>
      {canAdd && billing && canViewBalance && !showAddButton && (
        <>
          {['Send Funds', 'Fund Wallet'].includes(billing.value) && (
            <Button
              onClick={() => billing.toggle()}
              withIcon
              icon={
                billing.value === 'Fund Wallet' ? (
                  <FundWalletIcon />
                ) : billing.value === 'Send Funds' ? (
                  <SendMoneyIcon />
                ) : (
                  ''
                )
              }
              text={billing.value}
              addButton
              minWidth="auto"
              className="table-add-button"
            />
          )}
        </>
      )}
    </div>
  );
};

export default PatientTableSearchTab;
