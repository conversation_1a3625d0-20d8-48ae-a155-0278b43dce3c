import { ApolloLink } from '@apollo/client';
import { onError } from '@apollo/client/link/error';
import logOutOnError from 'app/shared/utils/logOutOnError';
import cloneDeep from 'lodash.clonedeep';
import { toKobo } from '../app/pages/dashboard/views/billings/constants';
import {
  convertToCurrencyString,
  convertToNumber,
} from '../app/pages/dashboard/views/invoice/utils';
import { isUUID } from '../app/shared/utils/isUUID';
import { convertBenefitModelToBenefitInput } from '../app/pages/dashboard/views/plans/components/BenefitsTable';
import { baseUrl } from '../configs';
import { InventoryItemInput } from '../graphql-types/globalTypes';

const currentDomain = window.location.origin;

export default onError(({ networkError, graphQLErrors, operation }) => {
  if (graphQLErrors)
    graphQLErrors.map(({ message, locations, path }) => {
      if (message === 'Unauthorized') logOutOnError();
      // eslint-disable-next-line
      return console.log(
        `[GraphQL error: ${operation.operationName}]: Message: ${message}, Location: ${locations}, Path: ${path}`,
      );
    });
  if (networkError) {
    console.log(`[Network error]: ${networkError.message}`); // eslint-disable-line
  }
});

export const cleanTypeName = new ApolloLink((operation, forward) => {
  const unwantedProps = [
    '__typename',
    'secondId',
    'updatedDate',
    'updatedBy',
    'createdBy',
    'creatorName',
    'creatorId',
    'lastModifierId',
    'lastModifierName',
    'createdDate',
    'nurseAdmissionNotes',
    'hospital',
    'admission',
    'medicationDetail',
    'medicationId',
    'billingId',
    'dispenseIds',
    'medication',
    'dischargePatients',
    'transferPatients',
    'bloodTransfusions',
    'inputDetails',
    'outputDetails',
    'External',
    'Internal',
    'admission',
    'oncologyRegister',
    'medicationStatus',
    'admissionLines',
    'billStatus',
    'appointmentId',
    'isPackage',
    '__initialPrice__',
    '__totalPrice__',
    '__capitated',
  ];
  if (typeof operation.variables.input === 'object') {
    const omitIsDirty = (key, value) => (key === '__isDirty' ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitIsDirty);
  }
  if (operation.variables.input && operation.operationName !== 'UpdateCustomMandatoryFields') {
    const omitTypename = (key, value) => (unwantedProps.includes(key) ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
    if (
      operation?.variables?.input?.duration &&
      ![
        'UpdateMedicationDetail',
        'SaveMedicationDetail',
        'UpdateMedicationBundleItem',
        'SaveMedicationBundleItem',
      ].includes(operation.operationName)
    ) {
      const durationSplitted = operation?.variables?.input?.duration?.split(':');
      durationSplitted.forEach((item, i) => {
        if (item.length === 0) durationSplitted[i] = '0';
      });
      operation.variables.input.duration = durationSplitted.join(':');
    }
  }
  if (operation.operationName === 'UpdateCustomMandatoryFields') {
    const omitTypename = (key, value) => (key === '__typename' ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (
    operation.operationName === 'SaveBloodTransfusion' ||
    operation.operationName === 'UpdateBloodTransfusion'
  ) {
    const omitTypename = (key, value) => (unwantedProps.includes(key) ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);

    if (operation?.variables?.input?.lengthOfTransfusion) {
      const durationSplitted = operation?.variables?.input?.lengthOfTransfusion.split(':');
      durationSplitted.forEach((item, i) => {
        if (item.length === 0) durationSplitted[i] = '0';
      });
      operation.variables.input.lengthOfTransfusion = durationSplitted.join(':');
    }

    if (operation?.variables?.input?.crossMatchingTime) {
      const durationSplitted = operation?.variables?.input?.crossMatchingTime.split(':');
      durationSplitted.forEach((item, i) => {
        if (item.length === 0) durationSplitted[i] = '0';
      });
      operation.variables.input.crossMatchingTime = durationSplitted.join(':');
    }
  }
  if (operation.operationName === 'AddAppointment') {
    const unwantedFields = ['facilityName', 'facilityAddress', 'confirmedBy', 'name'];
    const omitTypename = (key, value) => (unwantedFields.includes(key) ? undefined : value);
    const serviceDetails = operation.variables?.input?.serviceDetails || [
      { type: null, name: null },
    ];
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
    operation.variables.input = { ...operation.variables.input, serviceDetails };
  }

  const inventoryOperations = ['AddInventory', 'UpdateInventory'];

  if (inventoryOperations.includes(operation.operationName)) {
    const unwantedFields = [
      'sn',
      'disabled',
      'quantityDispensed',
      'quantitySold',
      'roomNumber',
      'averageCostPrice',
    ];
    const omitTypename = (key, value) => (unwantedFields.includes(key) ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }

  const inventoryOrderSupplyOperations = [
    'AddInventoryOrder',
    'UpdateInventoryOrder',
    'UpdateInventorySupply',
  ];

  if (inventoryOrderSupplyOperations.includes(operation.operationName)) {
    const unwantedFields = [
      'id',
      'sn',
      'disabled',
      'quantityDispensed',
      'quantitySold',
      'roomNumber',
      'orderFrom',
      'orderTo',
      'supplyTo',
      'supplyFrom',
      'order',
      'canceled',
      'deliveredDateTime',
      'orderedBy',
      'approved',
      'averageCostPrice',
      ...(operation.operationName === 'UpdateInventorySupply'
        ? ['orderToId', 'orderStatus']
        : ['deliveryStatus']),
    ];
    const omitTypename = (key, value) => (unwantedFields.includes(key) ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }

  if (['AddHospitalPrice', 'UpdateHospitalPrice'].includes(operation.operationName)) {
    const unwantedFields = ['sn', 'disabled'];
    const omitTypename = (key, value) => (unwantedFields.includes(key) ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }

  if (operation.operationName === 'UpdateAppointment') {
    const omitTypename = (key, value) =>
      [
        'facilityName',
        'facilityAddress',
        'profile',
        'status',
        'fullName',
        'name',
        'specialist',
        'id',
        'recordType',
        'patientInformation',
      ].includes(key)
        ? undefined
        : value;
    const serviceDetails = operation.variables?.input?.serviceDetails || [
      { type: null, name: null },
    ];
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
    operation.variables.input = { ...operation.variables.input, serviceDetails };
  }
  if (operation.operationName === 'UpdateWaiter') {
    const omitTypename = (key, value) => (['patient'].includes(key) ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (
    operation.operationName === 'UpdateInvestigationWithRadiologyResult' ||
    operation.operationName === 'AddInvestigationWithRadiologyResult'
  ) {
    const omitTypename = (key, value) =>
      [
        'testInfo',
        'labResult',
        'radiologyResult',
        'profile',
        'bill',
        'radiologyContrastConfirmation',
      ].includes(key)
        ? undefined
        : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (
    ['UpdateInvestigationLabResult', 'UpdateInvestigationRadiologyResult'].includes(
      operation.operationName,
    )
  ) {
    const omitTypename = (key, value) => (['hmoClaim'].includes(key) ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (
    operation.operationName === 'UpdateInvestigationWithLabResult' ||
    operation.operationName === 'AddInvestigationWithLabResult' ||
    operation.operationName === 'UpdateInvestigation' ||
    operation.operationName === 'UpdateInvestigationBill'
  ) {
    const omitTypename = (key, value) =>
      [
        'labResult',
        'radiologyResult',
        'referringHospital',
        'profile',
        'radiologyContrastConfirmation',
      ].includes(key)
        ? undefined
        : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (
    operation.operationName === 'UpdateInvestigationWithRadiologyResult' ||
    operation.operationName === 'UpdateInvestigationWithLabResult'
  ) {
    const omitTypename = (key, value) =>
      ['additionalNotes', 'referringHospital', 'bill', 'radiologyContrastConfirmation'].includes(
        key,
      )
        ? undefined
        : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (
    operation.operationName === 'UpdateMedicationDetail' ||
    operation.operationName === 'SaveMedicationDetail'
  ) {
    const omitTypename = (key, value) => {
      if (key === 'medicationConsumables' && value?.[0]?.name === null) return undefined;
      return value;
    };
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'UpdateMedicationDetail') {
    const omitTypename = (key, value) => {
      if (key === 'medicationStatus') return undefined;
      return value;
    };
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'AddPatientMedication') {
    const omitTypename = (key, value) => {
      if ((key === 'medicationConsumables' && !value?.[0]?.name) || key === 'administrationNotes')
        return undefined;
      return value;
    };
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (
    operation.operationName === 'AddConsultation' ||
    operation.operationName === 'UpdateConsultation' ||
    operation.operationName === 'UpdateConsultationBill'
  ) {
    const omitTypename = (key, value) =>
      [
        'treatmentPlans',
        'referredTo',
        'extHospitalId',
        'billStatus',
        '__copilotTranscript__',
      ].includes(key)
        ? undefined
        : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (
    operation.operationName === 'AddPatientSurgery' ||
    operation.operationName === 'UpdateSurgery' ||
    operation.operationName === 'UpdateSurgeryBill'
  ) {
    const omitTypename = (key, value) => (['operationNotes'].includes(key) ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (
    operation.operationName === 'AddInvestigation' ||
    operation.operationName === 'UpdateInvestigation' ||
    operation.operationName === 'UpdateInvestigationBill'
  ) {
    const omitTypename = (key, value) =>
      [
        'additionalNotes',
        'referringHospital',
        'bill',
        'isRequested',
        'radiologyContrastConfirmation',
      ].includes(key)
        ? undefined
        : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'AddAdmission') {
    const omitTypename = (key, value) => (['status'].includes(key) ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (
    operation.operationName === 'UpdateAdmission' ||
    operation.operationName === 'UpdateAdmissionBill' ||
    operation.operationName === 'UpdateAdmissionWard'
  ) {
    const omitTypename = (key, value) =>
      ['admissionNotes', 'vteAndBleedingRiskAssessment'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.variables.hmoProfileInput) {
    const omitTypename = (key, value) =>
      unwantedProps.slice(0, 2).includes(key) ? undefined : value;
    operation.variables.hmoProfileInput = JSON.parse(
      JSON.stringify(operation.variables.hmoProfileInput),
      omitTypename,
    );
  }
  if (['UpdateMedicationDetail', 'UpdatePatientMedication'].includes(operation.operationName)) {
    const omitTypename = (key, value) =>
      [
        'dispenseRegister',
        'administrationNotes',
        'oxygenTherapy',
        'infusion',
        'chemoDrugs',
      ].includes(key)
        ? undefined
        : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'AddPatientMedication') {
    const omitTypename = (key, value) => (['chemoDrugs'].includes(key) ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'UpdateMedicationBundle') {
    const omitTypename = (key, value) =>
      ['medicationBundleItems'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'UpdateFacilityBillingInformation') {
    const omitTypename = (key, value) =>
      ['id', 'isPreferredPayoutAccount'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (
    [
      'AddAdmission',
      'UpdateAdmission',
      'UpdateAdmissionBill',
      'UpdateAdmissionWard',
      'AddPatientAntenatal',
      'UpdatePatientAntenatal',
      'UpdatePatientAntenatalBill',
      'SaveAntenatlDetail',
      'UpdateAntenatalDetail',
      'AddConsultation',
      'UpdateConsultation',
      'UpdateConsultationBill',
      'AddImmunization',
      'UpdateImmunization',
      'UpdateImmunizationBill',
      'UpdateImmunizationDetail',
      'AddInvestigation',
      'UpdateInvestigation',
      'UpdateInvestigationBill',
      'SaveMedicationDetail',
      'UpdateMedicationDetail',
      'AddPatientSurgery',
      'UpdateSurgery',
      'UpdateSurgeryBill',
      'UpdatePatientMedication',
      'UpdateProfileDetails',
      'AddPatientNursingService',
      'UpdatePatientNursingService',
      'UpdatePatientNursingServiceBill',
      'UpdateDispenseDetail',
      'AddPostnatal',
      'UpdatePostnatal',
      'UpdatePostnatalBill',
      'AddLabourAndDelivery',
      'UpdateLabourAndDelivery',
      'UpdateLabourAndDeliveryBill',
      'UpdateInvestigationWithRadiologyResult',
      'UpdateInvestigationWithLabResult',
      'UpdateOncologyConsultationHistory',
      'UpdateOncologyConsultationHistoryBill',
      'AddRequestProcedure',
      'UpdateRequestProcedure',
      'UpdateRequestProcedureBill',
    ].includes(operation.operationName)
  ) {
    const omitTypename = (key, value) =>
      ['preauthorizationDetails', 'hospitalId'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (['UpdateMedicalReport', 'UpdateMedicalReportBill'].includes(operation.operationName)) {
    const omitTypename = (key, value) =>
      ['hospitalId', 'profile', 'clinifyId'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (
    [
      'UpdateSurgery',
      'UpdateSurgeryBill',
      'EditHmoClaim',
      'UpdateConsultation',
      'UpdateConsultationBill',
      'UpdateAdmission',
      'UpdateAdmissionBill',
      'UpdateAdmissionWard',
      'UpdateInvestigation',
      'UpdateInvestigationBill',
      'UpdateImmunization',
      'UpdateImmunizationBill',
      'UpdatePatientAntenatal',
      'UpdatePatientAntenatalBill',
      'UpdatePatientMedication',
      'UpdateMedicationDetail',
      'UpdateDispenseDetail',
      'UpdatePatientNursingService',
      'UpdatePatientNursingServiceBill',
      'UpdatePostnatal',
      'UpdatePostnatalBill',
      'UpdateLabourAndDelivery',
      'UpdateLabourAndDeliveryBill',
      'UpdateInvestigationWithRadiologyResult',
      'UpdateInvestigationWithLabResult',
      'UpdateOncologyConsultationHistory',
      'UpdateOncologyConsultationHistoryBill',
      'UpdateRequestProcedure',
      'UpdateRequestProcedureBill',
    ].includes(operation.operationName)
  ) {
    const omitTypename = (key, value) =>
      ['grandTotal', 'totalQuantity', 'enrolleeId', 'hmoClaim', 'hmoClaimId'].includes(key)
        ? undefined
        : value;
    const input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
    operation.variables.input = input;
  }
  if (['SaveDispenseDetails'].includes(operation.operationName)) {
    const omitTypename = (key, value) =>
      ['grandTotal', 'totalQuantity'].includes(key) ? undefined : value;
    const input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
    operation.variables.input = input;
  }

  if (operation.operationName === 'UpdateAdministrationNote') {
    const omitTypename = (key, value) => {
      if (key === 'id') return undefined;
      return value;
    };
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'UpdateHmo') {
    const omitTypename = (key, value) => {
      if (key === 'planEligibility') return undefined;
      return value;
    };
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'GetServiceAdmissionData') {
    if (
      operation.variables.filter.diagnosisType &&
      operation.variables.filter.diagnosisType.startsWith('ALL')
    ) {
      const omitTypename = (key, value) => (['diagnosisType'].includes(key) ? undefined : value);
      operation.variables.filter = JSON.parse(
        JSON.stringify(operation.variables.filter),
        omitTypename,
      );
    }
  }
  if (['EditInvoiceItem', 'AddInvoiceItem'].includes(operation.operationName)) {
    if (operation.variables.input.quantity) {
      const input = {
        ...operation.variables.input,
        quantity: convertToNumber(operation.variables.input.quantity),
        unitPrice: toKobo(convertToNumber(operation.variables.input.unitPrice)),
      };
      const omitTypename = (key, value) => (['id', 'invoiceId'].includes(key) ? undefined : value);
      operation.variables.input = JSON.parse(JSON.stringify(input), omitTypename);
    }
  }
  if (['AddInvoicePaymentMethod', 'UpdateInvoicePaymentMethod'].includes(operation.operationName)) {
    const input = {
      ...operation.variables.input,
      amountPaid: toKobo(convertToNumber(operation.variables.input.amountPaid)),
      amountDue: toKobo(convertToNumber(operation.variables.input.amountDue)),
      commissionFeeAmount: toKobo(convertToNumber(operation.variables.input.commissionFeeAmount)),
      commissionFeePercentage: convertToNumber(operation.variables.input.commissionFeePercentage),
      discountAmount: toKobo(convertToNumber(operation.variables.input.discountAmount)),
    };
    const omitTypename = (key, value) =>
      ['id', 'invoiceId', 'payoutStatus'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(input), omitTypename);
  }
  if (operation.operationName === 'CreateInvoice') {
    const input = {
      ...operation.variables.input,
      invoiceItems: operation.variables.input.invoiceItems?.map((item) => ({
        ...item,
        quantity: Number(item.quantity),
        unitPrice: toKobo(convertToNumber(item.unitPrice)),
      })),
      invoicePayments: operation.variables.input.invoicePayments?.map((item) => ({
        ...item,
        amountDue: toKobo(convertToNumber(item.amountDue)),
        amountPaid: toKobo(convertToNumber(item.amountPaid)),
        commissionFeeAmount: toKobo(convertToNumber(item.commissionFeeAmount)),
        commissionFeePercentage: convertToNumber(item.commissionFeePercentage),
        discountAmount: toKobo(convertToNumber(item.discountAmount)),
      })),
      discountPercentage: Number(operation.variables.input.discountPercentage),
      discountAmount: toKobo(convertToNumber(operation.variables.input.discountAmount)),
      professionalFeeAmount: toKobo(
        convertToNumber(operation.variables.input.professionalFeeAmount),
      ),
      professionalFeePercentage: Number(operation.variables.input.professionalFeePercentage),
      vatPercentage: convertToNumber(operation.variables.input.vatPercentage),
      vatAmount: toKobo(convertToNumber(operation.variables.input.vatAmount)),
      plasticIdCardCount: Number(operation.variables.input?.plasticIdCardCount),
      plasticIdCardAmount: Number(operation.variables.input?.plasticIdCardAmount),
      laminatedIdCardCount: Number(operation.variables.input?.laminatedIdCardCount),
      laminatedIdCardAmount: Number(operation.variables.input?.laminatedIdCardAmount),
    };
    const omitTypename = (key, value) =>
      [
        'subTotal',
        'totalAmount',
        'recipientClinifyId',
        'recipientStatus',
        'totalUnitCost',
        'totalAmountPaid',
      ].includes(key)
        ? undefined
        : value;
    operation.variables.input = JSON.parse(JSON.stringify(input), omitTypename);
  }
  if (operation.operationName === 'GetHospitalStaffsForVisitation') {
    const omitTypename = (key, value) => (key === 'creator' ? undefined : value);
    operation.variables.filterOptions = JSON.parse(
      JSON.stringify(operation.variables.filterOptions),
      omitTypename,
    );
  }
  if (operation.operationName === 'GetStaffActivitiesSummaryList') {
    const omitTypename = (key, value) => (key === 'creator' ? undefined : value);
    operation.variables.filter = JSON.parse(
      JSON.stringify(operation.variables.filter),
      omitTypename,
    );
  }
  if (
    ['SaveNursingServicesDetail', 'UpdateNursingServicesDetail'].includes(operation.operationName)
  ) {
    const omitTypename = (key, value) =>
      ['id', 'nursingServiceId'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (
    operation.operationName === 'UpdatePatientNursingService' ||
    operation.operationName === 'UpdatePatientNursingServiceBill'
  ) {
    const omitTypename = (key, value) =>
      ['details', 'progressNotes'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'EditInvoice') {
    const input = {
      ...operation.variables.input,
      discountPercentage: convertToNumber(operation.variables.input.discountPercentage),
      discountAmount: toKobo(convertToNumber(operation.variables.input.discountAmount)),
      professionalFeeAmount: toKobo(
        convertToNumber(operation.variables.input.professionalFeeAmount),
      ),
      professionalFeePercentage: convertToNumber(
        operation.variables.input.professionalFeePercentage,
      ),
      vatAmount: toKobo(convertToNumber(operation.variables.input.vatAmount)),
      vatPercentage: convertToNumber(operation.variables.input.vatPercentage),
      plasticIdCardCount: Number(operation.variables.input?.plasticIdCardCount),
      plasticIdCardAmount: Number(operation.variables.input?.plasticIdCardAmount),
      laminatedIdCardCount: Number(operation.variables.input?.laminatedIdCardCount),
      laminatedIdCardAmount: Number(operation.variables.input?.laminatedIdCardAmount),
    };
    delete input.clinifyId;
    const omitTypename = (key, value) =>
      [
        'id',
        'invoiceItems',
        'invoicePayments',
        'virtualAccount',
        'subTotal',
        'totalAmount',
        'invoiceReference',
        'invoiceStatus',
        'senderHospital',
        'senderHospitalId',
        'recipientStatus',
        'totalAmountPaid',
        'bankTransactionIds',
        'amountPaid',
        'totalUnitCost',
        'employer',
      ].includes(key)
        ? undefined
        : value;
    operation.variables.input = JSON.parse(JSON.stringify(input), omitTypename);
  }
  if (operation.operationName === 'GetAllUserWalletTransactions') {
    const omitTypename = (key, value) => (key === 'creator' ? undefined : value);
    operation.variables.filterOptions = JSON.parse(
      JSON.stringify(operation.variables.filterOptions),
      omitTypename,
    );
  }
  if (operation.operationName === 'GetHandoverNotes') {
    const omitTypename = (key, value) =>
      ['creator', 'referral'].includes(key) ? undefined : value;
    operation.variables.filterOptions = JSON.parse(
      JSON.stringify(operation.variables.filterOptions),
      omitTypename,
    );
  }
  if (['AddHandoverNote', 'EditHandoverNote'].includes(operation.operationName)) {
    let input = { ...operation.variables.input };
    input = {
      ...input,
      handoverById: input.handoverBy?.id,
      handoverToId: input.handoverTo?.id,
      staffs: input?.staffs?.length
        ? input?.staffs?.map(({ staffProfile, ...rest }) => ({
            ...rest,
            staffProfileId: staffProfile.id,
          }))
        : [],
    };
    delete input.handoverBy;
    delete input.handoverTo;
    delete input.hospital;
    operation.variables.input = input;
  }
  if (operation.operationName === 'EditHandoverNote') {
    const omitTypename = (key, value) =>
      ['id', 'items', 'staffs', 'additionalNotes', 'hospitalId'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (['AddHandoverStaff', 'UpdateHandoverStaff'].includes(operation.operationName)) {
    let input = { ...operation.variables.input };
    input = {
      ...input,
      staffProfileId: input?.staffProfile?.id || input?.staffProfileId,
    };
    delete input.staffProfile;
    operation.variables.input = input;
  }
  if (
    ['EditHandoverAdditionalNote', 'EditHandoverNoteItem', 'UpdateHandoverStaff'].includes(
      operation.operationName,
    )
  ) {
    const omitTypename = (key, value) =>
      ['id', 'handoverNoteId'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'GetPatientDeposits') {
    const omitTypename = (key, value) => (['referral'].includes(key) ? undefined : value);
    operation.variables.filterOptions = JSON.parse(
      JSON.stringify(operation.variables.filterOptions),
      omitTypename,
    );
  }
  if (['AddPaymentDeposit', 'UpdatePaymentDeposit'].includes(operation.operationName)) {
    let input = { ...operation.variables.input };
    input = {
      ...input,
      collectedById: input.collectedBy.id,
      amountDeposited: toKobo(convertToNumber(input.amountDeposited)),
      amountUsed: toKobo(convertToNumber(input.amountUsed)),
    };
    delete input.collectedBy;
    delete input.withdrawnBy;
    delete input.hospital;
    delete input.autoGenerated;
    operation.variables.input = input;
  }
  if (operation.operationName === 'UpdatePaymentDeposit') {
    const omitTypename = (key, value) =>
      ['id', 'profileId', 'hospitalId', 'clinifyId'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (['AddDepositRefund', 'UpdateDepositRefund'].includes(operation.operationName)) {
    operation.variables.input = {
      ...operation.variables.input,
      amountRefunded: toKobo(convertToNumber(operation.variables.input.amountRefunded)),
    };
  }
  if (operation.operationName === 'UpdateDepositRefund') {
    const omitTypename = (key, value) =>
      [
        'collectedBy',
        'withdrawnBy',
        'id',
        'depositDate',
        'depositMethod',
        'profileId',
        'hospitalId',
        'amountDeposited',
        'amountUsed',
        'collectedById',
        'autoGenerated',
        'finalDepositBalance',
        'initialDepositBalance',
        'isManualRefund',
        'clinifyId',
      ].includes(key)
        ? undefined
        : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'SendFundsToWallet') {
    let input = { ...operation.variables.input };
    input = {
      ...input,
      amount: toKobo(convertToNumber(input.amount)),
    };
    delete input.receiverFullName;
    operation.variables.input = input;
  }
  if (operation.operationName === 'UpdateCoverageInformation') {
    const omitTypename = (key, value) =>
      ['hmoProfile', 'planEligibility'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (
    ['GetHospitalVirtualCareAppointments', 'GetPatientVirtualCareAppointments'].includes(
      operation.operationName,
    )
  ) {
    const omitTypename = (key, value) =>
      ['referral', 'creator'].includes(key) ? undefined : value;
    operation.variables.filterOptions = JSON.parse(
      JSON.stringify(operation.variables.filterOptions),
      omitTypename,
    );
  }
  if (operation.operationName === 'UpdateVirtualCareAppointment') {
    const omitTypename = (key, value) =>
      ['id', 'liveSessionUrl', 'patientProfile'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (['AddAppointment', 'UpdateAppointment'].includes(operation.operationName)) {
    operation.variables.input.origin = baseUrl;
    const omitTypename = (key, value) => (key === 'patientStatus' ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'UpdateAppointmentTime') {
    operation.variables.input.origin = baseUrl;
  }
  if (
    operation.operationName === 'UpdateInvestigationWithRadiologyResult' ||
    operation.operationName === 'UpdateInvestigationWithLabResult'
  ) {
    const omitTypename = (key, value) =>
      ['hmoProviderId', 'isRequested'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'UpdateInvestigationBill') {
    const omitTypename = (key, value) =>
      ['details', 'hmoProviderId', 'patientType'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (
    operation.operationName === 'AddImmunization' ||
    operation.operationName === 'SaveImmunizationDetail' ||
    operation.operationName === 'UpdateImmunizationDetail'
  ) {
    const omitTypename = (key, value) => (['ref'].includes(key) ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'AddVital') {
    const { readingDateTime, additionalNote } = operation.variables.input;
    if (readingDateTime) {
      const _input = { ...operation.variables.input };
      _input.anthropometry = _input.anthropometry?.map((item) => ({
        ...item,
        readingDateTime,
      }));
      _input.bloodGlucose = _input.bloodGlucose?.map((item) => ({
        ...item,
        readingDateTime,
      }));
      _input.bloodPressure = _input.bloodPressure?.map((item) => ({
        ...item,
        readingDateTime,
      }));
      _input.pulseRate = _input.pulseRate?.map((item) => ({
        ...item,
        readingDateTime,
      }));
      _input.respiratoryRate = _input.respiratoryRate?.map((item) => ({
        ...item,
        readingDateTime,
      }));
      _input.temperature = _input.temperature?.map((item) => ({
        ...item,
        readingDateTime,
      }));
      _input.urineDipstick = _input.urineDipstick?.map((item) => ({
        ...item,
        readingDateTime,
      }));
      _input.visualAcuity = _input.visualAcuity?.map((item) => ({
        ...item,
        readingDateTime,
      }));
      delete _input.readingDateTime;
      operation.variables.input = _input;
    }
    if (additionalNote) {
      const _input = { ...operation.variables.input };
      _input.anthropometry = _input.anthropometry?.map((item) => ({
        ...item,
        additionalNote,
      }));
      _input.bloodGlucose = _input.bloodGlucose?.map((item) => ({
        ...item,
        additionalNote,
      }));
      _input.bloodPressure = _input.bloodPressure?.map((item) => ({
        ...item,
        additionalNote,
      }));
      _input.pulseRate = _input.pulseRate?.map((item) => ({
        ...item,
        additionalNote,
      }));
      _input.respiratoryRate = _input.respiratoryRate?.map((item) => ({
        ...item,
        additionalNote,
      }));
      _input.temperature = _input.temperature?.map((item) => ({
        ...item,
        additionalNote,
      }));
      _input.pain = _input.pain?.map((item) => ({
        ...item,
        additionalNote,
      }));
      delete _input.additionalNote;
      operation.variables.input = _input;
    }
  }
  if (
    [
      'UpdateAdmission',
      'UpdateAdmissionBill',
      'UpdateAdmissionWard',
      'UpdateConsultation',
      'UpdateConsultationBill',
      'UpdateImmunization',
      'UpdateImmunizationBill',
      'UpdatePatientMedication',
      'UpdateSurgery',
      'UpdateSurgeryBill',
      'UpdatePatientNursingService',
      'UpdatePatientNursingServiceBill',
      'UpdatePatientAntenatal',
      'UpdatePatientAntenatalBill',
      'UpdateLabourAndDelivery',
      'UpdateLabourAndDeliveryBill',
      'UpdatePostnatal',
      'UpdatePostnatalBill',
      'UpdatePatientPartograph',
      'UpdateRequestPackage',
      'UpdatePatientPreOperationChecklist',
      'AddAnaesthesiaChecklist',
      'UpdateAnaesthesiaChecklist',
      'UpdateDependents',
      'UpdateDisability',
      'UpdateFamilyHistory',
      'UpdateGynecologicHistory',
      'UpdateSocialHabit',
      'UpdateNextOfKin',
      'UpdateObstetrics',
      'UpdateEncounter',
      'UpdatePastSurgery',
      'UpdatePhysicalActivity',
      'UpdatePreExistingCondition',
      'UpdateHmo',
      'UpdateCoverageInformation',
      'UpdateOncologyHistoryInfo',
      'UpdatePatientAllergy',
      'UpdatePostOperationChecklist',
      'UpdateOncologyConsultationHistory',
      'UpdateOncologyConsultationHistoryBill',
      'UpdateRequestProcedure',
      'UpdateRequestProcedureBill',
      'UpdatePreChemoEducation',
      'UpdateCancerScreening',
      'EditHmoClaim',
    ].includes(operation.operationName)
  ) {
    const omitTypename = (key, value) => (['profile'].includes(key) ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (
    [
      'UpdateInvestigation',
      'AddPreauthorization',
      'EditHmoClaim',
      'UpdateRequestPackage',
      'UpdatePatientNursingService',
      'UpdateAdmission',
      'UpdateAdmissionWard',
      'UpdatePatientAllergy',
      'UpdateConsultation',
      'UpdateImmunization',
      'UpdateInvestigationLabResult',
      'UpdateInvestigationRadiologyResult',
      'UpdateInvestigationWithLabResult',
      'UpdateInvestigationWithRadiologyResult',
      'UpdatePatientMedication',
      'UpdateSurgery',
      'UpdateSurgeryBill',
      'UpdateInvestigationBill',
      'UpdateConsultationBill',
      'UpdateAdmissionBill',
      'UpdateImmunizationBill',
      'UpdatePatientAntenatalBill',
      'UpdatePatientNursingServiceBill',
      'UpdatePostnatalBill',
      'UpdateLabourAndDeliveryBill',
      'UpdatePatientAntenatal',
      'UpdateLabourAndDelivery',
      'UpdatePostnatal',
      'UpdatePatientPartograph',
      'UpdatePatientPreOperationChecklist',
      'UpdatePostOperationChecklist',
      'UpdateOncologyConsultationHistory',
      'UpdateOncologyConsultationHistoryBill',
      'UpdateRequestProcedure',
      'UpdateRequestProcedureBill',
      'UpdatePreChemoEducation',
      'UpdateCancerScreening',
    ].includes(operation.operationName)
  ) {
    const omitTypename = (key, value) => (['profileId'].includes(key) ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (['AddMedicalReport', 'UpdateMedicalReport'].includes(operation.operationName)) {
    const omitTypename = (key, value) => (key === 'patientStatus' ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (
    [
      'AddPatientMedication',
      'AddRequestPackage',
      'AddPatientNursingService',
      'AddVital',
      'AddPatientAntenatal',
      'AddLabourAndDelivery',
      'AddPostnatal',
      'AddAdmission',
      'AddPatientAllergy',
      'AddConsultation',
      'AddOncologyConsultationHistory',
      'AddImmunization',
      'AddPatientSurgery',
      'AddInvestigation',
      'AddRequestProcedure',
    ].includes(operation.operationName)
  ) {
    const omitTypename = (key, value) =>
      ['hospitalId', 'profileId', 'profile', 'radiologyResult', 'labResult'].includes(key)
        ? undefined
        : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (
    [
      'AddWalkInReferral',
      'UpdateWalkInReferral',
      'AddWalkInTransfer',
      'UpdateWalkInTransfer',
    ].includes(operation.operationName)
  ) {
    const omitTypename = (key, value) => (key === 'patientStatus' ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (['UpdateWalkInReferral', 'UpdateWalkInTransfer'].includes(operation.operationName)) {
    const omitTypename = (key, value) =>
      ['patientInformation', 'clinifyId'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (['SaveChemoVerificationTemplate'].includes(operation.operationName)) {
    const omitTypename = (key, value) => (key === 'id' ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (
    ['AddOncologyConsultationHistory', 'UpdateOncologyConsultationHistory'].includes(
      operation.operationName,
    )
  ) {
    const omitInvalidIdKey = (key: string, value: string) =>
      key === 'id' && !isUUID(value) ? undefined : value;
    operation.variables.input = JSON.parse(
      JSON.stringify(operation.variables.input),
      omitInvalidIdKey,
    );
  }
  if (
    ['UpdateOncologyConsultationHistory', 'UpdateOncologyConsultationHistoryBill'].includes(
      operation.operationName,
    )
  ) {
    const unwantedFields = ['medicationDetailsId'];
    const omitTypename = (key, value) => (unwantedFields.includes(key) ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'UpdateMedicationInfusion') {
    const unwantedFields = ['sn', 'disabled'];
    const omitTypename = (key, value) => (unwantedFields.includes(key) ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'GetOutPatientList') {
    const omitTypename = (key, value) => (key === 'status' ? undefined : value);
    operation.variables.filterOptions = JSON.parse(
      JSON.stringify(operation.variables?.filterOptions),
      omitTypename,
    );
  }
  if (
    operation.operationName === 'AddOncologyConsultationHistory' ||
    operation.operationName === 'UpdateOncologyConsultationHistory' ||
    operation.operationName === 'UpdateOncologyConsultationHistoryBill'
  ) {
    const omitTypename = (key, value) => (key === 'treatmentPlans' ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'AddPreauthorizationReferral') {
    const omitTypename = (key, value) =>
      ['treatmentStartDate', 'treatmentEndDate'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'UpdatePreauthorizationReferral') {
    const omitTypename = (key, value) =>
      [
        'totalQuantity',
        'grandTotal',
        'profileId',
        'referredProvider',
        'profile',
        'provider',
        'enrolleeNumber',
        'statusHistory',
      ].includes(key)
        ? undefined
        : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'UpdatePreauthorization') {
    const omitTypename = (key, value) =>
      [
        'status',
        'profile',
        'claimId',
        'profileId',
        'grandTotal',
        'claimStatus',
        'batchNumber',
        'totalQuantity',
        'enrolleeNumber',
        'singleVisitPACode',
        'amountCovered',
        'flag',
        'transferFundId',
        'transferFund',
        'aiReason',
        'code',
        'hospitalId',
        'responseDateTime',
        'isCompleted',
        'autoApprovalSource',
      ].includes(key)
        ? undefined
        : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (['AddHmoPlan', 'UpdateHmoPlan'].includes(operation.operationName)) {
    operation.variables.input = {
      ...operation.variables.input,
      benefits: convertBenefitModelToBenefitInput(operation.variables.input.benefits),
      premiumDetails: operation.variables.input.premiumDetails.map((item) => ({
        ...item,
        amount: convertToNumber(item.amount),
        commissionRate: item.commissionRate ? convertToNumber(item.commissionRate) : null,
        referralCommissionRate: item.referralCommissionRate
          ? convertToNumber(item.referralCommissionRate)
          : null,
        providerLedCommissionRate: item.providerLedCommissionRate
          ? convertToNumber(item.providerLedCommissionRate)
          : null,
        individualCommissionRate: item.individualCommissionRate
          ? convertToNumber(item.individualCommissionRate)
          : null,
        enrollmentCommissionRate: item.enrollmentCommissionRate?.map((rate) => ({
          ...rate,
          commissionRate: rate.commissionRate ? convertToNumber(rate.commissionRate) : null,
          referralCommissionRate: rate.referralCommissionRate
            ? convertToNumber(rate.referralCommissionRate)
            : null,
        })),
      })),
    };
  }
  if (operation.operationName === 'EditHmoClaim') {
    const omitTypename = (key, value) =>
      [
        'confirmation',
        'enrolleeNumber',
        'percentageCovered',
        'amountCovered',
        'flag',
        'flags',
        'confirmation',
        'payoutStatus',
        'transferFundId',
        'transferFund',
        'aiReason',
        'paUtilProcessed',
        'hospitalId',
        'totalRejectedAmount',
        'totalSubmittedAmount',
        'autoApprovalSource',
      ].includes(key)
        ? undefined
        : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (['UpdateHospitalPrice', 'AddHospitalPrice'].includes(operation.operationName)) {
    const _input = cloneDeep(operation.variables.input);
    if (operation.operationName === 'UpdateHospitalPrice') {
      delete _input.id;
    }
    delete _input.coverageName;
    delete _input.serviceName;

    operation.variables.input = {
      ..._input,
      price: convertToCurrencyString(convertToNumber(operation.variables.input.price)),
    };
  }
  if (
    [
      'UpdatePatientDataAccessSettings',
      'SendEnrolleeHmoClaim',
      'MovePreauthToClaims',
      'AddHmoClaim',
      'SubmitHmoClaims',
      'EditHmoClaim',
      'PayoutCapitation',
      'PayoutClaims',
      'AddPreauthorization',
      'UpdatePreauthorization',
    ].includes(operation.operationName)
  ) {
    operation.variables.origin = currentDomain;
  }
  if (['AddEmployer', 'EditEmployer'].includes(operation.operationName)) {
    const omitTypename = (key, value) =>
      ['__hmoPlanTypeName', 'planAmount', 'autoApprovalSource'].includes(key) ? undefined : value;
    operation.variables.input.planAmount = operation.variables.input.planAmount
      ? convertToNumber(operation.variables.input.planAmount)
      : null;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'EditEmployer') {
    const omitTypename = (key, value) =>
      ['hmoPlanType', 'hmoProvider', 'clinifyId', 'employees'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'UpdateNursingServicesProgressNotes') {
    const omitTypename = (key, value) => (['nursingServiceId'].includes(key) ? undefined : value);
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'AddEmployer') {
    operation.variables.input.employees = operation.variables.input.employees.map((emp) => {
      const dependants = emp.dependents;
      delete emp.dependents;
      return {
        ...emp,
        dependants,
      };
    });
  }
  if (operation.operationName === 'UpdateEmployeeDependent') {
    operation.variables.input = operation.variables.input.map((dep) => {
      delete dep.hmoProfile;
      delete dep.profile;
      delete dep.employeeId;
      return dep;
    });
  }
  if (operation.operationName === 'UpdateEmployees') {
    operation.variables.input = operation.variables.input.map((emp) => {
      delete emp.hmoProfile;
      delete emp.profile;
      delete emp.dependents;
      delete emp.dependants;
      delete emp.hmoPlanType;
      delete emp.planAmount;
      delete emp.__hmoPlanTypeName;
      delete emp.totalPremiumAmountPaid;

      return emp;
    });
  }
  if (operation.operationName === 'AddEmployees') {
    operation.variables.input = operation.variables.input.map((i) => {
      i.dependants = i.dependents;
      delete i.dependents;
      delete i.__hmoPlanTypeName;
      delete i.planAmount;

      return i;
    });
  }
  if (operation.operationName === 'AddInventory') {
    if (Array.isArray(operation.variables.input)) {
      operation.variables.input = operation.variables.input.map((i: InventoryItemInput) => {
        return {
          ...i,
          unitCost: convertToNumber(i.unitCost).toString(),
          unitSellingPrice: convertToNumber(i.unitSellingPrice).toString(),
          totalCost: convertToNumber(i.totalCost).toString(),
        };
      }) as InventoryItemInput[];
    } else {
      operation.variables.input = {
        ...operation.variables.input,
        unitCost: convertToNumber(operation.variables.input.unitCost).toString(),
        unitSellingPrice: convertToNumber(operation.variables.input.unitSellingPrice).toString(),
        totalCost: convertToNumber(operation.variables.input.totalCost).toString(),
      } as InventoryItemInput;
    }
  }
  if (operation.operationName === 'UpdateInventory') {
    operation.variables.input = {
      ...operation.variables.input,
      unitCost: convertToNumber(operation.variables.input.unitCost).toString(),
      unitSellingPrice: convertToNumber(operation.variables.input.unitSellingPrice).toString(),
      totalCost: convertToNumber(operation.variables.input.totalCost).toString(),
    } as InventoryItemInput;
  }
  if (operation.operationName === 'AddDependents') {
    const omitTypename = (key, value) =>
      ['memberPlan', 'phoneNumberAlt'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'UpdateDependents') {
    const omitTypename = (key, value) =>
      ['memberPlan', 'hmoProfile', 'phoneNumberAlt'].includes(key) ? undefined : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (['AddRequestPayout', 'EditRequestPayout'].includes(operation.operationName)) {
    operation.variables.input.invoicePaymentsIds = (
      operation.variables.input.invoicePayments || []
    ).map(({ id }) => id);
    operation.variables.input.virtualServicesPaymentsIds = (
      operation.variables.input.virtualServicesPayments || []
    ).map(({ id }) => id);
    const omitTypename = (key, value) =>
      [
        'invoicePayments',
        'virtualServicesPayments',
        'payout',
        'hospital',
        'hospitalName',
        'hospitalAddress',
        'id',
        'payoutStatus',
        'currency',
        'hospitalId',
        'clinifyId',
        'receiverInitialWalletBalanceBeforeRequest',
        'receiverInitialWalletBalanceBeforePayout',
      ].includes(key)
        ? undefined
        : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (['AddFeedback', 'UpdateFeedback'].includes(operation.operationName)) {
    const omitTypename = (key, value) =>
      ['comment', 'creatorName', 'isResolved', 'status', 'reviewerName'].includes(key)
        ? undefined
        : value;
    operation.variables.input = JSON.parse(JSON.stringify(operation.variables.input), omitTypename);
  }
  if (operation.operationName === 'RegisterPatient') {
    if (operation.variables.input.dependents) {
      operation.variables.input.dependents = operation.variables.input.dependents?.filter(
        (item) => !!item.firstName && !!item.lastName,
      );
    }
  }
  if (operation.operationName === 'UpdateBusinessRule') {
    const omitTypename = (key, value) => {
      return key === '__typename' ? undefined : value;
    };
    operation.variables.rules = JSON.parse(JSON.stringify(operation.variables.rules), omitTypename);
  }
  if (operation.operationName === 'UpdateProvider') {
    const omitTypename = (key, value) => {
      return key === '__typename' ? undefined : value;
    };
    operation.variables.updateProviderInput = JSON.parse(
      JSON.stringify(operation.variables.updateProviderInput),
      omitTypename,
    );
  }
  // remove clinifyId from the input
  if (['UpdateEnrollmentAgency', 'UpdateEnrollmentAgent'].includes(operation.operationName)) {
    delete operation.variables.input.clinifyId;
  }
  return forward(operation).map((data) => {
    return data;
  });
});
