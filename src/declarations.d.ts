export declare global {
  interface Window {
    googleTranslateElementInit: () => void;
    google: {
      translate: {
        TranslateElement: {
          InlineLayout: any;
          new (
            options: {
              pageLanguage: string;
              layout: number;
              autoDisplay?: boolean;
            },
            element: Element | string,
          ): void;
        };
      };
    };
  }

  interface Clipboard {
    read(): Promise<ClipboardItem[]>;
    write(data: ClipboardItem[]): Promise<void>;
  }

  interface DragEvent {
    rangeOffset?: number;
    rangeParent?: Node;
  }
}
