/* eslint-disable no-restricted-globals,@typescript-eslint/ban-ts-comment */
import { ExpirationPlugin } from 'workbox-expiration';
import { registerRoute } from 'workbox-routing';
import { StaleWhileRevalidate } from 'workbox-strategies';

declare const self: any;

registerRoute(
  new RegExp('/graphql(/)?'),
  async ({ event }) => {
    // Always fetch from network, no caching for GraphQL requests
    return fetch(event.request);
  },
  'POST',
);

registerRoute(
  /^https:\/\/fonts\.googleapis\.com/,
  new StaleWhileRevalidate({
    cacheName: 'google-fonts-stylesheets',
    plugins: [new ExpirationPlugin({ maxAgeSeconds: 86400 * 30 })],
  }),
);

registerRoute(
  /^https:\/\/fonts\.gstatic\.com/,
  new StaleWhileRevalidate({
    cacheName: 'google-fonts-webfonts',
    plugins: [new ExpirationPlugin({ maxAgeSeconds: 86400 * 365 })],
  }),
);

// @ts-ignore
self.addEventListener('install', (event: any) => {
  event.waitUntil(self.skipWaiting());
});

self.addEventListener('activate', (event) => {
  event.waitUntil(self.clients.claim());
});
