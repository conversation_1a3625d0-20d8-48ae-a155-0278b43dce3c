module.exports = function override(config, env) {
  // Add the optional chaining and nullish coalescing plugins to the Babel loader configuration
  config.module.rules.forEach((rule) => {
    if (rule.oneOf) {
      rule.oneOf.forEach((oneOf) => {
        if (oneOf.loader && oneOf.loader.includes('babel-loader')) {
          oneOf.options = {
            ...oneOf.options,
            plugins: [
              ...(oneOf.options.plugins || []),
              '@babel/plugin-proposal-optional-chaining',
              '@babel/plugin-proposal-nullish-coalescing-operator',
            ],
          };
        }
      });
    }
  });
  config.module.rules.push({
    test: /\.mjs$/,
    include: /node_modules/,
    type: 'javascript/auto',
  });

  return config;
};
