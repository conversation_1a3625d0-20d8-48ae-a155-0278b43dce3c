const path = require('path');
const webpack = require('webpack');
const dotenv = require('dotenv');

dotenv.config();

const webBuildTargetFolder = path.join(__dirname, 'public');
const targetServiceWorkerFilename = 'service-worker.js';

module.exports = {
  target: 'node',
  mode: 'production',
  entry: {
    index: path.join(__dirname, 'src', 'service-worker', 'sw.ts'),
  },
  resolve: {
    extensions: ['.js', '.ts', '.mjs'],
  },
  output: {
    path: webBuildTargetFolder,
    filename: targetServiceWorkerFilename,
  },
  module: {
    rules: [
      {
        test: /\.ts$/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env', '@babel/preset-react', '@babel/preset-typescript'],
            plugins: ['@babel/plugin-transform-runtime'],
          },
        },
      },
      {
        test: /\.mjs$/,
        include: /node_modules/,
        type: 'javascript/auto',
      },
    ],
  },
  plugins: [
    new webpack.EnvironmentPlugin({
      REACT_APP_API_URL: 'https://my-clinify-api-dev.herokuapp.com',
    }),
  ],
};
