const { writeFile } = require('fs');
const labTestInfo = require('../src/app/pages/dashboard/views/investigation/common/tests.json');

const ID_CATEGORY = {
  1: 'Haematology',
  2: 'Biochemistry',
  3: 'Microbiology',
  4: 'Histopathology',
};

const getLabTestWithCategory = () => {
  const { tests } = labTestInfo;

  const TestCategoryMap = {
    'PATERNITY DNA FATHER + CHILD': 'Others',
  };

  tests.forEach((item) => {
    if ([0, 5].includes(item.id)) return;

    const objKey = ID_CATEGORY[item.id];

    item[objKey].forEach(({ testName }) => {
      TestCategoryMap[testName] = objKey;
    });
  });

  const filePath = './utils/labtest_with_category.js';
  return new Promise((resolve, reject) => {
    writeFile(
      filePath,
      `export const LAB_TEST_CATEGORY = ${JSON.stringify(TestCategoryMap, null, 2)}\n`,
      (error) => {
        if (error) {
          process.stdout.write(`Error writing to file ${error}\n`);
          reject(error);
        } else {
          process.stdout.write(`Result written to file ${filePath}\n`);
          resolve();
        }
      },
    );
  });
};

getLabTestWithCategory();
