#!/usr/bin/env bash
set -e

# Build application
build() {
  if [ "$GITHUB_REF" == "refs/heads/develop" ]; then
    export REACT_APP_API_URL=$DEV_REACT_APP_API_URL
    export REACT_APP_SOC_URL=$DEV_REACT_APP_SOC_URL
    export REACT_APP_PAYSTACK_PUBLIC_KEY=$DEV_REACT_APP_PAYSTACK_PUBLIC_KEY
    export REACT_APP_ATTACHMENT_API=$DEV_REACT_APP_ATTACHMENT_API
    export REACT_APP_TRANSCRIPTION_OUTPUT_BUCKET_NAME=$DEV_REACT_APP_TRANSCRIPTION_OUTPUT_BUCKET_NAME
    export REACT_APP_TRANSCRIPTION_INPUT_BUCKET_NAME=$DEV_REACT_APP_TRANSCRIPTION_INPUT_BUCKET_NAME
    export REACT_APP_TRANSCRIBE_JOB_ACCESS_ROLE_ARN=$DEV_REACT_APP_TRANSCRIBE_JOB_ACCESS_ROLE_ARN
    export REACT_APP_TRANSLATION_API_URL=$REACT_APP_TRANSLATION_API_URL
    export REACT_APP_AWS_SECRET_ACCESS_KEY=$REACT_APP_AWS_SECRET_ACCESS_KEY
    export REACT_APP_AWS_ACCESS_KEY_ID=$REACT_APP_AWS_ACCESS_KEY_ID
    export REACT_APP_AWS_REGION=$REACT_APP_AWS_REGION
    export REACT_APP_MAPBOX_ACCESS_TOKEN=$REACT_APP_MAPBOX_ACCESS_TOKEN
    export REACT_APP_ENCRYPTION_KEY=$REACT_APP_ENCRYPTION_KEY
  elif [ "$GITHUB_REF" == "refs/heads/staging" ]; then
    export REACT_APP_API_URL=$STAGING_REACT_APP_API_URL
    export REACT_APP_SOC_URL=$STAGING_REACT_APP_SOC_URL
    export REACT_APP_PAYSTACK_PUBLIC_KEY=$STAGING_REACT_APP_PAYSTACK_PUBLIC_KEY
    export REACT_APP_ATTACHMENT_API=$STAGING_REACT_APP_ATTACHMENT_API
    export REACT_APP_TRANSCRIPTION_OUTPUT_BUCKET_NAME=$STAGING_REACT_APP_TRANSCRIPTION_OUTPUT_BUCKET_NAME
    export REACT_APP_TRANSCRIPTION_INPUT_BUCKET_NAME=$STAGING_REACT_APP_TRANSCRIPTION_INPUT_BUCKET_NAME
    export REACT_APP_TRANSCRIBE_JOB_ACCESS_ROLE_ARN=$STAGING_REACT_APP_TRANSCRIBE_JOB_ACCESS_ROLE_ARN
    export REACT_APP_TRANSLATION_API_URL=$REACT_APP_TRANSLATION_API_URL
    export REACT_APP_AWS_SECRET_ACCESS_KEY=$REACT_APP_AWS_SECRET_ACCESS_KEY
    export REACT_APP_AWS_ACCESS_KEY_ID=$REACT_APP_AWS_ACCESS_KEY_ID
    export REACT_APP_AWS_REGION=$REACT_APP_AWS_REGION
    export REACT_APP_MAPBOX_ACCESS_TOKEN=$REACT_APP_MAPBOX_ACCESS_TOKEN
    export REACT_APP_TRANSCRIBE_URL=$STAGING_REACT_APP_TRANSCRIBE_URL
    export REACT_APP_ENCRYPTION_KEY=$REACT_APP_ENCRYPTION_KEY
  else
    export REACT_APP_API_URL=$PRODUCTION_REACT_APP_API_URL
    export REACT_APP_SOC_URL=$PRODUCTION_REACT_APP_SOC_URL
    export REACT_APP_PAYSTACK_PUBLIC_KEY=$PRODUCTION_REACT_APP_PAYSTACK_PUBLIC_KEY
    export REACT_APP_ATTACHMENT_API=$PRODUCTION_REACT_APP_ATTACHMENT_API
    export REACT_APP_TRANSCRIBE_URL=$PRODUCTION_REACT_APP_TRANSCRIBE_URL
    export REACT_APP_WEMA_DOMAIN=$PRODUCTION_REACT_APP_WEMA_DOMAIN
    export REACT_APP_NHIA_DOMAIN=$PRODUCTION_REACT_APP_NHIA_DOMAIN
    export REACT_APP_LASHMA_DOMAIN=$PRODUCTION_REACT_APP_LASHMA_DOMAIN
    export REACT_APP_LEADWAY_DOMAIN=$PRODUCTION_REACT_APP_LEADWAY_DOMAIN
    export REACT_APP_NIGCOMSAT_DOMAIN=$PRODUCTION_REACT_APP_NIGCOMSAT_DOMAIN
    export REACT_APP_POLICE_HMO_DOMAIN=$PRODUCTION_REACT_APP_POLICE_HMO_DOMAIN
    export REACT_APP_TRANSLATION_API_URL=$REACT_APP_TRANSLATION_API_URL
    export REACT_APP_AWS_SECRET_ACCESS_KEY=$REACT_APP_AWS_SECRET_ACCESS_KEY
    export REACT_APP_AWS_ACCESS_KEY_ID=$REACT_APP_AWS_ACCESS_KEY_ID
    export REACT_APP_AWS_REGION=$REACT_APP_AWS_REGION
    export REACT_APP_MAPBOX_ACCESS_TOKEN=$REACT_APP_MAPBOX_ACCESS_TOKEN
    export REACT_APP_MEDPLUS_DOMAIN=$PRODUCTION_REACT_APP_MEDPLUS_DOMAIN
    export REACT_APP_OYSHIA_DOMAIN=$PRODUCTION_REACT_APP_OYSHIA_DOMAIN
    export REACT_APP_OGSHIA_DOMAIN=$PRODUCTION_REACT_APP_OGSHIA_DOMAIN
    export REACT_APP_ODCHC_DOMAIN=$PRODUCTION_REACT_APP_ODCHC_DOMAIN
    export REACT_APP_SYNDICATEBIO_DOMAIN=$PRODUCTION_REACT_APP_SYNDICATEBIO_DOMAIN
    export REACT_APP_NELFUND_DOMAIN=$PRODUCTION_REACT_APP_NELFUND_DOMAIN
    export REACT_APP_NGSCHS_DOMAIN=$PRODUCTION_REACT_APP_NGSCHS_DOMAIN
    export REACT_APP_TRUDOC_DOMAIN=$PRODUCTION_REACT_APP_TRUDOC_DOMAIN
    export REACT_APP_NPA_DOMAIN=$PRODUCTION_REACT_APP_NPA_DOMAIN
    export REACT_APP_FHIS_DOMAIN=$PRODUCTION_REACT_APP_FHIS_DOMAIN
    export REACT_APP_OHIS_DOMAIN=$PRODUCTION_REACT_APP_OHIS_DOMAIN
    export REACT_APP_NNPC_DOMAIN=$PRODUCTION_REACT_APP_NNPC_DOMAIN
    export REACT_APP_TRANSCRIPTION_OUTPUT_BUCKET_NAME=$PRODUCTION_REACT_APP_TRANSCRIPTION_OUTPUT_BUCKET_NAME
    export REACT_APP_TRANSCRIPTION_INPUT_BUCKET_NAME=$PRODUCTION_REACT_APP_TRANSCRIPTION_INPUT_BUCKET_NAME
    export REACT_APP_TRANSCRIBE_JOB_ACCESS_ROLE_ARN=$PRODUCTION_REACT_APP_TRANSCRIBE_JOB_ACCESS_ROLE_ARN
    export REACT_APP_ENCRYPTION_KEY=$REACT_APP_ENCRYPTION_KEY
  fi
  yarn build
}

# Deploy application
deploy_s3(){
 if [ "$GITHUB_REF" == "refs/heads/develop" ]; then
   export S3_BUCKET="s3://dashboard.dev.myclinify.com"
 elif [ "$GITHUB_REF" == "refs/heads/staging" ]; then
    export S3_BUCKET="s3://dashboard.staging.myclinify.com"
  else
    export S3_BUCKET="s3://dashboard.myclinify.com"
  fi
  aws s3 sync . $S3_BUCKET --acl public-read
}

# Clear Cache
clearCache() {
  if [ "$GITHUB_REF" == "refs/heads/develop" ]; then
   export DISTRIBUTION_ID=$DEV_DISTRIBUTION_ID
  elif [ "$GITHUB_REF" == "refs/heads/staging" ]; then
    export DISTRIBUTION_ID=$STAGING_DISTRIBUTION_ID
  else
    export DISTRIBUTION_ID=$PRODUCTION_DISTRIBUTION_ID
  fi
  aws cloudfront create-invalidation --distribution-id $DISTRIBUTION_ID --paths /*
}


main() {
  build
  clearCache
  deploy_s3
}
$@
