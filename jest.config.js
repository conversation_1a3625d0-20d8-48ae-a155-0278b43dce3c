module.exports = {
  preset: 'ts-jest',
  moduleDirectories: ['node_modules', 'src'],
  moduleNameMapper: {
    '\\.svg': '<rootDir>/__mocks__/svgrMock.js',
    '\\.(css|less|scss|sss|styl)$': '<rootDir>/node_modules/jest-css-modules',
  },
  transform: {
    '^.+\\.(j|t)sx?$': 'ts-jest',
    '\\.(css|less|scss|sss|styl)$': '<rootDir>/node_modules/jest-css-modules',
  },
  transformIgnorePatterns: [
    '<rootDir>/src/app/pages/dashboard/views/supplies/components/columns.ts',
    '<rootDir>/src/app/pages/dashboard/views/supplies/components/columns.tsx',
  ],
  collectCoverage: false,
  collectCoverageFrom: [
    '**/*.{ts,tsx}',
    '!**/__tests__/**',
    '!**/e2e-tests/**',
    '!**/public/**',
    '!**/node_modules/**',
  ],
  testTimeout: 15000,
};
