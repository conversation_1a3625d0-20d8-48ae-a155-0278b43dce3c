{"name": "clinify", "version": "0.1.0", "private": true, "engines": {"node": ">=12.13.0 <17.0"}, "dependencies": {"@akumzy/material-ui-pickers": "^4.1.3", "@apollo/client": "3.5.6", "@babel/preset-typescript": "7.13.0", "@chatscope/chat-ui-kit-react": "^1.10.1", "@chatscope/chat-ui-kit-styles": "^1.4.0", "@date-io/date-fns": "1.3.13", "@emoji-mart/data": "^1.1.2", "@emoji-mart/react": "^1.1.1", "@emotion/react": "11.10.5", "@emotion/styled": "11.10.5", "@excalidraw/excalidraw": "0.17.0", "@lexical/clipboard": "0.13.1", "@lexical/html": "0.13.1", "@lexical/react": "0.13.1", "@lexical/selection": "0.13.1", "@lexical/table": "0.13.1", "@lexical/utils": "0.13.1", "@material-ui/core": "4.12.3", "@material-ui/icons": "4.11.2", "@material-ui/pickers": "3.3.10", "@material-ui/styles": "4.11.4", "@mui/icons-material": "5.15.15", "@mui/material": "5.11.6", "@nivo/core": "0.80.0", "@nivo/line": "0.80.0", "@ramonak/react-progress-bar": "5.0.3", "@react-pdf/font": "2.2.1", "@react-pdf/fontkit": "2.0.2", "@react-pdf/pdfkit": "2.1.0", "@react-pdf/renderer": "2.0.19", "@tanstack/react-virtual": "^3.13.11", "@testing-library/jest-dom": "5.12.0", "@testing-library/react": "11.2.7", "@testing-library/react-hooks": "7.0.0", "@turf/turf": "^7.2.0", "@types/classnames": "2.2.10", "@types/jest": "26.0.0", "@types/lodash.clonedeep": "4.5.6", "@types/lodash.debounce": "4.0.6", "@types/node": "14.0.13", "@types/pdfmake": "0.1.18", "@types/qs": "6.9.3", "@types/react": "16.9.53", "@types/react-datepicker": "3.0.1", "@types/react-dom": "16.9.8", "@types/react-helmet": "6.0.0", "@types/react-router-dom": "5.1.6", "@types/react-select": "3.0.22", "@types/react-speech-recognition": "3.9.0", "@types/react-table": "7.0.24", "@types/react-toast-notifications": "2.4.0", "@types/react-typing-animation": "1.6.2", "@types/styled-components": "5.1.0", "@types/yup": "0.29.8", "@vercel/ncc": "0.28.6", "apollo": "2.34.0", "apollo-link-queue": "3.1.0", "apollo-link-serialize": "3.1.2", "axios": "0.21.0", "babel-loader": "8.1.0", "body-parser": "1.17.2", "chance": "1.1.7", "classnames": "2.3.0", "crypto-js": "4.0.0", "date-fns": "2.16.1", "dayjs": "1.10.7", "deep-equal": "2.0.5", "dexie": "3.0.3", "dexie-observable": "3.0.0-beta.11", "dot-prop": "5.2.0", "dotenv": "9.0.2", "emoji-mart": "5.5.2", "escape-html": "1.0.3", "eslint-plugin-jsx-a11y": "6.4.1", "eslint-plugin-react-hooks": "4.2.0", "express": "4.17.1", "file-saver": "2.0.5", "flatted": "3.2.0", "form-data": "3.0.0", "formik": "2.1.4", "global": "4.4.0", "google-maps-react": "2.0.6", "graphql": "15.3.0", "graphql-ws": "5.5.0", "html-to-pdfmake": "2.5.1", "html2canvas": "^1.4.1", "idb-keyval": "5.0.6", "intersection-observer": "0.10.0", "jest": "26.6.0", "jquery": "3.4.1", "jspdf": "2.5.1", "katex": "0.16.9", "lexical": "0.13.1", "lodash.camelcase": "4.3.0", "lodash.capitalize": "4.2.1", "lodash.chunk": "4.2.0", "lodash.clonedeep": "4.5.0", "lodash.combinations": "18.11.1", "lodash.debounce": "4.0.8", "lodash.differenceby": "4.8.0", "lodash.groupby": "4.6.0", "lodash.isequal": "4.5.0", "lodash.merge": "4.6.2", "lodash.omit": "4.5.0", "lodash.sortby": "4.7.0", "lodash.truncate": "4.4.2", "lodash.unionby": "4.8.0", "lodash.unionwith": "4.6.0", "mapbox-gl": "^3.9.3", "mockdate": "3.0.5", "moment": "2.27.0", "nanoid": "3.1.20", "nodejs-base64": "2.0.0", "p-event": "4.2.0", "pako": "^2.1.0", "papaparse": "5.3.1", "path": "0.12.7", "pdfmake": "0.2.7", "qs": "6.9.4", "quagga": "0.12.1", "re-resizable": "6.9.0", "react": "17.0.1", "react-anchor-link-smooth-scroll": "1.0.12", "react-big-calendar": "1.8.1", "react-csv": "2.0.3", "react-custom-scrollbars": "4.2.1", "react-data-grid": "7.0.0-beta.7", "react-dom": "17.0.1", "react-drag-drop-files": "2.3.8", "react-error-boundary": "4.0.13", "react-focus-lock": "2.4.0", "react-google-maps": "9.4.5", "react-helmet": "6.1.0", "react-input-number": "^5.0.19", "react-media-recorder": "1.6.4", "react-paystack": "3.0.3", "react-responsive-modal": "5.0.2", "react-rnd": "^10.4.13", "react-router-dom": "5.2.0", "react-scripts": "4.0.0", "react-select": "3.1.1", "react-select-async-paginate": "0.5.3", "react-signature-canvas": "1.0.6", "react-speech-recognition": "3.10.0", "react-spinners": "0.9.0", "react-spreadsheet-grid": "2.0.0", "react-table": "7.5.0", "react-time-picker": "4.0.1", "react-toast-notifications": "2.4.0", "react-tooltip": "5.29.1", "recharts": "2.1.2", "rosie": "2.1.0", "sass": "1.43.4", "socket.io-client": "4.7.5", "to-words": "2.3.2", "typescript": "4.0.5", "use-file-picker": "1.5.1", "uuid": "8.3.2", "webpack": "4.44.2", "workbox-background-sync": "6.1.5", "workbox-cacheable-response": "6.1.5", "workbox-expiration": "6.1.5", "workbox-navigation-preload": "6.1.5", "workbox-precaching": "6.1.5", "workbox-routing": "6.1.5", "workbox-strategies": "6.1.5", "worker-plugin": "5.0.1", "xlsx": "^0.18.5", "yup": "0.29.3"}, "resolutions": {"graphql": "15.3.0", "react-error-overlay": "6.0.9", "@react-pdf/font": "2.2.1", "@react-pdf/fontkit": "2.0.2", "@react-pdf/pdfkit": "2.1.0"}, "scripts": {"start:client": "react-app-rewired start", "start": "node src/server/index.js", "build": "export GENERATE_SOURCEMAP=false; webpack --config webpack.config.js && react-app-rewired build", "test": "react-app-rewired test --watchAll=false", "eject": "react-app-rewired eject", "lint": "eslint './src/**/*.{ts,tsx}' --ext .ts,.tsx", "lint:fix": "eslint \"./src/**/*.{ts,tsx}\" --ext .ts,.tsx --fix", "lint:e2e": "eslint './e2e-tests' --fix", "test:cov": "test --coverage", "test:watch": "react-app-rewired test", "apollo:generate": "apollo client:codegen --outputFlat --localSchemaFile=graphql-schema.json --target=typescript --includes=src/**/*.ts --excludes=src/apollo/*.ts --tagName=gql src/graphql-types", "apollo:download": "apollo client:download-schema --endpoint https://my-clinify-api-dev.herokuapp.com/graphql graphql-schema.json", "apollo:download:dev": "apollo client:download-schema --endpoint http://localhost:3001/graphql graphql-schema.json", "schema:check": "rm -rf src/graphql-types && npm run apollo:download && npm run apollo:generate", "schema:check:dev": "rm -rf src/graphql-types && npm run apollo:download:dev && npm run apollo:generate", "labtest_with_category": "node utils/getLabTestWithCategory.js"}, "husky": {"hooks": {"pre-commit": "npm run lint:fix && git add ."}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all", "not safari < 10", "not chrome < 51", "not android < 5", "not ie < 12"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/helper-builder-react-jsx-experimental": "7.12.11", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-runtime": "7.14.3", "@babel/preset-env": "7.14.0", "@types/audioworklet": "^0.0.50", "@types/chance": "1.1.1", "@types/escape-html": "^1.0.4", "@types/graphql": "14.5.0", "@types/lodash.camelcase": "^4.3.7", "@types/lodash.capitalize": "4.2.7", "@types/lodash.groupby": "4.6.7", "@types/lodash.merge": "4.6.7", "@types/react-signature-canvas": "1.0.2", "@types/react-test-renderer": "17.0.1", "@types/workbox-sw": "4.3.1", "@typescript-eslint/eslint-plugin": "4.5.0", "@typescript-eslint/parser": "4.5.0", "concurrently": "5.0.2", "eslint": "7.12.0", "eslint-config-airbnb": "18.2.0", "eslint-config-prettier": "6.14.0", "eslint-import-resolver-typescript": "2.3.0", "eslint-plugin-import": "2.22.1", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-react": "7.21.5", "fake-indexeddb": "3.1.3", "husky": "4.2.5", "jest-css-modules-transform": "4.2.1", "lint-staged": "10.2.11", "mock-apollo-client": "1.1.0", "prettier": "2.1.2", "react-app-rewired": "^2.2.1", "react-refresh": "0.8.3", "testcafe": "1.10.1", "testcafe-reporter-xunit": "2.1.0", "webpack-cli": "4.7.0", "worker-loader": "3.0.8"}, "lint-staged": {"**/*.{ts,js,tsx,json,md}": ["npm run --silent lint:fix"]}, "prettier": {"arrowParens": "always", "bracketSpacing": true, "jsxSingleQuote": false, "semi": true, "singleQuote": true, "useTabs": false, "tabWidth": 2, "printWidth": 100, "trailingComma": "all"}, "jest": {"collectCoverageFrom": ["**/*.{ts,tsx}", "!**/__tests__/**", "!**/e2e-tests/**", "!**/public/**", "!**/node_modules/**", "!**/src/@types/**"], "coveragePathIgnorePatterns": ["src/graphql-types", "src/@types"], "coverageThreshold": {"global": {"statements": 3.72, "branches": 1.03, "functions": 1.47, "lines": 3.24}}}, "volta": {"node": "16.17.0", "yarn": "1.22.22"}}