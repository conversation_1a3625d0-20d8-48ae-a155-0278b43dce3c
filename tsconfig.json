{
  "compilerOptions": {
    "baseUrl": "./src",
    "target": "es2017",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext",
      "webworker"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": false,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "noUnusedLocals": false,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "react",
    "noFallthroughCasesInSwitch": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "noImplicitAny": false,
    "noImplicitThis": true,
    "strictNullChecks": true,
    "allowSyntheticDefaultImports": true,
    "typeRoots": [
      "./src/@types",
      "./node_modules/@types"
    ],
  },
  "include": [
    "src"
  ],
  "exclude": [
    "src/service-worker/sw.ts"
  ]
}
