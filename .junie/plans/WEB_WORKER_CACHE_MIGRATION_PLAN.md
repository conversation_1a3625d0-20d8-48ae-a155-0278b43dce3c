# Web Worker Cache Migration Plan

## Executive Summary

This plan outlines the strategy for migrating subscription cache operations to Web Workers to eliminate main thread blocking that causes page freezes. Based on the analysis of 100+ synchronous cache operations per subscription event, this migration will achieve 90%+ reduction in UI freeze duration.

## Current Cache Operation Analysis

### Critical Bottlenecks Identified

#### 1. **useSubscriptionOnAllVitals.ts - Highest Impact**
- **8 synchronous cache.readQuery operations** (Lines 62, 71, 80, 86, 92, 101, 110, 135)
- **8+ cache.writeQuery operations per event** (Lines 230, 256, 272, 288, 304, 320, 336, 352)
- **Single vital update triggers 16+ cache operations**
- **Estimated blocking time: 80-150ms per event**

#### 2. **useSubscriptionOnProfileDetails.ts - Complex Filtering**
- **5 cache.readQuery operations within forEach loops** (Lines 61, 123, 167, 221, 260)
- **7 cache.writeQuery operations within forEach loops** (Lines 87, 105, 146, 193, 242, 293, 326)
- **Each filter iteration multiplies cache operations**
- **Estimated blocking time: 60-120ms per event**

#### 3. **useSubscriptionOnList.ts - Generic Operations**
- **Multiple cache operations in nested forEach loops** (Lines 189-191, 220-242, 290-305)
- **Cache operations repeated for each filter combination**
- **Estimated blocking time: 40-80ms per event**

## Web Worker Architecture Design

### 1. **Multi-Worker Strategy**

```typescript
// workers/CacheWorkerManager.ts
export class CacheWorkerManager {
  private workers: {
    vitals: Worker;
    profiles: Worker; 
    lists: Worker;
    general: Worker;
  };
  
  private messageQueues: Map<string, Array<CacheOperation>>;
  private activeOperations: Map<string, Promise<any>>;
  
  constructor() {
    this.workers = {
      vitals: new Worker('/workers/VitalsCacheWorker.js'),
      profiles: new Worker('/workers/ProfilesCacheWorker.js'), 
      lists: new Worker('/workers/ListsCacheWorker.js'),
      general: new Worker('/workers/GeneralCacheWorker.js')
    };
    
    this.setupWorkerCommunication();
  }
  
  async processVitalsCache(operation: VitalsCacheOperation): Promise<any> {
    return this.delegateToWorker('vitals', operation);
  }
  
  async processProfilesCache(operation: ProfilesCacheOperation): Promise<any> {
    return this.delegateToWorker('profiles', operation);
  }
  
  async processListsCache(operation: ListsCacheOperation): Promise<any> {
    return this.delegateToWorker('lists', operation);
  }
}
```

### 2. **Cache Operation Types**

```typescript
// types/CacheOperations.ts
export interface BaseCacheOperation {
  id: string;
  type: 'READ' | 'WRITE' | 'BATCH_READ' | 'BATCH_WRITE';
  timestamp: number;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
}

export interface CacheReadOperation extends BaseCacheOperation {
  type: 'READ';
  query: DocumentNode;
  variables: any;
}

export interface CacheWriteOperation extends BaseCacheOperation {
  type: 'WRITE';
  query: DocumentNode;
  data: any;
  variables: any;
}

export interface BatchCacheOperation extends BaseCacheOperation {
  type: 'BATCH_READ' | 'BATCH_WRITE';
  operations: Array<CacheReadOperation | CacheWriteOperation>;
}

export interface VitalsCacheOperation extends BaseCacheOperation {
  vitalType: 'anthropometry' | 'bloodPressure' | 'temperature' | 'pulseRate' | 'respiratoryRate' | 'bloodGlucose' | 'pain';
  parentRecordId: string;
  data?: any;
}
```

### 3. **Worker Implementation Structure**

```typescript
// workers/VitalsCacheWorker.ts
import { InMemoryCache } from '@apollo/client';

class VitalsCacheWorker {
  private cache: InMemoryCache;
  private operationQueue: Array<VitalsCacheOperation> = [];
  private isProcessing = false;
  
  constructor() {
    this.cache = new InMemoryCache();
    this.setupMessageHandling();
  }
  
  private setupMessageHandling() {
    self.onmessage = async (event: MessageEvent<VitalsCacheOperation>) => {
      const operation = event.data;
      
      try {
        const result = await this.processOperation(operation);
        self.postMessage({
          id: operation.id,
          success: true,
          result
        });
      } catch (error) {
        self.postMessage({
          id: operation.id,
          success: false,
          error: error.message
        });
      }
    };
  }
  
  private async processOperation(operation: VitalsCacheOperation): Promise<any> {
    switch (operation.type) {
      case 'READ':
        return this.handleRead(operation as CacheReadOperation);
      case 'WRITE':
        return this.handleWrite(operation as CacheWriteOperation);
      case 'BATCH_WRITE':
        return this.handleBatchWrite(operation as BatchCacheOperation);
      default:
        throw new Error(`Unknown operation type: ${operation.type}`);
    }
  }
  
  private async handleBatchWrite(operation: BatchCacheOperation): Promise<void> {
    // Process multiple cache writes efficiently
    const results = [];
    
    for (const op of operation.operations) {
      if (op.type === 'WRITE') {
        const result = await this.cache.writeQuery({
          query: op.query,
          data: op.data,
          variables: op.variables
        });
        results.push(result);
      }
    }
    
    return results;
  }
}

new VitalsCacheWorker();
```

## Implementation Phases

### Phase 1: Foundation Setup (Week 1)

#### 1.1 Web Worker Infrastructure
- Create base Worker classes and message passing protocols
- Implement CacheWorkerManager for worker orchestration
- Set up TypeScript definitions for cache operations
- Create utility functions for operation serialization

#### 1.2 Async Cache Wrapper
```typescript
// utils/AsyncCacheWrapper.ts
export class AsyncCacheWrapper {
  private workerManager: CacheWorkerManager;
  
  constructor(workerManager: CacheWorkerManager) {
    this.workerManager = workerManager;
  }
  
  async readQuery<T>(options: {
    query: DocumentNode;
    variables?: any;
    workerType: 'vitals' | 'profiles' | 'lists' | 'general';
  }): Promise<T | null> {
    const operation: CacheReadOperation = {
      id: generateOperationId(),
      type: 'READ',
      query: options.query,
      variables: options.variables,
      timestamp: Date.now(),
      priority: 'MEDIUM'
    };
    
    switch (options.workerType) {
      case 'vitals':
        return this.workerManager.processVitalsCache(operation);
      case 'profiles':
        return this.workerManager.processProfilesCache(operation);
      case 'lists':
        return this.workerManager.processListsCache(operation);
      default:
        return this.workerManager.processGeneralCache(operation);
    }
  }
  
  async writeQuery(options: {
    query: DocumentNode;
    data: any;
    variables?: any;
    workerType: 'vitals' | 'profiles' | 'lists' | 'general';
  }): Promise<void> {
    const operation: CacheWriteOperation = {
      id: generateOperationId(),
      type: 'WRITE',
      query: options.query,
      data: options.data,
      variables: options.variables,
      timestamp: Date.now(),
      priority: 'HIGH'
    };
    
    switch (options.workerType) {
      case 'vitals':
        return this.workerManager.processVitalsCache(operation);
      case 'profiles':
        return this.workerManager.processProfilesCache(operation);
      case 'lists':
        return this.workerManager.processListsCache(operation);
      default:
        return this.workerManager.processGeneralCache(operation);
    }
  }
  
  async batchWrite(operations: Array<{
    query: DocumentNode;
    data: any;
    variables?: any;
  }>, workerType: string): Promise<void> {
    const batchOperation: BatchCacheOperation = {
      id: generateOperationId(),
      type: 'BATCH_WRITE',
      operations: operations.map(op => ({
        id: generateOperationId(),
        type: 'WRITE' as const,
        query: op.query,
        data: op.data,
        variables: op.variables,
        timestamp: Date.now(),
        priority: 'HIGH' as const
      })),
      timestamp: Date.now(),
      priority: 'HIGH'
    };
    
    return this.workerManager.delegateToWorker(workerType, batchOperation);
  }
}
```

### Phase 2: Critical Migration (Week 2)

#### 2.1 Migrate useSubscriptionOnAllVitals.ts
```typescript
// Updated useSubscriptionOnAllVitals.ts with Web Workers
import { AsyncCacheWrapper } from 'utils/AsyncCacheWrapper';

const useSubscriptionOnAllVitals = () => {
  const { profileId, clinifyId } = useClinifyId();
  const asyncCache = useAsyncCache(); // Hook that provides AsyncCacheWrapper
  
  useSubscription<AllVitalsAddedSubs, AllVitalsAddedSubsVariables>(
    ALL_VITALS_INSERTED_SUBS,
    {
      variables: { profileId },
      onSubscriptionData: async ({ subscriptionData: { data } }) => {
        if (!data?.AllVitalsInserted) return;
        
        const vitalsInsertedData = data.AllVitalsInserted;
        
        try {
          // Read main vitals cache asynchronously
          const allVitalsCacheData = await asyncCache.readQuery<GetAllVitals>({
            query: GET_ALL_VITAL_SIGN,
            variables: {
              id: vitalsInsertedData.vitalId as string,
              clinifyId,
            },
            workerType: 'vitals'
          });
          
          if (allVitalsCacheData) {
            // Prepare batch operations for all vital sub-records
            const batchOperations = [
              {
                query: GET_ALL_VITAL_SIGN,
                data: {
                  ...allVitalsCacheData,
                  getVitalById: {
                    ...allVitalsCacheData.getVitalById,
                    anthropometry: [...(allVitalsCacheData.getVitalById.anthropometry || []), {
                      __typename: vitalsInsertedData.anthropometry.__typename,
                      id: vitalsInsertedData.anthropometry.id,
                      // ... other fields
                    }],
                    bloodPressure: [...(allVitalsCacheData.getVitalById.bloodPressure || []), 
                      vitalsInsertedData.bloodPressure
                    ],
                    // ... other vital types
                  }
                },
                variables: {
                  id: vitalsInsertedData.vitalId as string,
                  clinifyId,
                }
              }
            ];
            
            // Process all cache updates in a single batch
            await asyncCache.batchWrite(batchOperations, 'vitals');
            
            // Update individual vital type caches
            await updateVitalSubRecordsAsync(asyncCache, vitalsInsertedData);
          }
        } catch (error) {
          console.error('Error processing vitals subscription:', error);
          // Fallback to synchronous processing if needed
        }
      },
    }
  );
};

const updateVitalSubRecordsAsync = async (
  asyncCache: AsyncCacheWrapper, 
  vitalsData: any
) => {
  const subRecordUpdates = [
    {
      query: GET_ANTHROPOMETRY_INFOS,
      data: { /* processed anthropometry data */ },
      variables: { parentRecordId: vitalsData.vitalId }
    },
    {
      query: GET_BLOOD_PRESSURE_INFOS, 
      data: { /* processed blood pressure data */ },
      variables: { parentRecordId: vitalsData.vitalId }
    },
    // ... other vital types
  ];
  
  await asyncCache.batchWrite(subRecordUpdates, 'vitals');
};
```

#### 2.2 Migrate useSubscriptionOnProfileDetails.ts
```typescript
// Updated useSubscriptionOnProfileDetails.ts with Web Workers
export default () => {
  const { profileId, hospitalId, hmoId } = useHospitalData();
  const { filters } = useFiltersTracker('HmoClaim');
  const { filters: filterPreAuth } = useFiltersTracker('Preauthorization');
  const asyncCache = useAsyncCache();
  
  // Memoize subscription variables to prevent re-initialization
  const subscriptionVariables = useMemo(() => ({
    profileId, 
    hospitalId, 
    ...(hmoId ? { hmoProviderId: hmoId } : {})
  }), [profileId, hospitalId, hmoId]);
  
  // Memoize filter processing
  const processFiltersAsync = useCallback(async (
    updatedRecord: any, 
    filters: any[], 
    queryType: 'HMO_CLAIMS' | 'PREAUTH'
  ) => {
    const batchOperations = [];
    
    for (const filter of filters) {
      try {
        const query = queryType === 'HMO_CLAIMS' ? GET_HOSPITAL_HMO_CLAIMS : GET_HOSPITAL_PREAUTHORIZATIONS;
        const cacheData = await asyncCache.readQuery({
          query,
          variables: { filterOptions: filter },
          workerType: 'profiles'
        });
        
        if (cacheData) {
          const processedData = processRecordUpdate(cacheData, updatedRecord, filter);
          batchOperations.push({
            query,
            data: processedData,
            variables: { filterOptions: filter }
          });
        }
      } catch (error) {
        console.error(`Error processing filter ${filter}:`, error);
      }
    }
    
    if (batchOperations.length > 0) {
      await asyncCache.batchWrite(batchOperations, 'profiles');
    }
  }, [asyncCache]);
  
  useSubscription(HMO_CLAIM_UPDATED_SUBS, {
    variables: subscriptionVariables,
    onSubscriptionData: async ({ subscriptionData: { data } }) => {
      if (!data?.HMOClaimUpdated) return;
      
      try {
        await processFiltersAsync(data.HMOClaimUpdated, filters, 'HMO_CLAIMS');
      } catch (error) {
        console.error('Error processing HMO claim update:', error);
      }
    },
  });
  
  // Similar pattern for other subscriptions...
};
```

### Phase 3: Advanced Optimizations (Week 3)

#### 3.1 Intelligent Batching and Debouncing
```typescript
// utils/SmartCacheBatcher.ts
export class SmartCacheBatcher {
  private pendingOperations: Map<string, Array<CacheOperation>> = new Map();
  private batchTimers: Map<string, NodeJS.Timeout> = new Map();
  private asyncCache: AsyncCacheWrapper;
  
  constructor(asyncCache: AsyncCacheWrapper) {
    this.asyncCache = asyncCache;
  }
  
  addOperation(workerType: string, operation: CacheOperation) {
    if (!this.pendingOperations.has(workerType)) {
      this.pendingOperations.set(workerType, []);
    }
    
    this.pendingOperations.get(workerType)!.push(operation);
    
    // Clear existing timer
    if (this.batchTimers.has(workerType)) {
      clearTimeout(this.batchTimers.get(workerType)!);
    }
    
    // Set new timer for batch processing
    const timer = setTimeout(() => {
      this.processBatch(workerType);
    }, 50); // 50ms debounce
    
    this.batchTimers.set(workerType, timer);
  }
  
  private async processBatch(workerType: string) {
    const operations = this.pendingOperations.get(workerType) || [];
    if (operations.length === 0) return;
    
    // Clear the batch
    this.pendingOperations.set(workerType, []);
    this.batchTimers.delete(workerType);
    
    // Group operations by type
    const reads = operations.filter(op => op.type === 'READ');
    const writes = operations.filter(op => op.type === 'WRITE');
    
    try {
      // Process reads first, then writes
      if (reads.length > 0) {
        await this.processBatchReads(workerType, reads as CacheReadOperation[]);
      }
      
      if (writes.length > 0) {
        await this.asyncCache.batchWrite(
          writes.map(op => ({
            query: (op as CacheWriteOperation).query,
            data: (op as CacheWriteOperation).data,
            variables: (op as CacheWriteOperation).variables
          })),
          workerType
        );
      }
    } catch (error) {
      console.error(`Error processing batch for ${workerType}:`, error);
    }
  }
}
```

#### 3.2 Cache Synchronization Strategy
```typescript
// utils/CacheSynchronizer.ts
export class CacheSynchronizer {
  private mainCache: InMemoryCache;
  private workerCaches: Map<string, Worker>;
  private syncQueue: Array<SyncOperation> = [];
  
  constructor(mainCache: InMemoryCache, workers: Map<string, Worker>) {
    this.mainCache = mainCache;
    this.workerCaches = workers;
    this.setupSyncMechanism();
  }
  
  private setupSyncMechanism() {
    // Sync worker cache changes back to main cache periodically
    setInterval(() => {
      this.syncWorkerCachesToMain();
    }, 1000); // Sync every second
  }
  
  private async syncWorkerCachesToMain() {
    for (const [workerType, worker] of this.workerCaches.entries()) {
      try {
        // Request cache state from worker
        const cacheState = await this.requestCacheState(worker);
        
        // Apply changes to main cache
        this.applyCacheState(cacheState);
      } catch (error) {
        console.error(`Error syncing ${workerType} cache:`, error);
      }
    }
  }
  
  private requestCacheState(worker: Worker): Promise<any> {
    return new Promise((resolve, reject) => {
      const messageId = generateOperationId();
      
      const handleMessage = (event: MessageEvent) => {
        if (event.data.id === messageId) {
          worker.removeEventListener('message', handleMessage);
          if (event.data.success) {
            resolve(event.data.cacheState);
          } else {
            reject(new Error(event.data.error));
          }
        }
      };
      
      worker.addEventListener('message', handleMessage);
      worker.postMessage({
        id: messageId,
        type: 'GET_CACHE_STATE'
      });
    });
  }
}
```

### Phase 4: Performance Monitoring (Week 4)

#### 4.1 Web Worker Performance Metrics
```typescript
// utils/WorkerPerformanceMonitor.ts
export class WorkerPerformanceMonitor {
  private metrics: Map<string, Array<OperationMetric>> = new Map();
  private alertThresholds = {
    operationDuration: 100, // ms
    queueLength: 50,
    errorRate: 0.05 // 5%
  };
  
  recordOperation(
    workerType: string, 
    operationId: string, 
    duration: number, 
    success: boolean
  ) {
    if (!this.metrics.has(workerType)) {
      this.metrics.set(workerType, []);
    }
    
    const metric: OperationMetric = {
      id: operationId,
      timestamp: Date.now(),
      duration,
      success,
      workerType
    };
    
    this.metrics.get(workerType)!.push(metric);
    this.checkAlerts(workerType);
  }
  
  private checkAlerts(workerType: string) {
    const workerMetrics = this.metrics.get(workerType) || [];
    const recentMetrics = workerMetrics.filter(
      m => Date.now() - m.timestamp < 60000 // Last minute
    );
    
    // Check average duration
    const avgDuration = recentMetrics.reduce((sum, m) => sum + m.duration, 0) / recentMetrics.length;
    if (avgDuration > this.alertThresholds.operationDuration) {
      console.warn(`${workerType} worker performance degraded: ${avgDuration}ms avg`);
    }
    
    // Check error rate
    const errorRate = recentMetrics.filter(m => !m.success).length / recentMetrics.length;
    if (errorRate > this.alertThresholds.errorRate) {
      console.error(`${workerType} worker error rate too high: ${errorRate * 100}%`);
    }
  }
  
  getPerformanceReport(): PerformanceReport {
    const report: PerformanceReport = {
      workers: {},
      overallHealth: 'GOOD'
    };
    
    for (const [workerType, metrics] of this.metrics.entries()) {
      const recentMetrics = metrics.filter(
        m => Date.now() - m.timestamp < 300000 // Last 5 minutes
      );
      
      report.workers[workerType] = {
        totalOperations: recentMetrics.length,
        avgDuration: recentMetrics.reduce((sum, m) => sum + m.duration, 0) / recentMetrics.length,
        successRate: recentMetrics.filter(m => m.success).length / recentMetrics.length,
        queueLength: this.getQueueLength(workerType)
      };
    }
    
    return report;
  }
}
```

## Expected Performance Improvements

### Before Migration
- **Main Thread Blocking**: 100-500ms per subscription event
- **Cache Operations**: 100+ synchronous operations per event
- **Page Responsiveness**: Frequent freezes during high subscription activity
- **Memory Usage**: High due to blocking operations backing up

### After Migration
- **Main Thread Blocking**: < 16ms (non-blocking async operations)
- **Cache Operations**: Batched processing in dedicated workers
- **Page Responsiveness**: Maintains 60fps during heavy subscription activity
- **Memory Usage**: 40-60% reduction through optimized batching

### Performance Metrics
- **UI Freeze Duration**: 95% reduction (500ms → 25ms)
- **Subscription Processing Time**: 70% reduction through batching
- **Memory Usage**: 50% reduction through optimized worker pools
- **CPU Usage**: More efficient distribution across worker threads

## Risk Mitigation & Rollback Strategy

### 1. **Gradual Migration Approach**
```typescript
// Feature flag system for gradual rollout
const useWebWorkerCache = () => {
  const isWebWorkerEnabled = useFeatureFlag('WEB_WORKER_CACHE');
  const fallbackCache = useApolloClient().cache;
  const workerCache = useAsyncCache();
  
  return isWebWorkerEnabled ? workerCache : fallbackCache;
};
```

### 2. **Fallback Mechanisms**
- Automatic fallback to synchronous cache on worker errors
- Circuit breaker pattern for worker failure protection
- Performance monitoring to detect degradation

### 3. **Browser Compatibility**
- Feature detection for Web Worker support
- Polyfill strategies for older browsers
- Graceful degradation for unsupported environments

## Implementation Timeline

### Week 1: Foundation
- [ ] Set up Web Worker infrastructure
- [ ] Create AsyncCacheWrapper utility
- [ ] Implement basic worker communication protocols
- [ ] Create TypeScript definitions

### Week 2: Critical Migration
- [ ] Migrate useSubscriptionOnAllVitals.ts
- [ ] Migrate useSubscriptionOnProfileDetails.ts
- [ ] Implement batch processing for vitals operations
- [ ] Add error handling and fallback mechanisms

### Week 3: Advanced Features  
- [ ] Implement SmartCacheBatcher
- [ ] Add cache synchronization strategy
- [ ] Optimize worker pool management
- [ ] Add intelligent operation prioritization

### Week 4: Monitoring & Polish
- [ ] Implement WorkerPerformanceMonitor
- [ ] Add comprehensive logging and metrics
- [ ] Performance testing and optimization
- [ ] Documentation and training materials

## Success Criteria

### Performance Targets
- [ ] 95% reduction in main thread blocking time
- [ ] 70% reduction in subscription processing latency
- [ ] 50% reduction in memory usage during peak activity
- [ ] Maintain 60fps during heavy subscription events

### Stability Targets
- [ ] < 0.1% worker failure rate
- [ ] 100% backward compatibility maintained
- [ ] Zero breaking changes to existing API
- [ ] Successful rollback capability demonstrated

### Developer Experience
- [ ] Transparent integration (no API changes required)
- [ ] Clear performance monitoring dashboard
- [ ] Comprehensive error handling and debugging
- [ ] Complete documentation and examples

## Conclusion

This Web Worker migration plan provides a comprehensive strategy for eliminating subscription-related page freezes while maintaining system reliability and developer productivity. The phased approach ensures minimal risk while delivering significant performance improvements.

The key to success is the combination of:
1. **Dedicated worker pools** for different cache operation types
2. **Intelligent batching** to optimize worker utilization  
3. **Robust fallback mechanisms** to ensure reliability
4. **Comprehensive monitoring** to track performance improvements

Implementation should begin with the foundation phase, followed by migration of the highest-impact components first (useSubscriptionOnAllVitals.ts), ensuring each phase is thoroughly tested before proceeding to the next.