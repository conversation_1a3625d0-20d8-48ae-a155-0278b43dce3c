# Subscription Performance Issues Analysis

## Overview
The subscription system is causing page freezes due to multiple performance issues that create a perfect storm of blocking operations when subscription events are received.

## Critical Performance Issues

### 1. **Massive Subscription Orchestration (useFireSubscription.ts:30-55)**
**Issue**: Single hook calls 20+ different subscription hooks simultaneously without any optimization.

```typescript
export default () => {
  subscriptionParams.forEach((result) => useSubscriptionOnList(result));
  vitalSubscriptionParams.forEach((result) => useSubscriptionOnVitalSubrecords(result));
  subRecordsSubscriptionParams.forEach((result) => useSubscriptionOnSubrecords(result));
  profileSectionSubscriptionParams.forEach((result) => useSubscriptionOnProfileInfos(result));
  useSubscriptionOnProfileDetails();
  useSubscriptionOnPermissions();
  // ... 15+ more subscription hooks
};
```

**Problem**: All subscriptions fire simultaneously, creating cascade effects when events arrive.

### 2. **Nested forEach Loops in Subscription Handlers (34+ instances)**

#### useSubscriptionOnProfileDetails.ts
- **Line 60**: `filters.forEach((filter) => {`
- **Line 122**: `filters.forEach((filter) => {` 
- **Line 162**: `filters.forEach((filter) => {`
- **Line 209**: `filterPreAuth.forEach((_filter) => {`
- **Line 258**: `filterPreAuth.forEach((_filter) => {`

#### useSubscriptionOnList.ts
- **Line 189**: `allFilters.forEach((filterInput) => {`
- **Line 220**: `allPossibleFilters.forEach((filterInput_) =>`
- **Line 231**: `].forEach((filterInput) =>`
- **Lines 290, 294, 332, 337, 372, 379**: Multiple forEach loops in different subscription handlers

**Problem**: When a subscription event arrives, handlers execute nested loops over filter arrays, multiplying the work exponentially.

### 3. **Synchronous Cache Operations (38+ readQuery + 56+ writeQuery instances)**

#### useSubscriptionOnAllVitals.ts - Most Critical
**Lines 62, 71, 80, 86, 92, 101, 110, 135**: 8 synchronous cache.readQuery operations
**Lines 230, 256, 272, 288, 304, 320, 336, 352**: 8+ cache.writeQuery operations per event

#### useSubscriptionOnProfileDetails.ts
**Lines 61, 123, 167, 221, 260**: cache.readQuery within forEach loops
**Lines 87, 105, 146, 193, 242, 293, 326**: cache.writeQuery within forEach loops

**Problem**: Each subscription event triggers dozens of synchronous cache operations that block the main thread.

### 4. **Heavy Data Transformations in Subscription Handlers**

#### useSubscriptionOnProfileDetails.ts:72-84
```typescript
let list = cachedHmoClaimsData.map((item) =>
  item.id === updatedRecord?.id ? updatedRecord : item,
);
if (updatedRecord.utilizations?.length === 0) {
  list = cachedHmoClaimsData.filter((item) => item.id !== updatedRecord?.id);
}

const newData = {
  ...cacheData?.hospital,
  hmoClaims: {
    ...cacheData?.hospital?.hmoClaims,
    totalCount: cacheData?.hospital?.hmoClaims.totalCount,
    list: [...list],
  },
};
```

**Problem**: Complex object spreading, array mapping, and filtering operations performed synchronously in subscription handlers.

### 5. **No Memoization or Optimization Patterns**

#### Missing useMemo for expensive computations
- Filter generation: `generateFilterOptionsOnCreation(filter)` (useSubscriptionOnList.ts:167)
- Data transformations in subscription handlers
- Variable dependencies in subscription hooks

#### Missing useCallback for event handlers  
- All `onSubscriptionData` handlers recreated on every render
- No debouncing for rapid subscription events

#### Only one React.memo usage
- SubscriptionWrapper.tsx:10 - but the damage is already done in child hooks

### 6. **Inefficient Array Operations**

#### useSubscriptionOnList.ts:189-191
```typescript
allFilters.forEach((filterInput) => {
  addRecordToLinearPaginatedCache({ ...cacheParams, filterInput });
});
```

#### useSubscriptionOnProfileDetails.ts:209-250
```typescript
filterPreAuth.forEach((_filter) => {
  const filter = cloneDeep(_filter); // Expensive cloning operation
  // ... more operations
});
```

**Problem**: 
- Deep cloning operations (`cloneDeep`) in loops
- Array concatenation and spreading without optimization
- Linear searches through cached data arrays

### 7. **Cascading Cache Updates**

#### useSubscriptionOnAllVitals.ts Pattern
```typescript
// Single vital update triggers 8+ cache writes:
cache.writeQuery<GetAllVitals>(...);
cache.writeQuery<GetAnthropometryInfos>(...);
cache.writeQuery<GetBloodPressureInfos>(...);
cache.writeQuery<GetTemperatureInfos>(...);
// ... 5+ more cache writes
```

**Problem**: Single subscription event triggers multiple cache writes, each potentially causing React re-renders.

### 8. **Subscription Variable Dependencies**

#### useSubscriptionOnProfileDetails.ts:46-48
```typescript
useSubscription(UPDATE_PROFILE_DETAILS_SUB, {
  variables: { profileId, hospitalId },
});
```

**Problem**: Variables not memoized, causing subscription re-initialization on every render.

## Performance Impact Analysis

### Main Thread Blocking Sequence:
1. **Event Arrives** → Subscription handler starts
2. **forEach Loop** → Iterate over filters (5-10 iterations typical)
3. **cache.readQuery** → Synchronous cache read (blocks thread)
4. **Data Processing** → Array operations, object spreading, cloning
5. **cache.writeQuery** → Synchronous cache write (blocks thread)
6. **Repeat** → For each filter, each subscription type

### Estimated Operations Per Event:
- **Single subscription event** → 20+ subscription handlers activated
- **Each handler** → 2-10 filter iterations  
- **Each iteration** → 1-8 cache reads + 1-8 cache writes
- **Total per event** → 100+ synchronous cache operations

### Result: Page Freeze
With this volume of synchronous operations, even a single subscription event can block the main thread for 100-500ms, causing visible page freezes.

## Files Requiring Immediate Attention

### Critical Priority:
1. **useFireSubscription.ts** - Subscription orchestration
2. **useSubscriptionOnAllVitals.ts** - Most cache operations (23 writes per event)
3. **useSubscriptionOnProfileDetails.ts** - Complex nested loops with cache operations

### High Priority:
4. **useSubscriptionOnList.ts** - Generic list subscriptions with heavy forEach usage
5. **useSubscriptionOnBilling.ts** - 12 forEach loops with nested operations
6. **SubscriptionWrapper.tsx** - Entry point optimization

## Root Cause Summary

The page freezes are caused by a combination of:
1. **Architecture**: All subscriptions fire simultaneously without coordination
2. **Synchronous Operations**: Blocking cache reads/writes in main thread
3. **Nested Loops**: forEach operations multiplying computational complexity
4. **No Optimization**: Missing memoization, debouncing, and React optimizations
5. **Heavy Operations**: Complex data transformations in event handlers

This creates a cascading effect where each subscription event triggers hundreds of synchronous operations, overwhelming the main thread and causing UI freezes.