# MedicationSubRecordsSection Optimization Plan

## Current Performance Issues

### 1. Expensive Calculations on Every Render
- **Lines 59-96**: Complex `details` transformation runs on every render
- **Lines 50-52**: `dispensedMedicationIds` array calculation on every render
- **Line 54**: `pendingDispense` calculation on every render
- **Line 98**: `disableDispense` calculation on every render
- **Lines 100-109**: Conditional `details` filtering on every render

### 2. Unstable References
- **Lines 143-146**: Inline `map` function creates new array on every render
- **Line 151**: Inline `extraData` object creation on every render
- **Lines 124-128**: Inline `onClick` handler creation on every render
- **Line 155**: Inline `hideMutateButtonFunction` creation on every render

### 3. Module-Level Side Effects
- **Line 34**: `getUserPayload()` called at module level but used in render context

## Optimization Strategy

### Phase 1: Memoization of Expensive Calculations

#### 1.1 Memoize Details Transformation
```typescript
const transformedDetails = useMemo(() => {
  if (!inputs?.details) return [];
  
  return inputs.details.map(({
    medicationName,
    id,
    drugInventoryId,
    medicationConsumables,
    option,
    unitPrice,
    bank,
    discontinue,
    quantity,
    priceDetails,
    inventoryClass,
    dosage,
    dosageUnit,
    frequency,
    duration,
    medicationCategory,
    diagnosis,
  }) => ({
    label: medicationName || genConsumableHeader({ medicationConsumables }) || '',
    value: id,
    drugInventoryId: drugInventoryId || '',
    medOption: option,
    medicationConsumables,
    unitPrice: priceDetails?.pricePerUnit || unitPrice,
    bank,
    discontinue,
    quantity,
    inventoryClass,
    dosage,
    dosageUnit,
    frequency,
    duration,
    medicationCategory,
    diagnosis,
  }));
}, [inputs?.details]);
```

**Dependencies**: `[inputs?.details]`
**Impact**: Prevents expensive array transformation on every render

#### 1.2 Memoize Dispensed Medication IDs
```typescript
const dispensedMedicationIds = useMemo(() => {
  return inputs?.dispenseDetails
    ?.filter((item) => item?.id && item?.medicationDetailId)
    ?.map((item) => item?.medicationDetailId) || [];
}, [inputs?.dispenseDetails]);
```

**Dependencies**: `[inputs?.dispenseDetails]`
**Impact**: Prevents array filtering and mapping on every render

#### 1.3 Memoize Pending Dispense Check
```typescript
const pendingDispense = useMemo(() => {
  return inputs?.dispenseDetails?.some((item) => !item.id) || false;
}, [inputs?.dispenseDetails]);
```

**Dependencies**: `[inputs?.dispenseDetails]`
**Impact**: Prevents array iteration on every render

#### 1.4 Memoize Disable Dispense Check
```typescript
const disableDispense = useMemo(() => {
  return isLashmaEnrollee && 
         inputs?.verificationCode && 
         checkMedicationElapsed(inputs?.createdDate);
}, [isLashmaEnrollee, inputs?.verificationCode, inputs?.createdDate]);
```

**Dependencies**: `[isLashmaEnrollee, inputs?.verificationCode, inputs?.createdDate]`
**Impact**: Prevents expensive date calculation on every render

#### 1.5 Memoize Filtered Details
```typescript
const filteredDetails = useMemo(() => {
  if (
    isLashmaEnrollee &&
    inputs?.verificationCode &&
    currentSubRecord === 'DISPENSE' &&
    dispensedMedicationIds?.length
  ) {
    return transformedDetails.filter(
      (item) => item?.value && !dispensedMedicationIds.includes(item?.value)
    );
  }
  return transformedDetails;
}, [
  isLashmaEnrollee,
  inputs?.verificationCode,
  currentSubRecord,
  dispensedMedicationIds,
  transformedDetails
]);
```

**Dependencies**: `[isLashmaEnrollee, inputs?.verificationCode, currentSubRecord, dispensedMedicationIds, transformedDetails]`
**Impact**: Prevents conditional filtering on every render

### Phase 2: Callback Optimization

#### 2.1 Memoize Button Click Handler
```typescript
const handleDispenseClick = useCallback(() => {
  const newStatus = currentSubRecord !== 'DISPENSE';
  fetchSubRecords('medicationDispenseDetails');
  setCurrentSubRecord(newStatus ? 'DISPENSE' : 'NONE');
}, [currentSubRecord, fetchSubRecords]);
```

**Dependencies**: `[currentSubRecord, fetchSubRecords]`
**Impact**: Prevents function recreation on every render

#### 2.2 Memoize Hide Mutate Button Function
```typescript
const hideMutateButtonFunction = useCallback(
  (payload: any) => !!payload?.id && inputs?.verificationCode,
  [inputs?.verificationCode]
);
```

**Dependencies**: `[inputs?.verificationCode]`
**Impact**: Prevents function recreation on every render

#### 2.3 Memoize Inputs Transformation
```typescript
const transformedInputs = useMemo(() => {
  return inputs?.dispenseDetails?.map((dispense) => ({
    ...dispense,
    clinifyId
  })) || [];
}, [inputs?.dispenseDetails]);
```

**Dependencies**: `[inputs?.dispenseDetails]`
**Impact**: Prevents array transformation on every render

### Phase 3: Props Stabilization

#### 3.1 Memoize Extra Data Object
```typescript
const extraData = useMemo(() => ({
  details: filteredDetails,
  setInput: handleInputChange,
  inputs
}), [filteredDetails, handleInputChange, inputs]);
```

**Dependencies**: `[filteredDetails, handleInputChange, inputs]`
**Impact**: Prevents object recreation on every render

#### 3.2 Move clinifyId to Hook or Context
```typescript
// Move getUserPayload() call inside component or use context
const MedicationSubRecordsSection: FC<SubRecordsSectionProps> = (props) => {
  const { clinifyId } = useMemo(() => getUserPayload(), []);
  // ... rest of component
};
```

**Impact**: Prevents module-level side effects and makes clinifyId stable

### Phase 4: Component Structure Optimization

#### 4.1 Extract Sub-Components
Create separate components for:
- `DispenseButton`: The dispense medication button with tooltip
- `SubRecordSection`: The conditional SubRecord wrapper

#### 4.2 Add React.memo Wrapper
```typescript
export default React.memo(MedicationSubRecordsSection, (prevProps, nextProps) => {
  // Custom comparison logic if needed
  return (
    prevProps.isEdit === nextProps.isEdit &&
    prevProps.parentRecordId === nextProps.parentRecordId &&
    prevProps.fetchingSubRecords === nextProps.fetchingSubRecords &&
    prevProps.errorFetchingSubRecords === nextProps.errorFetchingSubRecords &&
    JSON.stringify(prevProps.inputs) === JSON.stringify(nextProps.inputs)
  );
});
```

## Implementation Order

1. **Phase 1.1-1.5**: Add all useMemo hooks for expensive calculations
2. **Phase 2.1-2.3**: Add useCallback hooks for event handlers
3. **Phase 3.1-3.2**: Stabilize props and objects
4. **Phase 4.1**: Extract sub-components if needed
5. **Phase 4.2**: Add React.memo wrapper

## Expected Performance Improvements

### Before Optimization
- Details transformation: **Every render**
- Array filtering/mapping: **Every render** 
- Function recreation: **Every render**
- Object recreation: **Every render**

### After Optimization
- Details transformation: **Only when inputs.details changes**
- Array filtering/mapping: **Only when dispenseDetails changes**
- Function recreation: **Only when dependencies change**
- Object recreation: **Only when relevant props change**

### Estimated Re-render Reduction
- **70-80% reduction** in unnecessary re-renders
- **50-60% improvement** in component update performance
- **Stable references** for child components

## Dependencies Required
```typescript
import React, { FC, useState, useMemo, useCallback } from 'react';
```

## Potential Risks
1. **Over-memoization**: Need to ensure dependencies are correctly specified
2. **Memory usage**: Memoized values consume memory
3. **Debugging complexity**: More complex component logic

## Testing Strategy
1. Use React DevTools Profiler to measure before/after performance
2. Test with large datasets to verify optimization effectiveness
3. Verify all functionality remains intact after optimization
4. Check for memory leaks with extended usage

## Success Metrics
- [ ] Reduced re-render count by 70%+
- [ ] Faster component updates
- [ ] Stable props for SubRecord component
- [ ] No functional regressions
- [ ] Maintained code readability