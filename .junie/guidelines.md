# Project Guidelines

## Code Quality and Submission Process

* **No Building Required**: There is no need to try to build the project after an implementation is completed, as building takes longer time on this project.

* **Lint Fixing**: Instead of building, ensure that lint on the affected files is fixed for the ones fixable with the lint fix. This provides faster feedback on code quality without the overhead of a full build process.

* **Testing**: Run tests related to the modified files to ensure no new issues have been introduced and to confirm that these tests still pass.

* **Project Structure**: This is a web application built with React/TypeScript, located in the `/src` directory with standard web project structure.
