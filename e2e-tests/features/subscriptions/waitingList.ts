import { Chance } from 'chance';
import { Selector } from 'testcafe';
import { sideMenu, InputSelect, element } from '../../utilities/common-selectors';
import { setFixture, getLocation, login, inputSelector } from '../../utilities/common-testscripts';
import { credentials } from '../../utilities/credentials';

setFixture();

test('check in a patient', async (t) => {
  const searchTerm = 'Semilore';
  await login(t, 'organisation', credentials.orgDoctor);

  await t
    .click(sideMenu('Patient Lookup'))
    .expect(getLocation())
    .match(/\/facility\/lookup$/i)
    .click(Selector(`div.app-search-input > input[placeholder="Enter Patient's Name"]`))
    .typeText(inputSelector('placeholder', "Enter Patient's Name"), searchTerm, { replace: true })
    .click(element.button('Search'))
    .expect(
      Selector('div.table-wrapper > table > tbody > tr > td').withText(`${searchTerm}`).exists,
    )
    .ok();

  await t
    .click(
      Selector(
        `div.table-wrapper > table > tbody > tr > td + span.checkbox__input > input[type="checkbox]`,
      ),
    )
    .click(Selector('div[data-tooltip-content="Check In"]'))
    .click(sideMenu('Patient Waiting List'))
    .expect(
      Selector('div.table-wrapper > table > tbody > tr > td').withText('Semilore Idowu').exists,
    )
    .ok();
});

test('check in and assign immediately', async (t) => {
  const searchTerm = 'Semilore';
  await login(t, 'organisation', credentials.orgDoctor);

  await t
    .click(sideMenu('Patient Lookup'))
    .expect(getLocation())
    .match(/\/facility\/lookup$/i)
    .click(Selector(`div.app-search-input > input[placeholder="Enter Patient's Name"]`))
    .typeText(inputSelector('placeholder', "Enter Patient's Name"), searchTerm, { replace: true })
    .click(element.button('Search'))
    .click(Selector('div.table-wrapper > table > tbody > tr > td').withText(Chance().name()))
    .click(Selector('div.react-select__placeholder').withText(Chance().word()))
    .click(Selector('div.react-select__singlevalue').withText('OrganizationDoctor'))
    .click(Selector('div.react-select__singlevalue').withText('Doctor Doctor'))
    .click(Selector('div.app-button > span').withText('Check In & Assign'));
});
