export interface ICredentials {
  origin: string;
  patient: {
    user: string;
    password: string;
  };
  orgDoctor: {
    user: string;
    password: string;
  };
}

export const credentials: ICredentials = {
  origin: 'http://localhost:3000/',
  patient: {
    user: `${process.env.REACT_APP_PATIENT_LOGIN_NUMBER}`,
    password: `${process.env.REACT_APP_PATIENT_LOGIN_PASSWORD}`,
  },
  orgDoctor: {
    user: `${process.env.REACT_APP_ORG_LOGIN_EMAIL}`,
    password: `${process.env.REACT_APP_ORG_LOGIN_PASSWORD}`,
  },
};
