## Problem Solved

-

## Things to consider
### Check all below that applies to this PR
- [ ] Add New Feature
- [ ] Update Existing Implementation
- [ ] Bug Fix

### General
- [ ] Did you to commit without bypassing the pre-commit hook?
- [ ] Did you ensure that your changes did not break existing implementations?
- [ ] Is the description sufficient to explain what this PR is about?
- [ ] Is this PR sufficient enough to address the issues stated in the description?

### Subscription
- [ ] Did you implement subscription for your task?
- [ ] Did you ensure the subscription is working?

### Permission
- [ ] Did you implement permission for your task?
- [ ] Did you ensure the permission is working?

### Responsiveness
- [ ] Is this PR responsive on Smart phones, Tablets, and Laptops?

### Linting
- [ ] Is your terminal console free of errors?
- [ ] Is this PR free of comments?
- [ ] Is this PR free of console objects e.g console.log, console.warn?
- [ ] Is this PR lint error free?

### Testing
- [ ] Is the unit test passing?
- [ ] Is the end to end test passing?
- [ ] Did you cover all lines added?
- [ ] Did you fix failing test of changed implementations?
- [ ] Did you meet test coverage?


## All Dashboard Icons
- [ ] Have you created, updated, and deleted the record/sub records?
- [ ] Are the data showing on the record table?
- [ ] Did you search, filter date range, view, edit, delete, archive, and unarchive records from actions and table header?
- [ ] Are the pre populated fields all having data?
- [ ] Are hidden fields not visible on record creation for patients by default?
- [ ] Are record creator name, created date & time, and last modified name, updated date & time showing and accurate?
- [ ] Did you ensure that deleted records on view records page gets redirected to the record table?
- [ ] Did you ensure all fields are holding updated and saved data on settings page?
- [ ] Did you ensure patient overview page is loading and showing correct information?

## Investigation (Laboratory and Radiology)
- [ ] Have you requested, processed, and updated an investigation?
- [ ] Are all investigation status working?
- [ ] Can organization users see record list, search, filter date range, view, edit, delete, archive, and unarchive records from actions and table header and view daily Investigations?
- [ ] Did requested and processed investigation auto generate a bill?
- [ ] Can you email and print investigation?

## Billing
### Bill Now
- [ ] Did you manually create a bill as Frontdesk and Cashier?
- [ ] Does each of the bill item amount match the total?
- [ ] Did patient exclude and include bill items before paying?
- [ ] Does recalled bill remove bill from patient bill list?
- [ ] Does resent bill add bill to patient bill list?
- [ ] Are split bill sorted and grouped together on bill statement?
- [ ] Did you pay using Clinify wallet for send bill and request payment? 

 
### Bill Later
- [ ] Does adding new labtest / radiology exam name add new bill item on the same bill?
- [ ] Does deleting existing labtest / radiology exam name remove the bill item generated?
- [ ] Does lab and radiology investigation requested on the same day add seperate bill item to the same auto generated bill?
- [ ] Does external investigation generates bill on process investigation?
- [ ] Does requesting same investigation (labtest and radiology exam) show investigation count I.e investigation—1 & investigation—2?
- [ ] Does created admission, consultation, Immunization, procedure record, have bill id, billing date & time, and bill status?
- [ ] Does medication record have bill id, billing date & time, and bill status after medication is dispensed?
- [ ] Does medication record without bill id, billing date & time, and bill status after all dispensed medication is deleted?
- [ ] Does dispensing medication once or more than once display medication quantity I.e 1x & 2x?
- [ ] Does the bill(s) for billable records created on the same day gets routed on one bill?
- [ ] Does the bill(s) for billable records created on the same day by different organization users gets routed/ re routed accordingly?
- [ ] Can you email and print bill?
- [ ] Can Cashier see all bills created by all users for the same day in one bill?
- [ ] Did you ensure that only bill(s) generated by organisation users with the same role can see each others bills?

### Patient Dashboard
- [ ] Can patient pay for the bill(s), fund wallet and transfer funds to another wallet?
- [ ] Bill(s) with zero amount should not be sent or made visible on patient dashboard.


## Vitals
- [ ] Is the BMI auto calculating on Anthropometry?
- [ ] When switching between tabs, does loader show and does it load once?

## Admission
- [ ] Is Discharged Patient, Transferred Patient, Blood Transfusion, Input / Output sub records working?

## Allergy
- [ ] Are the input tags working?

## Consultation
- [ ] Is internal and external referral working?

## Immunization
- [ ] Is the schedule chart showing?

## Medication
- [ ] Have you prescribed, dispensed and administered medication?

## Procedure
- [ ] Are you able upload, view, and delete attached files?