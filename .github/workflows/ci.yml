name: ci

on:
  push:
    branches:
      - develop
      - staging
      - master

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [16.x]
    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
      - name: Yarn Install
        run: yarn install
      - name: Yarn Lint
        run: yarn lint
      - name: Yarn Test
        run: yarn test
        env:
          CI: true

  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [16.x]
    needs: test
    if: contains('refs/heads/staging refs/heads/master refs/heads/develop', github.ref)
    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
      - name: Yarn Install
        run: yarn install
      - name: Yarn Build
        run: ./deploy.sh build
        env:
          NODE_OPTIONS: "--max_old_space_size=4096"
          DEV_REACT_APP_API_URL: ${{ secrets.DEV_REACT_APP_API_URL }}
          DEV_REACT_APP_SOC_URL: ${{ secrets.DEV_REACT_APP_SOC_URL }}
          DEV_REACT_APP_PAYSTACK_PUBLIC_KEY: ${{ secrets.DEV_REACT_APP_PAYSTACK_PUBLIC_KEY }}
          DEV_REACT_APP_ATTACHMENT_API: ${{ secrets.DEV_REACT_APP_ATTACHMENT_API }}
          DEV_REACT_APP_TRANSCRIPTION_OUTPUT_BUCKET_NAME: ${{ secrets.DEV_REACT_APP_TRANSCRIPTION_OUTPUT_BUCKET_NAME }}
          DEV_REACT_APP_TRANSCRIPTION_INPUT_BUCKET_NAME: ${{ secrets.DEV_REACT_APP_TRANSCRIPTION_INPUT_BUCKET_NAME }}
          DEV_REACT_APP_TRANSCRIBE_JOB_ACCESS_ROLE_ARN: ${{ secrets.DEV_REACT_APP_TRANSCRIBE_JOB_ACCESS_ROLE_ARN }}
          STAGING_REACT_APP_API_URL: ${{ secrets.STAGING_REACT_APP_API_URL }}
          STAGING_REACT_APP_SOC_URL: ${{ secrets.STAGING_REACT_APP_SOC_URL }}
          STAGING_REACT_APP_PAYSTACK_PUBLIC_KEY: ${{ secrets.STAGING_REACT_APP_PAYSTACK_PUBLIC_KEY }}
          STAGING_REACT_APP_ATTACHMENT_API: ${{ secrets.STAGING_REACT_APP_ATTACHMENT_API }}
          STAGING_REACT_APP_TRANSCRIPTION_OUTPUT_BUCKET_NAME: ${{ secrets.STAGING_REACT_APP_TRANSCRIPTION_OUTPUT_BUCKET_NAME }}
          STAGING_REACT_APP_TRANSCRIPTION_INPUT_BUCKET_NAME: ${{ secrets.STAGING_REACT_APP_TRANSCRIPTION_INPUT_BUCKET_NAME }}
          STAGING_REACT_APP_TRANSCRIBE_JOB_ACCESS_ROLE_ARN: ${{ secrets.STAGING_REACT_APP_TRANSCRIBE_JOB_ACCESS_ROLE_ARN }}
          STAGING_REACT_APP_TRANSCRIBE_URL: ${{ secrets.STAGING_REACT_APP_TRANSCRIBE_URL }}
          PRODUCTION_REACT_APP_API_URL: ${{ secrets.PRODUCTION_REACT_APP_API_URL }}
          PRODUCTION_REACT_APP_SOC_URL: ${{ secrets.PRODUCTION_REACT_APP_SOC_URL }}
          PRODUCTION_REACT_APP_PAYSTACK_PUBLIC_KEY: ${{ secrets.PRODUCTION_REACT_APP_PAYSTACK_PUBLIC_KEY }}
          PRODUCTION_REACT_APP_ATTACHMENT_API: ${{ secrets.PRODUCTION_REACT_APP_ATTACHMENT_API }}
          PRODUCTION_REACT_APP_WEMA_DOMAIN: ${{ secrets.PRODUCTION_REACT_APP_WEMA_DOMAIN }}
          PRODUCTION_REACT_APP_NHIA_DOMAIN: ${{ secrets.PRODUCTION_REACT_APP_NHIA_DOMAIN }}
          PRODUCTION_REACT_APP_LASHMA_DOMAIN: ${{ secrets.PRODUCTION_REACT_APP_LASHMA_DOMAIN }}
          PRODUCTION_REACT_APP_LEADWAY_DOMAIN: ${{ secrets.PRODUCTION_REACT_APP_LEADWAY_DOMAIN }}
          PRODUCTION_REACT_APP_NIGCOMSAT_DOMAIN: ${{ secrets.PRODUCTION_REACT_APP_NIGCOMSAT_DOMAIN }}
          PRODUCTION_REACT_APP_POLICE_HMO_DOMAIN: ${{ secrets.PRODUCTION_REACT_APP_POLICE_HMO_DOMAIN }}
          PRODUCTION_REACT_APP_MEDPLUS_DOMAIN: ${{ secrets.PRODUCTION_REACT_APP_MEDPLUS_DOMAIN }}
          PRODUCTION_REACT_APP_OYSHIA_DOMAIN: ${{ secrets.PRODUCTION_REACT_APP_OYSHIA_DOMAIN }}
          PRODUCTION_REACT_APP_OGSHIA_DOMAIN: ${{ secrets.PRODUCTION_REACT_APP_OGSHIA_DOMAIN }}
          PRODUCTION_REACT_APP_ODCHC_DOMAIN: ${{ secrets.PRODUCTION_REACT_APP_ODCHC_DOMAIN }}
          PRODUCTION_REACT_APP_SYNDICATEBIO_DOMAIN: ${{ secrets.PRODUCTION_REACT_APP_SYNDICATEBIO_DOMAIN }}
          PRODUCTION_REACT_APP_NELFUND_DOMAIN: ${{ secrets.PRODUCTION_REACT_APP_NELFUND_DOMAIN }}
          PRODUCTION_REACT_APP_NGSCHS_DOMAIN: ${{secrets.PRODUCTION_REACT_APP_NGSCHS_DOMAIN}}
          PRODUCTION_REACT_APP_NPA_DOMAIN: ${{ secrets.PRODUCTION_REACT_APP_NPA_DOMAIN }}
          PRODUCTION_REACT_APP_TRUDOC_DOMAIN: ${{ secrets.PRODUCTION_REACT_APP_TRUDOC_DOMAIN }}
          PRODUCTION_REACT_APP_FHIS_DOMAIN: ${{ secrets.PRODUCTION_REACT_APP_FHIS_DOMAIN }}
          PRODUCTION_REACT_APP_OHIS_DOMAIN: ${{ secrets.PRODUCTION_REACT_APP_OHIS_DOMAIN }}
          PRODUCTION_REACT_APP_NNPC_DOMAIN: ${{ secrets.PRODUCTION_REACT_APP_NNPC_DOMAIN }}
          PRODUCTION_REACT_APP_TRANSCRIPTION_OUTPUT_BUCKET_NAME: ${{ secrets.PRODUCTION_REACT_APP_TRANSCRIPTION_OUTPUT_BUCKET_NAME }}
          PRODUCTION_REACT_APP_TRANSCRIPTION_INPUT_BUCKET_NAME: ${{ secrets.PRODUCTION_REACT_APP_TRANSCRIPTION_INPUT_BUCKET_NAME }}
          PRODUCTION_REACT_APP_TRANSCRIBE_JOB_ACCESS_ROLE_ARN: ${{ secrets.PRODUCTION_REACT_APP_TRANSCRIBE_JOB_ACCESS_ROLE_ARN }}
          PRODUCTION_REACT_APP_TRANSCRIBE_URL: ${{ secrets.PRODUCTION_REACT_APP_TRANSCRIBE_URL }}
          REACT_APP_TRANSLATION_API_URL: ${{ secrets.REACT_APP_TRANSLATION_API_URL }}
          REACT_APP_AWS_SECRET_ACCESS_KEY: ${{ secrets.REACT_APP_AWS_SECRET_ACCESS_KEY }}
          REACT_APP_AWS_ACCESS_KEY_ID: ${{ secrets.REACT_APP_AWS_ACCESS_KEY_ID }}
          REACT_APP_AWS_REGION: ${{ secrets.REACT_APP_AWS_REGION }}
          REACT_APP_MAPBOX_ACCESS_TOKEN: ${{ secrets.REACT_APP_MAPBOX_ACCESS_TOKEN }}
          REACT_APP_ENCRYPTION_KEY: ${{ secrets.REACT_APP_ENCRYPTION_KEY }}
      - name: move deploy script
        run: mv deploy.sh build/
      - name: Share artifact inside workflow
        uses: actions/upload-artifact@v4
        with:
          name: build-artifact
          path: build

  deploy:
    runs-on: ubuntu-latest
    needs: build
    if: contains('refs/heads/develop refs/heads/staging refs/heads/master', github.ref)
    steps:
      # Download previously shared build
      - name: Get artifact
        uses: actions/download-artifact@v4
        with:
          name: build-artifact
          path: build
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_DEFAULT_REGION }}
      # Copy the files from build folder to the S3 bucket
      - name: Deploy to S3
        run: |
          chmod +x deploy.sh
          ./deploy.sh deploy_s3
        working-directory: build
      - name: Clear Cache
        run: ./deploy.sh clearCache
        env:
          DEV_DISTRIBUTION_ID: ${{ secrets.DEV_DISTRIBUTION_ID }}
          STAGING_DISTRIBUTION_ID: ${{ secrets.STAGING_DISTRIBUTION_ID }}
          PRODUCTION_DISTRIBUTION_ID: ${{ secrets.PRODUCTION_DISTRIBUTION_ID }}
        working-directory: build
